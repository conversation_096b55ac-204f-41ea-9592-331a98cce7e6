package com.pgb.service.domain.zc.common.textbook.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * 教材册别实体
 *
 * <AUTHOR>
 * Created by 2025/7/25 19:35
 */
@Data
@Schema(description = "教材册别实体")
public class PgTextbookVolume {

    @Schema(description = "主键ID")
    @TableId(value = "id")
    private Long id;

    @Schema(description = "册别名称")
    private String name;

    @Schema(description = "所属年级ID")
    private Long gradeId;

    @Schema(description = "排序值")
    private Integer sort;

    @Schema(description = "创建时间")
    private Date createTime;

    @Schema(description = "更新时间")
    private Date updateTime;
}
