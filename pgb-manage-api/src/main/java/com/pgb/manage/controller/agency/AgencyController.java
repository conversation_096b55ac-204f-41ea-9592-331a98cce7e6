package com.pgb.manage.controller.agency;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.pgb.common.core.global.BaseResult;
import com.pgb.common.core.global.GlobalCode;
import com.pgb.common.mybatis.global.PageQuery;
import com.pgb.manage.domain.dto.AddAgencyDTO;
import com.pgb.manage.domain.query.AgencyQuery;
import com.pgb.manage.domain.query.AgencyVerifyQuery;
import com.pgb.service.db.*;
import com.pgb.service.domain.distribution.PgDistribution;
import com.pgb.service.domain.distribution.PgDistributionVO;
import com.pgb.service.domain.distribution.agency.CommissionInfoDTO;
import com.pgb.service.domain.distribution.agency.PgDistributionAgency;
import com.pgb.service.domain.distribution.agency.PgDistributionAgencyVO;
import com.pgb.service.domain.distribution.agency.VerifyRecord;
import com.pgb.service.domain.distribution.verify.PgDistributionVerify;
import com.pgb.service.domain.order.PgOrder;
import com.pgb.service.domain.user.PgUsers;
import com.pgb.service.domain.vip.PgVip;
import com.pgb.service.enums.ProfitStatusEnum;
import com.pgb.service.enums.VipTypeEnum;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * Created by 2025/2/28 10:32
 */
@Tag(name = "管理端/代理")
@RestController("AgencyController")
@Transactional(rollbackFor = Exception.class)
@RequestMapping("/manage/agency")
@RequiredArgsConstructor
@Slf4j
public class AgencyController {

    private final PgUsersService pgUsersService;

    private final PgOrderService pgOrderService;

    private final PgDistributionService pgDistributionService;

    private final PgDistributionAgencyService pgDistributionAgencyService;

    private final PgDistributionVerifyService pgDistributionVerifyService;

    @Operation(summary = "添加代理")
    @PostMapping("create")
    public BaseResult<Boolean> create(@RequestBody AddAgencyDTO dto) {

        // 根据手机号获取用户
        PgUsers user = pgUsersService.getOrCreateUserByPhone(dto.getPhone());

        // 关联代理表
        PgDistributionAgency agency = new PgDistributionAgency();
        agency.setUserId(user.getId());
        agency.setRate(dto.getRate());
        agency.setCreateTime(new Date());

        return BaseResult.success(
                pgDistributionAgencyService.save(agency)
        );
    }

    @Operation(summary = "删除代理")
    @DeleteMapping("{agencyId}")
    public BaseResult<Boolean> delete(@PathVariable Long agencyId) {

        PgDistributionAgency agency = pgDistributionAgencyService.getById(agencyId);

        if (ObjectUtil.isNull(agency)) {
            return BaseResult.code(GlobalCode.Item_Null);
        }
        return BaseResult.success(
                pgDistributionAgencyService.removeById(agencyId)
        );
    }

    @Operation(summary = "查看代理信息")
    @GetMapping("info/{agencyId}")
    public BaseResult<PgDistributionAgencyVO> info(@PathVariable Long agencyId) {

        PgDistributionAgency agency = pgDistributionAgencyService.getById(agencyId);

        if (ObjectUtil.isNull(agency)) {
            return BaseResult.code(GlobalCode.Item_Null);
        }
        PgUsers users = pgUsersService.getById(agency.getUserId());

        PgDistributionAgencyVO agencyVO = BeanUtil.copyProperties(agency, PgDistributionAgencyVO.class);

        agencyVO.setUserInfo(users);

        return BaseResult.success(agencyVO);
    }

    @Operation(summary = "获取代理列表")
    @PostMapping("page")
    public BaseResult<IPage<PgDistributionAgencyVO>> page(@RequestBody AgencyQuery query) {

        LambdaQueryWrapper<PgDistributionAgency> queryWrapper = new LambdaQueryWrapper<>();

        if (StrUtil.isNotBlank(query.getPhone())) {
            List<Long> userIds = pgUsersService.list(new LambdaQueryWrapper<PgUsers>()
                            .like(PgUsers::getPhone, query.getPhone()))
                    .stream()
                    .map(PgUsers::getId)
                    .toList();
            if (userIds.isEmpty()) {
                return BaseResult.success(new Page<>());
            } else {
                queryWrapper.in(PgDistributionAgency::getUserId, userIds);
            }
        }

        IPage<PgDistributionAgencyVO> page = pgDistributionAgencyService.page(query.toMpPageSortByCreateTime(), queryWrapper
        ).convert(agency -> {
            PgDistributionAgencyVO agencyVO = BeanUtil.copyProperties(agency, PgDistributionAgencyVO.class);

            // 邀请成功人数
            long inviteCount = pgDistributionService.count(new LambdaQueryWrapper<PgDistribution>()
                    .eq(PgDistribution::getShareUserId, agency.getUserId())
                    .eq(PgDistribution::getIsSuccess, true));

            agencyVO.setInviteCount((int) inviteCount);

            // 详细信息
            PgUsers users = pgUsersService.getById(agency.getUserId());
            agencyVO.setUserInfo(users);

            return agencyVO;
        });

        return BaseResult.success(page);
    }


    @Operation(summary = "分页查看邀请的人员列表信息")
    @PostMapping("sharePage/{agencyUserId}")
    public BaseResult<IPage<PgDistributionVO>> sharePage(@RequestBody PageQuery query,
                                                         @PathVariable Long agencyUserId) {

        IPage<PgDistributionVO> page = pgDistributionService.page(query.toMpPageSortByCreateTime(), new LambdaQueryWrapper<PgDistribution>()
                .eq(PgDistribution::getShareUserId, agencyUserId)
        ).convert(pgDistribution -> {

            PgDistributionVO pgDistributionVO = BeanUtil.copyProperties(pgDistribution, PgDistributionVO.class);

            // 被邀请人信息
            PgUsers users = pgUsersService.getById(pgDistribution.getUserId());
            if (ObjectUtil.isNotNull(users)) {
                pgDistributionVO.setUserInfo(users);
            }

            return pgDistributionVO;

        });

        return BaseResult.success(page);
    }

    @Operation(summary = "查看代理的分销核销记录")
    @PostMapping("verifyRecord/{agencyId}")
    public BaseResult<IPage<VerifyRecord>> record(@PathVariable Long agencyId, @RequestBody AgencyVerifyQuery query) {

        LambdaQueryWrapper<PgOrder> queryWrapper = new LambdaQueryWrapper<PgOrder>()
                .eq(PgOrder::getIsPay, true);

        // 获取代理人
        PgDistributionAgency agency = pgDistributionAgencyService.getOne(new LambdaQueryWrapper<PgDistributionAgency>()
                .eq(PgDistributionAgency::getId, agencyId));

        // 查代理邀请的用户
        List<Long> userIds = pgDistributionService.list(new LambdaQueryWrapper<PgDistribution>()
                        .eq(PgDistribution::getIsSuccess, true)
                        .eq(PgDistribution::getShareUserId, agency.getUserId()))
                .stream()
                .map(PgDistribution::getUserId)
                .toList();

        if (userIds.isEmpty()) {
            return BaseResult.success(new Page<>());
        } else {
            queryWrapper.in(PgOrder::getUserId, userIds);
        }

        // 筛选被邀请用户的购买记录
        if (StrUtil.isNotBlank(query.getPhone())) {

            // 根据手机号查用户
            PgUsers users = pgUsersService.getOne(new LambdaQueryWrapper<PgUsers>()
                    .eq(PgUsers::getPhone, query.getPhone())
                    .last("LIMIT 1"));

            queryWrapper.eq(PgOrder::getUserId, users.getId());
        }

        IPage<VerifyRecord> page = pgOrderService.page(query.toMpPageSortByCreateTime(), queryWrapper
        ).convert(pgOrder -> {

            PgVip vip = JSONUtil.toBean(pgOrder.getSku().toString(), PgVip.class);
            // 排除周卡类型
            if (vip.getVipType().equals(VipTypeEnum.WEEK)) {
                return null;
            }

            VerifyRecord record = new VerifyRecord();

            // 查被邀请人信息
            PgUsers users = pgUsersService.getById(pgOrder.getUserId());
            if (ObjectUtil.isNotNull(users)) {
                record.setNickName(users.getNickName());
                record.setAvatarUrl(users.getAvatarImgUrl());
            }
            // 下单时间
            record.setCreateTime(pgOrder.getCreateTime());
            // 实际支付金额
            record.setPayAmount(pgOrder.getPayAmount());
            record.setOrderId(pgOrder.getId());

            // 若已处理 则是已转账
            PgDistributionVerify verify = pgDistributionVerifyService.getOne(new LambdaQueryWrapper<PgDistributionVerify>()
                    .eq(PgDistributionVerify::getOrderId, pgOrder.getId())
                    .last("LIMIT 1"));
            // 订单分销金额
            if (ObjectUtil.isNotNull(verify)) {
                record.setDistributeAmount(verify.getDistributeAmount());
                record.setStatus(ProfitStatusEnum.SUCCESS);
            } else {
                // 待转账金额
                record.setDistributeAmount(ObjectUtil.isNotNull(agency) ? (int) Math.ceil((double) (pgOrder.getPayAmount() * agency.getRate()) / 100) : 0);
                record.setStatus(ProfitStatusEnum.PENDING);
            }

            return record;
        });

        return BaseResult.success(page);
    }

    @Operation(summary = "查看代理的分销佣金统计信息")
    @GetMapping("verifyCommission/{agencyId}")
    public BaseResult<CommissionInfoDTO> verifyCommission(@PathVariable Long agencyId) {

        // 查找代理人
        PgDistributionAgency agency = pgDistributionAgencyService.getOne(new LambdaQueryWrapper<PgDistributionAgency>()
                .eq(PgDistributionAgency::getId, agencyId));

        CommissionInfoDTO commissionInfo = new CommissionInfoDTO();

        // 绑定人数
        long count = pgDistributionService.count(new LambdaQueryWrapper<PgDistribution>()
                .eq(PgDistribution::getShareUserId, agency.getUserId())
                .eq(PgDistribution::getIsSuccess, true));

        commissionInfo.setBindUserNum((int) count);

        // 累计佣金（分） 转账成功的
        List<PgDistributionVerify> verifyList = pgDistributionVerifyService.list(new LambdaQueryWrapper<PgDistributionVerify>()
                .eq(PgDistributionVerify::getShareUserId, agency.getUserId())
                .eq(PgDistributionVerify::getStatus, 1));

        int totalCommission = verifyList.stream()
                .mapToInt(PgDistributionVerify::getDistributeAmount)
                .sum();
        commissionInfo.setTotalCommission(totalCommission);

        // 查用户邀请用户列表
        List<Long> userIds = pgDistributionService.list(new LambdaQueryWrapper<PgDistribution>()
                        .eq(PgDistribution::getIsSuccess, true)
                        .eq(PgDistribution::getShareUserId, agency.getUserId()))
                .stream()
                .map(PgDistribution::getUserId)
                .toList();

        if (!userIds.isEmpty()) {

            // 已核销的orderIds
            List<Long> orderIds = verifyList.stream().map(PgDistributionVerify::getOrderId).toList();

            int unsettledCommission = pgOrderService.list(new LambdaQueryWrapper<PgOrder>()
                            .in(PgOrder::getUserId, userIds)
                            .eq(PgOrder::getIsPay, true))
                    .stream()
                    // 排除掉 已经在verify表中的orderId
                    .filter(pgOrder -> !orderIds.contains(pgOrder.getId()))
                    // 排除掉周卡类型的
                    .filter(pgOrder -> {
                        PgVip vip = JSONUtil.toBean(pgOrder.getSku().toString(), PgVip.class);
                        return !vip.getVipType().equals(VipTypeEnum.WEEK);
                    })
                    .mapToInt(PgOrder::getPayAmount)
                    .sum();
            commissionInfo.setUnsettledCommission(ObjectUtil.isNotNull(agency) ? (int) Math.ceil((double) (unsettledCommission * agency.getRate()) / 100) : 0);
        }

        return BaseResult.success(commissionInfo);
    }
}
