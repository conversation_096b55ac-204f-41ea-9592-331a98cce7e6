package com.pgb.service.domain.activity;

import com.pgb.service.enums.VipTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
public class ActivityDiscountConfig {

    @Schema(title = "价格")
    private Integer price;

    @Schema(title = "会员类型")
    private VipTypeEnum vipTypeEnum;

    @Schema(title = "赠送天数")
    private Integer presentDays;

    @Schema(title = "通过活动购买之后添加标签")
    private List<Long> buyUserTagList;

}
