package com.pgb.service.domain.zc.common.textbook.entity;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * 教材类型实体
 *
 * <AUTHOR>
 * Created by 2025/7/25 19:35
 */
@Data
@Schema(description = "教材类型实体")
public class PgTextbookType {

    @Schema(title = "ID")
    private Long id;

    @Schema(title = "类型名称")
    @NotBlank(message = "类型名称不能为空")
    private String name;

    @Schema(title = "排序值")
    @NotNull(message = "排序值不能为空")
    private Integer sort;

    @Schema(description = "创建时间")
    private Date createTime;

    @Schema(description = "更新时间")
    private Date updateTime;
}
