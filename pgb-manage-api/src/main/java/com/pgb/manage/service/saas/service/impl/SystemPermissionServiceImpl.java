package com.pgb.manage.service.saas.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.pgb.manage.service.saas.domain.SystemPermission;
import com.pgb.manage.service.saas.mapper.SystemPermissonMapper;
import com.pgb.manage.service.saas.service.SystemPermissionService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @description 针对表【system_permisson(系统菜单权限表)】的数据库操作Service实现
 * @createDate 2023-11-13 13:44:42
 */
@Service
@RequiredArgsConstructor
public class SystemPermissionServiceImpl extends ServiceImpl<SystemPermissonMapper, SystemPermission>
        implements SystemPermissionService {

}




