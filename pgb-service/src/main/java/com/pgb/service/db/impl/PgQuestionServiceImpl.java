package com.pgb.service.db.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.pgb.service.db.PgQuestionService;
import com.pgb.service.domain.question.PgQuestion;
import com.pgb.service.mapper.PgQuestionMapper;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【pg_question(题目表)】的数据库操作Service实现
* @createDate 2024-07-16 19:33:37
*/
@Service
public class PgQuestionServiceImpl extends ServiceImpl<PgQuestionMapper, PgQuestion>
    implements PgQuestionService {

}




