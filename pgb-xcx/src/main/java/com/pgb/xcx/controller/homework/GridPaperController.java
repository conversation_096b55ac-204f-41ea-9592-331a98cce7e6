package com.pgb.xcx.controller.homework;

import cn.dev33.satoken.annotation.SaCheckLogin;
import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.pgb.common.core.global.BaseResult;
import com.pgb.common.redis.QueueUtils;
import com.pgb.service.db.PgAnswerCostService;
import com.pgb.service.db.PgAnswerService;
import com.pgb.service.db.PgHomeworkGridPaperService;
import com.pgb.service.domain.answer.gridPaper.GridPaperResult;
import com.pgb.service.domain.common.image.FilePaperImg;
import com.pgb.service.domain.homework.PgHomeworkGridPaper;
import com.pgb.service.domain.homework.PgHomeworkGridPaperVO;
import com.pgb.service.enums.CorrectStatusEnum;
import com.pgb.service.domain.GlobQueueConstants;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.List;

@Tag(name = "用户端/作业/格子纸批量上传")
@RestController("GridPaperController")
@Transactional(rollbackFor = Exception.class)
@RequestMapping("/user/homework/grid")
@RequiredArgsConstructor
@Slf4j
public class GridPaperController {

    private final PgAnswerCostService pgAnswerCostService;

    private final PgHomeworkGridPaperService pgHomeworkGridPaperService;

    @Data
    public static class BatchSubmitForm {

        @Schema(title = "上传的作文图片")
        private List<FilePaperImg> answerList;

        @Schema(title = "所属作业id")
        private Long homeworkId;
    }

    @Operation(summary = "提交格子纸图片")
    @PostMapping("submit/batch")
    @SaCheckLogin
    public BaseResult<PgHomeworkGridPaper> submitBatch(@RequestBody BatchSubmitForm form) {

        if (CollUtil.isEmpty(form.getAnswerList())) {
            return BaseResult.error("请上传图片");
        }

        // 判断今天提交次数
        if (form.getAnswerList().size() > pgAnswerCostService.getTodaySubmitNum(StpUtil.getLoginIdAsLong()).getRemainNum()) {
            return BaseResult.error("今日批改次数已达上限");
        }

        // 所有图片
        // 不需要转tmp，当前文件只当临时文件使用
        List<String> userImgList = form.getAnswerList().stream().map(FilePaperImg::getImgUrl).toList();

        PgHomeworkGridPaper gridPaper = new PgHomeworkGridPaper();
        gridPaper.setUserId(StpUtil.getLoginIdAsLong());
        gridPaper.setHomeworkId(form.getHomeworkId());
        gridPaper.setStatus(CorrectStatusEnum.Uploaded);
        gridPaper.setCreateTime(new Date());
        gridPaper.setUserImgList(
                CollUtil.join(userImgList, StrUtil.COMMA)
        );
        gridPaper.setTokens(0);
        pgHomeworkGridPaperService.save(gridPaper);

        // 添加队列内容
        QueueUtils.addQueueObjectInTransaction(GlobQueueConstants.PGB_XCX_GRID_PAPER_DETECTION_QUEUE.name(), gridPaper.getId());

        return BaseResult.success(gridPaper);
    }


    @Operation(summary = "查看最近的一次批量提交情况")
    @GetMapping("detail/{homeworkId}")
    @SaCheckLogin
    public BaseResult<PgHomeworkGridPaperVO> detail(@PathVariable Long homeworkId) {
        PgHomeworkGridPaper record = pgHomeworkGridPaperService.getOne(new LambdaQueryWrapper<PgHomeworkGridPaper>()
                .eq(PgHomeworkGridPaper::getHomeworkId, homeworkId)
                .eq(PgHomeworkGridPaper::getUserId, StpUtil.getLoginIdAsLong())
                .orderByDesc(PgHomeworkGridPaper::getCreateTime)
                .last("limit 1")
        );

        if (ObjectUtil.isNull(record)) {
            return BaseResult.success();
        }

        PgHomeworkGridPaperVO vo = BeanUtil.copyProperties(record, PgHomeworkGridPaperVO.class, "imgResultJson");
        vo.setImgResultJson(
                JSONUtil.toList(JSONUtil.toJsonStr(record.getImgResultJson()), GridPaperResult.class)
        );

        // 计算成功情况
        vo.setSuccessNum(
                CollUtil.count(vo.getImgResultJson(), item -> ObjectUtil.isNotNull(item.getStudentId()))
        );
        vo.setFailNum(
                CollUtil.count(vo.getImgResultJson(), item -> ObjectUtil.isNull(item.getStudentId()))
        );

        return BaseResult.success(vo);
    }
}
