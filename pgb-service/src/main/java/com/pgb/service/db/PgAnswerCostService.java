package com.pgb.service.db;


import com.baomidou.mybatisplus.extension.service.IService;
import com.pgb.service.domain.answer.answerCost.PgAnswerCost;
import com.pgb.service.domain.common.TodayNum;

/**
 * <AUTHOR>
 * @description 针对表【pg_answer_cost(作文批改明细表)】的数据库操作Service
 * @createDate 2024-09-04 15:58:56
 */
public interface PgAnswerCostService extends IService<PgAnswerCost> {

    /**
     * 获取今日提交数量
     *
     * @return
     */
    TodayNum getTodaySubmitNum(Long userId);

    /**
     * 新增题目作答消耗
     *
     * @param userId
     * @param answerId
     * @param type
     */
    PgAnswerCost addAnswerCost(Long userId, Long answerId, Integer type);
}
