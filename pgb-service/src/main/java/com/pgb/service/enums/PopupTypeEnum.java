package com.pgb.service.enums;


import com.baomidou.mybatisplus.annotation.EnumValue;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;

@Schema(title = "弹窗类型")
@AllArgsConstructor
public enum PopupTypeEnum {
    POPUP(0,"弹窗广告"),

    PATCH(1,"贴片广告");

    /**
     * 枚举值对应的整数值
     */
    @EnumValue
    public final int value;

    /**
     * 文字说明
     */
    public final String desc;
}
