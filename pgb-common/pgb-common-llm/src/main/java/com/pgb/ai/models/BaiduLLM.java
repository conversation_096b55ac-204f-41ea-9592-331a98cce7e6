package com.pgb.ai.models;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.TimeInterval;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.alibaba.dashscope.aigc.multimodalconversation.MultiModalConversation;
import com.alibaba.dashscope.aigc.multimodalconversation.MultiModalConversationParam;
import com.alibaba.dashscope.aigc.multimodalconversation.MultiModalConversationResult;
import com.alibaba.dashscope.common.MultiModalMessage;
import com.alibaba.dashscope.common.Role;
import com.alibaba.dashscope.exception.ApiException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.openai.client.OpenAIClient;
import com.openai.client.okhttp.OpenAIOkHttpClient;
import com.openai.core.JsonValue;
import com.openai.core.http.StreamResponse;
import com.openai.errors.OpenAIInvalidDataException;
import com.openai.models.ResponseFormatText;
import com.openai.models.chat.completions.*;
import com.pgb.ai.LLMService;
import com.pgb.ai.domain.ChatRecord;
import com.pgb.ai.domain.ChatRes;
import com.pgb.ai.domain.GPTAnswer;
import com.pgb.ai.domain.agent.FileItem;
import com.pgb.common.core.utils.EnvUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.awt.image.BufferedImage;
import java.io.IOException;
import java.net.URI;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.time.Duration;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Consumer;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Slf4j
public class BaiduLLM implements LLMService {

    private final OpenAIClient client;

    public BaiduLLM(String apiKey) {
        // 将your_APIKey替换为真实值，如何获取API Key请查看https://cloud.baidu.com/doc/WENXINWORKSHOP/s/Um2wxbaps#步骤二-获取api-key
        this.client = OpenAIOkHttpClient.builder()
                .apiKey(apiKey)
                .baseUrl("https://qianfan.baidubce.com/v2/") // 千帆ModelBuilder平台地址
                .build();
    }

    /**
     * @param system
     * @param recordList 包含最新当前用户对话
     * @param onNext
     * @param isReason   是否启用深度思考
     */
    private void stream(String system, List<List<FileItem>> fileList, List<ChatRecord> recordList, Consumer<ChatCompletionChunk> onNext, boolean isReason, boolean isJson, Float temperature) {

        // 模型对应的model值，请查看支持的模型列表：https://cloud.baidu.com/doc/WENXINWORKSHOP/s/wm7ltcvgc
        // String model = "ernie-4.0-8k";
        String model = "ernie-4.5-turbo-32k";

        // 创建构建器（使用具体类型）
        ChatCompletionCreateParams.Builder builder = ChatCompletionCreateParams.builder()
                .model(model)
                .responseFormat(
                        ResponseFormatText.builder()
                                .type(JsonValue.Companion.from(isJson ? "json_object" : "text"))
                                .build()
                );

        // 添加温度
        if (ObjectUtil.isNotNull(temperature)) {
            builder.temperature(temperature);
        }

        // 添加预设
        if (StrUtil.isNotBlank(system)) {
            builder.addSystemMessage(system);
        }

        // 添加历史会话
        for (ChatRecord record : recordList) {
            if (StrUtil.isNotBlank(record.getContent())) {
                builder.addMessage(
                        ChatCompletionMessageParam.ofAssistant(
                                ChatCompletionAssistantMessageParam.builder()
                                        .content(record.getContent())
                                        .role(
                                                JsonValue.from(record.getALiRoleName())
                                        )
                                        .build()
                        )
                );
            }
        }

        // ================= 插件配置 =================
        // 1. 创建插件列表
        List<String> plugins = new ArrayList<>();
        plugins.add("ChatFilePlus");
        builder.putAdditionalBodyProperty("plugins", JsonValue.from(plugins));

        // 2. 构建文件参数
        //（1）type为doc时，name为文档名称，文档后缀支持：doc、docx、xls、xlsx、ppt、pptx、txt、pdf
        //（2）type为link时，name等同于url
        //（3）type为audio时，name为文件名称，文件后置支持：mp3、m4a、wav
        List<Map<String, String>> fileGroup = new ArrayList<>();
        if (CollUtil.isNotEmpty(fileList)) {

            for (List<FileItem> fileItems : fileList) {
                if (CollUtil.isNotEmpty(fileItems)) {

                    for (FileItem fileItem : fileItems) {

                        Map<String, String> file = new HashMap<>();
                        // 根据FileTypeEnum转换为阅读插件要求的type
                        String type = "";
                        switch (fileItem.getFileType()) {
                            case file:
                                type = "doc";
                                break;
                            case audio:
                                type = "audio";
                                break;
                            default:
                                break;
                        }
                        file.put("type", type);
                        file.put("url", fileItem.getUrl());
//                        String fileName = fileItem.getUrl().substring(fileItem.getUrl().lastIndexOf("/") + 1);
                        String fileName = fileItem.getName();
                        file.put("name", fileName);
                        fileGroup.add(file);
                    }
                }
            }
        }

        // 3. 创建插件选项
        Map<String, Object> pluginOptions = new HashMap<>();
        Map<String, Object> pluginArgs = new HashMap<>();
        Map<String, Object> chatFilePlusArgs = new HashMap<>();
        Map<String, Object> body = new HashMap<>();

        body.put("files", List.of(fileGroup));
        chatFilePlusArgs.put("body", body);
        pluginArgs.put("ChatFilePlus", chatFilePlusArgs);
        pluginOptions.put("plugin_args", pluginArgs);

        builder.putAdditionalBodyProperty("plugin_options", JsonValue.from(pluginOptions));
        // 4. 流式参数配置
//        builder.putAdditionalBodyProperty("stream", JsonValue.from(true));
//
//        Map<String, Boolean> streamOptions = new HashMap<>();
//        streamOptions.put("include_usage", true);
//        builder.putAdditionalBodyProperty("stream_options", JsonValue.from(streamOptions));

        // 是否开启深度思考
//        builder.putAdditionalBodyProperty("enable_thinking", JsonValue.from(isReason));

        // 获取数据
        StreamResponse<ChatCompletionChunk> chatCompletion = client.chat().completions().createStreaming(builder.build());
        chatCompletion.stream().forEach(onNext);
    }

    @Override
    public void chatStream(List<ChatRecord> recordList, SseEmitter emitter, StringBuilder resultBuilder, StringBuilder reasonBuilder, boolean isReason) {
        // 开始统计批改时长
        TimeInterval timer = DateUtil.timer();
        AtomicReference<Boolean> reasonStart = new AtomicReference<>(false);

        stream(
                "你是由“批改邦”公司开发的“AI教学助手”，是专门为老师打造的AI智能助理",
                new ArrayList<>(),
                recordList,
                chunk -> {
                    if (!EnvUtils.isProd()) {
                        log.info("模型生成：{}", JSONUtil.toJsonStr(chunk));
                    }
                    String reasoning = chunk.choices().get(0)._additionalProperties().get("reasoning_content").toString();

                    // 检查choices和delta是否存在
                    if (CollectionUtil.isEmpty(chunk.choices()) ||
                            ObjectUtil.isNull(chunk.choices().get(0).delta())) {
                        return;
                    }

                    String content = chunk.choices().get(0).delta().content().get();
                    log.info("模型生成内容：{}", content);

                    try {
                        if (isReason) {
                            // 判断是否开始思考
                            if (StrUtil.isNotBlank(reasoning)) {
                                reasonStart.set(true);
                            } else {
                                // 如果开始思考了，但结束了
                                if (reasonStart.get()) {
                                    reasonStart.set(false);
                                    ChatRes res = new ChatRes();
                                    res.setType(ChatRes.ChatResType.MESSAGE);
                                    res.setCode(ChatRes.CharResCode.SUCCESS);
                                    // 计算时间
                                    res.setResult(String.valueOf(timer.intervalSecond()));
                                    res.setBotType(ChatRes.BotType.ReasonFinish);
                                    emitter.send(res.toJSON());
                                }
                            }
                        }

                        ChatRes res = new ChatRes();
                        // 内容
                        if (StrUtil.isNotEmpty(reasoning)) {
                            reasonBuilder.append(reasoning);
                            res.setResult(reasoning);
                        } else {
                            resultBuilder.append(content);
                            res.setResult(content);
                        }

                        res.setBotType(StrUtil.isEmpty(reasoning) ? ChatRes.BotType.Bot : ChatRes.BotType.Reason);
                        emitter.send(res.toJSON());
                    } catch (IOException e) {
                        log.error("sse 发送失败：{}", e.getMessage(), e);
                    }
                },
                isReason,
                false,
                null
        );
    }

    @Override
    public void chatSocket(String answer, StringBuilder resultBuilder, StringBuilder reasonBuilder, Consumer<ChatRes> consumer, boolean isReason) {
        List<ChatRecord> recordList = new ArrayList<>();
        recordList.add(
                ChatRecord.builder()
                        .role(ChatRecord.Role.User)
                        .content(answer)
                        .build()
        );
        chatSocket(recordList, resultBuilder, reasonBuilder, consumer, isReason);
    }

    @Override
    public void chatSocket(List<ChatRecord> recordList, StringBuilder resultBuilder, StringBuilder reasonBuilder, Consumer<ChatRes> consumer, boolean isReason) {
        // 开始统计批改时长
        TimeInterval timer = DateUtil.timer();
        AtomicReference<Boolean> reasonStart = new AtomicReference<>(false);
        stream(
                "你是由“批改邦”公司开发的“AI教学助手”，是专门为老师打造的AI智能助理",
                new ArrayList<>(),
                recordList,
                chunk -> {

                    try {
                        // 基础检查
                        if (CollectionUtil.isEmpty(chunk.choices())) {
                            return;
                        }
                        ChatCompletionChunk.Choice choice = chunk.choices().get(0);

                        // 安全获取内容
                        String content = "";
                        if (ObjectUtil.isNotNull(choice.delta()) && choice.delta().content().isPresent()) {
                            content = choice.delta().content().get();
                        }

                        if (!EnvUtils.isProd()) {
                            log.info("模型生成内容：{}", content);
                        }
                        Object reasoningObj = chunk.choices().get(0)._additionalProperties().get("reasoning_content");
                        String reasoning = ObjectUtil.isNotNull(reasoningObj) ? reasoningObj.toString() : "";


                        if (isReason) {
                            // 判断是否开始思考
                            if (StrUtil.isNotBlank(reasoning)) {
                                reasonStart.set(true);
                            } else {
                                // 如果开始思考了，但结束了
                                if (reasonStart.get()) {
                                    reasonStart.set(false);
                                    ChatRes res = new ChatRes();
                                    res.setType(ChatRes.ChatResType.MESSAGE);
                                    res.setCode(ChatRes.CharResCode.SUCCESS);
                                    // 计算时间
                                    res.setResult(String.valueOf(timer.intervalSecond()));
                                    res.setBotType(ChatRes.BotType.ReasonFinish);

                                    consumer.accept(res);
                                }
                            }
                        }

                        ChatRes res = new ChatRes();
                        // 内容
                        if (StrUtil.isNotEmpty(reasoning)) {
                            reasonBuilder.append(reasoning);
                            res.setResult(reasoning);
                        } else {
                            resultBuilder.append(content);
                            res.setResult(content);
                        }

                        res.setType(ChatRes.ChatResType.MESSAGE);
                        res.setCode(ChatRes.CharResCode.SUCCESS);
                        res.setBotType(StrUtil.isEmpty(reasoning) ? ChatRes.BotType.Bot : ChatRes.BotType.Reason);

                        consumer.accept(res);
                    } catch (OpenAIInvalidDataException e) {
                        log.debug("忽略无效数据块: {}", e.getMessage());
                    }

                },
                isReason,
                false,
                null
        );
    }

    @Override
    public GPTAnswer vl(String imgUrl, String system, String user) {

        return null;
    }

    @Override
    public GPTAnswer vl(BufferedImage image, String system, String user) {
        return null;
    }


    @Override
    public GPTAnswer chatComplete(String system, List<String> user, boolean isJson, Float temperature) {
        return null;
    }

    @Override
    public GPTAnswer chatComplete(String system, List<String> user, boolean isJson, Float temperature, String
            model) {
        return null;
    }

    @Override
    public void chatSocket(List<List<FileItem>> fileList, List<ChatRecord> recordList, StringBuilder
            resultBuilder, StringBuilder reasonBuilder, Consumer<ChatRes> consumer, boolean isReason) {

        // 开始统计批改时长
        TimeInterval timer = DateUtil.timer();
        AtomicReference<Boolean> reasonStart = new AtomicReference<>(false);
        stream(
                "你是由“批改邦”公司开发的“AI教学助手”，是专门为老师打造的AI智能助理",
                fileList,
                recordList,
                chunk -> {
                    try {
                        // 基础检查
                        if (CollectionUtil.isEmpty(chunk.choices())) {
                            return;
                        }

                        ChatCompletionChunk.Choice choice = chunk.choices().get(0);

                        JsonValue plugin_info = choice._additionalProperties().get("plugin_info");

                        // 获取插件处理文件过程
                        String actionContent = "";
                        // 获取正文内容
                        String content = "";
                        if (ObjectUtil.isNotNull(plugin_info)) {

                            String pluginInfoStr = plugin_info.toString();

                            Pattern pattern = Pattern.compile("action_content=([^&\\s]+)");
                            Matcher matcher = pattern.matcher(pluginInfoStr);

                            if (matcher.find()) {
                                // 去除末尾的逗号
                                actionContent = matcher.group(1).replaceAll(",$", "");
                                if (!EnvUtils.isProd()) {
                                    log.info("插件处理内容: {}", actionContent);
                                }
                            }
                        } else {

                            if (ObjectUtil.isNotNull(choice.delta()) && choice.delta().content().isPresent()) {
                                content = choice.delta().content().get();
                            }
                            if (!EnvUtils.isProd()) {
                                log.info("模型生成内容：{}", content);
                            }
                        }

                        Object reasoningObj = chunk.choices().get(0)._additionalProperties().get("reasoning_content");
                        String reasoning = ObjectUtil.isNotNull(reasoningObj) ? reasoningObj.toString() : "";

                        if (isReason) {
                            // 判断是否开始思考
                            if (StrUtil.isNotBlank(reasoning)) {
                                reasonStart.set(true);
                            } else {
                                // 如果开始思考了，但结束了
                                if (reasonStart.get()) {
                                    reasonStart.set(false);
                                    ChatRes res = new ChatRes();
                                    res.setType(ChatRes.ChatResType.MESSAGE);
                                    res.setCode(ChatRes.CharResCode.SUCCESS);
                                    // 计算时间
                                    res.setResult(String.valueOf(timer.intervalSecond()));
                                    res.setBotType(ChatRes.BotType.ReasonFinish);

                                    consumer.accept(res);
                                }
                            }
                        }

                        ChatRes res = new ChatRes();
                        // 内容
                        if (StrUtil.isNotEmpty(reasoning)) {
                            reasonBuilder.append(reasoning);
                            res.setResult(reasoning);
                        } else if (StrUtil.isNotEmpty(content)) {
                            resultBuilder.append(content);
                            res.setResult(content);
                        } else {
                            resultBuilder.append(actionContent);
                            res.setResult(actionContent + "\n");
                        }

                        res.setType(ChatRes.ChatResType.MESSAGE);
                        res.setCode(ChatRes.CharResCode.SUCCESS);
                        res.setBotType(StrUtil.isEmpty(reasoning) ? ChatRes.BotType.Bot : ChatRes.BotType.Reason);

                        consumer.accept(res);
                    } catch (OpenAIInvalidDataException e) {
                        log.debug("忽略无效数据块: {}", e.getMessage());
                    }

                },
                isReason,
                false,
                null
        );
    }

    @Override
    public void chatStream(List<List<FileItem>> fileList, List<ChatRecord> recordList, SseEmitter emitter, StringBuilder resultBuilder, StringBuilder reasonBuilder, boolean isReason) {

        stream(
                "你是由“批改邦”公司开发的“AI教学助手”，是专门为老师打造的AI智能助理",
                fileList,
                recordList,
                chunk -> {
                    try {
                        // 基础检查
                        if (CollectionUtil.isEmpty(chunk.choices())) {
                            return;
                        }
                        ChatCompletionChunk.Choice choice = chunk.choices().get(0);

                        JsonValue plugin_info = choice._additionalProperties().get("plugin_info");
                        // 获取插件处理文件过程
                        String actionContent = "";
                        // 安全获取内容
                        String content = "";

                        if (ObjectUtil.isNotNull(plugin_info)) {

                            String pluginInfoStr = plugin_info.toString();

                            Pattern pattern = Pattern.compile("action_content=([^&\\s]+)");
                            Matcher matcher = pattern.matcher(pluginInfoStr);

                            if (matcher.find()) {
                                // 去除末尾的逗号
                                actionContent = matcher.group(1).replaceAll(",$", "");
                                if (!EnvUtils.isProd()) {
                                    log.info("插件处理内容: {}", actionContent);
                                }
                            }
                        } else {
                            // 安全获取内容
                            if (ObjectUtil.isNotNull(choice.delta()) && choice.delta().content().isPresent()) {
                                content = choice.delta().content().get();
                            }
                            if (!EnvUtils.isProd()) {
                                log.info("模型生成内容：{}", content);
                            }
                        }

                        ChatRes res = new ChatRes();
                        res.setType(ChatRes.ChatResType.MESSAGE);
                        res.setCode(ChatRes.CharResCode.SUCCESS);

                        // 内容
                        if (StrUtil.isNotEmpty(content)) {
                            resultBuilder.append(content);
                            res.setResult(content);
                        } else {
                            resultBuilder.append(actionContent);
                            res.setResult(actionContent + "\n");
                        }

                        res.setBotType(ChatRes.BotType.Bot);
                        emitter.send(res.toJSON());
                    } catch (IllegalStateException e) {
                        log.debug("SseEmitter 已完成，无法发送数据: {}", e.getMessage());
                    } catch (IOException e) {
                        log.error("sse 发送失败：{}", e.getMessage(), e);
                    } catch (OpenAIInvalidDataException e) {
                        log.debug("忽略无效数据块: {}", e.getMessage());
                    }
                },
                isReason,
                false,
                null
        );
    }
}
