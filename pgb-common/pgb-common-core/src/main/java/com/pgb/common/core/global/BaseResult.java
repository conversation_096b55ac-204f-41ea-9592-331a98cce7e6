package com.pgb.common.core.global;

import cn.hutool.core.util.ObjectUtil;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import lombok.AllArgsConstructor;
import lombok.Data;

@Data
@ApiResponse(responseCode = "200", description = "通用返回体")
@AllArgsConstructor
public class BaseResult<T> {

    @Schema(title = "消息状态码", description = "具体状态含义，详情见状态码说明", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer code;

    @Schema(title = "消息说明", requiredMode = Schema.RequiredMode.REQUIRED)
    private String msg;

    @Schema(title = "返回数据", nullable = true)
    private T data;

    /**
     * 响应成功
     *
     * @return MyResult
     */
    public static <T> BaseResult<T> success() {
        return new BaseResult<>(GlobalCode.Success.value, "响应成功", null);
    }


    /**
     * 响应成功，返回含消息的结果
     *
     * @param msg
     * @return
     */
    public static BaseResult<String> success(String msg) {
        return new BaseResult<>(GlobalCode.Success.value, msg, msg);
    }

    /**
     * 响应成功，返回含数据的结果
     *
     * @param data 返回数据
     * @return MyResult
     */
    public static <T> BaseResult<T> success(T data) {
        return new BaseResult<>(GlobalCode.Success.value, "响应成功", data);
    }

    /**
     * 响应成功，返回含消息和数据的结果
     *
     * @param msg
     * @param data
     * @return
     */
    public static <T> BaseResult<T> success(String msg, T data) {
        return new BaseResult<>(GlobalCode.Success.value, msg, data);
    }


    /**
     * 响应错误(不带状态码,带消息)
     *
     * @return MyResult
     */
    public static <T> BaseResult<T> error(String msg) {
        return new BaseResult<>(GlobalCode.Error.value, msg, null);
    }

    /**
     * 直接返回全局状态码
     * @param code
     * @return
     * @param <T>
     */
    public static <T> BaseResult<T> code(GlobalCode code) {
        return new BaseResult<>(code.value, code.desc, null);
    }

    /**
     * 响应错误，自定义返回消息
     *
     * @return
     */
    public static <T> BaseResult<T> error(Integer code, String msg) {
        return new BaseResult<>(code, msg, null);
    }

    /**
     * 响应错误, 自定义错误信息
     * @param code
     * @param msg
     * @return
     * @param <T>
     */
    public static <T> BaseResult<T> error(GlobalCode code, String msg) {
        return new BaseResult<>(code.value, msg, null);
    }

    /**
     * 自定义响应
     * @param code
     * @param msg
     * @param data
     * @return
     */
    public static <T> BaseResult<T> custom(Integer code, String msg, T data) {
        return new BaseResult<>(code, msg, data);
    }

    public boolean isSuccess() {
        return ObjectUtil.defaultIfNull(this.code, GlobalCode.Success.value) == GlobalCode.Success.value;
    }

}
