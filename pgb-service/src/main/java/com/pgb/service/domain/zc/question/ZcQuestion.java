package com.pgb.service.domain.zc.question;

import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import com.fasterxml.jackson.annotation.JsonGetter;
import com.pgb.service.domain.common.fabric.FabricJson;
import com.pgb.service.domain.common.image.FilePaperImg;
import com.pgb.service.domain.question.zwEssay.ZwTextDiff;
import com.pgb.service.enums.SubjectEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * Created by 2025/4/21 16:36
 */

@Data
@Schema(title = "默写题")
public class ZcQuestion {

    @Schema(title = "科目")
    private SubjectEnum subject;

    @Schema(title = "用户作答图片 URL")
    private List<FilePaperImg> userImgAnswerList;

    @Schema(title = "备注名称",description = "可自动识别")
    private String name;

    @Schema(title = "图片批改结果列表")
    private List<FabricJson> markJsonList;

    @Schema(title = "AI 脑力值")
    private Long aiTokens;

    @Schema(title = "用户得分", description = ">=90% 优 ；  >=80% 良  ；  >=60% 中等  ； <60% 不及格")
    private Double userScore;

    @Schema(title = "参考总分")
    private Double score;

    @Schema(title = "分数样式", description = "0：评级，1：分数")
    private Integer scoreStyle;

    @Schema(title = "与原文差异")
    private List<ZwTextDiff> polishDiffList;

    @Schema(title = "关联学生 id")
    private Long studentId;

    @Schema(title = "关联作业 id")
    private Long zcHomeworkId;

    @Schema(title = "所属题目id")
    private Long zcQuestionId;

    @Schema(title = "评级等级", description = "当分数样式为评级时 返回前端， 1：优，2：良，3：中等，4：不及格")
    @JsonGetter
    public Integer getScoreLevel() {
        // 评级 按分数给
        if (ObjectUtil.isAllNotEmpty(this.score, this.userScore)) {
            // >=85% 优 ；  >=70% 良  ；  >=55% 中等  ； <55% 不及格
            if (this.userScore >= NumberUtil.mul(this.score.doubleValue(), 0.85)) {
                return 1;
            } else if (this.userScore >= NumberUtil.mul(this.score.doubleValue(), 0.70)) {
                return 2;
            } else if (this.userScore >= NumberUtil.mul(this.score.doubleValue(), 0.55)) {
                return 3;
            } else {
                return 4;
            }
        }

        return 1;
    }

    @Schema(title = "设置评级 -- 更改分数")
    public void handleSetScoreLevel(Integer level) {

        if (ObjectUtil.isNull(level)) {
            return;
        }
        // >=85% 优 ；  >=70% 良  ；  >=55% 中等  ； <55% 不及格

        if (ObjectUtil.isNull(this.score)) {
            this.score = 30.0;
        }

        switch (level) {
            // >=85% 优
            case 1:
                this.userScore = Math.ceil(NumberUtil.mul(this.score.doubleValue(), 0.85));
                break;
            // 良 -- 70%
            case 2:
                this.userScore = Math.ceil(NumberUtil.mul(this.score.doubleValue(), 0.7));
                break;
            // 中等 -- 55%
            case 3:
                this.userScore = Math.ceil(NumberUtil.mul(this.score.doubleValue(), 0.55));
                break;
            // 不及格 -- 55% 以下
            case 4:
                this.userScore = Math.ceil(NumberUtil.mul(this.score.doubleValue(), 0.45));
                break;
            default:
                break;
        }
    }
}
