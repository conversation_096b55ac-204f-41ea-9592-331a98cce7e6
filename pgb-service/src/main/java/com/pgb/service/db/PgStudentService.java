package com.pgb.service.db;

import com.baomidou.mybatisplus.extension.service.IService;
import com.pgb.service.domain.student.PgStudent;
import com.pgb.service.domain.student.PgStudentVO;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【pg_student(学生表)】的数据库操作Service
* @createDate 2024-12-10 17:19:44
*/
public interface PgStudentService extends IService<PgStudent> {

    /**
     * 创建学生
     * @param classId
     */
    void createStudent(Long classId, String name);

    public List<PgStudentVO> sortByNoName(List<PgStudentVO> list);
}
