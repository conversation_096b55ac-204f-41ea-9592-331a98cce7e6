package com.pgb.student.controller.homework;

import cn.dev33.satoken.annotation.SaCheckLogin;
import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.lang.TypeReference;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.URLUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.json.JSONUtil;
import cn.hutool.poi.word.Word07Writer;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.pgb.common.core.global.BaseResult;
import com.pgb.common.core.global.GlobalCode;
import com.pgb.common.oss.service.OssService;
import com.pgb.service.db.*;
import com.pgb.service.domain.answer.PgAnswer;
import com.pgb.service.domain.answer.PgAnswerDTO;
import com.pgb.service.domain.answer.answerCost.PgAnswerCost;
import com.pgb.service.domain.answer.model.PgAnswerModel;
import com.pgb.service.domain.answer.model.PgAnswerModelVO;
import com.pgb.service.domain.classes.PgClasses;
import com.pgb.service.domain.common.image.FilePaperImg;
import com.pgb.service.domain.homework.PgHomework;
import com.pgb.service.domain.homework.PgHomeworkVO;
import com.pgb.service.domain.query.HomeworkQuery;
import com.pgb.service.domain.question.PgQuestion;
import com.pgb.service.domain.question.zwEssay.ZwEssayQuestion;
import com.pgb.service.domain.question.zwEssay.ZwEssayTypeEnum;
import com.pgb.service.domain.question.zwEssay.ZwRequire;
import com.pgb.service.domain.student.PgStudent;
import com.pgb.service.domain.user.PgUsers;
import com.pgb.service.domain.userConfig.CorrectConfigDTO;
import com.pgb.service.domain.userConfig.StudentReportConfigDTO;
import com.pgb.service.domain.userConfig.ExportConfigDTO;
import com.pgb.service.enums.CorrectStatusEnum;
import com.pgb.service.enums.SubjectEnum;
import com.pgb.service.interfaces.Debounce;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.xwpf.usermodel.ParagraphAlignment;
import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.apache.poi.xwpf.usermodel.XWPFParagraph;
import org.apache.poi.xwpf.usermodel.XWPFRun;
import org.apache.xmlbeans.XmlCursor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;
import org.springframework.web.bind.annotation.*;

import java.awt.*;
import java.io.*;
import java.net.URL;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * Created by 2025/2/24 19:28
 */
@Tag(name = "学生端/班级/作业")
@RestController("StudentClassHomeworkController")
@Transactional(rollbackFor = Exception.class)
@RequestMapping("/student/class/homework")
@RequiredArgsConstructor
@Slf4j
@DS("pgb")
public class HomeworkController {

    private final PgHomeworkService pgHomeworkService;

    private final PgStudentService pgStudentService;

    private final PgClassesService pgClassesService;

    private final PgAnswerCostService pgAnswerCostService;

    private final PgAnswerService pgAnswerService;

    private final PgExportRecordService pgExportRecordService;

    private final PgUsersService pgUsersService;

    private final PgUserConfigService pgUserConfigService;

    private final PgAnswerModelService pgAnswerModelService;

    private final OssService ossService;

    @Value("${correct.zw-correct}")
    private String correctUrl;

    @Operation(summary = "【学生端】重新提交作文")
    @PostMapping("reSubmit/{answerId}")
    @SaCheckLogin
    @Debounce(timeout = 1500)
    public BaseResult<Boolean> resubmit(@PathVariable Long answerId, @RequestBody ZwEssayQuestion essayForm) {

        PgAnswer answer = pgAnswerService.getById(answerId);

        if (ObjectUtil.isNull(answer)) {
            return BaseResult.code(GlobalCode.Item_Null);
        }

        // 判断是否有效
        if (CollUtil.isEmpty(essayForm.getUserImgAnswerList())) {
            return BaseResult.error("请上传图片");
        }

        // 保存关联明细数据
        PgAnswerCost cost = new PgAnswerCost();

        // 判断是否是会员
        PgUsers users = pgUsersService.getById(answer.getUserId());
        boolean isVip = users.getIsVip();
        // 获取今日剩余批改次数
        int remainNum = pgAnswerCostService.getTodaySubmitNum(answer.getUserId()).getRemainNum();

        if (remainNum == 0) {
            if (!isVip) {
                return BaseResult.error("今日老师的批改次数已耗尽，请联系老师哟~");
            } else {
                cost.setCreateTime(DateUtil.offsetDay(new Date(), 1));
            }
        } else {
            cost.setCreateTime(new Date());
        }

        // 处理图片 重命名
        for (FilePaperImg img : essayForm.getUserImgAnswerList()) {
            // 源
            String sourceKey = URLUtil.getPath(img.getImgUrl());

            if (sourceKey.contains("/tmp/")) {

                // 将 temp 替换为 zw
                String destinationKey = StrUtil.replace(sourceKey, "/tmp/", "/student/");
                ossService.renameFile(sourceKey, destinationKey);
                img.setImgUrl(
                        img.getImgUrl().replaceAll("/tmp/", "/student/")
                );
            }
        }

        // 替换用户上传图片
        ZwEssayQuestion userAnswer = JSONUtil.toBean(JSONUtil.toJsonStr(answer.getAnswer()), ZwEssayQuestion.class);
        userAnswer.setUserImgAnswerList(essayForm.getUserImgAnswerList());
        answer.setAnswer(userAnswer);

        // 更改状态
        answer.setStatus(CorrectStatusEnum.Uploaded);

        pgAnswerService.updateById(answer);

        // 保存消耗信息
        cost.setUserId(answer.getUserId());
        cost.setAnswerId(answer.getId());
        pgAnswerCostService.save(cost);

        // 事务完毕后执行
        if (TransactionSynchronizationManager.isActualTransactionActive()) {
            TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
                public void afterCommit() {
                    try {
                        // log.info("加入批改队列：{}", quesAnswer.getId());
                        // 加入批改队列
                        String url = StrUtil.format("{}/user/zw/info/addCorrect/addCorrect/{}", correctUrl, answer.getId());

                        HttpResponse response = HttpRequest.get(url)
                                .timeout(20 * 60 * 1000)
                                .execute();

                        if (!response.isOk()) {
                            log.error("【题目队列连接异常-ERROR】{},请检查：{}", answer.getId(), response.body());
                        }

                        String resultStr = response.body();

                        BaseResult<Boolean> result = Convert.convert(new TypeReference<>() {
                        }, JSONUtil.parseObj(resultStr));

                        if (!result.isSuccess()) {
                            log.error("【题目队列连接异常-ERROR】{},请检查：{}", answer.getId(), result.getMsg());
                        }
                    } catch (Exception e) {
                        log.error("【题目队列连接异常-ERROR】{},请检查：{}", answer.getId(), e.getMessage());
                    }
                }
            });
        }
        return BaseResult.success(true);
    }


    @Operation(summary = "查看当前用户是否提交过作业")
    @GetMapping("isSubmit/{homeworkId}")
    public BaseResult<Boolean> isSubmit(@PathVariable Long homeworkId) {

        // 获取当前登录用户的user_studentId
        long userStudentId = StpUtil.getSession().getLong("studentId");

        PgAnswer answer = pgAnswerService.getOne(new LambdaQueryWrapper<PgAnswer>()
                .eq(PgAnswer::getHomeworkId, homeworkId)
                .eq(PgAnswer::getStudentId, userStudentId)
                .eq(PgAnswer::getDeleted, false)
                .last("limit 1")
        );

        return BaseResult.success(ObjectUtil.isNotNull(answer));
    }

    @Operation(summary = "查看班级下的作业列表")
    @PostMapping("page/{classId}")
    @SaCheckLogin
    public BaseResult<IPage<PgHomeworkVO>> page(@PathVariable Long classId, @RequestBody HomeworkQuery query) {

        // 获取当前登录用户的user_studentId
        long userStudentId = StpUtil.getSession().getLong("studentId");

        PgStudent student = pgStudentService.getOne(new LambdaQueryWrapper<PgStudent>()
                .eq(PgStudent::getClassId, classId)
                .eq(PgStudent::getStudentUserId, userStudentId)
                .last("limit 1")
        );

        if (ObjectUtil.isNull(student)) {
            return BaseResult.error("学生未加入班级");
        }

        PgClasses classes = pgClassesService.getById(classId);

        if (ObjectUtil.isNull(classes)) {
            return BaseResult.error("班级不存在");
        }

        // 查看是否是审核模式
        StudentReportConfigDTO studentConfig = pgUserConfigService.getStudentConfig(classes.getUserId());

        IPage<PgHomeworkVO> page = pgHomeworkService.page(query.toMpPageSortByCreateTime(), new LambdaQueryWrapper<PgHomework>()
                .eq(PgHomework::getClassId, classId)
                .like(StrUtil.isNotBlank(query.getName()), PgHomework::getName, query.getName())
        ).convert(homework -> {

            PgHomeworkVO homeworkVO = BeanUtil.copyProperties(homework, PgHomeworkVO.class);

            // 返回作答状态 0：未作答，已作答
            PgAnswer answer = pgAnswerService.getOne(new LambdaQueryWrapper<PgAnswer>()
                    .select(PgAnswer::getId, PgAnswer::getHomeworkId, PgAnswer::getStudentId, PgAnswer::getDeleted, PgAnswer::getStatus)
                    .eq(PgAnswer::getHomeworkId, homework.getId())
                    .eq(PgAnswer::getStudentId, student.getId())
                    .eq(PgAnswer::getDeleted, false)
                    .last("limit 1"));

            if (ObjectUtil.isNotNull(answer)) {
                homeworkVO.setAnswerId(answer.getId());
                homeworkVO.setStatus(answer.getStatus());
            } else {
                homeworkVO.setStatus(CorrectStatusEnum.UnSubmit);
            }

            // 是否是审核模式
            if (ObjectUtil.isNotNull(studentConfig)) {
                homeworkVO.setIsCheckMode(studentConfig.getCheckMode());
            }

            // 是否允许错篇重提
            homeworkVO.setIsReSubmit(studentConfig.getReSubmit());
            return homeworkVO;
        });

        return BaseResult.success(page);
    }

    @Operation(summary = "【家长/学生】提交作文")
    @PostMapping("submit/{homeworkId}")
    @SaCheckLogin
    @Debounce(timeout = 1500)
    public BaseResult<Boolean> studentSubmit(@PathVariable Long homeworkId, @RequestBody ZwEssayQuestion essayForm) {

        long userStudentId;

        // 当家长点击分享链接提交时 需选择要提交作业的学生id
        if (ObjectUtil.isNotNull(essayForm.getStudentId())) {
            userStudentId = essayForm.getStudentId();
        } else {
            // 获取当前登录用户的user_studentId
            userStudentId = StpUtil.getSession().getLong("studentId");
        }

        // 获取作业
        PgHomework homework = pgHomeworkService.getById(homeworkId);

        if (ObjectUtil.isNull(homework)) {
            return BaseResult.error("作业不存在");
        }

        // 判断是否加入过班级
        PgStudent student = pgStudentService.getOne(new LambdaQueryWrapper<PgStudent>()
                .eq(PgStudent::getStudentUserId, userStudentId)
                .eq(PgStudent::getClassId, homework.getClassId())
                .last("limit 1"));

        if (ObjectUtil.isNull(student)) {
            return BaseResult.error(GlobalCode.Item_Null, "您还未加入该班级，请先加入班级！");
        }

        // 判断是否有效
        if (CollUtil.isEmpty(essayForm.getUserImgAnswerList())) {
            return BaseResult.error("请上传图片");
        }

        // 判断是否已经提交过
        if (pgAnswerService.exists(new LambdaQueryWrapper<PgAnswer>()
                .eq(PgAnswer::getHomeworkId, homeworkId)
                .eq(PgAnswer::getStudentId, student.getId())
                .eq(PgAnswer::getDeleted, false)
        )) {
            return BaseResult.error("当前学生作文已提交，请勿重复提交");
        }

        // 获取所属班级
        PgClasses classes = pgClassesService.getById(homework.getClassId());

        if (ObjectUtil.isNull(classes)) {
            return BaseResult.error("班级不存在");
        }

        // 查看老师配置
        CorrectConfigDTO configDTO = pgUserConfigService.getCorrectConfig(classes.getUserId());

        // 加入到提交列表中
        PgAnswer answer = new PgAnswer();

        // 保存关联明细数据
        PgAnswerCost cost = new PgAnswerCost();

        // 判断是否是会员
        PgUsers users = pgUsersService.getById(classes.getUserId());
        boolean isVip = users.getIsVip();
        // 获取今日剩余批改次数
        int remainNum = pgAnswerCostService.getTodaySubmitNum(classes.getUserId()).getRemainNum();

        if (remainNum == 0) {
            if (!isVip) {
                return BaseResult.error("今日老师的批改次数已耗尽，请联系老师哟~");
            } else {
                cost.setCreateTime(DateUtil.offsetDay(new Date(), 1));
            }
        } else {
            cost.setCreateTime(new Date());
        }

        answer.setStatus(CorrectStatusEnum.Uploaded);
        answer.setDeleted(false);
        // 班级的创建者
        answer.setUserId(classes.getUserId());
        answer.setAiTokens(0L);
        answer.setCreateTime(new Date());
        // 备注名称
        answer.setName(student.getName());
        answer.setStudentId(student.getId());
        // 关联作业提交
        answer.setHomeworkId(homework.getId());

        // 初始化批改设置
        ZwRequire require = new ZwRequire();
        // 批改配置
        require.setCorrectConfig(configDTO);

        // 所属题目
        if (ObjectUtil.isNotNull(homework.getQuestionInfo())) {
            PgQuestion question = JSONUtil.toBean(JSONUtil.toJsonStr(homework.getQuestionInfo()), PgQuestion.class);

            // 复制批改标准
            BeanUtil.copyProperties(question, require);

            // 设置分数
            essayForm.setScore(
                    Double.valueOf(
                            ObjectUtil.defaultIfNull(question.getScore(), 30)
                    )
            );

            // 设置作文类型
            if (question.getSubject().equals(SubjectEnum.Chinese)) {
                essayForm.setZwType(ZwEssayTypeEnum.Chinese);
            } else if (question.getSubject().equals(SubjectEnum.English)) {
                essayForm.setZwType(ZwEssayTypeEnum.English);
            }
        }
        // 若没有题目根据配置来
        else {
            // 设置默认分数
            essayForm.setScore(configDTO.getScoreStandard());
        }

        // 设置批改标准
        essayForm.setRequire(require);
        // 备注名称 姓名
        essayForm.setName(student.getName());

        // 处理图片 重命名
        for (FilePaperImg img : essayForm.getUserImgAnswerList()) {
            // 源
            String sourceKey = URLUtil.getPath(img.getImgUrl());

            if (sourceKey.contains("/tmp/")) {

                // 将 temp 替换为 zw
                String destinationKey = StrUtil.replace(sourceKey, "/tmp/", "/student/");
                ossService.renameFile(sourceKey, destinationKey);
                img.setImgUrl(
                        img.getImgUrl().replaceAll("/tmp/", "/student/")
                );
            }
        }

        answer.setAnswer(essayForm);

        // 保存
        pgAnswerService.save(answer);

        // 班级创建人
        cost.setUserId(classes.getUserId());
        cost.setAnswerId(answer.getId());
        pgAnswerCostService.save(cost);

//        PgUsers user = pgUsersService.getById(classes.getUserId());

        // TODO 接口形式
        // https://proxy.pigaibang.com/xcx/api/user/zw/info/addCorrect/addCorrect/1871451455035248641
        // http://localhost:8084/api/user/zw/info/addCorrect/addCorrect/1891725408344653826

        // 事务完毕后执行
        if (TransactionSynchronizationManager.isActualTransactionActive()) {
            TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
                public void afterCommit() {
                    try {
                        // log.info("加入批改队列：{}", quesAnswer.getId());
                        // 加入批改队列
                        String url = StrUtil.format("{}/user/zw/info/addCorrect/addCorrect/{}", correctUrl, answer.getId());

                        HttpResponse response = HttpRequest.get(url)
                                .timeout(20 * 60 * 1000)
                                .execute();

                        if (!response.isOk()) {
                            log.error("【题目队列连接异常-ERROR】{},请检查：{}", answer.getId(), response.body());
                        }

                        String resultStr = response.body();

                        BaseResult<Boolean> result = Convert.convert(new TypeReference<>() {
                        }, JSONUtil.parseObj(resultStr));

                        if (!result.isSuccess()) {
                            log.error("【题目队列连接异常-ERROR】{},请检查：{}", answer.getId(), result.getMsg());
                        }
                    } catch (Exception e) {
                        log.error("【题目队列连接异常-ERROR】{},请检查：{}", answer.getId(), e.getMessage());
                    }
                }
            });
        }

        return BaseResult.success(true);
    }

    @Operation(summary = "查看作业内容")
    @GetMapping("info/{homeworkId}")
    @SaCheckLogin
    public BaseResult<PgHomeworkVO> get(@PathVariable Long homeworkId) {

        PgHomework homework = pgHomeworkService.getById(homeworkId);

        if (ObjectUtil.isNull(homework)) {
            return BaseResult.error(GlobalCode.Item_Null, "作业不存在");
        }

        PgHomeworkVO homeworkVO = BeanUtil.copyProperties(homework, PgHomeworkVO.class);

        PgClasses classes = pgClassesService.getById(homework.getClassId());

        if (ObjectUtil.isNotNull(classes)) {
            // 班级名称
            homeworkVO.setClassName(classes.getName());
        }

        return BaseResult.success(homeworkVO);
    }

    @Operation(summary = "查看提交详情")
    @GetMapping("submitInfo/{answerId}")
    @SaCheckLogin
    public BaseResult<PgAnswerDTO> detail(@PathVariable Long answerId) {

        PgAnswer answer = pgAnswerService.getById(answerId);

        PgAnswerDTO answerDTO = JSONUtil.toBean(JSONUtil.toJsonStr(answer), PgAnswerDTO.class);

        if (ObjectUtil.isNull(answer.getCorrectResult())) {
            return BaseResult.error("当前批改内容不存在，请批改完成后查看");
        }
        ZwEssayQuestion userAnswer = JSONUtil.toBean(answer.getCorrectResult().toString(), ZwEssayQuestion.class);

        answerDTO.setCorrectResult(userAnswer);

        return BaseResult.success(answerDTO);
    }

    @Operation(summary = "删除作文提交记录")
    @DeleteMapping("{answerId}")
    @SaCheckLogin
    public BaseResult<Boolean> delete(@PathVariable Long answerId) {

        PgAnswer answer = pgAnswerService.getById(answerId);
        if (ObjectUtil.isNull(answer)) {
            return BaseResult.code(GlobalCode.Item_Null);
        }

        // 标记为删除
        answer.setDeleted(true);
        pgAnswerService.updateById(answer);

        return BaseResult.success(true);
    }

    @Operation(summary = "导出word")
    @GetMapping("export/word/{answerId}")
    public ResponseEntity<byte[]> exportWord(@PathVariable Long answerId) {

        // 获取批改详情
        PgAnswer answer = pgAnswerService.getById(answerId);

        String fileName;
        // 若备注不为空
        if (StrUtil.isNotBlank(answer.getName())) {
            fileName = URLEncoder.encode(answer.getName() + "_" + DateUtil.format(new Date(), "MM-dd") + "_作文批改报告", StandardCharsets.UTF_8);
        } else {
            // 导出的文件名
            fileName = URLEncoder.encode(DateUtil.format(new Date(), "MM-dd") + "_作文批改报告", StandardCharsets.UTF_8);
        }

        File exportWord = pgExportRecordService.getExportWord(answer, fileName);

        // 构建导出所需参数
        HttpHeaders headers = new HttpHeaders();
        headers.set("Access-Control-Expose-Headers", "Content-Disposition"); // 必须设置，否则无法获取文件名
        headers.set("Content-Disposition", "attachment;filename=" + exportWord.getName());
        headers.set("Content-Type", "application/vnd.openxmlformats-officedocument.wordprocessingml.document");

        // 读取文件
        byte[] wordBytes = FileUtil.readBytes(exportWord);

        // 删除临时文件
        FileUtil.del(exportWord);

        return ResponseEntity.ok()
                .headers(headers)
                .contentType(MediaType.valueOf("application/vnd.openxmlformats-officedocument.wordprocessingml.document"))
                .body(wordBytes);

    }

    @Operation(summary = "导出润色")
    @GetMapping("polish/{answerId}")
    @SaCheckLogin
    public ResponseEntity<byte[]> exportPolish(@PathVariable Long answerId) {

        // 获取批改详情
        PgAnswer answer = pgAnswerService.getById(answerId);

        ZwEssayQuestion userAnswer = JSONUtil.toBean(JSONUtil.toJsonStr(answer.getAnswer()), ZwEssayQuestion.class);

        // 获取润色内容
        String polish = JSONUtil.parseObj(answer.getCorrectResult()).getStr("polish");

        // 初始化最终导出的 word 文件
        File tempFile = null;
        if (ObjectUtil.isNotNull(userAnswer.getZwType())) {

            if (StrUtil.isNotBlank(polish)) {

                polish = polish.replaceAll("\n\n", "\n");
                List<String> polishList = ListUtil.toList(polish.split("\n"));

                if (!polishList.isEmpty()) {

                    // 语文作文
                    if (userAnswer.getZwType().equals(ZwEssayTypeEnum.Chinese)) {

                        // 【作文润色】 使用模板
                        String templatePath = "https://cdn.pigaibang.com/common/xcx-zw/template/polish_export_template.docx";

                        FileInputStream fis = null;
                        FileOutputStream fos = null;

                        try {
                            InputStream in = new URL(templatePath).openStream();
                            File tempFile1 = FileUtil.createTempFile();
                            FileUtil.writeFromStream(in, tempFile1);
                            // 删除临时文件
                            tempFile1.deleteOnExit();
                            fis = new FileInputStream(tempFile1);

                            // 初始化模板文档
                            XWPFDocument doc = new XWPFDocument(fis);
                            // 替换模板中的占位符
                            // 占位符
                            String placeholder = "polish";
                            String replacement = polishList.get(0);

                            // 替换占位符并记录第一个匹配的段落
                            XWPFParagraph firstMatchedParagraph = null;
                            for (XWPFParagraph paragraph : doc.getParagraphs()) {
                                for (XWPFRun run : paragraph.getRuns()) {
                                    // 获取文档中的占位符 从头开始获取
                                    String text = run.getText(0);
                                    if (ObjectUtil.isNotNull(text) && text.contains(placeholder)) {
                                        text = text.replace(placeholder, replacement);

                                        // 作文格长度
                                        int totalLength = 20;
                                        // 文本长度
                                        int textLength = text.length();

                                        // 有作文题目
                                        if (textLength < 15) {

                                            // 需要的总空格数
                                            int totalSpace = totalLength - textLength;
                                            // 前导空格
                                            int leadingSpaces = (int) Math.ceil(totalSpace / 2.0);
                                            StringBuilder builder = new StringBuilder();
                                            for (int i = -1; i < leadingSpaces; i++) {
                                                builder.append("  ");
                                            }
                                            run.setText(builder + text, 0);
                                        }
                                        // 没有作文题目时
                                        else {
                                            run.setText("    " + text, 0);
                                        }
                                        if (ObjectUtil.isNull(firstMatchedParagraph)) {
                                            firstMatchedParagraph = paragraph;
                                        }
                                    }
                                }
                            }

                            // 将光标移动到下一个段落  因为光标生成在段落最开始位置
                            XmlCursor xmlCursor = firstMatchedParagraph.getCTP().newCursor();
                            xmlCursor.toNextSibling();

                            // 在第一个匹配的段落后添加新段落
                            if (ObjectUtil.isNotNull(firstMatchedParagraph)) {

                                for (int i = 1; i < polishList.size(); i++) {
                                    // 从光标位置开始插入段落
                                    XWPFParagraph newParagraph = doc.insertNewParagraph(xmlCursor);
                                    XWPFRun newRun = newParagraph.createRun();
                                    newRun.setText(polishList.get(i));
                                    // 设置首行缩进两字符
                                    newParagraph.setIndentationFirstLine(720);

                                    // 移动光标
                                    xmlCursor = newParagraph.getCTP().newCursor();
                                    xmlCursor.toNextSibling();
                                }
                            }

                            // 创建临时文件
                            tempFile = FileUtil.createTempFile();
                            fos = new FileOutputStream(tempFile);
                            doc.write(fos);
                            doc.close();

                        } catch (IOException e) {
                            throw new RuntimeException(e);
                        } finally {
                            if (fis != null) {
                                try {
                                    fis.close();
                                } catch (IOException e) {
                                    e.printStackTrace();
                                }
                            }
                            if (fos != null) {
                                try {
                                    fos.close();
                                } catch (IOException e) {
                                    e.printStackTrace();
                                }
                            }
                        }
                    }
                    // 英语作文
                    else {

                        // 初始化
                        Word07Writer writer = new Word07Writer();
                        writer.addText(new Font("宋体", Font.BOLD, 15), "【全文润色】");
                        writer.addText(new Font("宋体", Font.PLAIN, 10), " ");

                        // 第一段内容
                        String first = polishList.get(0);
                        if (first.length() > 20) {
                            // 正文
                            writer.addText(new Font("宋体", Font.PLAIN, 11), "        " + first);
                        } else {
                            // 作文标题
                            writer.addText(ParagraphAlignment.CENTER, new Font("宋体", Font.PLAIN, 13), first);
                        }

                        for (int i = 1; i < polishList.size(); i++) {
                            // 构造润色内容
                            writer.addText(new Font("宋体", Font.PLAIN, 11), "        " + polishList.get(i));
                        }

                        // 创建临时文件
                        tempFile = FileUtil.createTempFile();
                        writer.flush(tempFile);
                        writer.close();
                    }
                }
            }
        }

        // 导出的文件名
        String fileName = URLEncoder.encode(DateUtil.format(new Date(), "yyyy-MM-dd") + "全文润色", StandardCharsets.UTF_8);

        // 构建导出所需参数
        HttpHeaders headers = new HttpHeaders();
        headers.set("Access-Control-Expose-Headers", "Content-Disposition"); // 必须设置，否则无法获取文件名
        headers.set("Content-Disposition", "attachment;filename=" + fileName + ".docx");
        headers.set("Content-Type", "application/vnd.openxmlformats-officedocument.wordprocessingml.document");

        // 读取文件
        byte[] wordBytes = FileUtil.readBytes(tempFile);

        return ResponseEntity.ok()
                .headers(headers)
                .contentType(MediaType.valueOf("application/vnd.openxmlformats-officedocument.wordprocessingml.document"))
                .body(wordBytes);

    }

    @Operation(summary = "获取单个记录批改状态")
    @GetMapping("status/{answerId}")
    @SaCheckLogin
    public BaseResult<CorrectStatusEnum> getStatus(@PathVariable Long answerId) {

        PgAnswer answer = pgAnswerService.getById(answerId);

        return BaseResult.success(answer.getStatus());
    }

    @Operation(summary = "某作业的范文列表")
    @GetMapping("models/{homeworkId}")
    @SaCheckLogin
    public BaseResult<List<PgAnswerModelVO>> models(@PathVariable Long homeworkId) {

        List<PgAnswerModelVO> list = pgAnswerModelService.list(new LambdaQueryWrapper<PgAnswerModel>()
                        .eq(PgAnswerModel::getHomeworkId, homeworkId)
                        .orderByAsc(PgAnswerModel::getSort)
                ).stream()
                .map(model -> {

                    PgAnswerModelVO modelVO = BeanUtil.copyProperties(model, PgAnswerModelVO.class);

                    PgAnswer answer = pgAnswerService.getById(model.getAnswerId());
                    ZwEssayQuestion userAnswer = JSONUtil.toBean(answer.getCorrectResult().toString(), ZwEssayQuestion.class);

                    // 用户图片
                    modelVO.setUserImgAnswerList(userAnswer.getUserImgAnswerList());
                    // 用户得分
                    modelVO.setUserScore(userAnswer.getUserScore());
                    // 作文提交时间
                    modelVO.setZwCreateTime(answer.getCreateTime());

                    // 学生信息
                    PgStudent student = pgStudentService.getById(answer.getStudentId());
                    if (ObjectUtil.isNotNull(student)) {
                        modelVO.setStudentName(student.getName());
                        modelVO.setStudentNo(student.getStudentNo());
                    }

                    return modelVO;

                })
                .toList();

        return BaseResult.success(list);

    }
}
