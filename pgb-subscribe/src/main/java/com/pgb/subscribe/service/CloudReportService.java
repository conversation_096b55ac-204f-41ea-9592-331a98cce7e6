package com.pgb.subscribe.service;

import cn.binarywang.wx.miniapp.api.WxMaService;
import cn.binarywang.wx.miniapp.bean.WxMaSubscribeMessage;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.TimeInterval;
import cn.hutool.core.lang.TypeReference;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.json.JSONUtil;
import com.pgb.common.core.global.BaseResult;
import com.pgb.common.core.global.exception.BaseException;
import com.pgb.common.core.utils.EnvUtils;
import com.pgb.common.redis.QueueUtils;
import com.pgb.common.redis.RedisUtils;
import com.pgb.service.custom.CorrectService;
import com.pgb.service.db.PgHomeworkReportService;
import com.pgb.service.db.PgHomeworkService;
import com.pgb.service.db.PgUsersService;
import com.pgb.service.domain.homework.PgHomework;
import com.pgb.service.domain.homework.PgHomeworkReport;
import com.pgb.service.domain.user.PgUsers;
import com.pgb.service.domain.GlobQueueConstants;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.error.WxErrorException;
import org.redisson.api.RBlockingQueue;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.concurrent.Executors;
import java.util.concurrent.atomic.AtomicReference;

import static com.pgb.service.custom.QwMsgService.sendErrorMessage;

@Service
@RequiredArgsConstructor
@Slf4j
public class CloudReportService {

    private final PgHomeworkReportService pgHomeworkReportService;

    private final PgUsersService pgUsersService;

    private final WxMaService wxMaService;

    private final PgHomeworkService pgHomeworkService;

    private final CorrectService correctService;

    @Async
    public void cloudScribe(String queueName) {
        // 获取批改队列
        RBlockingQueue<Long> queue = QueueUtils.getClient().getBlockingQueue(queueName);
        AtomicReference<Boolean> isContinue = new AtomicReference<>(true);
        Executor executor = Executors.newFixedThreadPool(300);
        while (isContinue.get()) {
            try {
                AtomicReference<Long> homeworkReportId = new AtomicReference<>();

                // 阻塞获取一个答案
                homeworkReportId.set(queue.take());

                CompletableFuture<Void> f = CompletableFuture.runAsync(() -> {
                    try {
                        log.info("【{}-接收班级报告生成监听信息】：{}", queueName, homeworkReportId.get());

                        boolean result = cloudCorrect(homeworkReportId.get());

                        if (result) {
                            // 获取已上传作业
                            PgHomeworkReport report = pgHomeworkReportService.getById(homeworkReportId.get());
                            if (!EnvUtils.isDev()) {
                                sendWxMsg(report);
                            }
                        }
                    } catch (Exception e) {
                        log.error("【{}-班级报告队列异常】：{}，进入重试机制", queueName, homeworkReportId.get(), e);
                        // 进入重试机制
                        retry(queueName, homeworkReportId);
                    }
                }, executor).exceptionally(e -> {
                    log.error("【{}-批改队列异常】：{}，异步异常", queueName, homeworkReportId.get(), e);
                    return null;
                }).whenComplete((v, e1) -> {
                    // 判断是否继续生成
                    if (!correctService.getCorrecting()) {
                        log.info("当前线程已手动停止批改");
                        isContinue.set(false);
                    }
                });;
            } catch (InterruptedException e) {
                if (ObjectUtil.isNotNull(e.getMessage()) && e.getMessage().contains("Redisson is shutdown")) {
                    log.error("CloudReportService --> Redisson 已中断连接");
                } else {
                    e.printStackTrace();
                }
            }
        }
    }

    /**
     * 重试机制
     *
     * @param queueName
     * @param homeworkId
     */
    private void retry(String queueName, AtomicReference<Long> homeworkId) {
        // 重试当前队列
        if (ObjectUtil.isNotNull(homeworkId.get())) {
            // 重试三次
            Integer retry = RedisUtils.getCacheObject(GlobQueueConstants.PGB_XCX_HOMEWORK_REPORT_RETRY_QUEUE.name() + homeworkId.get());

            if (ObjectUtil.defaultIfNull(retry, 0) < 3) {
                // 加入报告生成队列
                QueueUtils.addQueueObject(queueName, homeworkId.get());

                // 发送异常报警通知
                if (!EnvUtils.isDev()) {
                    sendErrorMessage("已进入重试队列，重试次数：" + ObjectUtil.defaultIfNull(retry, 0));
                }

                // 重试次数
                RedisUtils.setCacheObject(GlobQueueConstants.PGB_XCX_HOMEWORK_REPORT_RETRY_QUEUE.name() + homeworkId.get(), ObjectUtil.defaultIfNull(retry, 0) + 1, Duration.ofHours(6));
            } else {
                // 发送异常报警通知
                if (!EnvUtils.isDev()) {
                    sendErrorMessage("班级报告异常，已重试3次！必须线上排查！homeworkReportId：" + homeworkId.get());
                }
            }
        } else {
            if (!EnvUtils.isDev()) {
                log.error("无效homeworkReportId，需排查！");
            }
        }
    }

    /**
     * 异步执行反馈结果
     *
     * @param homeworkId
     * @return
     */
    private Boolean cloudCorrect(Long homeworkId) {

        TimeInterval timer = DateUtil.timer();

        String url = "http://zw-correct-cloud.pigaibang.com/api/correct/report/" + homeworkId;

        String resultStr = HttpRequest.get(url)
                .timeout(20 * 60 * 1000)
                .execute()
                .body();

        BaseResult<Boolean> result = Convert.convert(new TypeReference<BaseResult<Boolean>>() {
        }, JSONUtil.parseObj(resultStr));

        if (result.isSuccess()) {
            log.info("【云函数班级分析报告生成-生成结束】{}用时：{}", homeworkId, timer.intervalPretty());
            return true;
        } else {
            log.error("【云函数班级分析报告异常-ERROR】{}请检查：{}", homeworkId, result.getMsg());
            throw new BaseException("云函数班级分析报告异常, 请检查");
        }
    }

    public void sendWxMsg(PgHomeworkReport report) {
        // 发送通知
        PgUsers users = pgUsersService.getById(report.getUserId());
        PgHomework homework = pgHomeworkService.getById(report.getHomeworkId());

        WxMaSubscribeMessage message = new WxMaSubscribeMessage();
        message.setToUser(users.getWxOpenId());
        message.setTemplateId("JZbQmVNFa2OauXscmWzVNzBvSageD5niXtKyLxG4Uvc");
        // pages/homework/statistic/report?homeworkId=xxx
        message.setPage("pages/homework/statistic/report?homeworkId=" + report.getHomeworkId());
        message.setData(ListUtil.toList(
                new WxMaSubscribeMessage.MsgData("short_thing2", "作业"),
                new WxMaSubscribeMessage.MsgData("thing4", "班级作业分析报告"),
                new WxMaSubscribeMessage.MsgData("thing1", StrUtil.subPre(homework.getName(), 8))
        ));
        if (EnvUtils.isDev()) {
            message.setMiniprogramState("developer");
        }

        try {
            wxMaService.getMsgService().sendSubscribeMsg(message);
        } catch (WxErrorException e) {
            log.error("小程序模板发送失败：{}", e.getError());
        }
    }
}
