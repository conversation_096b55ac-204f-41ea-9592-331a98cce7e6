package com.pgb.service.domain.chat;

import java.io.Serializable;

import java.util.Date;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.ToString;
import lombok.Builder;
import org.springframework.validation.annotation.Validated;
import jakarta.validation.constraints.Size;

/**
* AI会话表
* @TableName pg_chat
*/
@Schema(description = "AI会话表 DTO")
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode
@ToString
@Validated
@Builder
public class PgChatDTO implements Serializable {

    @Schema(title = "会话表")
    private Long id;

    @Schema(title = "创建时间")
    private Date createTime;

    @Schema(title = "用户id")
    private Long userId;

    @Schema(title = "agentId")
    private Long agentId;

    @Schema(title = "AI返回内容")
    private String content;

    @Schema(title = "会话标题")
    private String title;

}
