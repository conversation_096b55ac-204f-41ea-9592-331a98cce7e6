package com.pgb.service.domain.zc.common.textbook.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 图片识别单词确认录入请求DTO
 *
 * <AUTHOR>
 */
@Schema(description = "图片识别单词确认录入请求DTO")
@Data
public class ImageWordConfirmDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "单元ID", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "单元ID不能为空")
    private Long unitId;

    @Schema(description = "单词详细信息列表", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "单词列表不能为空")
    @Valid
    private List<WordConfirmDetailDTO> wordDetails;
}
