<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.pgb</groupId>
        <artifactId>pgb-common</artifactId>
        <version>${revision}</version>
    </parent>

    <artifactId>pgb-common-oss</artifactId>
    <description>common-oss 对象存储服务</description>

    <dependencies>
        <dependency>
            <groupId>com.pgb</groupId>
            <artifactId>pgb-common-core</artifactId>
        </dependency>

        <dependency>
            <groupId>com.pgb</groupId>
            <artifactId>pgb-common-mybatis</artifactId>
        </dependency>

        <!-- OSS S3 协议，通用 -->
        <dependency>
            <groupId>com.amazonaws</groupId>
            <artifactId>aws-java-sdk-s3</artifactId>
        </dependency>

        <!--  腾讯云接口SDK   -->
        <dependency>
            <groupId>com.tencentcloudapi</groupId>
            <artifactId>tencentcloud-sdk-java</artifactId>
        </dependency>

        <!-- 腾讯云 OSS -->
        <dependency>
            <groupId>com.qcloud</groupId>
            <artifactId>cos_api</artifactId>
        </dependency>

        <!-- 阿里云 OSS -->
        <dependency>
            <groupId>com.aliyun.oss</groupId>
            <artifactId>aliyun-sdk-oss</artifactId>
        </dependency>

        <!-- 百度云 OSS -->
        <dependency>
            <groupId>com.baidubce</groupId>
            <artifactId>bce-java-sdk</artifactId>
        </dependency>
    </dependencies>

</project>
