package com.pgb.service.db.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.pgb.service.db.PgVipService;
import com.pgb.service.domain.vip.PgVip;
import com.pgb.service.mapper.PgVipMapper;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @description 针对表【pg_vip】的数据库操作Service实现
 * @createDate 2024-06-25 16:44:48
 */
@Service
public class PgVipServiceImpl extends ServiceImpl<PgVipMapper, PgVip> implements PgVipService {

    @Override
    public boolean isNewBuy(Long userId) {
        return count(new LambdaQueryWrapper<PgVip>().eq(PgVip::getUserId, userId)) < 2;
    }

    @Override
    public boolean isCodeNewBuy(Long userId) {
        return count(new LambdaQueryWrapper<PgVip>().eq(PgVip::getUserId, userId)) < 1;
    }

    @Override
    public Long buyNum(Long userId) {
        return count(new LambdaQueryWrapper<PgVip>().eq(PgVip::getUserId, userId));
    }

}




