package com.pgb.service.domain.vip;

import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;

import com.pgb.service.enums.ChannelTypeEnum;
import com.pgb.service.enums.VipTypeEnum;
import lombok.Data;

/**
 * 订单详情表（含详细地址）
 * @TableName pg_vip_channel
 */
@TableName(value ="pg_vip_channel")
@Data
public class PgVipChannel implements Serializable {
    /**
     * 会员渠道
     */
    private Long id;

    /**
     * 用户手机号
     */
    private String phone;

    /**
     * 省份
     */
    private String province;

    /**
     * 城市
     */
    private String city;

    /**
     * 区县
     */
    private String district;

    /**
     * 镇/街道
     */
    private String town;

    /**
     * 用户id，根据哪个使用兑换码的来锁定
     */
    private Long userId;

    /**
     * 小红书订单id
     */
    private String xhsOrderId;

    /**
     * 渠道来源，0：系统，1：小红书
     */
    private ChannelTypeEnum channelType;

    /**
     * 开通时间
     */
    private Date createTime;

    /**
     * 会员类型
     */
    private VipTypeEnum vipType;

    /**
     * 支付金额
     */
    private Integer amount;

    /**
     * 收货人姓名
     */
    private String name;

    /**
     * 收货人地址
     */
    private String address;

    /**
     * 纬度
     */
    private String poi;
}
