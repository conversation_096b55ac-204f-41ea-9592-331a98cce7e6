package com.pgb.student.service.impl;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.pgb.common.core.global.RoleConstants;
import com.pgb.common.satoken.LoginVO;
import com.pgb.service.domain.student.PgStudent;
import com.pgb.service.enums.DeviceTypeEnum;
import com.pgb.student.domain.PgUserStudent;
import com.pgb.student.service.PgUserStudentService;
import com.pgb.student.service.PgUsersService;
import com.pgb.student.domain.PgUsers;
import com.pgb.student.domain.vo.PgUsersVO;
import com.pgb.student.mapper.PgUsersMapper;
import com.pgb.student.service.PgbCustomService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.bean.WxOAuth2UserInfo;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【pg_users(用户表)】的数据库操作Service实现
 * @createDate 2025-02-24 18:34:22
 */
@Service("StudentPgUsersServiceImpl")
@RequiredArgsConstructor
@Slf4j
public class PgUsersServiceImpl extends ServiceImpl<PgUsersMapper, PgUsers>
        implements PgUsersService {

    private final PgbCustomService pgbCustomService;

    private final PgUserStudentService pgUserStudentService;

    @Override
    public PgUsers getOrCreateUserByPhone(String phone) {
        // 先查有没有
        PgUsers pgUsers = getOne(new LambdaQueryWrapper<PgUsers>().eq(PgUsers::getPhone, phone), false);

        if (ObjectUtil.isNull(pgUsers)) {
            return registerUser(phone, null, null, null);
        }

        return pgUsers;
    }

    @Override
    public PgUsers getOrCreateUserByOpenId(String openId, String unionId) {
        // 先查有没有
        PgUsers pgUsers = getOne(new LambdaQueryWrapper<PgUsers>()
                        .eq(PgUsers::getWxOpenId, openId)
                        .last("LIMIT 1")
                , false);

        if (ObjectUtil.isNull(pgUsers)) {
            return registerUser(null, openId, unionId, null);
        }

        return pgUsers;
    }

    @Override
    public PgUsers registerUser(String phone, String openId, String unionId, WxOAuth2UserInfo wxMpUser) {
        // 新用户注册，将生成的新用户信息保存到数据库中
        PgUsers user = new PgUsers();
        // 初始化用户数据
        if (ObjectUtil.isNull(wxMpUser)) {
            // 默认生成用户数数据
            user.setNickName("邦友" + StrUtil.subSufByLength(openId, 4));
            user.setAvatar("https://cdn.pigaibang.com/common/xcx-zw/avatar.png");
        } else {
            user.setNickName(ObjectUtil.defaultIfBlank(
                    wxMpUser.getNickname(),
                    StrUtil.format("微信用户 {}", StrUtil.subSufByLength(wxMpUser.getOpenid(), 4))
            ));
            user.setAvatar(wxMpUser.getHeadImgUrl());
        }
        user.setCreateTime(new Date());
        user.setLastLoginTime(new Date());

        // 手机
        if (!StrUtil.isBlank(phone)) user.setPhone(phone);

        // openId
        if (!StrUtil.isBlank(openId)) user.setWxOpenId(openId);

        // unionId
        if (!StrUtil.isEmpty(unionId)) user.setWxUnionId(unionId);

        save(user);

        return user;

    }

    @Override
    public LoginVO<PgUsersVO> loginSuccess(PgUsers user, DeviceTypeEnum deviceType) {
        // -- 设置上次登录时间
        user.setLastLoginTime(new Date());

        updateById(user);

        // 保存信息，生成token，保存token
        if (!StpUtil.isLogin()) {
            StpUtil.login(user.getId(), deviceType.name());
        }

        // 保存用户信息
        StpUtil.getSession().set(RoleConstants.User, user);

        // 保存角色
        StpUtil.getSession().set("role", RoleConstants.User);

        List<PgUserStudent> studentList = pgUserStudentService.getStudentList(StpUtil.getLoginIdAsLong());

        if (!studentList.isEmpty()) {
            // 保存角色
            StpUtil.getSession().set("studentId", studentList.get(0).getId());
//            log.info("用户学生id token {}", studentList.get(0).getId());
        }

        // 返回数据
        LoginVO<PgUsersVO> loginVO = new LoginVO<>();
        loginVO.setName(user.getNickName());
        loginVO.setPhone(user.getPhone());
        loginVO.setToken(StpUtil.getTokenValue());
        loginVO.setExpireTime(DateUtil.offsetSecond(new Date(), (int) StpUtil.getTokenTimeout()).getTime());
        loginVO.setAvatarUrl(user.getAvatar());
        loginVO.setUserInfo(BeanUtil.copyProperties(user, PgUsersVO.class));

        return loginVO;
    }
}




