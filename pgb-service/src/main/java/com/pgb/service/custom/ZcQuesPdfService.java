package com.pgb.service.custom;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.TimeInterval;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.URLUtil;
import cn.hutool.crypto.digest.DigestUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.pgb.common.oss.service.OssService;
import com.pgb.service.db.PgZcQuestionService;
import com.pgb.service.domain.zc.question.PgZcQuestion;
import com.pgb.service.enums.ExportStatusEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.pdfbox.Loader;
import org.apache.pdfbox.multipdf.Splitter;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.springframework.stereotype.Component;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * Created by 2025/7/7 11:53
 */
@Component("ZcQuesPdfService")
@RequiredArgsConstructor
@Slf4j
public class ZcQuesPdfService {

    private final OssService ossService;

    private final PgZcQuestionService pgZcQuestionService;

    public void renderPdf(PgZcQuestion zcQuestion) {

        TimeInterval timer = DateUtil.timer();

        // 任务有效性判断
        if (ObjectUtil.isNull(zcQuestion)){
            log.info("无效的字词题目-为空");
            return;
        }

        if (zcQuestion.getPdfStatus().equals(ExportStatusEnum.Completed)){
            log.info("pdf渲染任务已完成，跳过：{}", zcQuestion.getId());
            return;
        }

        log.info("开始渲染pdf任务：{}", zcQuestion.getId());

//        String baseUrl = "http://***************:3000/html2pdf";
//        String baseUrl = "http://***************:3000/html2pdf";
//        String baseUrl = "http://test-api-2.pigaibang.com/html2pdf";
//       String baseUrl = "http://localhost:3000/html2pdf";
//        String baseUrl = "http://puppeteer-sdccvasrha.cn-beijing.fcapp.run/html2pdf";'
//        String baseUrl = "http://**************:3000/html2pdf";
        String baseUrl = "http://*************:3000/html2pdf";

//        String baseUrl = EnvUtils.isDev() ? "https://puppeteer-bvwfuaqrgx.cn-beijing.fcapp.run/html2pdf" : "https://puppeteer-bvwfuaqrgx.cn-beijing-vpc.fcapp.run/html2pdf";
        // 对应前端的网址
        String renderUrl = StrUtil.format(
                "http://localhost:3000/{}?isPrint=true&id={}",
                zcQuestion.getType().printPath, zcQuestion.getId()
        );

        // 执行渲染
        HttpResponse response = HttpRequest.post(baseUrl)
                .body(JSONUtil.createObj()
                        .putOnce("url", renderUrl).toString()
                )
                .timeout(5 * 60 * 1000)
                .execute();

        // 生成失败
        if (!response.isOk()) {
            log.info("pdf渲染失败，请重试：{}", zcQuestion.getId());
        }

        // 返回的是PDF
        InputStream inputStream = response.bodyStream();
        File tempFile = FileUtil.createTempFile(".pdf", true);
        FileUtil.writeFromStream(inputStream, tempFile);

        // 此时拿更新坐标后的题目
        // 重新计算位置后 --> 更新md5
        PgZcQuestion newZcQuestion = pgZcQuestionService.getById(zcQuestion.getId());
        String currentMd5 = pgZcQuestionService.getZcQuestionMd5(newZcQuestion);
        newZcQuestion.setMd5(currentMd5);
        pgZcQuestionService.updateById(newZcQuestion);

        // 三个pdf链接
        List<String> pdfUrlList = new ArrayList<>();
        // 分割PDF（包含两部分-题目 答案）
        List<File> splitFiles = splitPdf(tempFile);
        for (int i = 0; i < splitFiles.size(); i++) {

            File pdfFile = splitFiles.get(i);

            String path = StrUtil.format(
                    "zc/pdf/{}/{}_{}.pdf",
                    zcQuestion.getUserId(),
                    newZcQuestion.getMd5(),
                    i + 1
            );

            // 上传到oss中
            String pdfUrl = ossService.putFile(path, pdfFile);

            log.info("pdf渲染成功：{}", pdfUrl);

            pdfUrlList.add(pdfUrl);
        }

        // 删除临时文件
        FileUtil.del(tempFile);

        // 删除旧的pdf
        removeOldPdf(zcQuestion);

        // 更新状态
        pgZcQuestionService.update(new LambdaUpdateWrapper<PgZcQuestion>()
                .eq(PgZcQuestion::getId, zcQuestion.getId())
                .set(PgZcQuestion::getPdfStatus, ExportStatusEnum.Completed)
                .set(PgZcQuestion::getPdfUrls,
                        StrUtil.join(StrUtil.COMMA, pdfUrlList)
                )
        );

        // 发送结果通知
//        sendWxMsg(pgZcQuestion.getUserId());

        log.info("【字词题目PDF渲染队列】结束：{}，耗时：{}",
                newZcQuestion.getId(),
                timer.intervalPretty()
        );

    }

    /**
     * 删除旧的pdf
     * @param zcQuestion
     */
    private void removeOldPdf(PgZcQuestion zcQuestion) {
        if (StrUtil.isNotBlank(zcQuestion.getPdfUrls())) {
            List<String> pdfUrls = StrUtil.split(zcQuestion.getPdfUrls(), StrUtil.COMMA);
            for (String pdfUrl : pdfUrls) {
                String path = URLUtil.getPath(pdfUrl);
                ossService.delete(path);
                log.info("删除旧的pdf：{}", path);
            }
        }
    }


    /**
     * 分割 pdf
     *
     * @param fullPdfFile
     * @return
     */
    private List<File> splitPdf(File fullPdfFile) {
        List<File> splitFiles = new ArrayList<>();

        try {
            PDDocument document = Loader.loadPDF(fullPdfFile);

            int pages = document.getNumberOfPages();

            Splitter splitter = new Splitter();
            splitter.setSplitAtPage(pages / 2);

            List<PDDocument> splitDocuments = splitter.split(document);
            File part1 = File.createTempFile("part1", ".pdf");
            File part2 = File.createTempFile("part2", ".pdf");

            splitDocuments.get(0).save(part1);
            splitDocuments.get(1).save(part2);

            for (PDDocument splitDocument : splitDocuments) {
                splitDocument.close();
            }

            splitFiles.add(part1);
            splitFiles.add(part2);
            splitFiles.add(fullPdfFile);

            document.close();

        } catch (IOException e) {
            throw new RuntimeException(e);
        }

        return splitFiles;
    }

}
