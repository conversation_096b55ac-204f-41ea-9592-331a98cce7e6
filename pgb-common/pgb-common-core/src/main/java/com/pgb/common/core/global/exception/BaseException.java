package com.pgb.common.core.global.exception;

import com.pgb.common.core.global.GlobalCode;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
public class BaseException extends RuntimeException{
    @Schema(title = "状态码")
    private GlobalCode code;

    @Schema(title = "消息")
    private String msg;

    public BaseException(GlobalCode code) {
        this.code = code;
        this.msg = code.desc;
    }

    public BaseException(String msg) {
        this.msg = msg;
        this.code = GlobalCode.Error;
    }
}
