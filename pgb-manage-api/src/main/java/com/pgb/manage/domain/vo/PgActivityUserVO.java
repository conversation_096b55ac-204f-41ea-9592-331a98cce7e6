package com.pgb.manage.domain.vo;

import com.pgb.service.enums.ActivityTypeEnum;
import lombok.Data;

import java.util.Date;

@Data
public class PgActivityUserVO {
    private Long id;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 订单id
     */
    private Long orderId;

    /**
     * 兑换码id
     */
    private String code;

    private String phone;

    private String activityName;

    /**
     * 是否使用兑换码
     */
    private Boolean isUseCode;

    /**
     * 创建时间
     */
    private Date createTime;

}
