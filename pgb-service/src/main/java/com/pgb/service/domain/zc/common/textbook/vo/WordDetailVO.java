package com.pgb.service.domain.zc.common.textbook.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 单词详情VO
 *
 * <AUTHOR>
 * Created by 2025/7/25 21:00
 */
@Data
@Schema(description = "单词详情VO")
public class WordDetailVO {

    @Schema(title = "ID")
    private Long id;

    @Schema(title = "单元ID")
    private Long unitId;

    @Schema(title = "单词")
    private String word;

    @Schema(title = "词性")
    private String partOfSpeech;

    @Schema(title = "难度等级")
    private Integer difficultyLevel;

    @Schema(title = "排序值")
    private Integer sort;

    @Schema(title = "是否启用")
    private Boolean enabled;

    @Schema(title = "备注")
    private String remark;

    @Schema(title = "创建时间")
    private Date createTime;

    @Schema(title = "更新时间")
    private Date updateTime;

    @Schema(title = "翻译列表")
    private List<TranslationDetail> translations;

    @Schema(title = "发音列表")
    private List<PronunciationDetail> pronunciations;

    @Schema(title = "例句列表")
    private List<ExampleDetail> examples;

    @Data
    @Schema(description = "翻译详情")
    public static class TranslationDetail {
        @Schema(title = "ID")
        private Long id;

        @Schema(title = "翻译内容")
        private String translation;

        @Schema(title = "词性")
        private String partOfSpeech;

        @Schema(title = "语言类型")
        private String languageType;

        @Schema(title = "排序值")
        private Integer sort;

        @Schema(title = "是否启用")
        private Boolean enabled;
    }

    @Data
    @Schema(description = "发音详情")
    public static class PronunciationDetail {
        @Schema(title = "ID")
        private Long id;

        @Schema(title = "音标")
        private String phonetic;

        @Schema(title = "发音类型")
        private String pronunciationType;

        @Schema(title = "音频URL")
        private String audioUrl;

        @Schema(title = "排序值")
        private Integer sort;

        @Schema(title = "是否启用")
        private Boolean enabled;
    }

    @Data
    @Schema(description = "例句详情")
    public static class ExampleDetail {
        @Schema(title = "ID")
        private Long id;

        @Schema(title = "例句内容")
        private String example;

        @Schema(title = "例句翻译")
        private String translation;

        @Schema(title = "语言类型")
        private String languageType;

        @Schema(title = "排序值")
        private Integer sort;

        @Schema(title = "是否启用")
        private Boolean enabled;
    }
}
