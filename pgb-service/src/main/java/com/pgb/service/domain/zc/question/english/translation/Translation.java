package com.pgb.service.domain.zc.question.english.translation;

import com.pgb.service.domain.zc.common.ZcLocation;
import com.pgb.service.domain.zc.question.english.dictation.EnDictation;
import com.pgb.service.domain.zc.word.english.EngWordItem;
import com.pgb.service.enums.ZcQuestionTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * Created by 2025/6/6 17:28
 */
@Schema(title = "英汉互译")
@Data
public class Translation {

    @Schema(title = "位置信息")
    private ZcLocation location;

    @Schema(title = "单词列表")
    private List<EngWordItem> engWordList;

    @Schema(title = "显示配置")
    private Config config;

    @Data
    public static class Config {

        @Schema(title = "课文填空类型", description = "0：两栏表格，1：两栏横线")
        private Integer type;

        @Schema(title = "是否显示英语")
        private Boolean showEng;

        @Schema(title = "是否显示中文")
        private Boolean showCh;

        @Schema(title = "是否显示音标")
        private Boolean showPhonetic;

        @Schema(title = "是否显示词性")
        private Boolean showWordClass;

        @Schema(title = "标题")
        private String title;

        @Schema(title = "标题字体",description = "0：宋体，1：楷体")
        private Integer titleFont;

        @Schema(title = "标题大小")
        private Double titleSize;

        @Schema(title = "课文字体大小")
        private Double textSize;

        @Schema(title = "课文字体",description = "0：宋体，1：楷体")
        private Integer textFont;

        @Schema(title = "行高")
        private Integer lineSize;

        @Schema(title = "是否显示姓名")
        private Boolean showName;

        @Schema(title = "是否显示学号")
        private Boolean showStudentNo;

        @Schema(title = "是否显示成绩")
        private Boolean showScore;

        @Schema(title = "文字字体颜色")
        private String wordColor;

    }

}
