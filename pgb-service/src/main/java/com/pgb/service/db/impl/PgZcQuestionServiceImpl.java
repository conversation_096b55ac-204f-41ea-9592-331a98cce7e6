package com.pgb.service.db.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.digest.DigestUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.pgb.common.redis.QueueUtils;
import com.pgb.common.redis.RedisUtils;
import com.pgb.service.domain.GlobQueueConstants;
import com.pgb.service.domain.GlobalXcxConstants;
import com.pgb.service.domain.zc.question.PgZcQuestion;
import com.pgb.service.db.PgZcQuestionService;
import com.pgb.service.domain.zc.question.chinese.pinyinAndWord.PinyinAndWord;
import com.pgb.service.enums.ExportStatusEnum;
import com.pgb.service.mapper.PgZcQuestionMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【pg_zc_question】的数据库操作Service实现
 * @createDate 2025-04-21 14:52:43
 */
@Service
@Slf4j
public class PgZcQuestionServiceImpl extends ServiceImpl<PgZcQuestionMapper, PgZcQuestion>
        implements PgZcQuestionService {

    @Override
    public Integer queryToRender() {

        List<PgZcQuestion> allList = list(new LambdaQueryWrapper<PgZcQuestion>()
                .eq(PgZcQuestion::getPdfStatus, ExportStatusEnum.Queuing)
        );

        List<PgZcQuestion> list = new ArrayList<>();

        // 排除正在渲染中的
        for (PgZcQuestion zcQuestion : allList) {

            // 是否在渲染中
            if (RedisUtils.isExistsObject(GlobalXcxConstants.XCX_ZC_QUES_PDF_RENDERING + zcQuestion.getId())) {
                log.info("当前题目正在渲染中：{}，跳过", zcQuestion.getId());
                continue;
            }

            // 是否已存在队列
            if (QueueUtils.containsQueueObject(GlobQueueConstants.PGB_XCX_ZC_QUES_PDF_QUEUE.name(), zcQuestion.getId())) {
                log.info("当前题目已存在渲染队列中：{}，跳过", zcQuestion.getId());
                continue;
            }

            list.add(zcQuestion);
        }

        log.info("【待渲染题目扫描】数量:{}个", list.size());

        list.forEach(zcQuestion -> {
            // 加入渲染队列
            QueueUtils.addQueueObject(GlobQueueConstants.PGB_XCX_ZC_QUES_PDF_QUEUE.name(), zcQuestion.getId());
        });
        return list.size();
    }

    @Override
    public void generatePdfQueue(Long id) {
        // 是否已存在队列
        if (QueueUtils.containsQueueObject(GlobQueueConstants.PGB_XCX_ZC_QUES_PDF_QUEUE.name(), id)) {
            log.info("【字词】题目已存在PDF导出队列中：{}，跳过", id);
            return;
        }

        // 是否渲染中
        if (RedisUtils.isExistsObject(GlobalXcxConstants.XCX_ZC_QUES_PDF_RENDERING + id)) {
            log.info("【字词】题目正在渲染中：{}，跳过", id);
            return;
        }

        // 加入队列
        QueueUtils.addQueueObject(GlobQueueConstants.PGB_XCX_ZC_QUES_PDF_QUEUE.name(), id);

        log.info("【字词】题目加入PDF导出队列：{}", id);
    }

    @Override
    public String getZcQuestionMd5(PgZcQuestion zcQuestion) {

        // 影响结果的字段
        String content = zcQuestion.getSubject() + zcQuestion.getName() + zcQuestion.getGrade() + zcQuestion.getType() + zcQuestion.getScore() + JSONUtil.toJsonStr(zcQuestion.getContentJson()) + zcQuestion.getIsOfficial() + zcQuestion.getSort();

        return DigestUtil.md5Hex(content);
    }

    @Override
    public String getPdfUrlByType(String pdfUrl, String type) {

        // pdfUrl 逗号分隔的三个链接：题目,答案,题目和答案
        String[] urls = ObjectUtil.defaultIfNull(pdfUrl, "").split(StrUtil.COMMA);
        return switch (type) {
            case "0" -> urls[0]; // 返回题目链接
            case "1" -> urls[1]; // 返回答案链接
            case "2" -> urls[2]; // 返回题目和答案链接
            default -> urls[0]; // 默认返回题目链接
        };
    }
}




