package com.pgb.ai.domain.doubao;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 豆包TTS最终结果
 *
 * <AUTHOR>
 */
@Schema(title = "豆包TTS最终结果")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DoubaoTTSResult {

    @Schema(title = "是否成功")
    private Boolean success;

    @Schema(title = "错误消息")
    private String errorMessage;

    @Schema(title = "音频数据", description = "完整的音频字节数组")
    private byte[] audioData;

    @Schema(title = "音频大小", description = "音频文件大小（字节）")
    private Long audioSize;

    @Schema(title = "音频格式")
    private String audioFormat;

    @Schema(title = "处理时长", description = "处理耗时（毫秒）")
    private Long processingTime;

    @Schema(title = "请求ID")
    private String requestId;

    /**
     * 创建成功结果
     *
     * @param audioData 音频数据
     * @param audioFormat 音频格式
     * @param processingTime 处理时长
     * @return 成功结果
     */
    public static DoubaoTTSResult success(byte[] audioData, String audioFormat, Long processingTime) {
        return DoubaoTTSResult.builder()
                .success(true)
                .audioData(audioData)
                .audioSize(audioData != null ? (long) audioData.length : 0L)
                .audioFormat(audioFormat)
                .processingTime(processingTime)
                .build();
    }

    /**
     * 创建成功结果（带请求ID）
     *
     * @param audioData 音频数据
     * @param audioFormat 音频格式
     * @param processingTime 处理时长
     * @param requestId 请求ID
     * @return 成功结果
     */
    public static DoubaoTTSResult success(byte[] audioData, String audioFormat, Long processingTime, String requestId) {
        return DoubaoTTSResult.builder()
                .success(true)
                .audioData(audioData)
                .audioSize(audioData != null ? (long) audioData.length : 0L)
                .audioFormat(audioFormat)
                .processingTime(processingTime)
                .requestId(requestId)
                .build();
    }

    /**
     * 创建失败结果
     *
     * @param errorMessage 错误消息
     * @return 失败结果
     */
    public static DoubaoTTSResult failure(String errorMessage) {
        return DoubaoTTSResult.builder()
                .success(false)
                .errorMessage(errorMessage)
                .audioSize(0L)
                .build();
    }

    /**
     * 创建失败结果（带请求ID）
     *
     * @param errorMessage 错误消息
     * @param requestId 请求ID
     * @return 失败结果
     */
    public static DoubaoTTSResult failure(String errorMessage, String requestId) {
        return DoubaoTTSResult.builder()
                .success(false)
                .errorMessage(errorMessage)
                .audioSize(0L)
                .requestId(requestId)
                .build();
    }

    /**
     * 获取音频大小（KB）
     *
     * @return 音频大小（KB）
     */
    public Double getAudioSizeInKB() {
        if (audioSize == null || audioSize == 0) {
            return 0.0;
        }
        return audioSize / 1024.0;
    }
}
