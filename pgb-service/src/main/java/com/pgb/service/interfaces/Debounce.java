package com.pgb.service.interfaces;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;
import java.time.Duration;

/**
 * <AUTHOR>
 * Created by 2025/3/31 16:13
 */
@Target(ElementType.METHOD) // 用于方法上
@Retention(RetentionPolicy.RUNTIME) // 运行时有效
public @interface Debounce {

    // 防抖时间 默认0.5秒
    long timeout() default 500;

}
