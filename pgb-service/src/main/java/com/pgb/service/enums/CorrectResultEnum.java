package com.pgb.service.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * Created by 2025/3/11 14:51
 */
@Schema(description = "作文批改结果信息枚举类型")
@AllArgsConstructor
public enum CorrectResultEnum {

    @Schema(title = "学生学号")
    Student_No("studentNo", "学生学号"),

    @Schema(title = "学生姓名")
    Name("name", "学生姓名"),

    @Schema(title = "分数")
    Score("score", "分数"),

//    @Schema(title = "旁批")
//    Answer_Img("answerImg", "旁批"),

    @Schema(title = "老师总评")
    Overall_Comment("overallComment", "老师总评"),

    @Schema(title = "详细点评")
    Comment("comment", "详细点评"),

    @Schema(title = "作文润色")
    Polish("polish", "作文润色");


    /**
     * 枚举值对应的整数值
     */
    @EnumValue
    public final String value;

    /**
     * 文字说明
     */
    @Getter
    public final String desc;
}
