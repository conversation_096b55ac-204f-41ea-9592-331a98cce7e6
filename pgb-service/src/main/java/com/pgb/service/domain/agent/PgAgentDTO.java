package com.pgb.service.domain.agent;

import java.io.Serializable;

import java.util.Date;
import java.util.List;

import cn.hutool.json.JSONArray;
import com.pgb.service.enums.FeeTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.ToString;
import lombok.Builder;
import org.springframework.validation.annotation.Validated;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import com.pgb.common.core.validate.UpdateGroup;

/**
 * @TableName pg_agent
 */
@Schema(description = " DTO")
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode
@ToString
@Validated
@Builder
public class PgAgentDTO implements Serializable {

    @Schema(title = "智能体")
    @NotNull(message = "[智能体]不能为空", groups = {UpdateGroup.class})
    private Long id;

    @Schema(title = "名称")
    @Size(max = 255)
    private String name;

    @Schema(title = "一句话描述")
    @Size(max = 255)
    private String description;

    @Schema(title = "图标图片")
    @Size(max = 255)
    private String avatar;

    @Schema(title = "智能体表单")
    private JSONArray form;

    @Schema(title = "创建时间")
    private Date createTime;

    @Schema(title = "AI 提示词，敏感数据")
    private String prompt;

    @Schema(title = "收费类型，枚举，0：免费，1：会员无限次，2：限次")
    private FeeTypeEnum feeType;

    @Schema(title = "是否启用")
    private Boolean isEnable;

    @Schema(title = "html链接")
    private String htmlUrl;

    @Schema(title = "智能体类型 0：普通对话，1：html")
    private Integer type;

}
