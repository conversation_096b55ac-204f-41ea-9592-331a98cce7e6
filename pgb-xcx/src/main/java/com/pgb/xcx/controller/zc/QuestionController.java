package com.pgb.xcx.controller.zc;

import cn.dev33.satoken.annotation.SaCheckLogin;
import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.houbb.pinyin.util.PinyinHelper;
import com.pgb.common.core.global.BaseResult;
import com.pgb.common.core.global.GlobalCode;
import com.pgb.common.core.utils.EnvUtils;
import com.pgb.common.oss.service.OssService;
import com.pgb.common.redis.QueueUtils;
import com.pgb.service.db.PgZcQuestionService;
import com.pgb.service.domain.GlobQueueConstants;
import com.pgb.service.domain.query.ZcQuestionQuery;
import com.pgb.service.domain.zc.question.PgZcQuestion;
import com.pgb.service.domain.zc.question.PgZcQuestionDTO;
import com.pgb.service.domain.zc.question.PgZcQuestionVO;
import com.pgb.service.domain.zc.question.chinese.textBlank.TextBlank;
import com.pgb.service.domain.zc.word.TextWordInfo;
import com.pgb.service.domain.zc.word.WordItem;
import com.pgb.service.enums.ExportStatusEnum;
import com.pgb.service.enums.ZcQuestionTypeEnum;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * Created by 2025/4/21 15:07
 */
@Tag(name = "用户端/字词/题目")
@RestController("UserZcQuestionController")
@Transactional(rollbackFor = Exception.class)
@RequestMapping("/user/zc/question")
@RequiredArgsConstructor
@Slf4j
public class QuestionController {

    private final PgZcQuestionService pgZcQuestionService;

    private final OssService ossService;

    @Operation(summary = "获取题目列表")
    @PostMapping("page")
    @SaCheckLogin
    public BaseResult<IPage<PgZcQuestionVO>> page(@RequestBody ZcQuestionQuery query) {

        IPage<PgZcQuestionVO> page = pgZcQuestionService.page(query.toMpPageSortByCreateTime(), new LambdaQueryWrapper<PgZcQuestion>()
                // 当前用户
                .eq(PgZcQuestion::getUserId, StpUtil.getLoginIdAsLong())
                // 科目
                .eq(ObjectUtil.isNotNull(query.getSubject()), PgZcQuestion::getSubject, query.getSubject())
                // 题目类型
                .in(CollUtil.isNotEmpty(query.getTypes()), PgZcQuestion::getType, query.getTypes())
                // 年级
                .eq(ObjectUtil.isNotNull(query.getGrade()), PgZcQuestion::getGrade, query.getGrade())
                // 题目名称
                .eq(StrUtil.isNotBlank(query.getName()), PgZcQuestion::getName, query.getName())
        ).convert(pgZcQuestion -> BeanUtil.copyProperties(pgZcQuestion, PgZcQuestionVO.class));

        return BaseResult.success(page);
    }

    @Operation(summary = "删除题目")
    @DeleteMapping("delete/{id}")
    @SaCheckLogin
    public BaseResult<Boolean> delete(@PathVariable Long id) {

        PgZcQuestion zcQuestion = pgZcQuestionService.getById(id);

        if (ObjectUtil.isNull(zcQuestion)) {
            return BaseResult.code(GlobalCode.Item_Null);
        }

        // 只有相同用户才能删除
        if (StpUtil.getLoginIdAsLong() != zcQuestion.getUserId()) {
            return BaseResult.code(GlobalCode.Permission_Not);
        }

        // 教材题目不允许删除
        if (zcQuestion.getIsOfficial()) {
            return BaseResult.code(GlobalCode.Permission_Not);
        }

        pgZcQuestionService.removeById(id);
        return BaseResult.success(true);
    }


    @Operation(summary = "查看题目pdf生成状态")
    @GetMapping("pdfStatus/{id}")
    public BaseResult<PdfResultDTO> pdfStatus(@PathVariable Long id) {

        PdfResultDTO result = new PdfResultDTO();

        PgZcQuestion zcQuestion = pgZcQuestionService.getById(id);

        if (ObjectUtil.isNull(zcQuestion)) {
            return BaseResult.code(GlobalCode.Item_Null);
        }
        result.setStatus(zcQuestion.getPdfStatus());
        return BaseResult.success(result);

    }


    @Data
    public static class PdfResultDTO {

        @Schema(title = "下载状态")
        private ExportStatusEnum status;

        @Schema(title = "PDF链接")
        private String pdfUrl;

    }

    @Operation(summary = "开始生成PDF", description = "type: 0：下载题目，1：下载答案，2：下载题目和答案")
    @GetMapping("pdf/{id}/{type}")
    @SaCheckLogin
    public BaseResult<PdfResultDTO> downloadPdf(@PathVariable Long id, @PathVariable String type) {

        PdfResultDTO result = new PdfResultDTO();

        PgZcQuestion zcQuestion = pgZcQuestionService.getById(id);

        if (ObjectUtil.isNull(zcQuestion)) {
            return BaseResult.code(GlobalCode.Item_Null);
        }

        if (ObjectUtil.isNull(zcQuestion.getPdfStatus())) {
            zcQuestion.setPdfStatus(ExportStatusEnum.Init);
        }

        // 生成中
        if (zcQuestion.getPdfStatus().equals(ExportStatusEnum.Queuing)) {
            result.setStatus(zcQuestion.getPdfStatus());
            return BaseResult.success(result);
        }

        // 判断是否需要重新生成 PDF
        String currentMd5 = pgZcQuestionService.getZcQuestionMd5(zcQuestion);

        // 如果没有 PDF 或者 MD5 不一致，则重新生成
        if (ObjectUtil.isNull(zcQuestion.getPdfUrls()) ||
                !currentMd5.equals(zcQuestion.getMd5()) ||
                zcQuestion.getPdfStatus().equals(ExportStatusEnum.Init)) {

            zcQuestion.setPdfStatus(ExportStatusEnum.Queuing);
            zcQuestion.setMd5(currentMd5);
            // 保存
            pgZcQuestionService.updateById(zcQuestion);

            // 删除之前的 pdf
            if (StrUtil.isNotBlank(zcQuestion.getPdfUrls())) {
                // pdfUrl 逗号分隔的三个链接：题目,答案,题目和答案
                String[] urls = ObjectUtil.defaultIfNull(zcQuestion.getPdfUrls(), "").split(StrUtil.COMMA);
                for (String url : urls) {
                    ossService.setTagList(url, "Deleted");
                }
            }
            // 执行生成 PDF
            pgZcQuestionService.generatePdfQueue(zcQuestion.getId());

            result.setStatus(zcQuestion.getPdfStatus());

            return BaseResult.success(result);
        }

        // 返回 PDF 链接
        String url = pgZcQuestionService.getPdfUrlByType(zcQuestion.getPdfUrls(), type);

        result.setStatus(zcQuestion.getPdfStatus());
        result.setPdfUrl(url);

        return BaseResult.success(result);
    }


    public record GetPinyinData(String wordText) {
    }

    @Operation(summary = "获取字词拼音")
    @PostMapping("pinyin")
    public BaseResult<List<TextWordInfo>> getPinyin(@RequestBody GetPinyinData data) {

        List<WordItem> items = new ArrayList<>();

        // 去除首位的空格 并按空格分隔
        String[] wordList = StrUtil.trim(data.wordText()).split("\\s+");

        for (String word : wordList) {

            WordItem item = new WordItem();

            String pinyin = PinyinHelper.toPinyin(word);
            item.setWord(word);
            item.setPinyin(pinyin);
            items.add(item);
        }

        List<TextWordInfo> textWordInfos = new ArrayList<>();

        TextWordInfo word = new TextWordInfo();
        word.setWordList(items);

        textWordInfos.add(word);

        return BaseResult.success(textWordInfos);
    }

    @Operation(summary = "获取单个字词拼音")
    @PostMapping("wordPy")
    public BaseResult<List<WordItem>> getWordPy(@RequestBody GetPinyinData data) {

        List<WordItem> items = new ArrayList<>();

        // 去除首位的空格 并按空格分隔
        String[] wordList = StrUtil.trim(data.wordText()).split("\\s+");

        for (String word : wordList) {

            WordItem item = new WordItem();

            String pinyin = PinyinHelper.toPinyin(word);

            item.setWord(word);
            item.setPinyin(pinyin);

            items.add(item);
        }

        return BaseResult.success(items);
    }


}
