package com.pgb.service.domain.zc.answer;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;

import com.pgb.common.mybatis.handler.JsonbTypeHandler;
import com.pgb.service.enums.CorrectStatusEnum;
import com.pgb.service.enums.ZcQuestionTypeEnum;
import lombok.Data;

/**
 *
 * @TableName pg_zc_answer
 */
@TableName(value ="pg_zc_answer")
@Data
public class PgZcAnswer implements Serializable {
    /**
     * 默写批改记录
     */
    @TableId
    private Long id;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 用户本题作答
     */
    @TableField(typeHandler = JsonbTypeHandler.class)
    private Object answer;

    /**
     * 批改状态
     */
    private CorrectStatusEnum status;

    /**
     * 消耗token量
     */
    private Long aiTokens;

    /**
     * 批改所需时长，单位：秒
     */
    private Integer correctDuration;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * AI批改完成时间
     */
    private Date correctTime;

    /**
     * 批改结果
     */
    @TableField(typeHandler = JsonbTypeHandler.class)
    private Object correctResult;

    /**
     * 所属题目id
     */
    private Long zcQuestionId;

    /**
     * 备注名称
     */
    private String name;

    /**
     * 学生id
     */
    private Long studentId;

    /**
     * 是否已删除
     */
    private Boolean deleted;

    /**
     * 作业id
     */
    private Long zcHomeworkId;

    /**
     * 所属批次id
     */
    private Long batchId;

    /**
     * 字词题目类型，0：通用，1：情景式填空
     */
    private ZcQuestionTypeEnum quesType;

}
