package com.pgb.student.service.impl;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.pgb.common.core.global.BaseResult;
import com.pgb.common.core.global.GlobalCode;
import com.pgb.common.core.global.exception.BaseException;
import com.pgb.service.db.PgClassesService;
import com.pgb.service.domain.answer.PgAnswer;
import com.pgb.service.domain.classes.PgClasses;
import com.pgb.service.domain.classes.PgClassesVO;
import com.pgb.service.domain.student.PgStudent;
import com.pgb.service.domain.student.StudentInfoDTO;
import com.pgb.service.mapper.PgAnswerMapper;
import com.pgb.service.mapper.PgClassesMapper;
import com.pgb.service.mapper.PgStudentMapper;
import com.pgb.student.domain.PgUserStudent;
import com.pgb.student.service.PgUserStudentService;
import com.pgb.student.service.PgbCustomService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Service
@RequiredArgsConstructor
public class PgbCustomServiceImpl implements PgbCustomService {

    private final PgStudentMapper pgStudentMapper;

    private final PgClassesMapper pgClassesMapper;

    private final PgAnswerMapper pgAnswerMapper;

    private final PgUserStudentService pgUserStudentService;

    @Override
    @DS("pgb")
    public List<PgStudent> getStudentsByClassId(Long classId, List<Long> userStudentIds) {

        return pgStudentMapper.selectList(new LambdaQueryWrapper<PgStudent>()
                .eq(PgStudent::getClassId, classId)
                .in(PgStudent::getStudentUserId, userStudentIds)
        );

    }


    @Override
    @DS("pgb")
    public List<PgStudent> getStudentListByStudentUserId(Long studentUserId) {

        if (ObjectUtil.isNull(studentUserId) || 0 == studentUserId) {
            return new ArrayList<>();
        }

        return pgStudentMapper.selectList(new LambdaQueryWrapper<PgStudent>()
                .eq(PgStudent::getStudentUserId, studentUserId)
        );

    }

    @Override
    @DS("pgb")
    public List<PgStudent> students() {
        return pgStudentMapper.selectList(new LambdaQueryWrapper<PgStudent>()
                .isNotNull(PgStudent::getStudentUserId));

    }

    @Override
    @DS("pgb")
    public List<PgStudent> students(Long userId) {
        return pgStudentMapper.selectList(new LambdaQueryWrapper<PgStudent>()
                .eq(PgStudent::getStudentUserId, userId));

    }

    @Override
    @DS("pgb")
    public void update(PgStudent student) {
        pgStudentMapper.updateById(student);

    }

    @Override
    @DS("pgb")
    public PgUserStudent joinClass(Long userId, Long classId, StudentInfoDTO dto) {

        // 根据名字查询学生
        PgStudent student = pgStudentMapper.selectOne(new LambdaQueryWrapper<PgStudent>()
                .eq(PgStudent::getClassId, classId)
                .eq(PgStudent::getName, dto.getName())
                .last("LIMIT 1")
        );

        // 是否已绑定
        if (ObjectUtil.isNotNull(student) && ObjectUtil.isNotNull(student.getStudentUserId())) {
            throw new BaseException("该学生已被绑定，不能重复绑定");
        }

        // 查询当前是否已存在创建的学生
        PgUserStudent userStudent = pgUserStudentService.getOne(new LambdaQueryWrapper<PgUserStudent>()
                .eq(PgUserStudent::getUserId, userId)
                .eq(PgUserStudent::getName, dto.getName())
                .last("LIMIT 1")
        );

        // 如果为空，则创建
        if (ObjectUtil.isNull(userStudent)) {
            // 不能超过四个
            List<PgUserStudent> studentList = pgUserStudentService.getStudentList(userId);
            if (studentList.size() >= 4) {
                throw new BaseException("最多只能绑定四位学生");
            }

            // 新增关联
            userStudent = new PgUserStudent();
            userStudent.setUserId(userId);
            userStudent.setName(dto.getName());
            userStudent.setCreateTime(new Date());
            userStudent.setAvatar("https://cdn.pigaibang.com/common/parent/avatar.png");
            userStudent.setIdentity(dto.getIdentityType());
            pgUserStudentService.save(userStudent);
        }

        // 如果学生没有创建过，先创建一个
        if (ObjectUtil.isNull(student)) {
            // 新增student信息
            student = new PgStudent();
            student.setClassId(classId);
            student.setName(dto.getName());
            student.setCreateTime(new Date());
            pgStudentMapper.insert(student);
        }

        student.setStudentUserId(userStudent.getId());
        pgStudentMapper.updateById(student);

        return userStudent;
    }

    @Override
    @DS("pgb")
    public PgClasses getClassById(Long classId) {

        return pgClassesMapper.selectById(classId);
    }

    @Override
    @DS("pgb")
    public PgClasses getClassByPassword(String password) {

        return pgClassesMapper.selectOne(new LambdaQueryWrapper<PgClasses>()
                .eq(PgClasses::getPassword, password));
    }


    @Override
    @DS("pgb")
    public PgStudent getStudentByClassId(Long classId, Long userStudentId) {

        return pgStudentMapper.selectOne(new LambdaQueryWrapper<PgStudent>()
                .eq(PgStudent::getClassId, classId)
                .eq(PgStudent::getStudentUserId, userStudentId)
                .last("limit 1"));
    }


    @Override
    @DS("pgb")
    public List<PgClassesVO> pgClasses(LambdaQueryWrapper<PgClasses> queryWrapper) {

        return pgClassesMapper.selectList(queryWrapper)
                .stream()
                .map(pgClasses -> BeanUtil.copyProperties(pgClasses, PgClassesVO.class))
                .toList();
    }

    @Override
    @DS("pgb")
    public void quitClass(Long classId, Long userStudentId) {

        PgStudent student = getStudentByClassId(classId, userStudentId);

        // 将student的studentUserId设置为空
        if (ObjectUtil.isNotNull(student)) {
            pgStudentMapper.update(Wrappers.<PgStudent>lambdaUpdate()
                    .eq(PgStudent::getId, student.getId())
                    .set(PgStudent::getStudentUserId, null));
        }

    }

    @Override
    @DS("pgb")
    public void unbind(Long studentId) {

        List<PgStudent> students = getStudentListByStudentUserId(studentId);

        if (!students.isEmpty()) {
            for (PgStudent student : students) {
                pgStudentMapper.update(Wrappers.<PgStudent>lambdaUpdate()
                        .eq(PgStudent::getId, student.getId())
                        .set(PgStudent::getStudentUserId, null));
            }
        }

    }

}
