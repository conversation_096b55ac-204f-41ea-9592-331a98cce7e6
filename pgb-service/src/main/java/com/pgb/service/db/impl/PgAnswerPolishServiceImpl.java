package com.pgb.service.db.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.pgb.service.domain.answer.PgAnswer;
import com.pgb.service.domain.answer.polish.PgAnswerPolish;
import com.pgb.service.db.PgAnswerPolishService;
import com.pgb.service.mapper.PgAnswerPolishMapper;
import org.springframework.stereotype.Service;

import java.util.Date;

/**
* <AUTHOR>
* @description 针对表【pg_answer_polish(作文润色记录表)】的数据库操作Service实现
* @createDate 2025-04-15 18:32:47
*/
@Service
public class PgAnswerPolishServiceImpl extends ServiceImpl<PgAnswerPolishMapper, PgAnswerPolish>
    implements PgAnswerPolishService{

}




