package com.pgb.service.domain.question.zwEssay;

import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.fasterxml.jackson.annotation.JsonGetter;
import com.pgb.service.domain.common.fabric.FabricJson;
import com.pgb.service.domain.common.image.FilePaperImg;
import com.pgb.service.domain.question.PgQuestion;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.util.List;


@Data
@Schema(title = "作文题")
public class ZwEssayQuestion {

    @Schema(title = "作文类型，中、英、其他")
    private ZwEssayTypeEnum zwType;

    @Schema(title = "用户作答图片 URL")
    private List<FilePaperImg> userImgAnswerList;

    @Schema(title = "备注名称")
    private String name;

    @Schema(title = "全文总评")
    private String overallComment;

    @Schema(title = "详细点评")
    private String comment;

    @Schema(title = "润色后文章")
    private String polish;

    @Schema(title = "图片批改结果列表")
    private List<FabricJson> markJsonList;

    @Schema(title = "AI 脑力值")
    private Long aiTokens;

    @Schema(title = "用户得分", description = ">=90% 优 ；  >=80% 良  ；  >=60% 中等  ； <60% 不及格")
    private Integer userScore;

    @Schema(title = "AI原生批改分数")
    private Double aiScore;

    @Schema(title = "作文参考总分", description = "默认为30")
    private Double score;

    @Schema(title = "所属题目id")
    private Long questionId;

    @Schema(title = "所属批改要求")
    private ZwRequire require;

    @Schema(title = "分数样式", description = "0：评级，1：分数")
    private Integer scoreStyle;

    @Schema(title = "作文全文、文字版")
    private String fullText;

    @Schema(title = "全文润色和作文的差异")
    private List<ZwTextDiff> polishDiffList;

    @Schema(title = "写作检测")
    private List<ZwDetect> detectList;

    @Schema(title = "关联学生 id")
    private Long studentId;

    @Schema(title = "关联作业 id")
    private Long homeworkId;

    @Schema(title = "得分细则")
    private List<ZwScoreItem> scoreItemList;

    @Schema(title = "润色幅度")
    private Integer polishRange;

    @Schema(title = "卷面整洁程度")
    private Surface surface;

    @Data
    @Schema(title = "卷面整洁程度")
    public static class Surface {
        @Schema(title = "卷面整洁一句话描述")
        private String desc;

        @Schema(title = "卷面程度，", description = "1-4级，级数越高越好")
        private Integer type;
    }


    @Schema(title = "评级等级", description = "当分数样式为评级时 返回前端， 1：优，2：良，3：中等，4：不及格")
    @JsonGetter
    public Integer getScoreLevel() {
        // 评级 按分数给
        if (ObjectUtil.isAllNotEmpty(this.score, this.userScore)) {
            // >=85% 优 ；  >=70% 良  ；  >=55% 中等  ； <55% 不及格
            if (this.userScore >= NumberUtil.mul(this.score.doubleValue(), 0.85)) {
                return 1;
            } else if (this.userScore >= NumberUtil.mul(this.score.doubleValue(), 0.70)) {
                return 2;
            } else if (this.userScore >= NumberUtil.mul(this.score.doubleValue(), 0.55)) {
                return 3;
            } else {
                return 4;
            }
        }

        return 1;
    }

    @Schema(title = "设置评级 -- 更改分数")
    public void handleSetScoreLevel(Integer level) {

        if (ObjectUtil.isNull(level)) {
            return;
        }
        // >=85% 优 ；  >=70% 良  ；  >=55% 中等  ； <55% 不及格

        if (ObjectUtil.isNull(this.score)) {
            this.score = 30.0;
        }

        switch (level) {
            // >=85% 优
            case 1:
                this.userScore = (int) Math.ceil(NumberUtil.mul(this.score.doubleValue(), 0.85));
                break;
            // 良 -- 70%
            case 2:
                this.userScore = (int) Math.ceil(NumberUtil.mul(this.score.doubleValue(), 0.7));
                break;
            // 中等 -- 55%
            case 3:
                this.userScore = (int) Math.ceil(NumberUtil.mul(this.score.doubleValue(), 0.55));
                break;
            // 不及格 -- 55% 以下
            case 4:
                this.userScore = (int) Math.ceil(NumberUtil.mul(this.score.doubleValue(), 0.45));
                break;
            default:
                break;
        }
    }

    @Schema(title = "原文字数统计")
    @JsonGetter
    public Integer getFullTextWordNum() {
        // 空字符串
        if (StrUtil.isBlank(this.fullText)) {
            return 0;
        }

        // 去掉换行
        String temp = this.fullText.replaceAll("\\n", "");

        // 中文单词
        if (zwType.equals(ZwEssayTypeEnum.English)) {
            // 英文单词数量统计
            return StrUtil.split(temp, " ").stream().filter(word -> !StrUtil.isBlank(word)).toList().size();
        }

        // 默认中文
        // 统计字数
        return StrUtil.length(temp);
    }

    @Schema(title = "原文润色字数统计")
    @JsonGetter
    public Integer getPolishWordNum() {
        // 空字符串
        if (StrUtil.isBlank(this.polish)) {
            return 0;
        }

        // 去掉换行
        String temp = this.polish.replaceAll("\\n", "");

        // 中文单词
        if (zwType.equals(ZwEssayTypeEnum.English)) {
            // 英文单词数量统计
            return StrUtil.split(temp, " ").stream().filter(word -> !StrUtil.isBlank(word)).toList().size();
        }

        // 默认中文
        // 统计字数
        return StrUtil.length(temp);
    }
}
