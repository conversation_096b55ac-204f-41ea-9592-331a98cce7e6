package com.pgb.service.domain.answer.polish;

import java.io.Serializable;

import java.util.Date;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.ToString;
import lombok.Builder;

/**
* 作文润色记录表
* @TableName pg_answer_polish
*/
@Schema(description = "作文润色记录表 VO")
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode
@ToString
@Builder
public class PgAnswerPolishVO implements Serializable {

    @Schema(title = "润色记录id", type = "string")
    private Long id;

    @Schema(title = "用户id")
    private Long userId;

    @Schema(title = "关联作答id")
    private Long answerId;

    @Schema(title = "润色内容")
    private String content;

    @Schema(title = "润色要求")
    private Object require;

    @Schema(title = "版本排序")
    private Integer sort;

    @Schema(title = "创建时间")
    private Date createTime;

}
