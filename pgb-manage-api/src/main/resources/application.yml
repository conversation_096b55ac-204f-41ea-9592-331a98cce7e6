# 【服务器】基本配置
server:
  port: 8085
  servlet:
    context-path: /api
    session:
      timeout: 120s

# 【Spring】 配置
spring:
  profiles:
    active: @profileActive@
  jackson:
    time-zone: GMT+8
    date-format: yyyy-MM-dd HH:mm:ss
  servlet:
    multipart:
      max-file-size: 10MB
      max-request-size: 30MB

# 【登录权限】 Sa-Token 配置
sa-token:
  # token名称 (同时也是cookie名称)
  token-name: token
  # token有效期，单位s 默认30天, -1代表永不过期
  timeout: 2592000
  # 是否允许同一账号并发登录 (为true时允许一起登录, 为false时新登录挤掉旧登录)
  is-concurrent: true
  # 在多人登录同一账号时，是否共用一个token (为true时所有登录共用一个token, 为false时每次登录新建一个token)
  is-share: false
  # token风格
  token-style: uuid
  # 是否输出操作日志
  is-log: false
  is-read-cookie: false

# 【微信】全局配置
wx:
  ma:
    appid: wx7605d5f8177ffecb #微信小程序的appid
    secret: b84dcd721d2c3613660f7305913cf3da #微信小程序的Secret
    token: 1
    aesKey: 2
    msgDataFormat: JSON
  # 微信支付服务商
  pay:
    app-id: wx7605d5f8177ffecb
    mch-id: 1623318765
    api-v3-key: Snros123456789012345678901234567
    key-path: classpath:pay/apiclient_cert.p12
    private-key-path: classpath:pay/apiclient_key.pem
    private-cert-path: classpath:pay/apiclient_cert.pem

# 【对象存储】OSS
oss:
  tencent:
    secret-id: AKIDrwc3Dm95pKH1Zqtm9G7RZS3VheOGr8Bz
    secret-key: gV4NQDk23C6k8KHZdSQkpeEZ6nFTSXNy
    region: ap-beijing
    bucket-name:
      - pgb-1300104514
    cdn-domain: https://cdn.pigaibang.com
  ali:
    secret-id: LTAI5tEVz9RiUWrnX9We7H7a
    secret-key: ******************************
    endpoint: oss-cn-beijing.aliyuncs.com
    region: oss-cn-beijing
    cdn-domain: https://cdn.pigaibang.com
    bucket-name: pigaibang

# 【百度ocr】 密钥
baidu:
  ocr:
    api-id: 45511560
    api-key: Z89kUSyIiYhrkzq9KqDUPvGW
    api-secret: 4ExDFFGYH6IGVyGzVU72FF3mP1RuLZWx

# 【短信配置】整合 sms4j
sms:
  is-print: false
  config-type: yaml
  blends:
    tencent:
      supplier: tencent
      access-key-id: AKIDgCTVHn20h8JDpBG4bqpXy94i5AyVllUw
      sdk-app-id: 1400835261
      access-key-secret: D9oCJ9j24qKXT8WcRzGG0UhdIgndCUVF
      signature: 批改邦小程序

# 豆包TTS配置
doubao:
  tts:
    app-id: 4381946153
    access-key: kAx-boB_DVgoLXzQHB9GKOYUD9cwd808
    resource-id: volc.service_type.10029
    default-speaker: zh_male_jieshuonansheng_mars_bigtts
    default-format: mp3
    default-sample-rate: 24000
    timeout: 30000
