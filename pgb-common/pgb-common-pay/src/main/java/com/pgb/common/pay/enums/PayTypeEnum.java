package com.pgb.common.pay.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;

@Schema(description = "支付方式 枚举")
@AllArgsConstructor
public enum PayTypeEnum {

    @Schema(title = "WX_H5", description = "微信H5")
    WX_H5(0, "微信H5"),

    @Schema(title = "WX_APP", description = "微信APP")
    WX_APP(1, "微信APP"),

    @Schema(title = "WX_NATIVE", description = "微信扫码")
    WX_NATIVE(2, "微信扫码"),

    @Schema(title = "WX_JS", description = "微信环境下JS支付")
    WX_JS(3, "微信浏览器"),

    @Schema(title = "WX_MINI", description = "微信小程序支付")
    WX_MINI(4, "微信小程序"),

    @Schema(title = "支付宝H5")
    ALI_H5(5, "支付宝H5"),

    @Schema(title = "支付宝APP")
    ALI_APP(6, "支付宝APP"),

    @Schema(title = "支付宝二维码 PC扫码")
    ALI_QR(7, "支付宝扫码"),

    @Schema(title = "苹果支付")
    APPLE_PAY(10, "苹果支付");

    /**
     * 枚举值对应的整数值
     */
    @EnumValue
    public final int value;

    /**
     * 文字说明
     */
    public final String desc;

    /**
     * 判断是否为微信支付
     * @return true-微信支付，false-非微信支付
     */
    public boolean isWxPay() {
        return this == WX_H5 || this == WX_APP || this == WX_NATIVE || this == WX_JS || this == WX_MINI;
    }
}
