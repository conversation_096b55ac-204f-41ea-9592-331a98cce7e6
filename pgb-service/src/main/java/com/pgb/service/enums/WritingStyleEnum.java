package com.pgb.service.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;

/**
 * <AUTHOR>
 * Created by 2024/7/23 18:41
 */
@Schema(description = "写作文体 枚举类型")
@AllArgsConstructor
public enum WritingStyleEnum {

    Narration_Person(0, "写人为主的记叙文"),

    Narrative_Thing(1, "叙事为主的记叙文"),

    Narrative_Scene(2, "写景为主的记叙文"),

    Narrative_Object(3, "状物为主的记叙文"),

    Lyric_essay(4, "抒情文"),

    Argumentation(5, "议论文"),

    Expository(6, "说明文"),

    Prose(7, "散文"),

    Speech(8, "演讲稿"),

    Practical(9,"应用文"),

    Poetry(10, "诗歌"),

    Other(11, "文体不限");


    /**
     * 枚举值对应的整数值
     */
    @EnumValue
    public final int value;

    /**
     * 文字说明
     */
    public final String desc;

}
