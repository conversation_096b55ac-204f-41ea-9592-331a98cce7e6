package com.pgb.service.db.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.pgb.service.db.PgTagService;
import com.pgb.service.db.PgTagUserService;
import com.pgb.service.domain.tag.PgTag;
import com.pgb.service.domain.tag.PgTagUser;
import com.pgb.service.mapper.PgTagMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【pg_tag】的数据库操作Service实现
 * @createDate 2024-12-23 16:22:03
 */
@Service
@RequiredArgsConstructor
public class PgTagServiceImpl extends ServiceImpl<PgTagMapper, PgTag>
        implements PgTagService {

    private final PgTagUserService pgTagUserService;

    @Override
    public List<PgTag> listByUserId(Long id) {

        List<Long> ids = pgTagUserService.list(new LambdaQueryWrapper<PgTagUser>()
                .eq(PgTagUser::getUserId, id)
        ).stream().map(PgTagUser::getTagId).toList();

        if (CollUtil.isEmpty(ids)) {
            return new ArrayList<>();
        }

        return listByIds(ids);
    }

    @Override
    public Boolean tagByUserId(Long userId, List<Long> tagIds) {

        for (Long tagId : tagIds) {
            // 如果贴过了就不贴了
            if (!pgTagUserService.exists(new LambdaQueryWrapper<PgTagUser>()
                    .eq(PgTagUser::getUserId, userId)
                    .eq(PgTagUser::getTagId, tagId)
            )) {
                PgTagUser tagUser = new PgTagUser();
                tagUser.setUserId(userId);
                tagUser.setTagId(tagId);
                tagUser.setCreateTime(new Date());
                pgTagUserService.save(tagUser);
            }
        }
        return true;
    }
}




