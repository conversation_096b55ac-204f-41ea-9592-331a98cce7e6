package com.pgb.service.domain.popup;

import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;

import com.pgb.service.enums.PopupTypeEnum;
import com.pgb.service.enums.ShowOnTypeEnum;
import lombok.Data;

/**
 * 弹窗表
 * @TableName pg_popup
 */
@TableName(value ="pg_popup")
@Data
public class PgPopup implements Serializable {
    /**
     * 弹窗id
     */
    private Long id;

    /**
     * 开始时间
     */
    private Date startTime;

    /**
     * 结束时间
     */
    private Date endTime;

    /**
     * 显示时机(ru：首次注册、每次进入，指定用户标签)
     */
    private ShowOnTypeEnum showOn;

    /**
     * 用户标签列表，根据英文逗号分隔
     */
    private String tagIds;

    /**
     * 排除特定的标签
     */
    private String notTagIds;

    /**
     * 权重排序
     */
    private Integer sort;

    /**
     * 是否有效
     */
    private Boolean isValid;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 弹窗图片url
     */
    private String imgUrl;

    /**
     * 备注
     */
    private String remark;

    /**
     * 弹窗类型，0：弹窗，1：贴片广告
     */
    private PopupTypeEnum type;

    /**
     * 活动id
     */
    private Long activityId;

    /**
     * 页面url
     */
    private String targetUrl;

}
