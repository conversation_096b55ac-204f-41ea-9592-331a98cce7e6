package com.pgb.service.domain.common.fabric;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.util.ArrayList;
import java.util.List;

@Schema(title = "图片类型")
@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MarkObjectImage extends MarkObject {
    @Schema(title = "类型，Image、txt、line")
    private final MarkObjectEnum type = MarkObjectEnum.Image;

    @Schema(title = "链接")
    private String src;

    @Schema(title = "left 位置")
    private Double left;

    @Schema(title = "top 位置")
    private Double top;

    @Schema(title = "scaleX")
    private Double scaleX;

    @Schema(title = "scaleY")
    private Double scaleY;

    @Schema(title = "跨域")
    private final String crossOrigin = "anonymous";

    @Schema(title = "角度")
    private Integer angle = 0;

    @Schema(title = "滤镜")
    private final List<String> filters = new ArrayList<>();
}
