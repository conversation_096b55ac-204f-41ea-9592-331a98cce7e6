package com.pgb.service.domain.question.mx;

import com.pgb.service.domain.common.fabric.FabricJson;
import com.pgb.service.domain.common.image.FilePaperImg;
import com.pgb.service.domain.question.zwEssay.ZwEssayTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
@Schema(title = "默写题")
public class MxCommonQuestion {

    @Schema(title = "AI 脑力值")
    private Long aiTokens;

    @Schema(title = "用户得分", description = ">=90% 优 ；  >=80% 良  ；  >=60% 中等  ； <60% 不及格")
    private Integer userScore;

    @Schema(title = "参考总分", description = "默认为30")
    private Double score;

    // --------

    @Schema(title = "作文类型，中、英、其他")
    private ZwEssayTypeEnum zwType;

    @Schema(title = "默写答案或者参考答案", description = "默写使用")
    private String quesAnswerStr;

    @Schema(title = "默写答案图片")
    private List<FilePaperImg> quesAnswerList;

    @Schema(title = "默写卡空白图片")
    private List<FilePaperImg> quesEmptyList;

    @Schema(title = "学员页面")
    private List<FilePaperImg> userImgAnswerList;

    @Schema(title = "图片批改结果列表")
    private List<FabricJson> markJsonList;

    @Schema(title = "默写作答情况", description = "用作统计使用")
    private List<MxQuestionItem> mxItemList;

    @Schema(title = "全文内容")
    private String fullText;
}
