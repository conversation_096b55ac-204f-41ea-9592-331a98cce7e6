package com.pgb.xcx.controller.distribution;

import com.github.binarywang.wxpay.bean.notify.OriginNotifyResponse;
import com.github.binarywang.wxpay.bean.notify.WxPayBaseNotifyV3Result;
import com.github.binarywang.wxpay.bean.notify.WxPayNotifyV3Result;
import com.github.binarywang.wxpay.service.WxPayService;
import com.google.gson.annotations.SerializedName;
import com.pgb.service.db.PgDistributionRecordService;
import com.pgb.service.enums.TransferStatusEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * Created by 2024/7/5 18:03
 */
@Tag(name = "用户端/分销/转账")
@RestController("UserTransferController")
@Transactional(rollbackFor = Exception.class)
@RequestMapping("/user/transfer/info")
@RequiredArgsConstructor
@Slf4j
public class TransferController {

    private final WxPayService wxPayService;

    private final PgDistributionRecordService pgDistributionRecordService;

    @Schema(description = "转账返回结果")
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @EqualsAndHashCode(callSuper = false)
    public static class ResultData extends WxPayNotifyV3Result.DecryptNotifyResult {

        @Schema(title = "商家批次单号")
        @SerializedName("out_batch_no")
        private String outBatchNo;

        @Schema(title = "微信批次单号")
        @SerializedName("batch_id")
        private String batchId;

        @Schema(title = "状态")
        @SerializedName("batch_status")
        private TransferStatusEnum batchStatus;

        @Schema(title = "批次关闭原因")
        @SerializedName("close_reason")
        private String closeReason;

        @Schema(title = "转账成功金额")
        @SerializedName("success_amount")
        private Integer successAmount;
    }

    @Data
    public static class WxPayNotifyDistribute implements WxPayBaseNotifyV3Result<ResultData> {

        private ResultData result;

        @Override
        public void setRawData(OriginNotifyResponse originNotifyResponse) {

        }
    }

//    @Operation(summary = "商家转账批次回调通知")
//    @PostMapping("transferNotify")
//    public void transferNotify(HttpServletRequest request, @RequestBody String data) {
//
//        String timestamp = request.getHeader("Wechatpay-Timestamp");
//        String nonce = request.getHeader("Wechatpay-Nonce");
//        String serialNo = request.getHeader("Wechatpay-Serial");
//        String signature = request.getHeader("Wechatpay-Signature");
//        log.info("timestamp:{} nonce:{} serialNo:{} signature:{} data:{}", timestamp, nonce, serialNo, signature, data);
//
//        SignatureHeader header = new SignatureHeader(timestamp, nonce, signature, serialNo);
//
//        try {
//            WxPayNotifyDistribute result = wxPayService.baseParseOrderNotifyV3Result(data, header, WxPayNotifyDistribute.class, ResultData.class);
//
//            // 更改转账状态
//            PgDistributionRecord record = pgDistributionRecordService.getOne(new LambdaQueryWrapper<PgDistributionRecord>()
//                    .eq(PgDistributionRecord::getOutBatchNo, result.getResult().getOutBatchNo())
//                    .last("LIMIT 1")
//            );
//
//            if (!record.getTransferStatus().equals(TransferStatusEnum.FINISHED)) {
//
//                // -- 备用方案，掉查询明细接口，判断是否成功
//
//                record.setTransferStatus(result.getResult().getBatchStatus());
//
//                // 等于成功的时候
//                if (result.getResult().getBatchStatus().equals(TransferStatusEnum.FINISHED)) {
//                    record.setIsTransfer(true);
//                    record.setTransferTime(new Date());
//                    // 更新佣金金额
//                    record.setPayCommission(result.getResult().getSuccessAmount());
//                }
//
//                // 关闭的时候
//                if (result.getResult().getBatchStatus().equals(TransferStatusEnum.CLOSED)) {
//                    record.setCloseReason(result.getResult().getCloseReason());
//                }
//
//                pgDistributionRecordService.updateById(record);
//            }
//
//        } catch (WxPayException e) {
//            log.error("商户转账回调错误", e);
//        }
//    }
}
