package com.pgb.service.domain.homework;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;

import com.pgb.common.mybatis.handler.JsonbTypeHandler;
import com.pgb.service.enums.CorrectStatusEnum;
import lombok.Data;

/**
 * 
 * @TableName pg_homework_grid_paper
 */
@TableName(value ="pg_homework_grid_paper")
@Data
public class PgHomeworkGridPaper {
    /**
     * 格子纸批量上传
     */
    @TableId
    private Long id;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 作业id
     */
    private Long homeworkId;

    /**
     * 当前处理状态
     */
    private CorrectStatusEnum status;

    /**
     * 检测及分割结果，对应哪个学生
     */
    @TableField(typeHandler = JsonbTypeHandler.class)
    private Object imgResultJson;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 完成状态
     */
    private Date completeTime;

    /**
     * 处理时长，秒
     */
    private Integer duration;

    /**
     * 消耗token
     */
    private Integer tokens;

    /**
     * 用户提交的原生图片，英文逗号分割
     */
    private String userImgList;
}