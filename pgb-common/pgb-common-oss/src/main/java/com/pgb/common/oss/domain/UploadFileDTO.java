package com.pgb.common.oss.domain;

import com.pgb.common.core.global.MaterialProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.*;
import org.springframework.validation.annotation.Validated;


@Schema(title = "上传后文件信息")
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ToString
@EqualsAndHashCode
@Validated
@Data
public class UploadFileDTO {
    @Schema(title = "素材属性")
    private MaterialProperty materialProperty;

    @Schema(title = "素材类型", description = "MaterialTypeEnum", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull
    private MaterialTypeEnum type;

    @Schema(title = "名称", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank
    private String title;

    @Schema(title = "大小", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull
    private Long size;

    @Schema(title = "key值，含路径", description = "OSS 完整值为：【filePath + fileUid + .文件后缀】; VOD：fileId", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank
    private String keyValue;

    @Schema(title = "所属分类")
    private Long cateId;

    @Schema(title = "URL", description = "素材原生URL，仅VOD上传使用")
    private String url;

    @Schema(title = "ConfigKey", description = "素材上传所属配置key", hidden = true)
    private String configKey;

    @Schema(title = "访问域名", hidden = true)
    private String cdnUrl;
}
