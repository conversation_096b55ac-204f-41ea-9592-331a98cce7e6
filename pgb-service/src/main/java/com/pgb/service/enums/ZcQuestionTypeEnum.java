package com.pgb.service.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;

/**
 * <AUTHOR>
 * Created by 2025/4/21 15:15
 */
@Schema(description = "字词类题目 枚举类型")
@AllArgsConstructor
public enum ZcQuestionTypeEnum {


    PyToWord(0, "看拼音写词语", "pages/zc/chinese/pyToWord/showPage"),

    WordToPy(1, "看词语写拼音", "pages/zc/chinese/pyToWord/showPage"),

    TextBlank(2, "课文填空", "pages/zc/chinese/textBlank/showPage"),

    Dictation(3, "词语听写", "pages/zc/chinese/dictation/showPage"),

    TextWriting(4, "课文默写", "pages/zc/chinese/dictation/showPage"),

    EnDictation(5, "单词听写", "pages/zc/english/enDictation/showPage"),

    EnTranslation(6, "中英互译", "pages/zc/english/translation/showPage"),

    SceneBlank(7, "AI情景式字词", "pages/zc/chinese/textBlank/showPage"),

    TextSceneBlank(8,"AI情景式默写","pages/zc/chinese/textBlank/showPage");

    /**
     * 枚举值对应的整数值
     */
    @EnumValue
    public final int value;

    /**
     * 文字说明
     */
    public final String desc;

    /**
     * 打印路径
     */
    public final String printPath;
}
