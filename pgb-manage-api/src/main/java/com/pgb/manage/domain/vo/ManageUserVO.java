package com.pgb.manage.domain.vo;

import com.pgb.service.domain.tag.PgTag;
import com.pgb.service.domain.user.PgUsersVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
public class ManageUserVO extends PgUsersVO {

    @Schema(title = "标签列表")
    private List<PgTag> tagList;

    @Schema(title = "购买次数")
    private Long buyNum;

}
