package com.pgb.student.controller.classes;

import cn.dev33.satoken.annotation.SaCheckLogin;
import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.ReUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.pgb.common.core.global.BaseResult;
import com.pgb.common.core.global.GlobalCode;
import com.pgb.common.core.global.exception.BaseException;
import com.pgb.service.db.PgClassesService;
import com.pgb.service.db.PgHomeworkService;
import com.pgb.service.db.PgStudentService;
import com.pgb.service.db.PgUsersService;
import com.pgb.service.domain.classes.JoinClassForm;
import com.pgb.service.domain.classes.PgClasses;
import com.pgb.service.domain.classes.PgClassesVO;
import com.pgb.service.domain.homework.PgHomework;
import com.pgb.service.domain.student.PgStudent;
import com.pgb.service.domain.student.StudentInfoDTO;
import com.pgb.service.domain.user.PgUsers;
import com.pgb.service.enums.IdentityTypeEnum;
import com.pgb.student.domain.PgUserStudent;
import com.pgb.student.service.PgUserStudentService;
import com.pgb.student.service.PgbCustomService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * Created by 2025/2/24 19:06
 */
@Tag(name = "学生端/班级/班级信息")
@RestController("StudentClassController")
@RequestMapping("/student/class")
@RequiredArgsConstructor
@Slf4j
public class ClassController {

    private final PgUserStudentService pgUserStudentService;

    private final PgbCustomService pgbCustomService;

    @Operation(summary = "学生退出班级")
    @PostMapping("quit/{classId}")
    @SaCheckLogin
    public BaseResult<Boolean> quit(@PathVariable Long classId) {

        // 获取当前登录学生id
        long userStudentId = StpUtil.getSession().getLong("studentId");
//        long userStudentId = 1900401329146458113L;

        pgbCustomService.quitClass(classId, userStudentId);

        return BaseResult.success(true);
    }


    @Operation(summary = "获取班级信息")
    @GetMapping("info/{classId}")
    public BaseResult<PgClassesVO> info(@PathVariable Long classId) {

        PgClasses classes = pgbCustomService.getClassById(classId);

        if (ObjectUtil.isNull(classes)) {
            return BaseResult.error("班级不存在");
        }

        PgClassesVO classesVO = BeanUtil.copyProperties(classes, PgClassesVO.class);

        return BaseResult.success(classesVO);
    }


    @Operation(summary = "获取班级列表")
    @GetMapping("list")
    @SaCheckLogin
    public BaseResult<List<PgClassesVO>> list() {

        Long userStudentId = StpUtil.getSession().getLong("studentId");

        LambdaQueryWrapper<PgClasses> queryWrapper = new LambdaQueryWrapper<>();

        // 获取当前用户加入的班级
        List<PgStudent> students = pgbCustomService.getStudentListByStudentUserId(userStudentId);

        List<Long> classIds = students.stream().map(PgStudent::getClassId).toList();

        if (classIds.isEmpty()) {
            return BaseResult.success(new ArrayList<>());
        } else {
            queryWrapper.in(PgClasses::getId, classIds);
        }

        List<PgClassesVO> list = pgbCustomService.pgClasses(queryWrapper);

        return BaseResult.success(list);
    }


    @Operation(summary = "学生加入班级", description = "通过班级口令加入")
    @PostMapping("joinClass")
    @SaCheckLogin
    public BaseResult<Boolean> joinClass(@RequestBody JoinClassForm form) {

        // 当前登录用户
        long userId = StpUtil.getLoginIdAsLong();

        // 根据班级口令查班级
        PgClasses pgClasses = pgbCustomService.getClassByPassword(form.getPassword());

        if (ObjectUtil.isNull(pgClasses)) {
            return BaseResult.error(GlobalCode.Item_Null, "该口令对应班级不存在");
        }

        // 将学生姓名，只保留数字和汉字
//        for (StudentInfoDTO infoDTO : form.getStudentInfo()) {
//            infoDTO.setName(
//                    ReUtil.replaceAll(infoDTO.getName(), "[^a-zA-Z0-9\\u4e00-\\u9fa5]", "")
//            );
//
//            // 空判断
//            if (StrUtil.isBlank(infoDTO.getName())) {
//                return BaseResult.error("学生姓名必填，仅支持汉字、数字和字母");
//            }
//        }

        for (StudentInfoDTO infoDTO : form.getStudentInfo()) {
            // 获取并创建用户学生身份
            try {

                // 空判断
                if (StrUtil.isBlank(infoDTO.getName())) {
                    return BaseResult.error("学生姓名必填");
                }

                // 去掉首尾空格
                infoDTO.setName(StrUtil.trim(infoDTO.getName()));

                // 学生加入班级
                PgUserStudent userStudent = pgbCustomService.joinClass(userId, pgClasses.getId(), infoDTO);

                // 切换用户身份为最新添加的userStudentId
                pgUserStudentService.switchByUserStudentId(userStudent.getId());
            } catch (BaseException e) {
                return BaseResult.error(e.getMsg());
            }
        }

        return BaseResult.success(true);
    }


    @Data
    public static class MultiJoinClassForm {

        @Schema(title = "学生信息")
        List<StudentInfoDTO> studentInfo;
    }

    @Operation(summary = "通过分享页面加入班级", description = "通过分享页面邀请(包括多个孩子)")
    @PostMapping("{classId}")
    @SaCheckLogin
    public BaseResult<Boolean> inviteStudent(@PathVariable Long classId, @RequestBody MultiJoinClassForm form) {

        // 当前登录用户id
        Long userId = StpUtil.getLoginIdAsLong();

        PgClasses classes = pgbCustomService.getClassById(classId);

        if (ObjectUtil.isNull(classes)) {
            return BaseResult.error(GlobalCode.Item_Null, "该班级不存在");
        }

        for (StudentInfoDTO dto : form.getStudentInfo()) {
            try {

                // 空判断
                if (StrUtil.isBlank(dto.getName())) {
                    return BaseResult.error("学生姓名必填");
                }

                // 去掉首尾空格
                dto.setName(StrUtil.trim(dto.getName()));

                // 加入班级
                PgUserStudent userStudent = pgbCustomService.joinClass(userId, classId, dto);

                // 切换用户身份为最新添加的userStudentId
                pgUserStudentService.switchByUserStudentId(userStudent.getId());
            } catch (BaseException e) {
                return BaseResult.error(e.getMsg());
            }
        }

        return BaseResult.success(true);
    }


    @Operation(summary = "判断当前登录用户是否加入过该班级")
    @GetMapping("isJoin/{classId}")
    public BaseResult<Boolean> isJoin(@PathVariable Long classId) {

        // 当前登录用户-学生id
        long userStudentId = StpUtil.getSession().getLong("studentId");

        // 判断班级是否存在
        PgClasses pgClasses = pgbCustomService.getClassById(classId);

        if (ObjectUtil.isNull(pgClasses)) {
            return BaseResult.error(GlobalCode.Item_Null, "该班级不存在");
        }

        PgStudent student = pgbCustomService.getStudentByClassId(classId, userStudentId);

        // 学生已加入过
        if (ObjectUtil.isNotNull(student)) {
            return BaseResult.success(true);
        }

        return BaseResult.success(false);
    }
}
