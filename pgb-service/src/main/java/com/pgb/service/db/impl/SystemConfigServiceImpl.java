package com.pgb.service.db.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.pgb.common.core.global.GlobalConstants;
import com.pgb.common.redis.RedisUtils;

import com.pgb.service.db.SystemConfigService;
import com.pgb.service.domain.systemConfig.SystemConfig;
import com.pgb.service.enums.SystemConfigEnum;
import com.pgb.service.mapper.SystemConfigMapper;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @description 针对表【system_config(系统配置)】的数据库操作Service实现
 * @createDate 2024-12-02 14:27:25
 */
@Service
public class SystemConfigServiceImpl extends ServiceImpl<SystemConfigMapper, SystemConfig>
        implements SystemConfigService {

    @Override
    public SystemConfig getByKeyByCache(SystemConfigEnum key) {

        String redisKey = GlobalConstants.XCX_VERSION_INFO_KEY + key.name();

        // 查看数据缓存
        SystemConfig systemConfig = RedisUtils.getCacheObject(redisKey);

        if (ObjectUtil.isNotNull(systemConfig)) {
            return systemConfig;
        }

        // 获取数据
        systemConfig = getById(key);

        if (ObjectUtil.isNotNull(systemConfig)) {
            // 保存缓存数据
            RedisUtils.setCacheObject(redisKey, systemConfig);
            return systemConfig;
        }

        return null;
    }

    @Override
    public SystemConfig getByKey(SystemConfigEnum key) {

        // 获取数据
        SystemConfig systemConfig = getById(key);

        if (ObjectUtil.isNotNull(systemConfig)) {

            return systemConfig;
        }

        return null;
    }

    @Override
    public void removeCache(SystemConfigEnum key) {

        String redisKey = GlobalConstants.XCX_VERSION_INFO_KEY + key.name();

        RedisUtils.deleteObject(redisKey);
    }
}




