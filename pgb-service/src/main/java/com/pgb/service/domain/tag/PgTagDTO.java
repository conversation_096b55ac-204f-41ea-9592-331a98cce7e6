package com.pgb.service.domain.tag;

import com.pgb.common.core.validate.UpdateGroup;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.*;
import org.springframework.validation.annotation.Validated;

import java.io.Serializable;
import java.util.Date;

/**
*
* @TableName pg_tag
*/
@Schema(description = " DTO")
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode
@ToString
@Validated
@Builder
public class PgTagDTO implements Serializable {

    @Schema(title = "用户标签", type = "string")
    @NotNull(message="[用户标签]不能为空", groups = {UpdateGroup.class})
    private Long id;

    @Schema(title = "标签名称")
    @Size(max= 255)
    private String name;

    @Schema(title = "创建时间")
    private Date createTime;

}
