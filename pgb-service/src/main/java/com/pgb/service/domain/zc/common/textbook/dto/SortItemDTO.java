package com.pgb.service.domain.zc.common.textbook.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serializable;

/**
 * 排序项DTO
 *
 * <AUTHOR>
 */
@Schema(description = "排序项DTO")
@Data
public class SortItemDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "ID")
    @NotNull(message = "ID不能为空")
    private Long id;

    @Schema(description = "排序值")
    @NotNull(message = "排序值不能为空")
    private Integer sort;
}
