<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.pgb</groupId>
        <artifactId>pgb-server</artifactId>
        <version>${revision}</version>
    </parent>

    <packaging>pom</packaging>

    <artifactId>pgb-common</artifactId>
    <description>通用模块</description>

    <modules>
        <module>pgb-common-bom</module>
        <module>pgb-common-core</module>
        <module>pgb-common-doc</module>
        <module>pgb-common-mq</module>
        <module>pgb-common-mybatis</module>
        <module>pgb-common-ocr</module>
        <module>pgb-common-oss</module>
        <module>pgb-common-pay</module>
        <module>pgb-common-redis</module>
        <module>pgb-common-satoken</module>
        <module>pgb-common-sms</module>
        <module>pgb-common-tenant</module>
        <module>pgb-common-wx</module>
        <module>pgb-common-llm</module>
    </modules>
</project>
