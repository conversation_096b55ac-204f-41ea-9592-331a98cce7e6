package com.pgb.service.domain.zc.text;

import com.pgb.service.domain.zc.word.WordItem;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * Created by 2025/4/28 15:25
 */
@Data
public class TextWordsInfo {

    @Schema(title = "第几课")
    private Integer sort;

    @Schema(title = "课文名称")
    private String textName;

    @Schema(title = "拼音对象")
    private List<WordItem> wordItems;
}
