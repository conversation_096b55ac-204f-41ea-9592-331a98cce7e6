package ${baseInfo.packageName};

import java.io.Serializable;
<#list tableClass.importList as fieldType>${"\n"}import ${fieldType};</#list>
import org.hibernate.validator.constraints.Length;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.ToString;
import lombok.Builder;
import org.springframework.validation.annotation.Validated;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import com.pgb.common.core.validate.UpdateGroup;

/**
* ${tableClass.remark!}
* @TableName ${tableClass.tableName}
*/
@Schema(description = "${tableClass.remark!} DTO")
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode
@ToString
@Validated
@Builder
public class ${baseInfo.fileName} implements Serializable {

<#list tableClass.allFields as field>
    @Schema(title = "${field.remark!}"<#if field.fieldName=="id">, type = "string"</#if>)<#if !field.nullable || field.jdbcType=="VARCHAR">${"\n    "}</#if><#if !field.nullable><#if field.jdbcType=="VARCHAR">@NotBlank(message="[${field.remark!}]不能为空")<#else>@NotNull(message="[${field.remark!}]不能为空"<#if field.fieldName=="id">, groups = {UpdateGroup.class}</#if>)</#if></#if><#if field.jdbcType=="VARCHAR"><#if !field.nullable>${"\n    "}</#if>@Size(max= ${field.columnLength?c})</#if>
    private ${field.shortTypeName} ${field.fieldName};

</#list>
}
