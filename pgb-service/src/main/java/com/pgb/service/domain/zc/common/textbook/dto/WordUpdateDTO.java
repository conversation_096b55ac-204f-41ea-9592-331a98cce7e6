package com.pgb.service.domain.zc.common.textbook.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * 更新单词DTO
 *
 * <AUTHOR>
 */
@Schema(description = "更新单词DTO")
@Data
public class WordUpdateDTO implements Serializable {

    @Schema(description = "排序值")
    private Integer sort;

    @Schema(description = "所属单元ID")
    private Long unitId;

    @Schema(description = "单词")
    private String word;

    @Schema(description = "单词信息")
    private WordInfoDTO wordInfo;


}
