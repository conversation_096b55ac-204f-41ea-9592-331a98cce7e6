package com.pgb.xcx.controller.pay;

import cn.dev33.satoken.annotation.SaCheckLogin;
import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.extra.servlet.JakartaServletUtil;
import com.pgb.common.core.global.BaseResult;
import com.pgb.common.core.utils.EnvUtils;
import com.pgb.common.pay.domain.apple.AppleReceiptResponse;
import com.pgb.common.pay.domain.apple.AppleReceiptVerification;
import com.pgb.common.pay.enums.BuyTypeEnum;
import com.pgb.common.pay.enums.PayTypeEnum;
import com.pgb.common.pay.service.AppleReceiptVerifier;
import com.pgb.service.db.PgOrderService;
import com.pgb.service.domain.order.OrderPayParam;
import com.pgb.service.domain.order.PgOrder;
import com.pgb.service.enums.VipTypeEnum;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

/**
 * @Datetime: 2025年06月14日18:17
 * @Description:
 */
@Tag(name = "用户端/订单/苹果支付")
@RestController("ApplePayController")
@Transactional(rollbackFor = Exception.class)
@RequestMapping("/user/order/applePay")
@RequiredArgsConstructor
@Slf4j
public class ApplePayController {

    private final PgOrderService pgOrderService;

    @Operation(summary = "创建订单")
    @GetMapping("/createOrder")
    @SaCheckLogin
    public BaseResult<OrderPayParam<String>> createOrder(HttpServletRequest request, VipTypeEnum vipType) {
        // 生成订单
        PgOrder order = pgOrderService.generateOrder(
                vipType,
                PayTypeEnum.APPLE_PAY,
                BuyTypeEnum.PAY,
                JakartaServletUtil.getClientIP(request, null),
                StpUtil.getLoginIdAsLong()
        );

        OrderPayParam<String> payParam = new OrderPayParam<>();
        payParam.setOrderId(order.getId());

        // 根据会员类型，设置 product_id
        if (vipType.equals(VipTypeEnum.YEAR) || vipType.equals(VipTypeEnum.YEAR_198)) {
            payParam.setParams("pgb_year_vip");
        }
        else if (vipType.equals(VipTypeEnum.MONTH)) {
            payParam.setParams("pgb_month_vip");
        }

        return BaseResult.success(payParam);
    }

    @Value("${apple.pay.shared-secret}")
    private String sharedSecret;

    @Operation(summary = "验证支付结果")
    @PostMapping("/verify")
    @SaCheckLogin
    public BaseResult<AppleReceiptResponse> verifyReceipt(@Valid @RequestBody AppleReceiptVerification request) {
        // 生成验证器
        AppleReceiptVerifier verifier = new AppleReceiptVerifier(sharedSecret);

        // 使用 Hutool 快速校验参数
        AppleReceiptResponse response = verifier.verifyReceipt(
                request, EnvUtils.isProd()
        );

        log.info("【苹果支付验证】: 验证码：{}\n{}", response.getStatus(), response.getLatestReceiptInfo());

        // 支付成功
        if (response.getIsSuccess()) {
            pgOrderService.paySuccess(request.getOrderId(), response.getLatestReceiptInfo());
        }

        return BaseResult.success(response);
    }

}
