package com.pgb.service.domain.query;

import com.pgb.common.mybatis.global.PageQuery;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * Created by 2024/12/23 16:45
 */
@EqualsAndHashCode(callSuper = true)
@Schema(description = "弹窗 搜索实体")
@Data
public class PopupQuery extends PageQuery {

    @Schema(title = "备注")
    private String remark;
}
