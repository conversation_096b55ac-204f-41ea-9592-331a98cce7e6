package com.pgb.xcx.controller.application;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.json.JSONUtil;
import cn.hutool.poi.word.Word07Writer;
import com.pgb.common.core.global.BaseResult;
import com.pgb.service.db.PgChatMsgService;
import com.pgb.service.db.PgChatService;
import com.pgb.service.domain.chat.PgChat;
import com.pgb.service.domain.chat.msg.PgChatMsg;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.commonmark.node.Node;
import org.commonmark.parser.Parser;
import org.commonmark.renderer.html.HtmlRenderer;
import org.commonmark.renderer.text.TextContentRenderer;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.awt.*;
import java.io.File;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * Created by 2025/2/8 18:21
 */
@Tag(name = "用户端/智能体/导出")
@RestController("AiAgentExportController")
@Transactional(rollbackFor = Exception.class)
@RequestMapping("/user/agent/export")
@RequiredArgsConstructor
@Slf4j
public class ExportController {

    private final PgChatMsgService pgChatMsgService;

    private final PgChatService pgChatService;

    @Operation(summary = "导出word")
    @GetMapping("word/{chatMsgId}")
    public ResponseEntity<byte[]> exportWord(@PathVariable Long chatMsgId) {

        PgChatMsg chatMsg = pgChatMsgService.getById(chatMsgId);

        // 获取所属会话
        PgChat chat = pgChatService.getById(chatMsg.getChatId());

        Word07Writer writer = new Word07Writer();

        List<String> contentList = ListUtil.toList(chatMsg.getContent().split("\n"));

        for (String content : contentList) {
            writer.addText(new Font("宋体", Font.PLAIN, 11), content);
        }

        // 创建临时文件
        File tempFile = FileUtil.createTempFile();
        writer.flush(tempFile);
        writer.close();

        // 导出的文件名
        String fileName = URLEncoder.encode(DateUtil.format(new Date(), "yyyy-MM-dd") + chat.getTitle(), StandardCharsets.UTF_8);

        // 构建导出所需参数
        HttpHeaders headers = new HttpHeaders();
        headers.set("Access-Control-Expose-Headers", "Content-Disposition"); // 必须设置，否则无法获取文件名
        headers.set("Content-Disposition", "attachment;filename=" + fileName + ".docx");
        headers.set("Content-Type", "application/vnd.openxmlformats-officedocument.wordprocessingml.document");

        // 读取文件
        byte[] wordBytes = FileUtil.readBytes(tempFile);

        // 删除临时文件
        FileUtil.del(tempFile);

        return ResponseEntity.ok()
                .headers(headers)
                .contentType(MediaType.valueOf("application/vnd.openxmlformats-officedocument.wordprocessingml.document"))
                .body(wordBytes);
    }

    @Deprecated
    @Operation(summary = "导出wordV2")
    @GetMapping("wordV2/{chatMsgId}")
    public ResponseEntity<byte[]> exportWordV2(@PathVariable Long chatMsgId) throws Exception {

        PgChatMsg chatMsg = pgChatMsgService.getById(chatMsgId);

        // 获取AI返回内容
        String content = chatMsg.getContent();

        String htmlContent;
        // 如果是 html 格式
        if (content.startsWith("```html")) {
            content = content.substring(7);
            if (content.endsWith("```")) {
                content = content.substring(0, content.length() - 3);
            }

            htmlContent = "<div>" + content + "</div>";
        }

        // 若是 markdown 格式 则 转为 html
        else {
            Parser parser = Parser.builder().build();

            Node document = parser.parse(content);

            HtmlRenderer renderer = HtmlRenderer.builder().build();

            htmlContent = "<div>" + renderer.render(document) + "</div>";
        }

        HttpResponse response = HttpRequest.post("http://82.156.150.140:8081")
                .body(JSONUtil.createObj()
                        // imgHs * (800. / imgWs) + 100
                        .putOnce("html", htmlContent).toString()
                )
                .timeout(15 * 60 * 1000)
                .execute();

        if (!response.isOk()) {
            throw new Exception("获取html转word失败");
        }

        // 导出的文件名
        String fileName = URLEncoder.encode(DateUtil.format(new Date(), "yyyy-MM-dd"), StandardCharsets.UTF_8);

        // 构建导出所需参数
        HttpHeaders headers = new HttpHeaders();
        headers.set("Access-Control-Expose-Headers", "Content-Disposition"); // 必须设置，否则无法获取文件名
        headers.set("Content-Disposition", "attachment;filename=" + fileName + ".docx");
        headers.set("Content-Type", "application/vnd.openxmlformats-officedocument.wordprocessingml.document");

        return ResponseEntity.ok()
                .headers(headers)
                .contentType(MediaType.valueOf("application/vnd.openxmlformats-officedocument.wordprocessingml.document"))
                .body(response.bodyStream().readAllBytes());

    }

    @Operation(summary = "导出wordV3", description = "markdown转word")
    @GetMapping("wordV3/{chatMsgId}")
    public ResponseEntity<byte[]> exportWordV3(@PathVariable Long chatMsgId) throws Exception {

        PgChatMsg chatMsg = pgChatMsgService.getById(chatMsgId);

        PgChat chat = pgChatService.getById(chatMsg.getChatId());

        String name = "";
        if (ObjectUtil.isNotNull(chat)) {
            name = chat.getTitle();
        }

        // 获取AI返回内容
        String content = chatMsg.getContent();

        HttpResponse response = HttpRequest.post("https://pandoc-sjdenrdhhh.cn-beijing.fcapp.run/markdown2word")
                .body(JSONUtil.createObj()
                        // imgHs * (800. / imgWs) + 100
                        .putOnce("markdown", content).toString()
                )
                .timeout(15 * 60 * 1000)
                .execute();

        if (!response.isOk()) {
            throw new Exception("获取markdown转word失败");
        }

        // 导出的文件名 教学计划-08011934
        String fileName = URLEncoder.encode(name + "-" + DateUtil.format(new Date(), "yyyyMMddHHmm"), StandardCharsets.UTF_8);

        // 构建导出所需参数
        HttpHeaders headers = new HttpHeaders();
        headers.set("Access-Control-Expose-Headers", "Content-Disposition"); // 必须设置，否则无法获取文件名
        headers.set("Content-Disposition", "attachment;filename=" + fileName + ".docx");
        headers.set("Content-Type", "application/vnd.openxmlformats-officedocument.wordprocessingml.document");

        return ResponseEntity.ok()
                .headers(headers)
                .contentType(MediaType.valueOf("application/vnd.openxmlformats-officedocument.wordprocessingml.document"))
                .body(response.bodyStream().readAllBytes());

    }

    @Operation(summary = "将生成的内容转为纯文本", description = "复制时使用")
    @GetMapping("toWord/{chatMsgId}")
    public BaseResult<String> toWord(@PathVariable Long chatMsgId) {

        PgChatMsg chatMsg = pgChatMsgService.getById(chatMsgId);

        // 获取AI返回内容
        String content = chatMsg.getContent();

        // 一段 纯文本
        String plainText = content;

        if (ObjectUtil.isNotNull(content)) {

            // 【HTML】如果是 HTML 格式
            if (content.startsWith("```html")) {
                content = content.substring(7);
                if (content.endsWith("```")) {
                    content = content.substring(0, content.length() - 3);
                }
                // 解析HTML文档
                Document parse = Jsoup.parse(content);

                // 提取纯文本
                plainText = parse.body().text();
            }
            // 【Markdown】解析Markdown文本
            else if (content.startsWith("```markdown")) {
                content = content.substring(11);
                if (content.endsWith("```")) {
                    content = content.substring(0, content.length() - 3);
                }
                Parser parser = Parser.builder().build();
                Node document = parser.parse(content);
                // 渲染为纯文本
                TextContentRenderer renderer = TextContentRenderer.builder().build();
                plainText = renderer.render(document);
            }
        }
        return BaseResult.success(plainText);
    }

}
