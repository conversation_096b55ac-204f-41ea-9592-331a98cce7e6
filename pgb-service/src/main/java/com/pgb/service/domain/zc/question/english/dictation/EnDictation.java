package com.pgb.service.domain.zc.question.english.dictation;

import com.pgb.service.domain.zc.common.ZcLocation;
import com.pgb.service.domain.zc.word.english.EngWordItem;
import com.pgb.service.enums.ZcQuestionTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * Created by 2025/6/6 16:55
 */
@Schema(title = "单词听写")
@Data
public class EnDictation {

    @Schema(title = "题目类型", description = "听写或默写")
    private ZcQuestionTypeEnum type;

    @Schema(title = "题目名称")
    private String name;

    @Schema(title = "听写的单词内容", description = "如果是单词听写，以换行分隔")
    private String text;

}
