package com.pgb.service.db.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.pgb.service.domain.agent.PgAgent;
import com.pgb.service.db.PgAgentService;
import com.pgb.service.mapper.PgAgentMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【pg_agent】的数据库操作Service实现
* @createDate 2025-02-13 14:15:23
*/
@Service
@RequiredArgsConstructor
public class PgAgentServiceImpl extends ServiceImpl<PgAgentMapper, PgAgent>
    implements PgAgentService{
}




