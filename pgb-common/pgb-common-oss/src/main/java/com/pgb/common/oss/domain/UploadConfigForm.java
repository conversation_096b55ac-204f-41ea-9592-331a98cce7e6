package com.pgb.common.oss.domain;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.springframework.validation.annotation.Validated;

@Schema(title = "上传配置 DTO")
@Data
@Validated
public class UploadConfigForm {
    @Schema(title = "上传文件类型")
    @NotNull
    private MaterialTypeEnum fileType;

    @Schema(title = "文件后缀")
    @NotBlank
    private String suffix;
}
