package com.pgb.service.domain.distribution.record;

import com.baomidou.mybatisplus.annotation.TableName;
import com.pgb.service.enums.ProfitStatusEnum;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 *
 * @TableName pg_distribution_record
 */
@TableName(value ="pg_distribution_record")
@Data
public class PgDistributionRecord implements Serializable {
    /**
     * 分销记录id
     */
    private Long id;

    /**
     * 邀请人id
     */
    private Long shareUserId;

    /**
     * 被邀请人id
     */
    private Long userId;

    /**
     * 支付金额
     */
    private Integer payAmount;

    /**
     * 佣金
     */
    private Integer payCommission;

    /**
     * 订单 id
     */
    private Long orderId;

    /**
     * 记录创建时间
     */
    private Date createTime;


    /**
     * 是否转账
     */
    private Boolean isTransfer;

    /**
     * 转账时间
     */
    private Date transferTime;

    /**
     * 批次关闭原因
     */
    private String closeReason;

    // --------------------  分账专用  --------------------------

    /**
     * 微信订单号
     */
    private String transactionId;

    /**
     * 商户分账单号
     */
    private String outOrderNo;

    /**
     * 分账单状态
     */
    private ProfitStatusEnum status;

}
