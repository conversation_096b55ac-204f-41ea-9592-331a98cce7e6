package com.pgb.service.domain.distribution.verify;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 分销核销表
 * @TableName pg_distribution_verify
 */
@TableName(value ="pg_distribution_verify")
@Data
public class PgDistributionVerify implements Serializable {
    /**
     * 分销核销id
     */
    private Long id;

    /**
     * 订单id
     */
    private Long orderId;

    /**
     * 分销金额（单位：分）
     */
    private Integer distributeAmount;

    /**
     * 分销订单状态（0待处理，1已处理）
     */
    private Integer status;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 用户实际支付金额（单位：分）
     */
    private Integer payAmount;

    /**
     * 邀请人id
     */
    private Long shareUserId;

    /**
     * 被邀请人id
     */
    private Long userId;

}
