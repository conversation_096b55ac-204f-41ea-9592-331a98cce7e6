package com.pgb.manage.service.saas.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * 系统菜单权限表
 * @TableName system_permission
 */
@TableName(value ="system_permission")
@Data
public class SystemPermission implements Serializable {
    /**
     * 系统菜单权限表
     */
    @TableId
    private Long id;

    /**
     * 0：目录，1：页面，2：权限
     */
    private Integer type;

    /**
     * 名称
     */
    private String name;

    /**
     * 如果为0，则为父菜单
     */
    private Long parentId;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 权限标识
     */
    private String perms;

    /**
     * 是否外链
     */
    private Boolean isLink;

    /**
     * 是否启用
     */
    private Boolean isValid;

    /**
     * 备注
     */
    private String remark;

    /**
     * 0：内部管理，1：saas系统
     */
    private Integer systemType;

    /**
     * 是否是通用权限
     */
    private Boolean isCommon;
}
