package com.pgb.service.domain.zc.text;

import java.io.Serializable;

import java.util.Date;

import com.pgb.common.core.validate.UpdateGroup;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.ToString;
import lombok.Builder;
import org.springframework.validation.annotation.Validated;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;

/**
* 课文表
* @TableName pg_text
*/
@Schema(description = "课文表 DTO")
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode
@ToString
@Validated
@Builder
public class PgZcTextDTO implements Serializable {

    @Schema(title = "id")
    @NotNull(message="[]不能为空", groups = {UpdateGroup.class})
    private Long id;

    @Schema(title = "课文名")
    @Size(max= 255)
    private String name;

    @Schema(title = "字词ids")
    private String wordIds;

    @Schema(title = "创建时间")
    private Date createTime;

    @Schema(title = "更新时间")
    private Date updateTime;

    @Schema(title = "所属教材id")
    private Long textbookId;

    @Schema(title = "单元（1，2，3，4）")
    private Integer unit;

}
