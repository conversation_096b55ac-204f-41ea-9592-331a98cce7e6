package com.pgb.correct.script;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.TimeInterval;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.URLUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.pgb.common.oss.service.OssService;
import com.pgb.service.custom.CorrectService;
import com.pgb.service.db.PgAnswerService;
import com.pgb.service.domain.answer.PgAnswer;
import com.pgb.service.domain.common.image.FilePaperImg;
import com.pgb.service.domain.question.zwEssay.ZwEssayQuestion;
import com.pgb.service.enums.CorrectStatusEnum;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.File;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Future;

/**
 * @Datetime: 2025年07月30日23:34
 * @Description:
 */
@Component
@Slf4j
public class DbClearBoot {
    @Autowired
    private PgAnswerService pgAnswerService;

    @Autowired
    private CorrectService correctService;

    @Autowired
    private OssService ossService;

    // 用于平滑中断
    @Setter
    private Boolean status = true;

    /**
     * 批改结果归档迁移方法
     *
     * 功能说明：
     * 1. 筛选指定日期范围内已批改完成的答案记录
     * 2. 将批改结果(correctResult)转换为JSON格式并上传到OSS
     * 3. OSS存储路径格式：/correctResult/【answerId】.json
     * 4. 设置记录为归档状态，保存归档URL，清空原有correctResult字段
     *
     * 注意事项：
     * - 支持分页处理，避免内存溢出
     * - 支持多线程并发处理，提高效率
     * - 包含完整的异常处理和日志记录
     * - 支持平滑中断机制
     */
    public void correctResultMove() {
        log.info("开始执行批改结果归档迁移任务");

        // 分页执行配置
        int pageSize = 100;
        int pageNo = 1;
        int endPageNo = 99999;

        // 构建查询条件：筛选指定日期范围内已批改完成的记录
        LambdaQueryWrapper<PgAnswer> queryWrapper = new LambdaQueryWrapper<PgAnswer>()
                .ne(PgAnswer::getStatus, CorrectStatusEnum.Uploaded)
                .between(PgAnswer::getCreateTime, DateUtil.parse("2024-12-31"), DateUtil.parse("2025-04-01"))
                .orderByAsc(PgAnswer::getCreateTime);

        // 获取总记录数和总页数
        IPage<PgAnswer> totalPage = pgAnswerService.page(new Page<>(pageNo, pageSize), queryWrapper);
        log.info("批改结果归档迁移 - 总记录数：{}，总页数：{}", totalPage.getTotal(), totalPage.getPages());

        // 分页处理数据
        while (this.status && pageNo <= totalPage.getPages()) {
            TimeInterval interval = DateUtil.timer();
            log.info("------开始处理第{}页数据", pageNo);

            // 获取当前页数据
            IPage<PgAnswer> page = pgAnswerService.page(new Page<>(pageNo, pageSize), queryWrapper);

            // 多线程并发处理当前页数据
            List<Future<Void>> futureList = new ArrayList<>();
            int finalPageNo = pageNo;

            for (PgAnswer answer : page.getRecords()) {
                Future<Void> future = ThreadUtil.execAsync(() -> {
                    try {
                        // 处理单个答案记录的归档迁移
                        processAnswerArchive(answer, finalPageNo, totalPage.getPages(), page.getRecords().indexOf(answer) + 1);
                    } catch (Exception e) {
                        log.error("处理答案记录归档失败，answerId: {}", answer.getId(), e);
                    }
                    return null;
                });
                futureList.add(future);
            }

            // 等待当前页所有任务完成
            futureList.forEach(future -> {
                try {
                    future.get();
                } catch (InterruptedException | ExecutionException e) {
                    log.error("处理异常，第{}页", finalPageNo, e);
                }
            });

            // 检查是否需要停止处理
            if (!correctService.getCorrecting() || endPageNo == pageNo) {
                log.info("停止处理，当前已执行页数：{}", pageNo);
                break;
            }

            log.info("------处理完成第{}/{}页数据，用时：{}", pageNo, totalPage.getPages(), interval.intervalPretty());
            pageNo++;
        }

        log.info("批改结果归档迁移任务完成 - 总记录数：{}，总页数：{}，停止页数：{}",
                totalPage.getTotal(), totalPage.getPages(), (pageNo - 1));
    }

    /**
     * 处理单个答案记录的归档迁移
     *
     * @param answer 答案记录
     * @param currentPage 当前页数
     * @param totalPages 总页数
     * @param indexInPage 在当前页中的索引
     */
    private void processAnswerArchive(PgAnswer answer, int currentPage, long totalPages, int indexInPage) {
        try {
            // 检查是否已经归档
            if (Boolean.TRUE.equals(answer.getIsArchive()) && StrUtil.isNotBlank(answer.getArchiveUrl())) {
                log.info("答案记录已归档，跳过处理，answerId: {}", answer.getId());
                return;
            }

            // 检查批改结果是否存在
            if (ObjectUtil.isNull(answer.getCorrectResult())) {
                log.warn("答案记录批改结果为空，跳过处理，answerId: {}", answer.getId());
                return;
            }

            // 将批改结果转换为JSON字符串
            String correctResultJson = JSONUtil.toJsonStr(answer.getCorrectResult());

            // 生成OSS存储的key
            String ossKey = StrUtil.format("correctResult/{}.json", answer.getId());

            // 创建临时文件保存JSON数据
            File tempFile = null;
            try {
                tempFile = FileUtil.createTempFile(".json", true);
                FileUtil.writeUtf8String(correctResultJson, tempFile);

                // 上传文件到OSS
                String archiveUrl = ossService.putFile(ossKey, tempFile);

                // 更新数据库记录
                PgAnswer updateAnswer = new PgAnswer();
                updateAnswer.setId(answer.getId());
                updateAnswer.setIsArchive(true);                    // 设置为归档状态
                updateAnswer.setArchiveUrl(archiveUrl);             // 保存归档URL
                updateAnswer.setCorrectResult(null);                // 清空原有批改结果

                // 确保deleted字段不为null
                if (ObjectUtil.isNull(answer.getDeleted())) {
                    updateAnswer.setDeleted(false);
                }

                // 执行数据库更新
                boolean updateSuccess = pgAnswerService.updateById(updateAnswer);

                // 设置 correctResult 字段为null，不能使用对象更新，否则会忽略
                pgAnswerService.lambdaUpdate().set(PgAnswer::getCorrectResult, null).eq(PgAnswer::getId, answer.getId()).update();

                if (updateSuccess) {
                    log.info("当前页数：{}/{}，处理完成第{}个：{}，归档URL：{}",
                            currentPage, totalPages, indexInPage, answer.getId(), archiveUrl);
                } else {
                    log.error("数据库更新失败，answerId: {}", answer.getId());
                }

            } finally {
                // 清理临时文件
                if (tempFile != null && tempFile.exists()) {
                    FileUtil.del(tempFile);
                }
            }

        } catch (Exception e) {
            log.error("处理答案记录归档失败，answerId: {}", answer.getId(), e);
            throw e;
        }
    }
}
