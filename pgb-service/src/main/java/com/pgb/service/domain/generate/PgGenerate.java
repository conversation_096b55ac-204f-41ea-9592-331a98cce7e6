package com.pgb.service.domain.generate;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;

import com.pgb.common.mybatis.handler.JsonbTypeHandler;
import com.pgb.service.enums.GenerateTypeEnum;
import lombok.Data;

/**
 * 【万能写】记录表
 * @TableName pg_generate
 */
@TableName(value ="pg_generate")
@Data
public class PgGenerate implements Serializable {
    /**
     * 万能写记录
     */
    private Long id;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 类型（全文/段落/英文）
     */
    private GenerateTypeEnum type;

    /**
     * 参数表单
     */
    @TableField(typeHandler = JsonbTypeHandler.class)
    private Object form;

    /**
     * AI返回内容
     */
    private String content;

    /**
     * 关联作文题目
     */
    private Long quesId;

}
