package com.pgb.common.oss.enumd;

import com.amazonaws.services.s3.model.CannedAccessControlList;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 桶访问策略配置
 *
 */
@Getter
@AllArgsConstructor
public enum AccessPolicyType {

    /**
     * private
     */
    PRIVATE(CannedAccessControlList.Private, PolicyType.WRITE),

    /**
     * public
     */
    PUBLIC(CannedAccessControlList.PublicRead, PolicyType.READ),

    /**
     * 继承
     */
    INHERIT(CannedAccessControlList.BucketOwnerFullControl, PolicyType.READ);

    /**
     * 文件对象 权限类型
     */
    private final CannedAccessControlList acl;

    /**
     * 桶策略类型
     */
    private final PolicyType policyType;

}
