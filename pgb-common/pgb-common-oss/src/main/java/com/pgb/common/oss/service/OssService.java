package com.pgb.common.oss.service;

import io.swagger.v3.oas.annotations.media.Schema;

import java.awt.image.BufferedImage;
import java.io.File;
import java.time.Duration;
import java.util.List;

@Schema(title = "各家云 OSS 统一入口")
public abstract class OssService {
    /**
     * 生成预签名 URL
     *
     * @param bucketName
     * @param key
     * @param expiration
     * @return
     */
    public abstract String presignedUrl(String bucketName, String key, Duration expiration);

    /**
     * 使用 默认桶（第一个） 生成预签名 URL
     *
     * @param key
     * @param expiration
     * @return
     */
    public abstract String presignedUrl(String key, Duration expiration);

    public abstract String presignedUrl(String bucketName, String key, Duration expiration, String contentType);

    public abstract String presignedUrl(String key, Duration expiration, String contentType);

    /**
     * 获取 CDN URL
     *
     * @param key
     * @return
     */
    public abstract String getCdnUrl(String key);

    /**
     * 上传图片文件
     * 路径 + 名称
     *
     * @return
     */
    public abstract String putCdnImg(String key, BufferedImage image);

    /**
     * 上传文档文件
     */
    public abstract String putFile(String key, File file);

    /**
     * 判断文件是否存在
     * @param key
     * @return
     */
    public abstract Boolean isExist(String key);

    /**
     * 设置标签
     *
     * @param key
     * @param tags
     */
    public abstract void setTagList(String key, String... tags);

    /**
     * 删除上传的文件
     */
    public abstract void delete(String key);

    /**
     * 获取压缩的url图片
     *
     * @param url
     * @return
     */
    public abstract String getResizeUrl(String url);

    /**
     * 生成指定格式的url
     *
     * @param url
     * @param format
     * @return
     */
    public abstract String getFormat(String url, String format);


    /**
     * 列举指定前缀的文件
     */
    public abstract List<String> listObjects(String path);

    /**
     * 下载文件到本地
     *
     * @param key      不包含Bucket名称在内的Object完整路径
     * @param pathName 下载到本地的完整路径
     */
    public abstract void download(String key, String pathName);

    /**
     * 重命名文件
     *
     * @param sourceKey      源文件
     * @param destinationKey 目标文件
     */
    public abstract void renameFile(String sourceKey, String destinationKey);
}

