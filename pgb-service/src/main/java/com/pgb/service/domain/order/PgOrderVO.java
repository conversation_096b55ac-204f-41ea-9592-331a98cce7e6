package com.pgb.service.domain.order;

import java.io.Serializable;

import java.util.Date;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.ToString;
import lombok.Builder;

/**
* 订单系统
* @TableName pg_order
*/
@Schema(description = "订单系统 VO")
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode
@ToString
@Builder
public class PgOrderVO implements Serializable {

    @Schema(title = "订单系统", type = "string")
    private Long id;

    @Schema(title = "全系统唯一订单号，字符串")
    private String orderNo;

    @Schema(title = "sku 信息")
    private Object sku;

    @Schema(title = "金额，单位分")
    private Integer totalAmount;

    @Schema(title = "用户实际支付金额")
    private Integer payAmount;

    @Schema(title = "实际支付方式")
    private Integer payType;

    @Schema(title = "是否已支付")
    private Boolean isPay;

    @Schema(title = "是否已退款")
    private Boolean isRefund;

    @Schema(title = "是否免费获取")
    private Boolean isFree;

    @Schema(title = "0：系统付费订单，1：系统免费订单，2：兑换订单，3：渠道订单，4：导入订单")
    private Integer orderType;

    @Schema(title = "订单状态，0：订单生成，1：支付中，2：支付成功，3：支付失败，4：已撤销，5：退款中，6：已退款，7：订单关闭")
    private Integer status;

    @Schema(title = "下单时间")
    private Date createTime;

    @Schema(title = "付款时间")
    private Date payTime;

    @Schema(title = "订单过期时间")
    private Date expireTime;

    @Schema(title = "支付订单ip地址")
    private String clientIp;

    @Schema(title = "购买用户id，支持多租户模式")
    private Long userId;

    @Schema(title = "商品标题")
    private String title;

    @Schema(title = "商品描述")
    private String description;

    @Schema(title = "平台支付回调返回数据")
    private Object platformData;

    @Schema(title = "分销订单状态（0待处理，1已处理）")
    private Integer verifyStatus;


}
