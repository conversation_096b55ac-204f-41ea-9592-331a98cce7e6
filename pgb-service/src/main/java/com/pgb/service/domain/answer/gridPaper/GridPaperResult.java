package com.pgb.service.domain.answer.gridPaper;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Schema(title = "标准格子纸识别结果")
@Data
public class GridPaperResult {
    @Schema(title = "识别的学生名字")
    private String name;

    @Schema(title = "学号", description = "优先学号匹配")
    private String studentNo;

    @Schema(title = "当前是第几页", description = "如果是0，则没有页数")
    private Integer pageNo;

    @Schema(title = "原图内容")
    private String imgUrl;

    @Schema(title = "用户上传的图片内容", description = "同一份会自动合并，后面oss保留一个月会自动删除")
    private List<String> userImgList;

    @Schema(title = "分割的图片URL", description = "上传oss后保存为url")
    private List<String> splitImgUrlList;

    @Schema(title = "分割的图片", description = "模型返回base64格式的，不保存到数据库")
    private List<String> splitImgList;

    @Schema(title = "消耗的token")
    private Long aiTokens;

    @Schema(title = "成功匹配的学生id")
    private Long studentId;

    @Schema(title = "状态类型", description = "0：没有匹配到，1：正常上传，2：重复提交，跳过")
    private Integer status;

    public void addSplitImgUrl(String url) {
        if (ObjectUtil.isNull(this.splitImgUrlList)) {
            this.splitImgUrlList = new ArrayList<>();
        }

        this.splitImgUrlList.add(url);
    }
}
