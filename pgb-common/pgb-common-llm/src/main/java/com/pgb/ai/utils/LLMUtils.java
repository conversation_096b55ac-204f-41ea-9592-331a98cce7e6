package com.pgb.ai.utils;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;

public class LLMUtils {
    /**
     * 直接转换为 数组格式的
     * @param answer 字符串：[{},{}]
     * @return
     */
    public static JSONArray clearArray(String answer) {
        // 为空直接返回
        if (StrUtil.isBlank(answer)) {
            throw new RuntimeException("无效字符串");
        }

        // 清洗json
        int start = StrUtil.indexOf(answer, '[');
        int end = StrUtil.lastIndexOfIgnoreCase(answer, "]") + 1;
        String jsonStr = StrUtil.sub(answer, start, end);

        // 转化为json
        JSONArray array = JSONUtil.parseArray(jsonStr);

        if (ObjectUtil.isNull(array)) {
            throw new RuntimeException("格式化JSON数组异常");
        }

        return array;
    }

    public static JSONObject clearJson(String answer) {
        // 为空直接返回
        if (StrUtil.isBlank(answer)) {
            throw new RuntimeException("无效字符串");
        }

        // 清洗json
        int start = StrUtil.indexOf(answer, '{');
        int end = StrUtil.lastIndexOfIgnoreCase(answer, "}") + 1;
        String jsonStr = StrUtil.sub(answer, start, end);

        // 转化为json
        return JSONUtil.parseObj(jsonStr);
    }
}
