package com.pgb.service.domain.zc.common.textbook.dto;

import com.pgb.common.mybatis.global.PageQuery;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.Min;

/**
 * 单词分页查询DTO
 *
 * <AUTHOR>
 * Created by 2025/7/25 21:00
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Schema(description = "单词分页查询DTO")
public class WordPageDTO extends PageQuery {

    @Schema(description = "单元ID")
    @NotNull(message = "单元ID不能为空")
    private Long unitId;

    @Schema(description = "单词关键字")
    private String word;
}
