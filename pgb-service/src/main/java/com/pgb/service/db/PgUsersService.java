package com.pgb.service.db;

import com.baomidou.mybatisplus.extension.service.IService;
import com.pgb.common.satoken.LoginVO;
import com.pgb.service.domain.user.PgUsers;
import com.pgb.service.domain.user.PgUsersVO;
import com.pgb.service.enums.DeviceTypeEnum;
import me.chanjar.weixin.common.bean.WxOAuth2UserInfo;

/**
* <AUTHOR>
* @description 针对表【pg_users】的数据库操作Service
* @createDate 2024-06-19 13:37:53
*/
public interface PgUsersService extends IService<PgUsers> {

    /**
     * 根据手机号获取用户, 如果不存在则创建
     * @param phone
     * @return
     */
    PgUsers getOrCreateUserByPhone(String phone);

    /**
     * 通用登录成功
     * @param user
     * @return
     */
    LoginVO<PgUsersVO> loginSuccess(PgUsers user, DeviceTypeEnum deviceType);

    /**
     * 添加vip时间
     * @param duration
     */
    void addVipDay(Long userId, Integer duration);

    /**
     * 注册用户
     * @param phone
     * @param openId
     * @param unionId
     * @param wxMpUser
     * @return
     */
    PgUsers registerUser(String phone, String openId, String unionId, WxOAuth2UserInfo wxMpUser);

    /**
     * 根据openId获取用户
     * @param account
     * @return
     */
    PgUsers getByOpenId(String account);
}
