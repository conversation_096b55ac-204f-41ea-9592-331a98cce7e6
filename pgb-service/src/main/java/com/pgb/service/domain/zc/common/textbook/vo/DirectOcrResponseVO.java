package com.pgb.service.domain.zc.common.textbook.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 直接OCR识别响应VO
 *
 * <AUTHOR>
 * Created by 2025/7/25 21:00
 */
@Data
@Schema(description = "直接OCR识别响应VO")
public class DirectOcrResponseVO {

    @Schema(title = "识别出的单词列表")
    private List<String> words;

    @Schema(title = "整体识别置信度")
    private Double confidence;

    @Schema(title = "处理耗时（毫秒）")
    private Long processTime;

    @Schema(title = "图片的Base64数据URL")
    private String imageUrl;

    @Schema(title = "原始文件名")
    private String fileName;

    @Schema(title = "文件大小（字节）")
    private Long fileSize;

    @Schema(title = "单词详细信息数组")
    private List<WordDetail> details;

    @Data
    @Schema(description = "单词详细信息")
    public static class WordDetail {
        @Schema(title = "单词原文")
        private String word;

        @Schema(title = "中文翻译")
        private String translation;

        @Schema(title = "国际音标")
        private String phonetic;

        @Schema(title = "词性缩写")
        private String partOfSpeech;
    }
}
