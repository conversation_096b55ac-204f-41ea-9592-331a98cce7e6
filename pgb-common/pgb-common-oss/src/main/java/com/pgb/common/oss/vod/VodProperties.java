package com.pgb.common.oss.vod;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

@Data
@AllArgsConstructor
@EqualsAndHashCode
@ToString
@Builder
@Schema(title = "云点播配置")
public class VodProperties {
    @Schema(title = "ACCESS_KEY")
    private String accessKey;

    @Schema(title = "SECRET_KEY")
    private String secretKey;

    @Schema(title = "应用ID")
    private String appId;

    @Schema(title = "播放域名")
    private String domain;

    @Schema(title = "事件回调接口")
    private String callbackUrl;
}
