package com.pgb.service.db;

import com.pgb.service.domain.chat.PgChat;
import com.pgb.service.domain.chat.msg.PgChatMsg;
import com.baomidou.mybatisplus.extension.service.IService;
import com.pgb.service.model.ChatFrom;

/**
* <AUTHOR>
* @description 针对表【pg_chat_msg(会话消息内容记录表)】的数据库操作Service
* @createDate 2025-02-08 18:47:02
*/
public interface PgChatMsgService extends IService<PgChatMsg> {

    /**
     * 获取下一个排序号
     *
     * @param chatId
     * @return
     */
    int getNextSortNumber(Long chatId);

}
