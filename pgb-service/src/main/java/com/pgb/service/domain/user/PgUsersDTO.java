package com.pgb.service.domain.user;

import java.io.Serializable;

import java.util.Date;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.ToString;
import lombok.Builder;
import org.springframework.validation.annotation.Validated;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import com.pgb.common.core.validate.UpdateGroup;

/**
*
* @TableName pg_users
*/
@Schema(description = " DTO")
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode
@ToString
@Validated
@Builder
public class PgUsersDTO implements Serializable {

    @Schema(title = "用户", type = "string")
    @NotNull(message="[用户]不能为空", groups = {UpdateGroup.class})
    private Long id;

    @Schema(title = "用户昵称")
    @Size(max= 255)
    private String nickName;

    @Schema(title = "头像")
    @Size(max= 255)
    private String avatarImgUrl;

    @Schema(title = "绑定手机号")
    private String phone;

    @Schema(title = "用户注册来源")
    private Integer userFrom;

    @Schema(title = "微信openId")
    @Size(max= 255)
    private String wxOpenId;

    @Schema(title = "微信unionId")
    @Size(max= -1)
    private String wxUnionId;

    @Schema(title = "创建时间")
    private Date createTime;

    @Schema(title = "上次登录时间")
    private Date lastLoginTime;

}
