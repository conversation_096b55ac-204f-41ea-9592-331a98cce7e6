package com.pgb.service.domain.userConfig;

import com.pgb.service.domain.userConfig.enums.ExportConfigItemEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * Created by 2024/10/8 18:00
 */
@Schema(title = "自定义导出配置 DTO")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ExportConfigDTO {

    @Schema(title = "分数", description = "是否导出分数")
    private Boolean score = true;

    @Schema(title = "分数样式", description = "0:评级,1:分数")
    private Integer scoreStyle = 0;

    @Schema(title = "旁批图片")
    private Boolean answerImg = true;

    @Schema(title = "老师总评")
    private Boolean overallComment = true;

    @Schema(title = "详细点评")
    private Boolean comment = true;

    @Schema(title = "润色")
    private Boolean polish = true;

    @Schema(title = "原文对比")
    private Boolean compare = true;

    @Schema(title = "写作指导")
    private Boolean writingGuide = true;

    @Schema(title = "题目相关信息")
    private Boolean questionInfo = true;

    @Schema(title = "是否导出至一个文档中", description = "0:压缩包（一篇一个），1：单个word")
    private Integer exportFileStyle = 0;

    @Schema(title = "导出原文对比样式", description = "0：普通导出，1：分栏导出")
    private Integer polishCompareStyle = 0;

    @Schema(title = "润色幅度", description = "1：低，2：中，3：高")
    private Integer polishRange = 2;

    @Schema(title = "评分标准", description = "30分制，50分制，60分制，百分制，自定义")
    private Double scoreStandard = 30.;

    @Schema(title = "导出内容顺序",
            description = "这里注意，使用字符串映射，防止修改字段名导致崩溃，在程序中手动映射key")
    private List<String> sortList;

    @Schema(title = "是否导出作文报告标题")
    private Boolean reportTitle = true;

}
