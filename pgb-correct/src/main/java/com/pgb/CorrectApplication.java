package com.pgb;

import com.pgb.correct.script.DbClearBoot;
import com.pgb.correct.script.ImgClearBootTest;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.ConfigurableApplicationContext;

@SpringBootApplication
@MapperScan("com.pgb.**.mapper")
public class CorrectApplication {

    public static void main(String[] args) {
        ConfigurableApplicationContext context = SpringApplication.run(CorrectApplication.class, args);

        // 手动执行迁移逻辑
        // ImgClearBootTest test = context.getBean(ImgClearBootTest.class);
        // test.answerMove();

        // 批改结果迁移
         DbClearBoot dbClearBoot = context.getBean(DbClearBoot.class);
         dbClearBoot.correctResultMove();
        //
        // 关闭上下文
         context.close();
    }

}
