package com.pgb.service.domain.answer.model;

import java.io.Serializable;

import java.util.Date;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.ToString;
import lombok.Builder;
import org.springframework.validation.annotation.Validated;
import jakarta.validation.constraints.Size;

/**
 * 范文表
 *
 * @TableName pg_answer_model
 */
@Schema(description = "范文表 DTO")
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode
@ToString
@Validated
@Builder
public class PgAnswerModelDTO implements Serializable {

    @Schema(title = "id")
    private Long id;

    @Schema(title = "关联范文id")
    private Long answerId;

    @Schema(title = "评语（如：被选为范文的原因）")
    private String comment;

    @Schema(title = "创建时间")
    private Date createTime;

    @Schema(title = "所属作业id")
    private Long homeworkId;

    @Schema(title = "所属用户id")
    private Long userId;

    @Schema(title = "排序")
    private Integer sort;

}
