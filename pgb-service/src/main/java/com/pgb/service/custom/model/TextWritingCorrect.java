package com.pgb.service.custom.model;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import com.github.difflib.DiffUtils;
import com.github.difflib.patch.DeltaType;
import com.github.difflib.patch.Patch;
import com.pgb.ai.LLMService;
import com.pgb.ai.domain.GPTAnswer;
import com.pgb.common.ocr.OCRService;
import com.pgb.common.ocr.domain.OCRChars;
import com.pgb.common.ocr.domain.OCRLocation;
import com.pgb.common.ocr.domain.OCRResult;
import com.pgb.common.ocr.domain.OCRWordResult;
import com.pgb.service.custom.model.util.NlpUtil;
import com.pgb.service.custom.model.util.OcrUtil;
import com.pgb.service.domain.common.image.FilePaperImg;
import com.pgb.service.domain.zc.answer.PgZcAnswer;
import com.pgb.service.domain.zc.common.MarkLoc;
import com.pgb.service.domain.zc.common.ZcChar;
import com.pgb.service.domain.zc.common.ZcLocation;
import com.pgb.service.domain.zc.question.PgZcQuestion;
import com.pgb.service.domain.zc.question.chinese.ZcSubmitForm;
import com.pgb.service.domain.zc.question.chinese.dictation.ZcDictation;
import com.pgb.service.domain.zc.question.chinese.pinyinAndWord.PinyinAndWordResult;
import com.pgb.service.domain.zc.word.WordItem;
import com.pgb.service.factory.LLMServiceFactory;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * Created by 2025/6/18 18:55
 */
@Component
@RequiredArgsConstructor
public class TextWritingCorrect {

    private static final Logger log = LoggerFactory.getLogger(TextWritingCorrect.class);
    private final OCRService ocrService;

    private final LLMServiceFactory llmServiceFactory;

    private LLMService getLLMService() {
        return llmServiceFactory.getLLMService("ali");
    }


    public PinyinAndWordResult correct(PgZcAnswer zcAnswer) {

        // 初始化批改结果
        PinyinAndWordResult result = new PinyinAndWordResult();

        // 用户答案
        ZcSubmitForm form = JSONUtil.toBean(JSONUtil.toJsonStr(zcAnswer.getAnswer()), ZcSubmitForm.class);

        // 获取题目json
        String questionJsonUrl = form.getQuestionJsonUrl();

        String jsonContent = HttpUtil.get(questionJsonUrl);

        PgZcQuestion question = JSONUtil.toBean(jsonContent, PgZcQuestion.class);

        ZcDictation zcDictation = JSONUtil.toBean(JSONUtil.toJsonStr(question.getContentJson()), ZcDictation.class);

        String text = zcDictation.getText();
        if (StrUtil.isNotBlank(text)) {

            // 将最终结果封装为嵌套列表以匹配接口定义
            List<List<WordItem>> wordList = new ArrayList<>();

            // ocr识别用户答案
            for (int i = 0; i < form.getUserImgAnswerList().size(); i++) {
                FilePaperImg userImg = form.getUserImgAnswerList().get(i);

                OCRResult ocrResult = ocrService.handWriting(userImg.getImgUrl());
                List<OCRWordResult> userAnswers = ocrResult.getWords_result();

                // 匹配 OCR 内容并设置用户作答信息
                List<WordItem> wordItems = llmCorrect(text, userAnswers, i);
                wordList.add(wordItems);
            }

            result.setUserImgAnswerList(form.getUserImgAnswerList());
            result.setUserWordList(wordList);

            zcAnswer.setCorrectResult(result);
        }
        return result;
    }

    /**
     * 使用大模型进行批改，返回正误情况
     *
     * @param answer
     * @param userOcr
     * @param pageNo
     * @return
     */
    private List<WordItem> llmCorrect(String answer, List<OCRWordResult> userOcr, Integer pageNo) {

        // 构建返回参数
        List<WordItem> wordItemList = new ArrayList<>();

        // 构建 用户ocr字符串
        StringBuilder userStrBuilder = new StringBuilder();
        List<OCRChars> userOcrCharList = new ArrayList<>();
        List<String> userOcrCharStrList = new ArrayList<>();

        for (OCRWordResult wordResult : userOcr) {

            userStrBuilder.append(
                    wordResult.getWords()
            ).append(" ");

            userOcrCharList.addAll(wordResult.getChars());

            userOcrCharStrList.addAll(
                    wordResult.getChars().stream().map(OCRChars::getChars).toList()
            );
        }

        String prompt = """
                你需要对提供的学生作答内容（OCR后），与标准答案（包含所有内容）对比，进行语文课文默写句子批改，请严格按单句批改。
                批改规则如下：
                   - 忽略所有空格和标点符号的差异，不视为错误；
                   - 只判断错别字（汉字错误）、多字、少字情况。
                   - 错别字必须严格按单字返回，格式示例：[像][盒]。
                   - 返回的错别字是用户作答内容里面的,，不要返回正确答案
                   - 每行为一个课文单句批改情况
                                
                【返回要求，请严格遵守】
                - 返回格式：答案中的课文单个句子[#号]学生默写的课文单个句子（即使内容是错误的，如果没写则占位“空”）[#号]正确/错误[#号]用户答案中的错别字（仅汉字,格式实例：[字1],[字2]）
                - 若完全正确则无需返回错别字部分
                           
                【示例】
                所谓伊人，在水一方。#所谓伊人，在 水一方。#正确
                远上寒山石径斜#远 下寒山石经斜#错误#[下],[经]
                白云生处有人家#白云生处有人家#正确
                停车坐爱枫林晚#空#错误
                """;
        List<String> userList = new ArrayList<>();
        userList.add(
                StrUtil.format("""
                        这是需要默写的正确答案：
                        {}
                        """, answer)
        );

        userList.add(StrUtil.format("""
                这是学生默写后的内容（OCR识别后）：
                {}
                """, userStrBuilder.toString())
        );

        LLMService llmService = getLLMService();
        GPTAnswer llmAnswer = llmService.chatComplete(
                prompt, userList, false, null
        );

        // 对 llmAnswer 进行处理，一行一个
        List<String> stringList = StrUtil.split(llmAnswer.getAnswer(), "\n");
        int lastFindIndex = 0;

        // 定义 used 数组用于记录哪些 standardItemList 项已被使用
//        boolean[] used = new boolean[standardItemList.size()];

        for (String s : stringList) {
            List<String> correctSplit = StrUtil.split(s, "#");

            if (correctSplit.size() < 3){
                continue;
            }

            // 正确单词
            String rightWord = correctSplit.get(0);
            if (StrUtil.isBlank(rightWord)) {
                continue;
            }

            // 用户答案
            String userAnswer = correctSplit.get(1);
            if (StrUtil.isBlank(userAnswer)) {
                continue;
            }

            // 正误
            String rightType = correctSplit.get(2);
            boolean right = rightType.contains("正确");

            // 构建标准答案与用户作答对比结果
            WordItem wordItem = new WordItem();

            // 标准答案
            wordItem.setWord(rightWord);

            // 找到符合的ocr字符串
            List<Integer> subIndex = NlpUtil.findChineseSubString(userOcrCharStrList, userAnswer, lastFindIndex);

            // 开始索引和结束索引
            Integer startIndex = -1;
            Integer endIndex = -1;

            if (subIndex.size() >= 2) {
                startIndex = subIndex.get(0);
                endIndex = subIndex.get(1);
            }

            // 说明找不到坐标
            if (startIndex == -1) {

                wordItem.setUserContent(userAnswer);
//                wordItem.setRightType(0);
                wordItemList.add(wordItem);
                continue;
            }

            lastFindIndex = NumberUtil.max(lastFindIndex, startIndex);

            // 找到了,设置用户作答对应坐标
            List<OCRChars> sub = CollUtil.sub(userOcrCharList, startIndex, endIndex + 1);
            ZcLocation markLocation = OcrUtil.areaLocation(sub, 2);
            ZcLocation userLocation = OcrUtil.areaLocation(sub, sub.size());

            // 设置用户作答对应坐标
            if (ObjectUtil.isNotNull(userLocation)) {
                userLocation.setPageNo(pageNo);
                wordItem.setLocation(userLocation);
            }

            wordItem.setUserContent(userAnswer);

            // 判断是否正确
//            wordItem.setRightType(right ? 1 : 2);

            // 处理标注位置：正确的 -> 在一句话的最后两个字符位置
            //            错误的 -> 标注字
            List<MarkLoc> marketLocList = new ArrayList<>();

            // 如果正确
            if (right) {
                if (ObjectUtil.isNotNull(markLocation)) {

                    MarkLoc markLoc = new MarkLoc();
                    markLocation.setPageNo(pageNo);
                    markLoc.setZcLocation(markLocation);
                    // 正确
                    markLoc.setRightType(1);
                    marketLocList.add(markLoc);
                }
            }

            // 处理错别字
            if (correctSplit.size() == 4) {
                // 错别字列表
                String errorInfo = correctSplit.get(3);
                // 解析错别字内容
                List<ZcChar> errorWords = getErrorWordList(errorInfo);

                // 查找错别字的坐标
                List<ZcLocation> errorWordLoc = getErrorWordLoc(errorWords, userOcrCharList, startIndex, endIndex);

                // 将错别字坐标添加到 wordItem 中
                for (int i = 0; i < errorWordLoc.size(); i++) {

                    ZcLocation errorLoc = errorWordLoc.get(i);

                    MarkLoc errorMarkLoc = new MarkLoc();
                    errorLoc.setPageNo(pageNo);
                    errorMarkLoc.setZcLocation(errorLoc);
                    // 错误
                    errorMarkLoc.setRightType(2);
                    // 错别字
                    errorMarkLoc.setErrorWord(errorWords.get(i).getChars());
                    marketLocList.add(errorMarkLoc);
                }
            }

            // 设置标注位置
            wordItem.setMarkLocation(marketLocList);
            wordItemList.add(wordItem);
        }
        return wordItemList;

    }


    /**
     * 处理大模型返回的错别字
     *
     * @param errorInfo
     * @return
     */
    private List<ZcChar> getErrorWordList(String errorInfo) {

        List<ZcChar> errorWords = new ArrayList<>();

        if (StrUtil.isBlank(errorInfo) || !errorInfo.contains("[")) {
            return errorWords;
        }
        // 使用正则表达式提取 [字] 格式
        Pattern pattern = Pattern.compile("\\[([^\\]]+)\\]");
        Matcher matcher = pattern.matcher(errorInfo);

        while (matcher.find()) {
            String word = matcher.group(1).trim();
            ZcChar item = new ZcChar();
            item.setChars(word);
            errorWords.add(item);
        }
        return errorWords;

    }


    /**
     * 查找错别字的坐标
     *
     * @param errorWords  错别字列表
     * @param ocrCharList OCR识别字符列表
     * @param startIdx    开始查找的索引位置
     * @param endIdx      结束查找的索引位置
     * @return 错别字坐标列表
     */
    private List<ZcLocation> getErrorWordLoc(List<ZcChar> errorWords, List<OCRChars> ocrCharList, int startIdx, int endIdx) {

        List<ZcLocation> errorLocations = new ArrayList<>();

        if (CollUtil.isEmpty(errorWords) || CollUtil.isEmpty(ocrCharList)) {
            return errorLocations;
        }

        // 确保endIdx不超过列表长度
        endIdx = Math.min(endIdx, ocrCharList.size() - 1);

        for (ZcChar errorWord : errorWords) {
            String targetChar = errorWord.getChars();
            if (StrUtil.isBlank(targetChar)) {
                continue;
            }

            // 在指定范围内查找匹配的字符
            for (int i = startIdx; i <= endIdx + 1; i++) {
                OCRChars ocrChar = ocrCharList.get(i);
                if (ocrChar != null &&
                        ocrChar.getLocation() != null &&
                        targetChar.equals(ocrChar.getChars())) {

                    ZcLocation location = new ZcLocation();
                    location.setLeft(ocrChar.getLocation().getLeft());
                    location.setTop(ocrChar.getLocation().getTop());
                    location.setWidth(ocrChar.getLocation().getWidth());
                    location.setHeight(ocrChar.getLocation().getHeight());
                    errorLocations.add(location);
                    break; // 找到第一个匹配项即可
                }
            }
        }
        return errorLocations;
    }


}
