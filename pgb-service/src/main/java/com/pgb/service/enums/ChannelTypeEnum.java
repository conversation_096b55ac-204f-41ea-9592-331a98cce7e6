package com.pgb.service.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;

@Schema(title = "渠道类型")
@AllArgsConstructor
public enum ChannelTypeEnum {

    PAY(0,"小程序付费开通"),

    SYSTEM(1,"后台开通"),

    XHS(2,"小红书"),

    ACTIVITY(3, "活动开通"),

    ACTIVITY_PRESENT(4, "活动免费赠送");

    /**
     * 枚举值对应的整数值
     */
    @EnumValue
    public final int value;

    /**
     * 文字说明
     */
    public final String desc;
}
