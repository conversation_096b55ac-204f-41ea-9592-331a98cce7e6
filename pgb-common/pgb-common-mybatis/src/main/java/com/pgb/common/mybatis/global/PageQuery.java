package com.pgb.common.mybatis.global;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Min;
import lombok.*;
import org.springframework.validation.annotation.Validated;

import java.io.Serializable;

/**
 * 分页基础类
 */
@Data
@Schema(title = "通用分页实体")
@Validated
@ToString
public class PageQuery implements Serializable {

    @Schema(title = "当前页数", requiredMode = Schema.RequiredMode.REQUIRED, defaultValue = "1")
    @Min(value = 1)
    private Long pageNo = 1L;

    @Schema(title = "当前页大小", requiredMode = Schema.RequiredMode.REQUIRED, defaultValue = "10")
    @Min(value = 1)
    private Long pageSize = 10L;

    @Schema(title = "排序字段")
    private String sortBy;

    @Schema(title = "是否正序", defaultValue = "true")
    private Boolean isAsc = true;

    public <T> Page<T> toMpPage(OrderItem... items) {
        // 构造分页条件
        Page<T> page = new Page<>(pageNo, pageSize);

        if (StrUtil.isNotBlank(sortBy)) {
            page.addOrder(new OrderItem().setColumn(sortBy).setAsc(isAsc));
        } else if (items != null) {
            page.addOrder(items);
        }

        return page;
    }

    public <T> Page<T> toMpPage(String defaultSortBy, Boolean defaultAsc) {
        return toMpPage(new OrderItem().setColumn(defaultSortBy).setAsc(defaultAsc));
    }

    public <T> Page<T> toMpPageSortByCreateTime() {
        return toMpPage(new OrderItem().setColumn("create_time").setAsc(false));
    }
}
