package com.pgb.service.domain.distribution;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 分销关系表
 * @TableName pg_distribution
 */
@TableName(value ="pg_distribution")
@Data
public class PgDistribution implements Serializable {
    /**
     * 分销 id
     */
    private Long id;

    /**
     * 邀请人id
     */
    private Long shareUserId;

    /**
     * 被邀请人 id
     */
    private Long userId;

    /**
     * 邀请是否成功
     */
    private Boolean isSuccess;

    /**
     * 邀请失败的原因
     */
    private String reason;

    /**
     * 创建时间
     */
    private Date createTime;

}
