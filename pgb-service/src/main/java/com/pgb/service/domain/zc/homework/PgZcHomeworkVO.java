package com.pgb.service.domain.zc.homework;

import java.io.Serializable;

import java.util.Date;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.ToString;
import lombok.Builder;

/**
* 字词作业表
* @TableName pg_zc_homework
*/
@Schema(description = "字词作业表 VO")
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode
@ToString
@Builder
public class PgZcHomeworkVO implements Serializable {

    @Schema(title = "字词作业id", type = "string")
    private Long id;

    @Schema(title = "用户id")
    private Long userId;

    @Schema(title = "作业名称")
    private String name;

    @Schema(title = "题目内容")
    private Object questionInfo;

    @Schema(title = "关联的字词题目id")
    private Long zcQuestionId;

    @Schema(title = "班级id")
    private Long classId;

    @Schema(title = "创建时间")
    private Date createTime;

    @Schema(title = "更新时间")
    private Date updateTime;

    @Schema(title = "所属班级")
    private String className;

    @Schema(title = "已提交人数")
    private Integer submitNum;

    @Schema(title = "未提交人数")
    private Integer unSubmitNum;

    @Schema(title = "Ai已批改数量")
    private Integer correctedNum;

}
