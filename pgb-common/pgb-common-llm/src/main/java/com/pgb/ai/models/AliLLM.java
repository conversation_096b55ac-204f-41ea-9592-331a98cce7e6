package com.pgb.ai.models;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.TimeInterval;
import cn.hutool.core.img.ImgUtil;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.dashscope.aigc.generation.Generation;
import com.alibaba.dashscope.aigc.generation.GenerationParam;
import com.alibaba.dashscope.aigc.generation.GenerationResult;
import com.alibaba.dashscope.aigc.multimodalconversation.MultiModalConversation;
import com.alibaba.dashscope.aigc.multimodalconversation.MultiModalConversationParam;
import com.alibaba.dashscope.aigc.multimodalconversation.MultiModalConversationResult;
import com.alibaba.dashscope.common.Message;
import com.alibaba.dashscope.common.MultiModalMessage;
import com.alibaba.dashscope.common.ResponseFormat;
import com.alibaba.dashscope.common.Role;
import com.alibaba.dashscope.exception.ApiException;
import com.alibaba.dashscope.exception.InputRequiredException;
import com.alibaba.dashscope.exception.NoApiKeyException;
import com.alibaba.dashscope.exception.UploadFileException;
import com.pgb.ai.LLMService;
import com.pgb.ai.domain.ChatRecord;
import com.pgb.ai.domain.ChatRes;
import com.pgb.ai.domain.GPTAnswer;
import com.pgb.ai.domain.agent.FileItem;
import com.pgb.common.core.utils.EnvUtils;
import io.reactivex.Flowable;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.awt.image.BufferedImage;
import java.io.IOException;
import java.time.Duration;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Consumer;

@Slf4j
public class AliLLM implements LLMService {

    private final String apiKey;

    public AliLLM(String apiKey) {
        this.apiKey = apiKey;
    }

    /**
     * @param system
     * @param recordList 包含最新当前用户对话
     * @param onNext
     * @param isReason   是否启用深度思考
     */
    private void stream(String system, List<ChatRecord> recordList, Consumer<GenerationResult> onNext, boolean isReason) {

        // 基础服务
        Generation gen = new Generation();

        // 初始化列表
        List<Message> msg = new ArrayList<>();

        // 系统设定
        Message systemMsg = Message.builder()
                .role(Role.SYSTEM.getValue())
                .content(ObjectUtil.defaultIfBlank(system, ""))
                .build();
        msg.add(systemMsg);

        // 历史会话
        for (ChatRecord record : recordList) {
            if (StrUtil.isNotBlank(record.getContent())) {
                Message history = Message.builder()
                        .role(record.getALiRoleName())
                        .content(record.getContent())
                        .build();
                msg.add(history);
            }
        }

        GenerationParam param = GenerationParam.builder()
                .apiKey(this.apiKey)
                .model(isReason ? "deepseek-r1" : "qwen-plus")
                .resultFormat(GenerationParam.ResultFormat.MESSAGE)
                .messages(msg)
                .temperature(1.2f)
                .enableSearch(false)
                .incrementalOutput(true)
                .build();

        // 获取数据
        try {
            Flowable<GenerationResult> result = gen.streamCall(param);
            StringBuilder content = new StringBuilder();
            result.blockingForEach(onNext::accept);
        } catch (NoApiKeyException | InputRequiredException e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public void chatStream(List<ChatRecord> recordList, SseEmitter emitter, StringBuilder resultBuilder, StringBuilder reasonBuilder, boolean isReason) {
        // 开始统计批改时长
        TimeInterval timer = DateUtil.timer();
        AtomicReference<Boolean> reasonStart = new AtomicReference<>(false);

        stream(
                "你是由“批改邦”公司开发的“AI教学助手”，是专门为老师打造的AI智能助理",
                recordList,
                chunk -> {
                    if (!EnvUtils.isProd()) {
                        log.info("模型生成：{}", JSONUtil.toJsonStr(chunk));
                    }
                    String reasoning = chunk.getOutput().getChoices().get(0).getMessage().getReasoningContent();
                    String content = chunk.getOutput().getChoices().get(0).getMessage().getContent();

                    try {
                        if (isReason) {
                            // 判断是否开始思考
                            if (StrUtil.isNotBlank(reasoning)) {
                                reasonStart.set(true);
                            } else {
                                // 如果开始思考了，但结束了
                                if (reasonStart.get()) {
                                    reasonStart.set(false);
                                    ChatRes res = new ChatRes();
                                    res.setType(ChatRes.ChatResType.MESSAGE);
                                    res.setCode(ChatRes.CharResCode.SUCCESS);
                                    // 计算时间
                                    res.setResult(String.valueOf(timer.intervalSecond()));
                                    res.setBotType(ChatRes.BotType.ReasonFinish);
                                    emitter.send(res.toJSON());
                                }
                            }
                        }

                        ChatRes res = new ChatRes();
                        // 内容
                        if (StrUtil.isNotEmpty(reasoning)) {
                            reasonBuilder.append(reasoning);
                            res.setResult(reasoning);
                        }
                        else {
                            resultBuilder.append(content);
                            res.setResult(content);
                        }

                        res.setBotType(StrUtil.isEmpty(reasoning) ? ChatRes.BotType.Bot : ChatRes.BotType.Reason);
                        emitter.send(res.toJSON());
                    } catch (IOException e) {
                        log.error("sse 发送失败：{}", e.getMessage(), e);
                    }
                },
                isReason
        );
    }

    @Override
    public void chatSocket(String answer, StringBuilder resultBuilder, StringBuilder reasonBuilder, Consumer<ChatRes> consumer, boolean isReason) {
        List<ChatRecord> recordList = new ArrayList<>();
        recordList.add(
                ChatRecord.builder()
                        .role(ChatRecord.Role.User)
                        .content(answer)
                        .build()
        );
        chatSocket(recordList, resultBuilder, reasonBuilder, consumer, isReason);
    }

    @Override
    public void chatSocket(List<ChatRecord> recordList, StringBuilder resultBuilder, StringBuilder reasonBuilder, Consumer<ChatRes> consumer, boolean isReason) {
        // 开始统计批改时长
        TimeInterval timer = DateUtil.timer();
        AtomicReference<Boolean> reasonStart = new AtomicReference<>(false);
        stream(
                "你是由“批改邦”公司开发的“AI教学助手”，是专门为老师打造的AI智能助理",
                recordList,
                chunk -> {
                    if (!EnvUtils.isProd()) {
                        log.info("模型生成：{}", JSONUtil.toJsonStr(chunk));
                    }
                    String reasoning = chunk.getOutput().getChoices().get(0).getMessage().getReasoningContent();
                    String content = chunk.getOutput().getChoices().get(0).getMessage().getContent();

                    if (isReason) {
                        // 判断是否开始思考
                        if (StrUtil.isNotBlank(reasoning)) {
                            reasonStart.set(true);
                        } else {
                            // 如果开始思考了，但结束了
                            if (reasonStart.get()) {
                                reasonStart.set(false);
                                ChatRes res = new ChatRes();
                                res.setType(ChatRes.ChatResType.MESSAGE);
                                res.setCode(ChatRes.CharResCode.SUCCESS);
                                // 计算时间
                                res.setResult(String.valueOf(timer.intervalSecond()));
                                res.setBotType(ChatRes.BotType.ReasonFinish);

                                consumer.accept(res);
                            }
                        }
                    }

                    ChatRes res = new ChatRes();
                    // 内容
                    if (StrUtil.isNotEmpty(reasoning)) {
                        reasonBuilder.append(reasoning);
                        res.setResult(reasoning);
                    }
                    else {
                        resultBuilder.append(content);
                        res.setResult(content);
                    }

                    res.setType(ChatRes.ChatResType.MESSAGE);
                    res.setCode(ChatRes.CharResCode.SUCCESS);
                    res.setBotType(StrUtil.isEmpty(reasoning) ? ChatRes.BotType.Bot : ChatRes.BotType.Reason);

                    consumer.accept(res);
                },
                isReason
        );
    }

    @Override
    public GPTAnswer vl(String imgUrl, String system, String user) {
        MultiModalConversation conv = new MultiModalConversation();
        MultiModalMessage systemMessage = MultiModalMessage.builder()
                .role(Role.SYSTEM.getValue()).content(
                        Arrays.asList(Collections.singletonMap("text", system))
                ).build();

        MultiModalMessage userMessage = MultiModalMessage.builder().role(Role.USER.getValue()).content(
                Arrays.asList(
                        Collections.singletonMap("image", imgUrl),
                        Collections.singletonMap("text", user)
                )
        ).build();

        MultiModalConversationParam param = MultiModalConversationParam.builder()
                .apiKey(apiKey)
                .model("qwen-vl-plus")
                .messages(Arrays.asList(systemMessage, userMessage))
                .build();
        try {
            MultiModalConversationResult result = conv.call(param);
            StringBuilder content = new StringBuilder();
            content.append(
                    result.getOutput().getChoices().get(0).getMessage().getContent().get(0).get("text")
            );
            return new GPTAnswer(content.toString(), result.getUsage().getInputTokens() + result.getUsage().getOutputTokens());
        } catch (ApiException e) {
            log.info("【GPT多模态访问异常】：{}", e.getMessage());
            if (e.getStatus().getStatusCode() == 500) {
                log.info("【GPT内部调用失败】");
                return new GPTAnswer("", 0, 2);
            }
            // 如果频率过高，等待一秒，继续
            else if (e.getStatus().getStatusCode() == 429 || e.getStatus().getMessage().equals("timeout")) {
                log.info("【GPT频率过高】...正在重试");
                ThreadUtil.sleep(Duration.ofSeconds(1).toMillis());
                // 重试机制
                return vl(imgUrl, system, user);
            } else if (e.getStatus().getStatusCode() == 400 && e.getStatus().getCode().equals("DataInspectionFailed")) {
                log.info("【GPT敏感词调用失败】");
                return new GPTAnswer("", 0, 1);
            }
            return new GPTAnswer("", 0, 2);
        } catch (NoApiKeyException | UploadFileException e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public GPTAnswer vl(BufferedImage image, String system, String user) {
        MultiModalConversation conv = new MultiModalConversation();
        MultiModalMessage systemMessage = MultiModalMessage.builder()
                .role(Role.SYSTEM.getValue()).content(
                        Arrays.asList(Collections.singletonMap("text", system))
                ).build();

        MultiModalMessage userMessage = MultiModalMessage.builder().role(Role.USER.getValue()).content(
                Arrays.asList(
                        Collections.singletonMap("image", ImgUtil.toBase64DataUri(image, ImgUtil.IMAGE_TYPE_JPG)),
                        Collections.singletonMap("text", user)
                )
        ).build();

        MultiModalConversationParam param = MultiModalConversationParam.builder()
                .apiKey(apiKey)
                .model("qwen-vl-max")
                .messages(Arrays.asList(systemMessage, userMessage))
                .build();
        try {
            MultiModalConversationResult result = conv.call(param);
            StringBuilder content = new StringBuilder();
            content.append(
                    result.getOutput().getChoices().get(0).getMessage().getContent().get(0).get("text")
            );
            return new GPTAnswer(content.toString(), result.getUsage().getInputTokens() + result.getUsage().getOutputTokens());
        } catch (ApiException e) {
            log.info("【GPT多模态访问异常】：{}", e.getMessage());
            if (e.getStatus().getStatusCode() == 500) {
                log.info("【GPT内部调用失败】");
                return new GPTAnswer("", 0, 2);
            }
            // 如果频率过高，等待一秒，继续
            else if (e.getStatus().getStatusCode() == 429 || e.getStatus().getMessage().equals("timeout")) {
                log.info("【GPT频率过高】...正在重试");
                ThreadUtil.sleep(Duration.ofSeconds(1).toMillis());
                // 重试机制
                return vl(image, system, user);
            } else if (e.getStatus().getStatusCode() == 400 && e.getStatus().getCode().equals("DataInspectionFailed")) {
                log.info("【GPT敏感词调用失败】");
                return new GPTAnswer("", 0, 1);
            }
            return new GPTAnswer("", 0, 2);
        } catch (NoApiKeyException | UploadFileException e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public GPTAnswer chatComplete(String system, List<String> userList, boolean isJson, Float temperature) {
        return chatComplete(system, userList, isJson, temperature, "qwen-plus");
    }

    @Override
    public GPTAnswer chatComplete(String system, List<String> userList, boolean isJson, Float temperature, String model) {
        try {

            // 基础服务
            Generation gen = new Generation();

            List<Message> msgList = new ArrayList<>();

            Message systemMsg = Message.builder()
                    .role(Role.SYSTEM.getValue())
                    .content(ObjectUtil.defaultIfBlank(system, ""))
                    .build();

            msgList.add(systemMsg);

            for (String user : userList) {
                Message userMsg = Message.builder()
                        .role(Role.USER.getValue())
                        .content(user)
                        .build();
                msgList.add(userMsg);
            }

            GenerationParam param = GenerationParam.builder()
                    .apiKey(apiKey)
                    .model(model)
                    .resultFormat(GenerationParam.ResultFormat.MESSAGE)
                    .messages(msgList)
                    .responseFormat(
                            ResponseFormat.builder()
                                    .type(isJson ? ResponseFormat.JSON_OBJECT : ResponseFormat.TEXT)
                                    .build()
                    )
                    .enableSearch(false)
                    .build();

            if (ObjectUtil.isNotNull(temperature)) {
                param.setTemperature(temperature);
            }

            // 获取数据
            GenerationResult result = gen.call(param);
            StringBuilder content = new StringBuilder();
            result.getOutput().getChoices().forEach(choice -> {
                content.append(choice.getMessage().getContent());
            });

            return new GPTAnswer(content.toString(), result.getUsage().getInputTokens() + result.getUsage().getOutputTokens(), result.getRequestId());
        } catch (ApiException e) {
            log.info("【GPT访问异常】：{}", e.getMessage());

            if (e.getStatus().getStatusCode() == 500) {
                log.info("【GPT内部调用失败】");
                return new GPTAnswer("", 0, 2);
            }
            // 如果频率过高，等待一秒，继续
            else if (e.getStatus().getStatusCode() == 429 || e.getStatus().getMessage().equals("timeout")) {
                log.info("【GPT频率过高】...正在重试");
                ThreadUtil.sleep(Duration.ofSeconds(1).toMillis());
                // 重试机制
                return chatComplete(system, userList, isJson, temperature);
            } else if (e.getStatus().getStatusCode() == 400 && e.getStatus().getCode().equals("DataInspectionFailed")) {
                log.info("【GPT敏感词调用失败】");
                return new GPTAnswer("", 0, 1);
            }

            return new GPTAnswer("", 0, 2);
        } catch (NoApiKeyException | InputRequiredException e) {
            log.info("【GPT异常】：{}", e.getMessage());
            return new GPTAnswer("", 0, 2);
        }
    }

    @Override
    public void chatSocket(List<List<FileItem>> fileList, List<ChatRecord> recordList, StringBuilder resultBuilder, StringBuilder reasonBuilder, Consumer<ChatRes> consumer, boolean isReason) {

    }

    @Override
    public void chatStream(List<List<FileItem>> fileList, List<ChatRecord> recordList, SseEmitter emitter, StringBuilder resultBuilder, StringBuilder reasonBuilder, boolean isReason) {

    }
}
