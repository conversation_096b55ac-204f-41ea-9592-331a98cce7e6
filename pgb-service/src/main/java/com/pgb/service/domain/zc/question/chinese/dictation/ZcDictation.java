package com.pgb.service.domain.zc.question.chinese.dictation;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.fasterxml.jackson.annotation.JsonGetter;
import com.pgb.service.domain.zc.common.ZcLocation;
import com.pgb.service.domain.zc.word.WordItem;
import com.pgb.service.enums.ZcQuestionTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@Data
public class ZcDictation {

    @Schema(title = "题目类型", description = "听写或默写")
    private ZcQuestionTypeEnum type;

    @Schema(title = "题目名称")
    private String name;

    @Schema(title = "听写的词语或者课文", description = "如果是词语听写，以空格分隔")
    private String text;

    @Schema(title = "学生书写文字")
    private List<String> userTextChars;

    @Schema(title = "学生文字坐标")
    private List<ZcLocation> userTextLocations;

    @Schema(title = "按单字返回的课文内容")
    @JsonGetter
    public List<String> getTextChars() {

        // 如果是默写
        if (type == ZcQuestionTypeEnum.TextWriting) {
            String[] textChars = ObjectUtil.defaultIfNull(this.text, "").split("");

            if (ObjectUtil.isEmpty(textChars)) {
                return new ArrayList<>();
            }

            // 返回非空格的内容 可变集合
            return new ArrayList<>(Arrays.stream(textChars).filter(item -> !StrUtil.isBlank(item)).toList());
        } else {
            return new ArrayList<>();
        }
    }

}

