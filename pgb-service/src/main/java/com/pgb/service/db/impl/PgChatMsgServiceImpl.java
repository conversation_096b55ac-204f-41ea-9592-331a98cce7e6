package com.pgb.service.db.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.pgb.service.domain.chat.PgChat;
import com.pgb.service.domain.chat.msg.PgChatMsg;
import com.pgb.service.db.PgChatMsgService;
import com.pgb.service.enums.RoleTypeEnum;
import com.pgb.service.mapper.PgChatMsgMapper;
import com.pgb.service.model.ChatFrom;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 针对表【pg_chat_msg(会话消息内容记录表)】的数据库操作Service实现
 * @createDate 2025-02-08 18:47:02
 */
@Service
public class PgChatMsgServiceImpl extends ServiceImpl<PgChatMsgMapper, PgChatMsg>
        implements PgChatMsgService {

    @Override
    public int getNextSortNumber(Long chatId) {
        PgChatMsg maxChatMsg = getOne(new LambdaQueryWrapper<PgChatMsg>()
                .eq(PgChatMsg::getChatId, chatId)
                .orderByDesc(PgChatMsg::getSort)
                .last("LIMIT 1"));
        return ObjectUtil.isNotNull(maxChatMsg) ? maxChatMsg.getSort() + 1 : 0;
    }
}




