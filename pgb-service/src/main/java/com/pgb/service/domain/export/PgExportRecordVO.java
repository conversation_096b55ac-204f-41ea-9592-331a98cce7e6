package com.pgb.service.domain.export;

import java.io.Serializable;

import java.util.Date;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.ToString;
import lombok.Builder;

/**
*
* @TableName pg_export_record
*/
@Schema(description = " VO")
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode
@ToString
@Builder
public class PgExportRecordVO implements Serializable {

    @Schema(title = "导出记录", type = "string")
    private Long id;

    @Schema(title = "用户id")
    private Long userId;

    @Schema(title = "导出篇数")
    private Integer totalNum;

    @Schema(title = "创建时间")
    private Date createTime;

    @Schema(title = "导出时间")
    private Date exportTime;

    @Schema(title = "导出状态")
    private Integer status;

    @Schema(title = "压缩包地址")
    private String zipUrl;

    @Schema(title = "导出md5")
    private String md5;

    @Schema(title = "导出id列表，用分号分隔")
    private String answerIds;

}
