package com.pgb.common.pay.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;

@Schema(description = "订单状态")
@AllArgsConstructor
public enum OrderStatusEnum {

    @Schema(title = "订单生成", description = "订单生成")
    Generate(0, "订单生成"),

    @Schema(title = "支付中", description = "订单生成")
    Paying(1, "支付中"),

    @Schema(title = "支付成功", description = "支付成功")
    PaySuccess(2, "支付成功"),

    @Schema(title = "支付失败", description = "支付失败")
    PayFail(3, "支付失败"),

    @Schema(title = "已撤销", description = "已撤销")
    Cancel(4, "已撤销"),

    @Schema(title = "退款中", description = "退款中")
    Refunding(5, "退款中"),

    @Schema(title = "已退款", description = "已退款")
    Refunded(6, "已退款"),

    @Schema(title = "订单关闭", description = "订单关闭")
    Close(7, "订单关闭");

    /**
     * 枚举值对应的整数值
     */
    @EnumValue
    public final int value;

    /**
     * 文字说明
     */
    public final String desc;
}
