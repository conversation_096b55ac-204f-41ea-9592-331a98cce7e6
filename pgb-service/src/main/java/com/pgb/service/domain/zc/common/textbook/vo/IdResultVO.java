package com.pgb.service.domain.zc.common.textbook.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * ID结果VO
 *
 * <AUTHOR>
 * Created by 2025/7/25 19:30
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "ID结果VO")
public class IdResultVO {

    @Schema(title = "ID")
    private Long id;
}
