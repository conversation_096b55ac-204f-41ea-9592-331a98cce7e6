package com.pgb.service.db.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.img.ImgUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.EnumUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.URLUtil;
import cn.hutool.json.JSONUtil;
import cn.hutool.poi.word.PicType;
import cn.hutool.poi.word.Word07Writer;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.pgb.service.db.*;
import com.pgb.service.domain.answer.PgAnswer;
import com.pgb.service.domain.classes.PgClasses;
import com.pgb.service.domain.common.fabric.FabricJson;
import com.pgb.service.domain.common.image.FilePaperImg;
import com.pgb.service.domain.export.PgExportRecord;
import com.pgb.service.domain.question.PgQuestion;
import com.pgb.service.domain.question.zwEssay.ZwEssayQuestion;
import com.pgb.service.domain.question.zwEssay.ZwTextDiff;
import com.pgb.service.domain.student.PgStudent;
import com.pgb.service.domain.userConfig.ExportConfigDTO;
import com.pgb.service.domain.userConfig.enums.ExportConfigItemEnum;
import com.pgb.service.domain.zc.answer.PgZcAnswer;
import com.pgb.service.domain.zc.common.MarkLoc;
import com.pgb.service.domain.zc.common.ZcChar;
import com.pgb.service.domain.zc.common.ZcLocation;
import com.pgb.service.domain.zc.question.chinese.pinyinAndWord.PinyinAndWordResult;
import com.pgb.service.domain.zc.word.WordItem;
import com.pgb.service.enums.ZcQuestionTypeEnum;
import com.pgb.service.mapper.PgExportRecordMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.pdmodel.PDPage;
import org.apache.pdfbox.pdmodel.PDPageContentStream;
import org.apache.pdfbox.pdmodel.common.PDRectangle;
import org.apache.pdfbox.pdmodel.graphics.image.LosslessFactory;
import org.apache.pdfbox.pdmodel.graphics.image.PDImageXObject;
import org.apache.poi.common.usermodel.PictureType;
import org.apache.poi.openxml4j.exceptions.InvalidFormatException;
import org.apache.poi.util.Units;
import org.apache.poi.xwpf.usermodel.*;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.*;
import org.springframework.stereotype.Service;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.geom.Rectangle2D;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.math.BigInteger;
import java.net.URL;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【pg_export_record】的数据库操作Service实现
 * @createDate 2024-09-25 19:34:27
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class PgExportRecordServiceImpl extends ServiceImpl<PgExportRecordMapper, PgExportRecord>
        implements PgExportRecordService {

    private final PgUserConfigService pgUserConfigService;

    private final PgStudentService pgStudentService;

    private final PgClassesService pgClassesService;

    private final PgAnswerService pgAnswerService;

    // 定义字体
    private final Font spaceFont = new Font("宋体", Font.PLAIN, 10);

    // 加粗
    private final Font boldFont = new Font("宋体", Font.BOLD, 15);

    // 标准
    private final Font STANDARD_FONT = new Font("宋体", Font.PLAIN, 12);

    // 段落缩进
    private final String INDENT = "    ";

    @Override
    public File getExportWord(PgAnswer answer, String fileName) {

        ZwEssayQuestion userAnswer = JSONUtil.toBean(answer.getCorrectResult().toString(), ZwEssayQuestion.class);

        // 获取用户导出配置
        ExportConfigDTO exportConfig = pgUserConfigService.getExportConfig(answer.getUserId());

        // 获取导出顺序
        List<String> contentSortStrList = exportConfig.getSortList();
        List<ExportConfigItemEnum> contentSortList = new ArrayList<>();

        // 如果 contentOrder 为空或为 null，使用默认顺序
        if (ObjectUtil.isNull(contentSortStrList) || contentSortStrList.isEmpty()) {
            contentSortList = List.of(ExportConfigItemEnum.values());
        } else {
            for (String key : contentSortStrList) {
                if (EnumUtil.getNames(ExportConfigItemEnum.class).contains(key)) {
                    contentSortList.add(ExportConfigItemEnum.valueOf(key));
                }
            }
        }

        // 初始化
        Word07Writer writer = new Word07Writer();

        // 设置页面边距：上、下、左、右，单位为二十分之一磅（1/20 point），约为0.05毫米
        CTPageMar ctPageMar = writer.getDoc().getDocument().getBody().addNewSectPr().addNewPgMar();
        ctPageMar.setTop(1000);
        ctPageMar.setBottom(1000);
        ctPageMar.setLeft(1000);
        ctPageMar.setRight(1000);

        // 添加【批改报告标题】
        addTitle(writer, exportConfig);

        // 添加【批改分数】
        addScore(userAnswer, exportConfig, writer, fileName);

        // 添加【学生信息】
        addStudentInfo(answer, writer);

        // 双直线--分割内容
        if (exportConfig.getScore() || exportConfig.getReportTitle() || ObjectUtil.isNotNull(answer.getStudentId()) || StrUtil.isNotBlank(answer.getName())) {
            XWPFParagraph linePara = writer.getDoc().createParagraph();
            linePara.setBorderTop(Borders.DOUBLE);
        }

        for (ExportConfigItemEnum content : contentSortList) {
            switch (content) {
                case questionInfo:
                    // 添加【题目信息】
                    addQuesInfo(userAnswer, exportConfig, writer);
                    break;
                case overallComment:
                    // 添加【老师总评】
                    addOverallComment(answer, exportConfig, writer);
                    break;
                case comment:
                    // 添加【详细点评】
                    addComment(answer, exportConfig, writer);
                    break;
                case answerImg:
                    // 添加【旁批】
                    addParagraphComment(userAnswer, exportConfig, writer, fileName);
                    break;
                case polish:
                    // 添加【作文润色】
                    addPolish(answer, exportConfig, writer);
                    break;
                case compare:
                    // 添加【原文对比】 分栏导出
                    if (exportConfig.getPolishCompareStyle().equals(1)) {
                        addCompareDoubleColumn(userAnswer, exportConfig, writer);
                    }
                    // 普通导出
                    else {
                        addPolishCompare(userAnswer, exportConfig, writer);
                    }
                    break;
                case writingGuide:
                    // 添加【写作指导】
                    addGuide(userAnswer, exportConfig, writer);
                    break;
                default:
                    log.warn("未知的内容类型: {}", content);
                    break;
            }
        }

        // 创建临时文件
        File tempFile = FileUtil.createTempFile(
                fileName,
                ".docx",
                false
        );
        writer.flush(tempFile);
        writer.close();

        return tempFile;
    }

    @Override
    public File getExportOneDoc(List<Long> answerIds, ExportConfigDTO exportConfig) {

        // 获取导出顺序
        List<String> contentSortStrList = exportConfig.getSortList();
        List<ExportConfigItemEnum> contentSortList = new ArrayList<>();

        // 如果 contentOrder 为空或为 null，使用默认顺序
        if (ObjectUtil.isNull(contentSortStrList) || contentSortStrList.isEmpty()) {
            contentSortList = List.of(ExportConfigItemEnum.values());
        } else {
            for (String key : contentSortStrList) {
                if (EnumUtil.getNames(ExportConfigItemEnum.class).contains(key)) {
                    contentSortList.add(ExportConfigItemEnum.valueOf(key));
                }
            }
        }

        // 初始化
        Word07Writer writer = new Word07Writer();

        // 设置页面边距：上、下、左、右，单位为二十分之一磅（1/20 point），约为0.05毫米
        CTPageMar ctPageMar = writer.getDoc().getDocument().getBody().addNewSectPr().addNewPgMar();
        ctPageMar.setTop(1000);
        ctPageMar.setBottom(1000);
        ctPageMar.setLeft(1000);
        ctPageMar.setRight(1000);

        String fileName = URLEncoder.encode(DateUtil.format(new Date(), "yyyy-MM-dd") + "总批改报告", StandardCharsets.UTF_8);

        for (int j = 0; j < answerIds.size(); j++) {

            log.info("正在导出answerId：{}", answerIds.get(j));

            // 获取已上传作业
            PgAnswer answer = pgAnswerService.getById(answerIds.get(j));

            ZwEssayQuestion userAnswer = JSONUtil.toBean(answer.getCorrectResult().toString(), ZwEssayQuestion.class);

            // 如果删除状态，跳过
            if (ObjectUtil.isNull(answer) || ObjectUtil.defaultIfNull(answer.getDeleted(), false)) {
                continue;
            }

            // 添加【批改报告标题】
            addTitle(writer, exportConfig);

            // 添加【批改分数】
            addScore(userAnswer, exportConfig, writer, fileName);

            // 添加【学生信息】
            addStudentInfo(answer, writer);

            // 双直线
            if (exportConfig.getScore() || exportConfig.getReportTitle() || ObjectUtil.isNotNull(answer.getStudentId()) || StrUtil.isNotBlank(answer.getName())) {
                XWPFParagraph linePara = writer.getDoc().createParagraph();
                linePara.setBorderTop(Borders.DOUBLE);
            }

            for (ExportConfigItemEnum content : contentSortList) {
                switch (content) {
                    case questionInfo:
                        // 添加【题目信息】
                        addQuesInfo(userAnswer, exportConfig, writer);
                        break;
                    case overallComment:
                        // 添加【老师总评】
                        addOverallComment(answer, exportConfig, writer);
                        break;
                    case comment:
                        // 添加【详细点评】
                        addComment(answer, exportConfig, writer);
                        break;
                    case answerImg:
                        // 添加【旁批】
                        addParagraphComment(userAnswer, exportConfig, writer, fileName);
                        break;
                    case polish:
                        // 添加【作文润色】
                        addPolish(answer, exportConfig, writer);
                        break;
                    case compare:
                        // 添加【原文对比】 分栏导出
                        if (exportConfig.getPolishCompareStyle().equals(1)) {
                            addCompareDoubleColumn(userAnswer, exportConfig, writer);
                        }
                        // 普通导出
                        else {
                            addPolishCompare(userAnswer, exportConfig, writer);
                        }
                        break;
                    case writingGuide:
                        // 添加【写作指导】
                        addGuide(userAnswer, exportConfig, writer);
                        break;
                    default:
                        log.warn("未知的内容类型: {}", content);
                        break;
                }
            }

            // 插入分页符 最后不用加
            if (j != answerIds.size() - 1) {
                writer.getDoc().createParagraph().createRun().addBreak(BreakType.PAGE);
            }
        }

        // 创建临时文件
        File tempFile = FileUtil.createTempFile(
                fileName,
                ".docx",
                false
        );
        writer.flush(tempFile);
        writer.close();

        return tempFile;
    }

    @Override
    public File getZcExportPdf(PgZcAnswer zcAnswer, String fileName) {

        PinyinAndWordResult result = JSONUtil.toBean(zcAnswer.getCorrectResult().toString(), PinyinAndWordResult.class);

        // 用户提交的图片
        List<FilePaperImg> userImgList = result.getUserImgAnswerList();

        // 正误标注图片
        String rightPath = "https://cdn-common.pigaibang.com/xcx/zc/common/image/right.png";
        String errorPath = "https://cdn-common.pigaibang.com/xcx/zc/common/image/error.png";

        List<BufferedImage> combinedImages = new ArrayList<>();
        try {

            for (int i = 0; i < userImgList.size(); i++) {

                FilePaperImg userImg = userImgList.get(i);

                // 下载用户上传图片
                BufferedImage userBufferedImage = downloadImage(userImg.getImgUrl());

                // 下载图标图片
                BufferedImage rightImg = downloadImage(rightPath);
                BufferedImage errorImg = downloadImage(errorPath);

                // 用于绘制合成图
                BufferedImage combinedImg = new BufferedImage(
                        userImg.getImgW(),
                        userImg.getImgH(),
                        BufferedImage.TYPE_INT_ARGB
                );

                // 初始化g2d
                Graphics2D g2d = combinedImg.createGraphics();
                // 绘制用户图片
                g2d.drawImage(userBufferedImage, 0, 0, null);

                // 正误信息
                if (i < result.getUserWordList().size()) {
                    List<WordItem> wordItems = result.getUserWordList().get(i);
                    for (WordItem wordItem : wordItems) {

                        // 拿标注位置
                        List<MarkLoc> markLocation = wordItem.getMarkLocation();

                        if (CollUtil.isEmpty(markLocation)) {
                            continue;
                        }

                        for (MarkLoc markLoc : markLocation) {
                            // 如果没有就跳过
                            if (ObjectUtil.isNull(markLoc)) {
                                continue;
                            }
                            ZcLocation zcLocation = markLoc.getZcLocation();

                            Float left = zcLocation.getLeft();
                            Float top = zcLocation.getTop();
                            Float width = zcLocation.getWidth();
                            Float height = zcLocation.getHeight();

                            // 如果宽或高有一个为0则跳过 不做标注
                            if (width == 0 || height == 0) {
                                continue;
                            }

                            // 正确图标
                            if (markLoc.getRightType().equals(1)) {
                                int iconSize = (int) (width / 1.5);
                                int x = (int) (left + width - iconSize + 10);
                                int y = (int) (top + height - iconSize);
                                // 缩放并绘制right图标
                                Image scaledRightImg = rightImg.getScaledInstance(iconSize, iconSize, Image.SCALE_SMOOTH);
                                g2d.drawImage(scaledRightImg, x, y, null);

                                rightImg.flush();
                                scaledRightImg.flush();
                            }
                            // 错误图标
                            else if (markLoc.getRightType().equals(2)) {
                                // 如果是默写类型，使用错别字坐标
                                Image scaledErrorImg = errorImg.getScaledInstance(
                                        width.intValue(),
                                        height.intValue(),
                                        Image.SCALE_SMOOTH
                                );
                                g2d.drawImage(
                                        scaledErrorImg,
                                        left.intValue(),
                                        top.intValue(),
                                        null
                                );
                                scaledErrorImg.flush();
                            }
                            errorImg.flush();
                        }
                    }
                }
                g2d.dispose();
                combinedImages.add(combinedImg);
            }

            // 初始化PDF文档
            PDDocument document = new PDDocument();

            for (BufferedImage image : combinedImages) {

                // 创建一个于图片尺寸匹配的PDF页面
                PDPage page = new PDPage(new PDRectangle(image.getWidth(), image.getHeight()));
                document.addPage(page);

                PDImageXObject pdfImage = LosslessFactory.createFromImage(document, image);

                // 将图片绘制到pdf页面  确保 ContentStream 正确关闭
                try (PDPageContentStream contentStream = new PDPageContentStream(document, page)) {
                    contentStream.drawImage(pdfImage, 0, 0, image.getWidth(), image.getHeight());
                }
            }

            // 创建临时文件
            File tempFile = FileUtil.createTempFile(
                    fileName,
                    ".pdf",
                    true
            );
            FileUtil.del(tempFile);

            document.save(tempFile);
            document.close();

            return tempFile;

        } catch (IOException e) {
            throw new RuntimeException(e);
        }

    }

    @Override
    public File getZcExportImage(PgZcAnswer zcAnswer, String fileName) {
        PinyinAndWordResult result = JSONUtil.toBean(zcAnswer.getCorrectResult().toString(), PinyinAndWordResult.class);

        // 用户提交的图片
        List<FilePaperImg> userImgList = result.getUserImgAnswerList();

        // 正误标注图片
        String rightPath = "https://cdn-common.pigaibang.com/xcx/zc/common/image/right.png";
        String errorPath = "https://cdn-common.pigaibang.com/xcx/zc/common/image/error.png";

        List<BufferedImage> combinedImages = new ArrayList<>();

        try {
            for (int i = 0; i < userImgList.size(); i++) {

                FilePaperImg userImg = userImgList.get(i);

                // 下载用户原始图片
                BufferedImage userBufferedImage = downloadImage(userImg.getImgUrl());

                // 下载图标图片
                BufferedImage rightImg = downloadImage(rightPath);
                BufferedImage errorImg = downloadImage(errorPath);

                // 创建合成图像
                BufferedImage combinedImg = new BufferedImage(
                        userImg.getImgW(),
                        userImg.getImgH(),
                        BufferedImage.TYPE_INT_ARGB
                );

                Graphics2D g2d = combinedImg.createGraphics();
                g2d.drawImage(userBufferedImage, 0, 0, null);

                // 仅当存在对应批改结果时添加标注
                // 获取正误信息并绘制图标
                if (i < result.getUserWordList().size()) {
                    List<WordItem> wordItems = result.getUserWordList().get(i);
                    for (WordItem wordItem : wordItems) {

                        // 拿标注位置
                        List<MarkLoc> markLocation = wordItem.getMarkLocation();

                        if (CollUtil.isEmpty(markLocation)) {
                            continue;
                        }
                        for (MarkLoc markLoc : markLocation) {
                            // 如果没有就跳过
                            if (ObjectUtil.isNull(markLoc)) {
                                continue;
                            }
                            ZcLocation zcLocation = markLoc.getZcLocation();

                            Float left = zcLocation.getLeft();
                            Float top = zcLocation.getTop();
                            Float width = zcLocation.getWidth();
                            Float height = zcLocation.getHeight();

                            // 如果宽或高有一个为0则跳过 不做标注
                            if (width == 0 || height == 0) {
                                continue;
                            }

                            // 正确图标
                            if (markLoc.getRightType().equals(1)) {
                                int iconSize = (int) (width / 1.5);
                                int x = (int) (left + width - iconSize + 10);
                                int y = (int) (top + height - iconSize);
                                // 缩放并绘制right图标
                                Image scaledRightImg = rightImg.getScaledInstance(iconSize, iconSize, Image.SCALE_SMOOTH);
                                g2d.drawImage(scaledRightImg, x, y, null);

                                rightImg.flush();
                                scaledRightImg.flush();
                            }
                            // 错误图标
                            else if (markLoc.getRightType().equals(2)) {
                                // 如果是默写类型，使用错别字坐标
                                Image scaledErrorImg = errorImg.getScaledInstance(
                                        width.intValue(),
                                        height.intValue(),
                                        Image.SCALE_SMOOTH
                                );
                                g2d.drawImage(
                                        scaledErrorImg,
                                        left.intValue(),
                                        top.intValue(),
                                        null
                                );
                                scaledErrorImg.flush();
                            }
                            errorImg.flush();
                        }
                    }
                }

                g2d.dispose();
                combinedImages.add(combinedImg);
            }

            // 合成多张图片为一张（可选）
            BufferedImage finalImage = combineImages(combinedImages);

            // 创建临时文件
            File tempFile = FileUtil.createTempFile(
                    fileName,
                    ".jpg",
                    true);
            FileUtil.del(tempFile);

            // 写入 JPEG 文件
            ImageIO.write(finalImage, "jpg", tempFile);

            return tempFile;

        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 合并图片（垂直合并）
     */
    private BufferedImage combineImages(List<BufferedImage> images) {
        int totalHeight = 0;
        int maxWidth = 0;

        for (BufferedImage img : images) {
            totalHeight += img.getHeight();
            maxWidth = Math.max(maxWidth, img.getWidth());
        }

        BufferedImage combined = new BufferedImage(maxWidth, totalHeight, BufferedImage.TYPE_INT_RGB);
        Graphics2D g2d = combined.createGraphics();

        int currentY = 0;
        for (BufferedImage img : images) {
            g2d.drawImage(img, 0, currentY, null);
            currentY += img.getHeight();
        }

        g2d.dispose();
        return combined;
    }


    /**
     * 下载图片
     *
     * @param imageUrl
     * @return
     * @throws IOException
     */
    private BufferedImage downloadImage(String imageUrl) throws IOException {
        URL url = new URL(imageUrl);
        return ImageIO.read(url);
    }


    // 添加 word 段落
    private void addWordParagraph(Word07Writer writer, Font font, String text) {
        XWPFParagraph paragraph = writer.getDoc().createParagraph();
        paragraph.setFirstLineIndent(480);
        XWPFRun run = paragraph.createRun();
        run.setText(text);
        run.setFontSize(font.getSize());
        run.setFontFamily(font.getFamily());
        run.setBold(font.isBold());
        run.setColor("000000");
    }

    // 添加【批改报告标题】
    private void addTitle(Word07Writer writer, ExportConfigDTO exportConfig) {

        if (exportConfig.getReportTitle().equals(true)) {

            // BufferedImage markImg = ImgUtil.read(
            //        URLUtil.url("https://cdn.pigaibang.com/common/xcx-zw/word/word_title.png")
            //);
            XWPFParagraph paragraph = writer.getDoc().createParagraph();
            XWPFRun run = paragraph.createRun();
            // try {
            //    run.addPicture(ImgUtil.toStream(markImg, ImgUtil.IMAGE_TYPE_PNG), PictureType.PNG, "title.png", Units.toEMU(30), Units.toEMU(30));
            //} catch (InvalidFormatException | IOException e) {
            //    throw new RuntimeException(e);
            //}
            run.setText("作文批改报告");
            run.setFontSize(20);
            run.setBold(true);
            run.setFontFamily("宋体");
            paragraph.setAlignment(ParagraphAlignment.CENTER);
        }
    }

    // 添加【批改分数】
    private void addScore(ZwEssayQuestion question, ExportConfigDTO exportConfig, Word07Writer writer, String fileName) {
        if (ObjectUtil.isNotNull(question.getUserScore()) && exportConfig.getScore().equals(true)) {

            Integer userScore = question.getUserScore();

            // 判断导出 评级 or 分数
            // 样式---分数
            if (exportConfig.getScoreStyle().equals(1)) {

                List<Integer> nums = new ArrayList<>();

                while (userScore >= 10) {
                    nums.add(
                            userScore % 10
                    );
                    userScore /= 10;
                }
                nums.add(userScore);

                XWPFParagraph scorePara = writer.getDoc().createParagraph();
                XWPFRun scoreRun = scorePara.createRun();
                // 最右侧
                scorePara.setAlignment(ParagraphAlignment.RIGHT);

                // 遍历nums 匹配 加数字图片 1.png 2.png
                for (Integer num : ListUtil.reverse(nums)) {
                    try {
                        // http://oss-bucket.pigaibang.com/xcx/score/1.png
                        BufferedImage image = ImgUtil.read(URLUtil.url("https://cdn.pigaibang.com/common/xcx-zw/score/" + num + ".png"));
                        scoreRun.addPicture(ImgUtil.toStream(image, ImgUtil.IMAGE_TYPE_PNG),
                                PictureType.PNG, fileName, Units.toEMU((double) (20 * image.getWidth()) / image.getHeight()), Units.toEMU(20));
                    } catch (InvalidFormatException | IOException e) {
                        throw new RuntimeException(e);
                    }
                }

                // 加空格
                scoreRun.setText(" ");

                // 分
                try {
                    scoreRun.addPicture(
                            ImgUtil.toStream(ImgUtil.read(URLUtil.url("https://cdn.pigaibang.com/common/xcx-zw/score/fen.png")), ImgUtil.IMAGE_TYPE_PNG),
                            PictureType.PNG, fileName, Units.toEMU(14), Units.toEMU(20));
                } catch (InvalidFormatException | IOException e) {
                    throw new RuntimeException(e);
                }

            }
            // 样式---评级
            else if (exportConfig.getScoreStyle().equals(0)) {
                // 默认为优
                String scoreUrl = StrUtil.format(
                        "https://cdn.pigaibang.com/common/xcx-zw/grade/{}.png",
                        question.getScoreLevel().toString()
                );

                writer.addPicture(
                        ImgUtil.toStream(ImgUtil.read(URLUtil.url(scoreUrl)), ImgUtil.IMAGE_TYPE_PNG),
                        PicType.PNG,
                        fileName,
                        question.getScoreLevel() <= 2 ? 28 : 50,
                        30,
                        ParagraphAlignment.RIGHT
                );
            }
        }
    }

    // 添加学生信息
    private void addStudentInfo(PgAnswer answer, Word07Writer writer) {
        // 班级学生信息
        if (ObjectUtil.isNotNull(answer.getStudentId())) {
            PgStudent student = pgStudentService.getById(answer.getStudentId());

            if (ObjectUtil.isNotNull(student)) {

                XWPFParagraph stuParagraph = writer.getDoc().createParagraph();
                XWPFRun stuRun = stuParagraph.createRun();
                stuRun.setFontFamily("宋体");
                stuRun.setFontSize(12);
                stuRun.setText("姓名：" + student.getName() + this.INDENT);

                // 学号
                if (StrUtil.isNotBlank(student.getStudentNo())) {
                    stuRun.setText("学号：" + student.getStudentNo() + this.INDENT);
                }

                // 班级
                if (ObjectUtil.isNotNull(student.getClassId())) {
                    PgClasses classes = pgClassesService.getById(student.getClassId());
                    if (ObjectUtil.isNotNull(classes)) {
                        if (StrUtil.isNotBlank(classes.getName())) {
                            stuRun.setText("班级：" + classes.getName() + this.INDENT);
                        }
                    }
                }
            }
        } else {
            // 【备注信息】
            if (StrUtil.isNotBlank(answer.getName())) {

                XWPFParagraph stuParagraph = writer.getDoc().createParagraph();
                XWPFRun stuRun = stuParagraph.createRun();
                stuRun.setFontFamily("宋体");
                stuRun.setFontSize(12);
                stuRun.setText("备注：" + answer.getName() + this.INDENT);
            }
        }
    }

    // 添加【题目信息】
    private void addQuesInfo(ZwEssayQuestion question, ExportConfigDTO exportConfig, Word07Writer writer) {
        // 获取关联题目相关
        PgQuestion require = question.getRequire();

        if (ObjectUtil.isNotNull(require) && exportConfig.getQuestionInfo().equals(true)) {
            // 题目
            if (ObjectUtil.isNotNull(require.getName())) {
                writer.addText(STANDARD_FONT, "题目：" + require.getName());
            }

            // 写作要求
            if (ObjectUtil.isNotNull(require.getWritingRequest())) {
                writer.addText(STANDARD_FONT, "写作要求：");
                List<String> split = StrUtil.split(require.getWritingRequest(), "\n");
                split.forEach(line -> {
                    addWordParagraph(writer, this.STANDARD_FONT, line);
                });
//                writer.addText(spaceFont, " ");
            }

            // 写作文体
            if (ObjectUtil.isNotNull(require.getStyle())) {
                writer.addText(STANDARD_FONT, "写作文体：" + require.getStyle().desc);
            }
        }

    }

    // 添加【旁批】
    private void addParagraphComment(ZwEssayQuestion question, ExportConfigDTO exportConfig, Word07Writer writer, String fileName) {
        // markJson
        List<FabricJson> markJsonList = question.getMarkJsonList();

        if (ObjectUtil.isNotNull(markJsonList) && exportConfig.getAnswerImg().equals(true)) {

            // 如果都不导出则不加分页符
            if (exportConfig.getQuestionInfo().equals(true) || exportConfig.getComment().equals(true)) {
                // 插入分页符
                writer.getDoc().createParagraph().createRun().addBreak(BreakType.PAGE);
            }

            // 【旁批】
            writer.addText(boldFont, "【旁批】");

            // 传 高 宽 markJson
            for (int i = 0; i < markJsonList.size(); i++) {
                String imgUrl = question.getUserImgAnswerList().get(i).getImgUrl();
                BufferedImage img;
                try {
                    img = ImgUtil.read(URLUtil.url(imgUrl));
                } catch (Exception e) {
                    log.info("导出word加载图片失败，使用格式转化：{}", imgUrl);
                    img = ImgUtil.read(URLUtil.url(imgUrl + "?x-oss-process=image/format,jpg"));
                }

                // 1300  1891
//                log.info("图片的宽：{},图片的高：{}", img.getWidth(), img.getHeight());
//                writer.addPicture(ImgUtil.toStream(img, ImgUtil.IMAGE_TYPE_PNG), PicType.PNG, fileName, 500, (int) ((500.0 / img.getWidth()) * 1650));
                if (img.getHeight() > 1650) {
                    writer.addPicture(ImgUtil.toStream(img, ImgUtil.IMAGE_TYPE_PNG), PicType.PNG, fileName, 420, (int) ((420.0 / img.getWidth()) * img.getHeight()));
                } else {
                    writer.addPicture(ImgUtil.toStream(img, ImgUtil.IMAGE_TYPE_PNG), PicType.PNG, fileName, 500, (int) ((500.0 / img.getWidth()) * img.getHeight()));
                }
            }
        }

//        if (exportConfig.getAnswerImg().equals(true)) {
//            // 插入分页符
//            writer.getDoc().createParagraph().createRun().addBreak(BreakType.PAGE);
//        }
    }

    // 添加【老师总评】
    private void addOverallComment(PgAnswer answer, ExportConfigDTO exportConfig, Word07Writer writer) {
        String overallComment = JSONUtil.parseObj(answer.getCorrectResult()).getStr("overallComment");

        if (StrUtil.isNotBlank(overallComment) && exportConfig.getOverallComment().equals(true)) {
            writer.addText(spaceFont, " ");
            writer.addText(boldFont, "【老师总评】");

            List<String> commentList = ListUtil.toList(overallComment.split("\n"));
            // 首段 首行缩进
            for (String line : commentList) {
                // 构造评语内容
                addWordParagraph(writer, this.STANDARD_FONT, line);
            }
        }
    }

    // 添加【详细点评】
    private void addComment(PgAnswer answer, ExportConfigDTO exportConfig, Word07Writer writer) {
        String comment = JSONUtil.parseObj(answer.getCorrectResult()).getStr("comment");

        if (StrUtil.isNotBlank(comment) && exportConfig.getComment().equals(true)) {
            writer.addText(spaceFont, " ");
            writer.addText(boldFont, "【详细点评】");

            List<String> commentList = ListUtil.toList(comment.split("\n"));
            // 首段 首行缩进
            for (String line : commentList) {
                // 直接过滤 纯换行
                if (StrUtil.length(line) == 1 && line.equals("\n")) {
                    continue;
                }

                line = line.replaceAll("\n", "");

                if (StrUtil.isBlank(line)) {
                    continue;
                }

                // 构造评语内容
                addWordParagraph(writer, this.STANDARD_FONT, line);
            }
        }
    }

    // 添加【作文润色】
    private void addPolish(PgAnswer answer, ExportConfigDTO exportConfig, Word07Writer writer) {
        String polish = JSONUtil.parseObj(answer.getCorrectResult()).getStr("polish");

        if (StrUtil.isNotBlank(polish) && exportConfig.getPolish().equals(true)) {

            writer.addText(boldFont, "【作文润色】");

            List<String> polishList = ListUtil.toList(polish.split("\n"));

            if (!polishList.isEmpty()) {

                // 第一段内容
                String first = polishList.get(0);

                if (first.length() > 20) {
                    // 正文
                    addWordParagraph(writer, this.STANDARD_FONT, first);
                } else {
                    // 作文标题
                    writer.addText(ParagraphAlignment.CENTER, new Font("宋体", Font.PLAIN, 13), first);
                }

                // 移除第一个
                polishList.remove(0);

                for (String line : polishList) {
                    // 直接过滤 纯换行
                    if (StrUtil.length(line) == 1 && line.equals("\n")) {
                        continue;
                    }

                    line = line.replaceAll("\n", "");

                    if (StrUtil.isBlank(line)) {
                        continue;
                    }

                    // 构造润色内容
                    addWordParagraph(writer, this.STANDARD_FONT, line);
                }
            }
        }
    }

    // 添加【润色对比】
    private void addPolishCompare(ZwEssayQuestion question, ExportConfigDTO exportConfig, Word07Writer writer) {
        if (CollUtil.isNotEmpty(question.getPolishDiffList()) && exportConfig.getCompare().equals(true)) {
            writer.addText(spaceFont, " ");
            writer.addText(boldFont, "【原文对比】");

            XWPFParagraph paragraph = writer.getDoc().createParagraph();
            paragraph.setFirstLineIndent(480);

            // 获取全文润色和作文的差异
            List<ZwTextDiff> polishDiffList = question.getPolishDiffList();

            for (ZwTextDiff diff : polishDiffList) {
                // 根据差异类型设置样式
                String color = "000000"; // 默认黑色
                boolean strikeThrough = false;
                String sentence = "";

                switch (diff.getType()) {
                    case DELETE:
                        color = "FF0000";
                        strikeThrough = true;
                        sentence = diff.getSource();
                        break;
                    case EQUAL:
                        color = "000000";
                        sentence = diff.getTarget();
                        break;
                    case INSERT, CHANGE:
                        color = "42A5F5";
                        sentence = diff.getTarget();
                        break;
                    default:
                        break;
                }
                // 处理文本中的匹配项
                // 创建一个新的 XWPFRun 来处理匹配的部分
                XWPFRun styledRun = paragraph.createRun();
                styledRun.setText(sentence);
                styledRun.setFontFamily(this.STANDARD_FONT.getFamily());
                styledRun.setColor(color);
                styledRun.setStrikeThrough(strikeThrough);
                styledRun.setFontSize(this.STANDARD_FONT.getSize());

                // 防止只有换行符的问题
                if (StrUtil.endWith(sentence, "\n") && StrUtil.length(sentence) > 1) {
                    // 清洗换行内容
//                    styledRun.setText(
//                            sentence.replaceAll("\n", "")
//                    );

                    // 换行，使用新的段落
                    paragraph = writer.getDoc().createParagraph();
                    paragraph.setFirstLineIndent(480);
                }
            }
        }
    }

    // 添加【写作指导】
    private void addGuide(ZwEssayQuestion question, ExportConfigDTO exportConfig, Word07Writer writer) {
        // 获取关联题目相关
        PgQuestion require = question.getRequire();

        // 当题目不为空且好标题，好词 好句 等其中任一项不为空时才显示”写作指导“
        if (ObjectUtil.isNotNull(require) &&
                exportConfig.getWritingGuide().equals(true) &&
                (StrUtil.isNotBlank(require.getHeadline()) ||
                        StrUtil.isNotBlank(require.getWord()) ||
                        StrUtil.isNotBlank(require.getOpening()) ||
                        StrUtil.isNotBlank(require.getEnding()))) {

            // 判断是否要加分页符
            // if (exportConfig.getQuestionInfo().equals(true) ||
            //        exportConfig.getComment().equals(true) ||
            //        exportConfig.getAnswerImg().equals(true) ||
            //        exportConfig.getPolish().equals(true) ||
            //        exportConfig.getCompare().equals(true)
            //) {
            //    // 插入分页符
            //    writer.getDoc().createParagraph().createRun().addBreak(BreakType.PAGE);
            //}

            writer.addText(ParagraphAlignment.CENTER,
                    new Font("宋体", Font.BOLD, 20),
                    ObjectUtil.isNotNull(require.getName()) ? "《" + require.getName() + "》写作指导" : "写作指导"
            );

            // 【好标题/选材】
            if (ObjectUtil.isNotNull(require.getHeadline())) {
                writer.addText(spaceFont, " ");
                writer.addText(boldFont, "【好标题/选材】");
                List<String> headlineList = ListUtil.toList(require.getHeadline().split("\n"));

                for (String headline : headlineList) {
                    // 构造好标题/选材内容
                    writer.addText(STANDARD_FONT, headline);
                }
            }

            // 【好词语】
            if (ObjectUtil.isNotNull(require.getWord())) {
                writer.addText(spaceFont, " ");
                writer.addText(boldFont, "【好词语】");
                List<String> wordList = ListUtil.toList(require.getWord().split("\n"));

                for (String word : wordList) {
                    // 构造好词语内容
                    writer.addText(STANDARD_FONT, word);
                }
            }

            // 【好开头】
            if (ObjectUtil.isNotNull(require.getOpening())) {
                writer.addText(spaceFont, " ");
                writer.addText(boldFont, "【好开头】");
                List<String> beginningList = ListUtil.toList(require.getOpening().split("\n"));

                for (String beginning : beginningList) {

                    if (!beginning.trim().isEmpty()) {
                        // 构造好开头内容
                        writer.addText(STANDARD_FONT, "⭐ " + beginning);
                    }
                }
            }

            // 【好结尾】
            if (ObjectUtil.isNotNull(require.getEnding())) {
                writer.addText(spaceFont, " ");
                writer.addText(boldFont, "【好结尾】");
                List<String> endingList = ListUtil.toList(require.getEnding().split("\n"));

                for (String ending : endingList) {

                    if (!ending.trim().isEmpty()) {
                        // 构造好结尾内容
                        writer.addText(STANDARD_FONT, "⭐ " + ending);
                    }
                }
            }
        }
    }

    // 添加【双栏润色对比】 左：原文，右：润色后
    private void addCompareDoubleColumn(ZwEssayQuestion question, ExportConfigDTO exportConfig, Word07Writer writer) {

        if (CollUtil.isNotEmpty(question.getPolishDiffList()) && exportConfig.getCompare().equals(true)) {
            writer.addText(spaceFont, " ");
            writer.addText(boldFont, "【原文对比】");

            // 创建一个表格，默认一行一列  两列
            XWPFTable table = writer.getDoc().createTable();

            // 设置表格边框颜色
            table.setBottomBorder(XWPFTable.XWPFBorderType.NONE, 0, 0, "");// 底部
            table.setTopBorder(XWPFTable.XWPFBorderType.NONE, 0, 0, "");// 顶部
            table.setLeftBorder(XWPFTable.XWPFBorderType.NONE, 0, 0, "");// 左
            table.setRightBorder(XWPFTable.XWPFBorderType.NONE, 0, 0, "");// 右
//        table.setInsideHBorder(XWPFTable.XWPFBorderType.NONE,0,5,"");// 内部水平边框
            table.setInsideVBorder(XWPFTable.XWPFBorderType.NONE, 0, 0, ""); // 内部垂直边框

            // 获取第一行
            XWPFTableRow row = table.getRow(0);

            // 确保第一行有两个单元格
            while (row.getTableCells().size() < 2) {
                row.createCell();
            }

            for (int i = 0; i < row.getTableCells().size(); i++) {
                XWPFTableCell cell = row.getCell(i);

                // 平分列 50%
                cell.setWidth("50%");

                // 创建或获取CTTcPr 用于设置单元格的各种属性，如宽度、边框、背景色等
                CTTcPr tcPr = cell.getCTTc().addNewTcPr();

                // 设置上边距
                CTTblWidth top = tcPr.addNewTcMar().addNewTop();
                top.setW(100); // 设置上边距

                CTTblWidth bottom = tcPr.addNewTcMar().addNewBottom();
                bottom.setW(100); // 设置下边距

                CTTblWidth left = tcPr.addNewTcMar().addNewLeft();
                left.setW(BigInteger.valueOf(100)); // 设置左边距

                CTTblWidth right = tcPr.addNewTcMar().addNewRight();
                right.setW(BigInteger.valueOf(100)); // 设置右边距
            }

            // 原文
            XWPFTableCell originalCell = row.getCell(0);
            // 润色对比
            XWPFTableCell polishCompareCell = row.getCell(1);

            // 初始化原文段落
            XWPFParagraph originalPara = originalCell.addParagraph();
            // 首段首行缩进
            originalPara.setFirstLineIndent(480);

            // 初始化润色对比段落
            XWPFParagraph comparePara = polishCompareCell.addParagraph();
            // 首段首行缩进
            comparePara.setFirstLineIndent(480);

            // 获取全文润色和作文的差异
            List<ZwTextDiff> polishDiffList = question.getPolishDiffList();

            for (ZwTextDiff diff : polishDiffList) {
                // 根据差异类型设置样式
                String color = "000000"; // 默认黑色
                boolean strikeThrough = false;
                String sentence = "";

                switch (diff.getType()) {
                    case DELETE:
                        color = "FF0000";
                        strikeThrough = true;
                        sentence = diff.getSource();
                        break;
                    case EQUAL:
                        color = "000000";
                        sentence = diff.getTarget(); // 原文
                        break;
                    case INSERT, CHANGE:
                        color = "42A5F5";
                        sentence = diff.getTarget(); // 修改后的内容
                        break;
                    default:
                        break;
                }

                // 设置修改后的内容单元格内容 不要删除的
                if (!diff.getType().equals(ZwTextDiff.ZwTextDiffTypeEnum.DELETE)) {

                    XWPFRun compareRun = comparePara.createRun();
                    compareRun.setText(sentence);
                    compareRun.setFontFamily(this.STANDARD_FONT.getFamily());
                    compareRun.setColor(color);
                    compareRun.setStrikeThrough(strikeThrough);
                    compareRun.setFontSize(this.STANDARD_FONT.getSize());
                }

                // 设置原文单元格内容
                if (diff.getType().equals(ZwTextDiff.ZwTextDiffTypeEnum.CHANGE) || diff.getType().equals(ZwTextDiff.ZwTextDiffTypeEnum.DELETE)) {
                    color = "f6810a";
                    strikeThrough = false;
                }
                XWPFRun originalRun = originalPara.createRun();
                originalRun.setText(diff.getSource());
                originalRun.setFontFamily(this.STANDARD_FONT.getFamily());
                originalRun.setColor(color);
                originalRun.setStrikeThrough(strikeThrough);
                originalRun.setFontSize(this.STANDARD_FONT.getSize());

                // 解决润色换行符的问题
                if (StrUtil.endWith(sentence, "\n") && StrUtil.length(sentence) > 1) {
                    comparePara = polishCompareCell.addParagraph();
                    comparePara.setFirstLineIndent(480);
                }
                // 解决原文换行符的问题
                if (StrUtil.endWith(diff.getSource(), "\n") && StrUtil.length(diff.getSource()) > 1) {
                    originalPara = originalCell.addParagraph();
                    originalPara.setFirstLineIndent(480);
                }
            }
        }
    }

}




