package com.pgb.xcx.controller.homework;

import cn.dev33.satoken.annotation.SaCheckLogin;
import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.pgb.common.core.global.BaseResult;
import com.pgb.common.core.global.CommonForm;
import com.pgb.common.core.global.GlobalCode;
import com.pgb.common.mybatis.global.PageQuery;
import com.pgb.service.db.PgAnswerModelService;
import com.pgb.service.db.PgAnswerService;
import com.pgb.service.db.PgStudentService;
import com.pgb.service.domain.answer.PgAnswer;
import com.pgb.service.domain.answer.PgAnswerDTO;
import com.pgb.service.domain.answer.PgAnswerVO;
import com.pgb.service.domain.answer.model.PgAnswerModel;
import com.pgb.service.domain.answer.model.PgAnswerModelVO;
import com.pgb.service.domain.answer.polish.PolishInfo;
import com.pgb.service.domain.query.AnswerQuery;
import com.pgb.service.domain.question.zwEssay.ZwEssayQuestion;
import com.pgb.service.domain.student.PgStudent;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * Created by 2025/3/17 18:07
 */
@Tag(name = "用户端/作业/范文管理")
@RestController("UserHomeworkModelController")
@Transactional(rollbackFor = Exception.class)
@RequestMapping("/user/homework/model")
@RequiredArgsConstructor
@Slf4j
public class ModelController {

    private final PgAnswerModelService pgAnswerModelService;

    private final PgAnswerService pgAnswerService;

    private final PgStudentService pgStudentService;

    @Operation(summary = "保存作业的范文列表")
    @PostMapping("saveList/{homeworkId}")
    @SaCheckLogin
    public BaseResult<Boolean> saveList(@PathVariable Long homeworkId, @RequestBody CommonForm<List<PgAnswerModel>> form) {
        List<PgAnswerModel> modelList = form.getData();

        // 删除
        pgAnswerModelService.remove(new LambdaQueryWrapper<PgAnswerModel>()
                .eq(PgAnswerModel::getUserId, StpUtil.getLoginIdAsLong())
                .eq(PgAnswerModel::getHomeworkId, homeworkId)
        );

        // 设置排序
        for (int i = 0; i < modelList.size(); i++) {
            PgAnswerModel model = modelList.get(i);
            model.setUserId(StpUtil.getLoginIdAsLong());
            model.setCreateTime(new Date());
            model.setSort(i); // 重新设置排序
        }

        // 保存
        pgAnswerModelService.saveBatch(modelList);
        return BaseResult.success(true);

    }

    @Operation(summary = "获取全部范文列表")
    @PostMapping("page")
    @SaCheckLogin
    public BaseResult<IPage<PgAnswerModelVO>> page(@RequestBody AnswerQuery query) {

        LambdaQueryWrapper<PgAnswerModel> wrapper = new LambdaQueryWrapper<PgAnswerModel>()
                .eq(PgAnswerModel::getUserId, StpUtil.getLoginIdAsLong());

        if (StrUtil.isNotBlank(query.getName())) {

            List<Long> answerIds = pgAnswerService.list(new LambdaQueryWrapper<PgAnswer>()
                            .like(PgAnswer::getName, query.getName()))
                    .stream()
                    .map(PgAnswer::getId)
                    .toList();

            if (!answerIds.isEmpty()) {
                wrapper.in(PgAnswerModel::getAnswerId, answerIds);
            } else {
                return BaseResult.success(new Page<>());
            }
        }

        // 获取标记为范文的answerIds
        IPage<PgAnswerModelVO> page = pgAnswerModelService.page(query.toMpPageSortByCreateTime(), wrapper
        ).convert(model -> {

            PgAnswerModelVO modelVO = BeanUtil.copyProperties(model, PgAnswerModelVO.class);

            PgAnswer answer = pgAnswerService.getById(model.getAnswerId());

            ZwEssayQuestion userAnswer = JSONUtil.toBean(answer.getCorrectResult().toString(), ZwEssayQuestion.class);

            // 用户图片
            modelVO.setUserImgAnswerList(userAnswer.getUserImgAnswerList());
            // 用户得分
            modelVO.setUserScore(userAnswer.getUserScore());
            // 作文提交时间
            modelVO.setZwCreateTime(answer.getCreateTime());

            // 学生信息 姓名、学号
            PgStudent student = pgStudentService.getById(answer.getStudentId());
            if (ObjectUtil.isNotNull(student)) {
                modelVO.setStudentName(student.getName());
                modelVO.setStudentNo(student.getStudentNo());
            }

            return modelVO;
        });

        return BaseResult.success(page);

    }


    @Operation(summary = "标记学生作业为范文")
    @PostMapping("save/{answerId}")
    @SaCheckLogin
    public BaseResult<Boolean> model(@PathVariable Long answerId) {

        PgAnswer answer = pgAnswerService.getById(answerId);

        if (ObjectUtil.isNull(answer)) {
            return BaseResult.code(GlobalCode.Item_Null);
        }

        // 增加范文关联表
        if (!pgAnswerModelService.exists(new LambdaQueryWrapper<PgAnswerModel>()
                .eq(PgAnswerModel::getAnswerId, answerId)
        )) {
            PgAnswerModel model = new PgAnswerModel();
            model.setAnswerId(answerId);

            if (ObjectUtil.isNotNull(answer.getHomeworkId())) {
                model.setHomeworkId(answer.getHomeworkId());
            }
            model.setCreateTime(new Date());
            model.setUserId(StpUtil.getLoginIdAsLong());

            // 排序
            long sort = pgAnswerModelService.count(new LambdaQueryWrapper<PgAnswerModel>()
                    .eq(PgAnswerModel::getUserId, StpUtil.getLoginIdAsLong())
                    .eq(PgAnswerModel::getHomeworkId, answer.getHomeworkId()));
            model.setSort((int) sort);

            pgAnswerModelService.save(model);
        }


        return BaseResult.success(true);
    }

    @Operation(summary = "取消范文")
    @PostMapping("cancel/{answerId}")
    @SaCheckLogin
    public BaseResult<Boolean> cancelModel(@PathVariable Long answerId) {

        PgAnswerModel model = pgAnswerModelService.getOne(new LambdaQueryWrapper<PgAnswerModel>()
                .eq(PgAnswerModel::getAnswerId, answerId)
                .last("LIMIT 1")
        );

        if (ObjectUtil.isNull(model)) {
            return BaseResult.error("当前作业不是范文！");
        }

        pgAnswerModelService.removeById(model.getId());

        return BaseResult.success(true);
    }

    @Operation(summary = "某作业下的范文列表")
    @PostMapping("listByHomeworkId/{homeworkId}")
    @SaCheckLogin
    public BaseResult<List<PgAnswerModelVO>> listByHomeworkId(@PathVariable Long homeworkId) {

        // 获取标记为范文的answerIds
        List<PgAnswerModelVO> list = pgAnswerModelService.list(new LambdaQueryWrapper<PgAnswerModel>()
                        .eq(PgAnswerModel::getHomeworkId, homeworkId)
                        .orderByAsc(PgAnswerModel::getSort)
                ).stream()
                .map(model -> {

                    PgAnswerModelVO modelVO = BeanUtil.copyProperties(model, PgAnswerModelVO.class);

                    PgAnswer answer = pgAnswerService.getById(model.getAnswerId());
                    ZwEssayQuestion userAnswer = JSONUtil.toBean(answer.getCorrectResult().toString(), ZwEssayQuestion.class);

                    // 用户图片
                    modelVO.setUserImgAnswerList(userAnswer.getUserImgAnswerList());
                    // 用户得分
                    modelVO.setUserScore(userAnswer.getUserScore());
                    // 作文提交时间
                    modelVO.setZwCreateTime(answer.getCreateTime());

                    // 学生信息
                    PgStudent student = pgStudentService.getById(answer.getStudentId());
                    if (ObjectUtil.isNotNull(student)) {
                        modelVO.setStudentName(student.getName());
                        modelVO.setStudentNo(student.getStudentNo());
                    }

                    return modelVO;
                })
                .toList();

        return BaseResult.success(list);
    }


    /**
     * 点评内容
     *
     * @param comment
     */
    public record ModelCommentData(String comment) {
    }

    @Operation(summary = "点评范文")
    @PostMapping("comment/{answerId}")
    @SaCheckLogin
    public BaseResult<Boolean> comment(@PathVariable Long answerId, @RequestBody ModelCommentData data) {

        PgAnswer answer = pgAnswerService.getById(answerId);

        if (ObjectUtil.isNull(answer)) {
            return BaseResult.code(GlobalCode.Item_Null);
        }

        PgAnswerModel model = pgAnswerModelService.getOne(new LambdaQueryWrapper<PgAnswerModel>()
                .eq(PgAnswerModel::getAnswerId, answerId)
                .last("LIMIT 1")
        );

        // 若没有则创建
        if (ObjectUtil.isNull(model)) {
            model = new PgAnswerModel();
            model.setAnswerId(answerId);

            if (ObjectUtil.isNotNull(answer.getHomeworkId())) {
                model.setHomeworkId(answer.getHomeworkId());
            }
            model.setCreateTime(new Date());
            model.setUserId(StpUtil.getLoginIdAsLong());

            // 排序
            long sort = pgAnswerModelService.count(new LambdaQueryWrapper<PgAnswerModel>()
                    .eq(PgAnswerModel::getUserId, StpUtil.getLoginIdAsLong())
                    .eq(PgAnswerModel::getHomeworkId, answer.getHomeworkId()));
            model.setSort((int) sort);

            pgAnswerModelService.save(model);
        }

        // 保存评语
        model.setComment(data.comment());

        return BaseResult.success(
                pgAnswerModelService.updateById(model)
        );
    }

}
