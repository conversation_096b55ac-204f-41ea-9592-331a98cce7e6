package com.pgb.service.domain.activity;

import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import com.fasterxml.jackson.annotation.JsonGetter;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

@Data
@Schema(title = "单个活动购买记录")
public class ActivityBuyRecord {
    @Schema(title = "购买时间")
    private Date buyTime;

    @Schema(title = "购买金额")
    private Integer price;

    @Schema(title = "兑换码")
    private String code;

    @JsonGetter
    public String getYuan() {
        return NumberUtil.decimalFormatMoney(ObjectUtil.defaultIfNull(this.price, 0) / 100.0);
    }
}
