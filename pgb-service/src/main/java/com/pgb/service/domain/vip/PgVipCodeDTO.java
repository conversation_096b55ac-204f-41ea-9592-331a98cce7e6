package com.pgb.service.domain.vip;

import java.io.Serializable;

import java.util.Date;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.ToString;
import org.springframework.validation.annotation.Validated;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;

/**
*
* @TableName pg_vip_code
*/
@Schema(description = " DTO")
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode
@ToString
@Validated
public class PgVipCodeDTO implements Serializable {

    @Schema(title = "用户兑换码")
    @NotNull(message="[用户兑换码]不能为空")
    private Long id;

    @Schema(title = "兑换码")
    @Size(max= 255)
    private String code;

    @Schema(title = "使用状态，0：未使用，1:已使用，2：已作废")
    private Integer status;

    @Schema(title = "创建时间")
    private Date createTime;

    @Schema(title = "兑换时间")
    private Date exchangeTime;

    @Schema(title = "兑换用户id")
    private Long userId;

    @Schema(title = "生成渠道，0：系统生成，1：小红书")
    private Integer channelType;

    @Schema(title = "会员类型")
    private Integer vipType;

    @Schema(title = "如果有渠道id")
    private Long channelId;

}
