package com.pgb.student.domain.vo;

import java.io.Serializable;

import java.util.Date;

import com.pgb.service.enums.IdentityTypeEnum;
import org.hibernate.validator.constraints.Length;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.ToString;
import lombok.Builder;

/**
* 用户学生表（一对多）
* @TableName pg_user_student
*/
@Schema(description = "用户学生表（一对多） VO")
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode
@ToString
@Builder
public class PgUserStudentVO implements Serializable {

    @Schema(title = "用户-学生id", type = "string")
    private Long id;

    @Schema(title = "关联用户id")
    private Long userId;

    @Schema(title = "创建时间")
    private Date createTime;

    @Schema(title = "姓名")
    private String name;

    @Schema(title = "头像")
    private String avatar;

    @Schema(title = "身份")
    private IdentityTypeEnum identity;
}
