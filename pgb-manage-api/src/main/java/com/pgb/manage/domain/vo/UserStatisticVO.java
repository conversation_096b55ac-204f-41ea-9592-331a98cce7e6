package com.pgb.manage.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * Created by 2024/12/27 12:09
 */
@Schema(title = "统计结果")
@Data
public class UserStatisticVO {

    @Schema(title = "今天有多少新用户")
    private Integer newUser;

    @Schema(title = "今天有多少新用户提交作文")
    private Long newUserSubmit;

    @Schema(title = "统计今天有多少用户提交作文，去重")
    private Integer userSubmit;

    @Schema(title = "今天总共提交多少份作文")
    private Long totalSubmit;

    @Schema(title = "作文总提交数量")
    private Long totalSubmitCount;

    @Schema(title = "作文待批改数量")
    private Long totalCorrect;

    // --------------- 昨天的数据 --------------
    @Schema(title = "昨天有多少新用户")
    private Integer yesterdayNewUser;

    @Schema(title = "昨天有多少新用户提交作文")
    private Long yesterdayNewUserSubmit;

    @Schema(title = "昨天有多少用户提交作文，去重")
    private Integer yesterdayUserSubmit;

    @Schema(title = "昨天总共提交多少份作文")
    private Long yesterdayTotalSubmit;

}
