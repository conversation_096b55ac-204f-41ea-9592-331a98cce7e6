package com.pgb.common.oss.service;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.img.ImgUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.net.url.UrlBuilder;
import cn.hutool.core.util.StrUtil;
import com.pgb.common.oss.property.OssTencentProperty;
import com.qcloud.cos.COSClient;
import com.qcloud.cos.ClientConfig;
import com.qcloud.cos.auth.BasicCOSCredentials;
import com.qcloud.cos.auth.COSCredentials;
import com.qcloud.cos.http.HttpMethodName;
import com.qcloud.cos.http.HttpProtocol;
import com.qcloud.cos.model.*;
import com.qcloud.cos.model.Tag.Tag;
import com.qcloud.cos.region.Region;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.awt.image.BufferedImage;
import java.io.File;
import java.net.URL;
import java.time.Duration;
import java.util.Date;
import java.util.List;

@Slf4j
@RequiredArgsConstructor
public class OssTencentService extends OssService {

    private COSClient client;

    private OssTencentProperty property;

    public OssTencentService(OssTencentProperty property) {
        // 构建凭证
        COSCredentials cred = new BasicCOSCredentials(property.getSecretId(), property.getSecretKey());

        // 配置
        ClientConfig clientConfig = new ClientConfig();

        // 设置 bucket 的地域
        // COS_REGION 请参见 https://cloud.tencent.com/document/product/436/6224
        clientConfig.setRegion(new Region(property.getRegion()));

        clientConfig.setHttpProtocol(HttpProtocol.https);

        this.client = new COSClient(cred, clientConfig);

        this.property = property;
    }

    @Override
    public String presignedUrl(String bucketName, String key, Duration expiration) {
        return presignedUrl(
                this.property.getBucketName().get(0), key, expiration, null
        );
    }

    @Override
    public String presignedUrl(String key, Duration expiration) {
        return presignedUrl(
                this.property.getBucketName().get(0), key, expiration
        );
    }

    @Override
    public String presignedUrl(String bucketName, String key, Duration expiration, String contentType) {
        // 设置签名过期时间(可选), 若未进行设置则默认使用 ClientConfig 中的签名过期时间(1小时)
        // 这里设置签名在半个小时后过期
        Date expirationDate = new Date(System.currentTimeMillis() + expiration.toMillis());
        URL url = this.client.generatePresignedUrl(bucketName, key, expirationDate, HttpMethodName.PUT);

        return url.toString();
    }

    @Override
    public String presignedUrl(String key, Duration expiration, String contentType) {
        return presignedUrl(
                this.property.getBucketName().get(0), key, expiration, contentType
        );
    }

    @Override
    public String getCdnUrl(String key) {
        if (StrUtil.isEmpty(this.property.getCdnDomain())) {
            throw new RuntimeException("当前 OSS 未配置 CND 域名");
        }

        return UrlBuilder.of(this.property.getCdnDomain()).addPath(key).toString();
    }

    /**
     * 上传图片文件
     *
     * @param key
     * @param image
     * @return
     */
    @Override
    public String putCdnImg(String key, BufferedImage image) {

        // 删除临时文件，防止不会删除文件
        File tempFile = FileUtil.createTempFile();

        FileUtil.writeBytes(ImgUtil.toBytes(image, ImgUtil.IMAGE_TYPE_PNG), tempFile);

        this.client.putObject(
                this.property.getBucketName().get(0),
                key,
                tempFile
        );

        // 主动删除临时文件
        FileUtil.del(tempFile);

        return UrlBuilder.of(this.property.getCdnDomain()).addPath(key).toString();
    }

    /**
     * 上传文档文件
     *
     * @param key
     */
    @Override
    public String putFile(String key, File file) {

        File tempFile = FileUtil.createTempFile();

        FileUtil.writeBytes(FileUtil.readBytes(file), tempFile);

        this.client.putObject(
                this.property.getBucketName().get(0),
                key,
                file
        );

        // 主动删除临时文件
        FileUtil.del(tempFile);

        return UrlBuilder.of(this.property.getCdnDomain()).addPath(key).toString();
    }

    @Override
    public Boolean isExist(String key) {
        try {
            return client.doesObjectExist(
                    this.property.getBucketName().get(0),
                    key
            );
        } catch (Exception e) {
            log.error("【COS】判断文件对象是否存在失败：{}\n", this.property, e);
        }

        return false;
    }

    @Override
    public void setTagList(String key, String... tags) {

        // 转为tagList
        List<Tag> tagList = ListUtil.toList(tags).stream().map(tag -> new Tag(tag, tag)).toList();

        ObjectTagging objectTagging = new ObjectTagging(tagList);

        SetObjectTaggingRequest setObjectTaggingRequest = new SetObjectTaggingRequest(
                this.property.getBucketName().get(0), key, objectTagging);

        SetObjectTaggingResult taggingResult = this.client.setObjectTagging(setObjectTaggingRequest);
    }


    @Override
    public void delete(String key) {
        // 移除首符号
        if (StrUtil.startWith(key, "/")) {
            key = StrUtil.subSuf(key, 1);
        }

        this.client.deleteObject(
                this.property.getBucketName().get(0),
                key
        );
    }

    @Override
    public String getResizeUrl(String url) {
        return url + "?imageView2/1/w/80/h/100/q/85";
    }

    @Override
    public String getFormat(String url, String format) {
        // ?imageMogr2/format/png
        return url + "?imageMogr2/format/" + format;
    }


    @Override
    public List<String> listObjects(String path) {
        return List.of();
    }

    @Override
    public void download(String objectName, String pathName) {
        // TODO
    }

    /**
     * 重命名文件
     *
     * @param sourceKey      源文件
     * @param destinationKey 目标文件
     */
    @Override
    public void renameFile(String sourceKey, String destinationKey) {
        // TODO
    }
}
