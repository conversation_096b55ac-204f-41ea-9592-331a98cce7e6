package com.pgb.xcx.controller.zw;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.pgb.common.core.global.BaseResult;
import com.pgb.common.core.global.GlobalCode;
import com.pgb.service.db.PgAnswerPolishService;
import com.pgb.service.db.PgAnswerService;
import com.pgb.service.domain.answer.PgAnswer;
import com.pgb.service.domain.answer.polish.PgAnswerPolish;
import com.pgb.service.domain.answer.polish.PolishRequire;
import com.pgb.service.domain.question.zwEssay.ZwEssayQuestion;
import com.pgb.service.domain.question.zwEssay.ZwTextDiff;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * Created by 2025/3/19 16:30
 */
@Tag(name = "用户端/批改/润色")
@RestController("UserRecordPolishController")
@Transactional(rollbackFor = Exception.class)
@RequestMapping("/user/record/polish")
@RequiredArgsConstructor
@Slf4j
public class PolishController {

    private final PgAnswerService pgAnswerService;

    private final PgAnswerPolishService pgAnswerPolishService;

    @Operation(summary = "查看各版本润色")
    @PostMapping("list/{answerId}")
    public BaseResult<List<PgAnswerPolish>> list(@PathVariable Long answerId) {

        PgAnswer answer = pgAnswerService.getById(answerId);

        if (ObjectUtil.isNull(answer)) {
            return BaseResult.code(GlobalCode.Item_Null);
        }

        List<PgAnswerPolish> polishes = pgAnswerPolishService.list(new LambdaQueryWrapper<PgAnswerPolish>()
                .eq(PgAnswerPolish::getAnswerId, answerId)
                .orderByDesc(PgAnswerPolish::getSort));

        return BaseResult.success(polishes);
    }

    @Data
    public static class RePolishRequireParam {

        @Schema(title = "润色要求")
        private PolishRequire require;
    }

    @Operation(summary = "重新润色")
    @PostMapping("rePolish/{answerId}")
    public BaseResult<Boolean> rePolish(@PathVariable Long answerId, @RequestBody RePolishRequireParam param) {

        PgAnswer answer = pgAnswerService.getById(answerId);

        if (ObjectUtil.isNull(answer)) {
            return BaseResult.code(GlobalCode.Item_Null);
        }

        List<PgAnswerPolish> polishes = pgAnswerPolishService.list(new LambdaQueryWrapper<PgAnswerPolish>()
                .eq(PgAnswerPolish::getAnswerId, answerId));

        // 限制润色三次
        if (polishes.size() >= 3) {
            return BaseResult.error(GlobalCode.Limit_Over, "单篇作文润色次数不足~");
        }

        // 若为空 则添加原文润色到记录中
        if (polishes.isEmpty()) {

            ZwEssayQuestion result = JSONUtil.toBean(JSONUtil.toJsonStr(answer.getCorrectResult()), ZwEssayQuestion.class);
            if (StrUtil.isNotBlank(result.getPolish())) {
                PgAnswerPolish polish = new PgAnswerPolish();
                polish.setUserId(answer.getUserId());
                polish.setAnswerId(answerId);
                polish.setContent(result.getPolish());
                polish.setSort(polishes.size() + 1);
                polish.setCreateTime(new Date());
                pgAnswerPolishService.save(polish);
            }
        }

        // TODO 重新润色
        String polishContent = "";

        // 添加记录
        PgAnswerPolish polish = new PgAnswerPolish();
        polish.setUserId(answer.getUserId());
        polish.setAnswerId(answerId);
        polish.setContent(polishContent);
        // 重新润色要求
        polish.setRequire(param);
        polish.setSort(polishes.size() + 1);
        polish.setCreateTime(new Date());
        pgAnswerPolishService.save(polish);

        return BaseResult.success(true);

    }


    @Operation(summary = "选择润色版本", description = "包括修改批改结果")
    @PostMapping("select/{polishId}")
    public BaseResult<Boolean> select(@PathVariable Long polishId) {

        PgAnswerPolish polish = pgAnswerPolishService.getById(polishId);

        if (ObjectUtil.isNull(polish)) {
            return BaseResult.code(GlobalCode.Item_Null);
        }

        PgAnswer answer = pgAnswerService.getById(polish.getAnswerId());
        ZwEssayQuestion userAnswer = JSONUtil.toBean(answer.getCorrectResult().toString(), ZwEssayQuestion.class);

        userAnswer.setPolish(polish.getContent());
        // TODO 更新润色对比
        List<ZwTextDiff> polishDiffList = new ArrayList<>();
        userAnswer.setPolishDiffList(polishDiffList);

        // 更新
        answer.setCorrectResult(userAnswer);
        return BaseResult.success(
                pgAnswerService.updateById(answer)
        );
    }

}
