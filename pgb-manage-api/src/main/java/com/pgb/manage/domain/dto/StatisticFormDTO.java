package com.pgb.manage.domain.dto;

import com.pgb.service.enums.QueryPeriodEnum;
import com.pgb.service.enums.StatisticContentTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * Created by 2025/1/8 15:37
 */
@Data
@Schema(title = "统计数据查询参数")
public class StatisticFormDTO {

    @Schema(title = "时间范围")
    private QueryPeriodEnum period;

    @Schema(title = "统计内容类型")
    private StatisticContentTypeEnum contentType;
}
