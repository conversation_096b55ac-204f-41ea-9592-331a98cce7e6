package com.pgb.service.domain.activity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.pgb.common.mybatis.handler.JsonbTypeHandler;
import com.pgb.service.enums.ActivityTypeEnum;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 *
 * @TableName pg_activity
 */
@TableName(value ="pg_activity")
@Data
public class PgActivity implements Serializable {
    /**
     * 活动表
     */
    @TableId
    private Long id;

    /**
     * 活动名称
     */
    private String name;

    /**
     * 活动类型，0：年卡立减，1：买多少送多少（天）
     */
    private ActivityTypeEnum type;

    /**
     * 购买类型，0：直购，1：兑换码
     */
    private Integer buyType;

    /**
     * 活动配置，对应不同实体
     */
    @TableField(typeHandler = JsonbTypeHandler.class)
    private Object config;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 开始时间
     */
    private Date startTime;

    /**
     * 结束时间
     */
    private Date endTime;

}
