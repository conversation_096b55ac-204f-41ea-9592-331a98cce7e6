package com.pgb.service.domain.vip;

import java.io.Serializable;

import java.util.Date;

import cn.hutool.core.util.ObjectUtil;
import com.fasterxml.jackson.annotation.JsonGetter;
import com.pgb.common.pay.enums.BuyTypeEnum;
import com.pgb.service.enums.ChannelTypeEnum;
import com.pgb.service.enums.VipTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.ToString;
import lombok.Builder;

/**
 * @TableName pg_vip
 */
@Schema(description = " VO")
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode
@ToString
@Builder
public class PgVipVO implements Serializable {

    @Schema(title = "会员充值表", type = "string")
    private Long id;

    @Schema(title = "用户id")
    private Long userId;

    @Schema(title = "会员类型，0：年卡，1：月卡")
    private VipTypeEnum vipType;

    @Schema(title = "购买类型")
    private BuyTypeEnum buyType;

    @Schema(title = "购买天数")
    private Integer duration;

    @Schema(title = "订单id")
    private Long orderId;

    @Schema(title = "生效时间")
    private Date effectTime;

    @Schema(title = "过期时间")
    private Date expireTime;

    @Schema(title = "创建时间")
    private Date createTime;

    @Schema(title = "付款金额")
    private Integer amount;

    @Schema(title = "手机号")
    private String phone;

    @Schema(title = "会员开通方式，0：自行开通，1：后台开通")
    private ChannelTypeEnum openType;

    @Schema(title = "会员类型")
    @JsonGetter("vipTypeStr")
    public String getVipTypeStr() {
        return ObjectUtil.isNotNull(this.vipType) ? this.vipType.desc : null;
    }

    @Schema(title = "购买类型")
    @JsonGetter("buyTypeStr")
    public String getBuyTypeStr() {
        return ObjectUtil.isNotNull(this.buyType) ? this.buyType.desc : null;
    }

    @Schema(title = "会员开通渠道")
    @JsonGetter("openTypeStr")
    public String getOpenTypeStr() {
        return ObjectUtil.isNotNull(this.openType) ? this.openType.desc : null;
    }
}
