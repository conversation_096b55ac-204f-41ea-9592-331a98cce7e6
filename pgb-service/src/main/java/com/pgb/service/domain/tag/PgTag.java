package com.pgb.service.domain.tag;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 *
 * @TableName pg_tag
 */
@TableName(value ="pg_tag")
@Data
public class PgTag implements Serializable {
    /**
     * 用户标签
     */
    @TableId
    private Long id;

    /**
     * 标签名称
     */
    private String name;

    /**
     * 创建时间
     */
    private Date createTime;

}
