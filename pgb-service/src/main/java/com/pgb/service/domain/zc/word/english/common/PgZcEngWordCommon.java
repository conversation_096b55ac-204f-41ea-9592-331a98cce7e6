package com.pgb.service.domain.zc.word.english.common;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;

import com.pgb.service.enums.ExportStatusEnum;
import com.pgb.service.enums.GenerateStatusEnum;
import lombok.Data;

/**
 *
 * @TableName pg_zc_eng_word_common
 */
@TableName(value ="pg_zc_eng_word_common")
@Data
public class PgZcEngWordCommon implements Serializable {
    /**
     * 词汇表，AI生成缓存表
     */
    @TableId
    private Long id;

    /**
     * 单词
     */
    private String word;

    /**
     * 词性
     */
    private String wordClass;

    /**
     * 中文释义
     */
    private String chinese;

    /**
     * 音标
     */
    private String phonetic;

    /**
     * 美式发音
     */
    private String usAudioUrl;

    /**
     * 英式发音
     */
    private String ukAudioUrl;

    /**
     * 状态，单词信息生成的状态
     */
    private GenerateStatusEnum status;

}
