package com.pgb.service.enums;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * Created by 2024/8/16 11:13
 */
@Data
@Schema(title = "分组信息")
@AllArgsConstructor
public class CategoryInfo {

    @Schema(title = "分组名称")
    private String cateName;

    @Schema(title = "分组内容")
    private List<EnumDetail> enums;

}

