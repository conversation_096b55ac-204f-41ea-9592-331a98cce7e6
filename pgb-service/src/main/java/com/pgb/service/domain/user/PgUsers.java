package com.pgb.service.domain.user;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.util.Date;

import com.pgb.service.enums.UserTypeEnum;
import lombok.Data;

/**
 * @TableName pg_users
 */
@TableName(value = "pg_users")
@Data
public class PgUsers implements Serializable {
    /**
     * 用户
     */
    private Long id;

    /**
     * 用户昵称
     */
    private String nickName;

    /**
     * 头像
     */
    private String avatarImgUrl;

    /**
     * 绑定手机号
     */
    private String phone;

    /**
     * 用户注册来源 0：小程序， 1：小红书
     */
    private Integer userFrom;

    /**
     * 微信openId
     */
    private String wxOpenId;

    /**
     * 微信unionId
     */
    private String wxUnionId;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 上次登录时间
     */
    private Date lastLoginTime;

    /**
     * 会员过期时间
     */
    private Date vipExpireTime;

    /**
     * 备注
     */
    private String remark;

    public Boolean getIsVip() {
        if (ObjectUtil.isNull(this.vipExpireTime)) {
            return false;
        }

        return this.vipExpireTime.after(new Date());
    }

    public UserTypeEnum getUserType() {

        if (ObjectUtil.isNotNull(this.vipExpireTime)) {
            if (this.vipExpireTime.after(new Date())) {
                return UserTypeEnum.MEMBER;
            } else if (this.vipExpireTime.before(new Date())) {
                return UserTypeEnum.EXPIRED_MEMBER;
            }
        }
        return UserTypeEnum.UN_MEMBER;
    }
}
