package com.pgb.common.mybatis.handler;

import cn.hutool.json.JSONUtil;
import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedTypes;
import org.postgresql.util.PGobject;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;

@MappedTypes({Object.class})
public class JsonbTypeHandler extends BaseTypeHandler<Object> {
//    private static final PGobject jsonObject = new PGobject();

    @Override
    public void setNonNullParameter(PreparedStatement preparedStatement, int i, Object o, JdbcType jdbcType) throws SQLException {
        if (preparedStatement != null) {
            // 每次创建新的实例，避免线程安全问题
            PGobject jsonObject = new PGobject();
            jsonObject.setType("jsonb");
            jsonObject.setValue(JSONUtil.toJsonStr(o));
            preparedStatement.setObject(i, jsonObject);
        }
    }

    @Override
    public Object getNullableResult(ResultSet resultSet, String s) throws SQLException {
        return getJSON(resultSet.getString(s));
    }

    @Override
    public Object getNullableResult(ResultSet resultSet, int i) throws SQLException {
        return getJSON(resultSet.getString(i));
    }

    @Override
    public Object getNullableResult(CallableStatement callableStatement, int i) throws SQLException {
        return getJSON(callableStatement.getString(i));
    }

    private Object getJSON(String str) {
        if (JSONUtil.isTypeJSONObject(str)) {
            return JSONUtil.parseObj(str);
        } else if (JSONUtil.isTypeJSONArray(str)) {
            return JSONUtil.parseArray(str);
        } else {
            return JSONUtil.parse(str);
        }
    }
}
