package com.pgb.service.domain.zc.common.textbook.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

/**
 * 单词例句DTO
 *
 * <AUTHOR>
 * Created by 2025/7/25 21:00
 */
@Data
@Schema(description = "单词例句")
public class PgWordExampleDTO implements Serializable {

    @Schema(description = "主键ID")
    private Long id;

    @Schema(description = "所属单词ID")
    private Long wordId;

    @Schema(description = "例句内容")
    private String example;

    @Schema(description = "例句翻译")
    private String translation;

    @Schema(description = "排序值")
    private Integer sort;

    @Schema(description = "创建时间")
    private Date createTime;

    @Schema(description = "更新时间")
    private Date updateTime;
}
