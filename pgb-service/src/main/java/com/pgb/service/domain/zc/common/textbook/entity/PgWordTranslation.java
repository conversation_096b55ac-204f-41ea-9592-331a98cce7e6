package com.pgb.service.domain.zc.common.textbook.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * 单词翻译实体
 *
 * <AUTHOR>
 * Created by 2025/7/25 21:00
 */
@Data
@Schema(description = "单词翻译实体")
public class PgWordTranslation {

    @Schema(description = "主键ID")
    @TableId(value = "id")
    private Long id;

    @Schema(description = "所属单词ID")
    private Long wordId;

    @Schema(description = "翻译内容")
    private String translation;

    @Schema(description = "词性")
    private String partOfSpeech;

    @Schema(description = "排序值")
    private Integer sort;

    @Schema(description = "创建时间")
    private Date createTime;

    @Schema(description = "更新时间")
    private Date updateTime;
}
