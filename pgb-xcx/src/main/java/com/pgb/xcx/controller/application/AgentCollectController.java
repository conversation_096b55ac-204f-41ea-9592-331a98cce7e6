package com.pgb.xcx.controller.application;

import cn.dev33.satoken.annotation.SaCheckLogin;
import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.pgb.common.core.global.BaseResult;
import com.pgb.common.core.global.CommonForm;
import com.pgb.common.core.global.GlobalCode;
import com.pgb.service.db.PgAgentCollectService;
import com.pgb.service.db.PgAgentService;
import com.pgb.service.domain.agent.PgAgent;
import com.pgb.service.domain.agent.PgAgentVO;
import com.pgb.service.domain.agent.collect.PgAgentCollect;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Tag(name = "用户端/智能体/收藏管理")
@RestController("AgentCollectController")
@Transactional(rollbackFor = Exception.class)
@RequestMapping("/user/agent/collect")
@RequiredArgsConstructor
@Slf4j
public class AgentCollectController {

    private final PgAgentService pgAgentService;

    private final PgAgentCollectService pgAgentCollectService;

    @Operation(summary = "获取我收藏的智能体")
    @GetMapping("/list")
    public BaseResult<List<PgAgentVO>> collectList() {

        LambdaQueryWrapper<PgAgent> queryWrapper = new LambdaQueryWrapper<>();

        // 获取收藏的智能体ids
        List<Long> agentIds = pgAgentCollectService.list(new LambdaQueryWrapper<PgAgentCollect>()
                        .eq(PgAgentCollect::getUserId, StpUtil.getLoginIdAsLong())
                        .orderByAsc(PgAgentCollect::getSort))
                .stream()
                .map(PgAgentCollect::getAgentId)
                .toList();

        if (agentIds.isEmpty()) {
            return BaseResult.success(new ArrayList<>());
        } else {
            queryWrapper.in(PgAgent::getId, agentIds);
        }

        List<PgAgent> pgAgents = pgAgentService.list(queryWrapper);
        Map<Long, PgAgent> agentMap = pgAgents.stream().collect(Collectors.toMap(PgAgent::getId, Function.identity()));

        List<PgAgentVO> list = agentIds.stream()
                .map(agentMap::get)
                .map(pgAgent -> BeanUtil.copyProperties(pgAgent, PgAgentVO.class))
                .toList();

//        List<PgAgentVO> list = pgAgentService.list(queryWrapper)
//                .stream()
//                .map(pgAgent -> BeanUtil.copyProperties(pgAgent, PgAgentVO.class))
//                .toList();

        return BaseResult.success(list);
    }

    @Operation(summary = "保存智能体收藏列表")
    @PostMapping("/save")
    @SaCheckLogin
    public BaseResult<Boolean> collectList(@RequestBody CommonForm<List<PgAgentVO>> form) {

        List<PgAgentVO> data = form.getData();

        List<PgAgentCollect> collectList = new ArrayList<>();

        // 设置排序
        for (int i = 0; i < data.size(); i++) {
            PgAgentVO agent = data.get(i);

            PgAgentCollect collect = new PgAgentCollect();
            collect.setUserId(StpUtil.getLoginIdAsLong());
            collect.setAgentId(agent.getId());
            collect.setCreateTime(new Date());
            collect.setSort(i);
            collectList.add(collect);
        }

        // 删除
        pgAgentCollectService.remove(new LambdaQueryWrapper<PgAgentCollect>()
                .eq(PgAgentCollect::getUserId, StpUtil.getLoginIdAsLong())
        );

        // 保存
        pgAgentCollectService.saveBatch(collectList);

        return BaseResult.success(true);
    }

    @Operation(summary = "收藏智能体")
    @PutMapping("collect/{agentId}")
    @SaCheckLogin
    public BaseResult<Boolean> collect(@PathVariable Long agentId) {
        PgAgent agent = pgAgentService.getById(agentId);

        if (ObjectUtil.isNull(agent)) {
            return BaseResult.code(GlobalCode.Item_Null);
        }

        // 收藏
        pgAgentCollectService.collect(agentId, StpUtil.getLoginIdAsLong());

        return BaseResult.success();
    }

    @Operation(summary = "取消收藏智能体")
    @DeleteMapping("collect/{agentId}")
    @SaCheckLogin
    public BaseResult<Boolean> cancelCollect(@PathVariable Long agentId) {
        PgAgent agent = pgAgentService.getById(agentId);

        if (ObjectUtil.isNull(agent)) {
            return BaseResult.code(GlobalCode.Item_Null);
        }

        // 取消收藏
        pgAgentCollectService.unCollect(agentId, StpUtil.getLoginIdAsLong());

        return BaseResult.success();
    }
}
