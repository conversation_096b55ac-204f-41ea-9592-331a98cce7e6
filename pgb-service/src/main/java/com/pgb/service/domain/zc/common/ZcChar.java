package com.pgb.service.domain.zc.common;

import cn.hutool.core.annotation.Alias;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class ZcChar {

    private ZcLocation location;

    @Schema(title = "文字内容")
    @Alias("char")
    @JsonProperty("char")
    private String chars;

    @Schema(title = "正误情况：0：未作答，1：正确，2：错误")
    private Integer rightType;

    @Schema(title = "是否显示")
    private Boolean isShow;

}
