package com.pgb.subscribe.service;

import cn.binarywang.wx.miniapp.api.WxMaService;
import cn.binarywang.wx.miniapp.bean.WxMaSubscribeMessage;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.TimeInterval;
import cn.hutool.core.lang.TypeReference;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.json.JSONUtil;
import com.pgb.common.core.global.BaseResult;
import com.pgb.common.core.global.exception.BaseException;
import com.pgb.common.core.utils.EnvUtils;
import com.pgb.common.redis.QueueUtils;
import com.pgb.common.redis.RedisUtils;
import com.pgb.service.custom.CorrectService;
import com.pgb.service.custom.ZwCorrectService;
import com.pgb.service.db.PgAnswerService;
import com.pgb.service.db.PgUsersService;
import com.pgb.service.domain.GlobQueueConstants;
import com.pgb.service.domain.GlobalXcxConstants;
import com.pgb.service.domain.answer.PgAnswer;
import com.pgb.service.domain.user.PgUsers;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RBlockingQueue;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.concurrent.Executors;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReference;

import static com.pgb.service.custom.QwMsgService.sendErrorMessage;

/**
 * @Datetime: 2025年04月09日22:22
 * @Description:
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class CorrectSubscribeService {

    private final PgAnswerService pgAnswerService;

    private final CorrectService correctService;

    private final ZwCorrectService zwCorrectService;

    private final WxMaService wxMaService;

    private final PgUsersService pgUsersService;

    @Async
    public void cloudSubscribe(String queueName) {
        Integer threadNums = 300;
        subscribe(true, queueName, threadNums);
    }

    @Async
    public void localSubscribe(String queueName, Integer threadNums) {
        subscribe(false, queueName, threadNums);
    }

    /**
     * 订阅服务
     * @param useCloud
     * @param queueName
     * @param threadNums
     */
    @Async
    public void subscribe(Boolean useCloud, String queueName, Integer threadNums) {
        log.info("【{}】作文AI批改队列监听 --> 最大队列数量：{}", queueName, threadNums);
        // 获取批改队列
        RBlockingQueue<Long> queue = QueueUtils.getClient().getBlockingQueue(queueName);
        AtomicReference<Boolean> isContinue = new AtomicReference<>(true);
        Executor executor = Executors.newFixedThreadPool(useCloud ? 500 : threadNums);
        AtomicInteger nums = new AtomicInteger(0);
        while (isContinue.get()) {
            if (nums.get() >= threadNums && !useCloud) {
                log.info("【{}】批改队列并发数量已达上限：{}", queueName, nums.get());
                ThreadUtil.safeSleep(2000L);
                continue;
            }
            try {
                AtomicReference<Long> answerId = new AtomicReference<>();

                // 阻塞获取一个答案
                answerId.set(queue.take());

                CompletableFuture<Void> f = CompletableFuture.runAsync(() -> {
                    // 线程数加1
                    nums.incrementAndGet();

                    log.info("【{}】接收批改监听信息：{}，当前并发量：{}", queueName, answerId.get(), nums.get());

                    // 批改中状态，维持10分钟
                    RedisUtils.setCacheObject(GlobalXcxConstants.XCX_ZW_CORRECTING + answerId.get(), answerId.get(), Duration.ofMinutes(10));

                    // 云端批改
                    if (useCloud && nums.get() >= threadNums) cloudCorrect(answerId.get());
                    // 本地批改
                    else localCorrect(answerId.get());
                }, executor);
                f.whenComplete((v, e) -> {
                    // 线程数减1
                    nums.decrementAndGet();

                    log.info("【{}】批改完成:{}剩余并发量：{}", queueName, answerId.get(), nums.get());

                    // 清空批改中状态
                    RedisUtils.deleteObject(GlobalXcxConstants.XCX_ZW_CORRECTING + answerId.get());

                    if (e != null) {
                        // 如果是 RedissonShutdownException 则直接跳过
                        if (e.getCause().getMessage().contains("Redisson is shutdown")) {
                            log.error("批改队列监听异常 --> redisson 已中断！");
                            return;
                        }

                        log.error("【{}】批改队列监听异常 --> {}", queueName, e.getMessage());

                        // 重试机制
                        retry(answerId);
                    }

                    // 判断是否继续批改
                    if (!correctService.getCorrecting()) {
                        log.info("当前线程已手动停止批改，剩余线程量：{}", nums.get());
                        isContinue.set(false);
                    }
                });
            } catch (Exception e) {
                if (ObjectUtil.isNotNull(e.getMessage()) && e.getMessage().contains("Redisson is shutdown")) {
                    log.error("CloudCorrectService --> Redisson 已中断连接");
                    break;
                } else {
                    e.printStackTrace();
                }
            }
        }
        log.info("【{}】批改队列监听：已完全停止", queueName);
    }

    /**
     * 本地批改
     * @param answerId
     */
    public void localCorrect(Long answerId) {
        // 获取已上传作业
        PgAnswer answer = pgAnswerService.getById(answerId);

        // 如果为空
        if (ObjectUtil.isNull(answer)) {
            log.warn("【批改队列】：{}，记录不存在，等待2秒重试", answerId);
            // 执行2秒后重试，保证数据库一定插入完毕了
            ThreadUtil.safeSleep(Duration.ofSeconds(2).toMillis());
            answer = pgAnswerService.getById(answerId);
        }

        zwCorrectService.correct(answer);

        // 发送微信批改通知
        sendMsg(answer);
    }

    /**
     * 云弹性批改，异步执行反馈结果
     *
     * @param answerId
     * @return
     */
    private void cloudCorrect(Long answerId) {

        TimeInterval timer = DateUtil.timer();

        String url = "http://zw-correct-cloud.pigaibang.com/api/correct/zw/" + answerId;

        String resultStr = HttpRequest.get(url)
                .timeout(20 * 60 * 1000)
                .execute()
                .body();

        BaseResult<Boolean> result = Convert.convert(new TypeReference<BaseResult<Boolean>>() {
        }, JSONUtil.parseObj(resultStr));

        if (result.isSuccess()) {
            log.info("【云函数题目批改-批改结束】{}用时：{}", answerId, timer.intervalPretty());

            // 获取已上传作业
            PgAnswer answer = pgAnswerService.getById(answerId);
            if (!EnvUtils.isDev()) {
                sendMsg(answer);
            }
        } else {
            log.error("【云函数题目批改异常-ERROR】{}请检查：{}", answerId, result.getMsg());
            throw new RuntimeException("云函数批改异常, 请检查");
        }
    }

    /**
     * 发送微信批改通知
     * @param answer
     */
    public void sendMsg(PgAnswer answer) {
        // 发送批改结束通知
        PgUsers users = pgUsersService.getById(answer.getUserId());

        WxMaSubscribeMessage message = new WxMaSubscribeMessage();
        message.setToUser(users.getWxOpenId());
        message.setTemplateId("Qa5iizS13gl7a0z11F3aFihf-6nSyeivLR1DU7BcoUc");
        message.setPage("/pages/zw/index?id=" + answer.getId());
        message.setData(ListUtil.toList(
                // new WxMaSubscribeMessage.MsgData("thing2", DateUtil.format(answer.getCreateTime(), "yyyy-MM-dd HH:mm:ss") + " 上传作文"),
                new WxMaSubscribeMessage.MsgData("thing2", "作文已批改"),
                new WxMaSubscribeMessage.MsgData("thing4", "点击立即阅览")
        ));
        if (EnvUtils.isDev()) {
            message.setMiniprogramState("developer");
        }
        try {
            wxMaService.getMsgService().sendSubscribeMsg(message);
        } catch (Exception e) {
            log.error("小程序模板发送失败：{}", e.getMessage());
        }
    }

    /**
     * 重试机制
     *
     * @param answerId
     */
    public void retry(AtomicReference<Long> answerId) {
        // 重试当前队列
        if (ObjectUtil.isNotNull(answerId.get())) {
            // 重试三次
            Integer retry = RedisUtils.getCacheObject(GlobQueueConstants.PGB_XCX_CORRECT_QUES_RETRY.name() + answerId.get());

            if (ObjectUtil.defaultIfNull(retry, 0) < 3) {
                // 加入批改队列
                QueueUtils.addQueueObject(GlobQueueConstants.PGB_XCX_CORRECT_QUEUE.name(), answerId.get());

                // 发送异常报警通知
                if (!EnvUtils.isDev()) {
                    sendErrorMessage("已进入重试队列，重试次数：" + ObjectUtil.defaultIfNull(retry, 0));
                }

                // 重试次数
                RedisUtils.setCacheObject(GlobQueueConstants.PGB_XCX_CORRECT_QUES_RETRY.name() + answerId.get(), ObjectUtil.defaultIfNull(retry, 0) + 1, Duration.ofHours(6));
            } else {
                // 发送异常报警通知
                if (!EnvUtils.isDev()) {
                    sendErrorMessage("已重试3次！必须线上排查！id：" + answerId.get());
                }
            }
        } else {
            if (!EnvUtils.isDev()) {
                log.error("无效answerId，需排查！");
            }
        }
    }
}
