package com.pgb.service.domain.query;

import com.pgb.common.mybatis.global.PageQuery;
import com.pgb.service.enums.CorrectStatusEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * Created by 2025/4/24 17:09
 */
@EqualsAndHashCode(callSuper = true)
@Schema(description = "题目 搜索实体")
@Data
public class PcAnswerQuery extends PageQuery {

    @Schema(title = "备注名称")
    private String name;

    @Schema(title = "批改状态")
    private CorrectStatusEnum status;
}
