package com.pgb.manage.controller.crm;

import cn.dev33.satoken.annotation.SaCheckRole;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.pgb.common.core.global.BaseResult;
import com.pgb.common.core.global.GlobalCode;
import com.pgb.common.core.global.RoleConstants;
import com.pgb.common.mybatis.global.PageQuery;
import com.pgb.manage.domain.query.MemberQuery;
import com.pgb.manage.domain.vo.ManageUserVO;
import com.pgb.service.db.*;
import com.pgb.service.domain.tag.PgTagUser;
import com.pgb.service.domain.user.PgUsers;
import com.pgb.service.domain.vip.PgVip;
import com.pgb.service.domain.vip.PgVipVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * Created by 2025/1/6 16:02
 */
@Tag(name = "管理端/用户管理")
@RestController("ManageUserController")
@Transactional(rollbackFor = Exception.class)
@RequestMapping("/manage/user")
@RequiredArgsConstructor
@Slf4j
@SaCheckRole(RoleConstants.Manager)
public class UserController {

    private final PgUsersService pgUsersService;

    private final PgVipService pgVipService;

    private final PgTagService pgTagService;

    private final PgTagUserService pgTagUserService;

    @Operation(summary = "用户列表")
    @PostMapping("page")
    public BaseResult<IPage<ManageUserVO>> userPage(@RequestBody MemberQuery query) {

        LambdaQueryWrapper<PgUsers> lambdaQueryWrapper = new LambdaQueryWrapper<PgUsers>()
                .like(StrUtil.isNotBlank(query.getPhone()), PgUsers::getPhone, query.getPhone());

        // 根据标签筛选
        if (CollUtil.isNotEmpty(query.getTagList())) {
            List<Long> userIds = pgTagUserService.list(new LambdaQueryWrapper<PgTagUser>()
                    .in(PgTagUser::getTagId, query.getTagList())
            ).stream().map(PgTagUser::getUserId).distinct().toList();

            if (ObjectUtil.isEmpty(userIds)) {
                return BaseResult.success(new Page<>());
            }

            lambdaQueryWrapper.in(PgUsers::getId, userIds);
        }

        // 根据是否是会员筛选
        if (ObjectUtil.isNotNull(query.getIsVip())) {
            if (query.getIsVip()) {
                // 会员
                lambdaQueryWrapper.and(wrapper -> wrapper
                        .isNotNull(PgUsers::getVipExpireTime)
                        .or()
                        .gt(PgUsers::getVipExpireTime, new Date()));
            } else {
                // 非会员
                lambdaQueryWrapper.and(wrapper -> wrapper
                        .isNull(PgUsers::getVipExpireTime)
                        .or()
                        .le(PgUsers::getVipExpireTime, new Date()));
            }
        }

        // 根据购买过的会员类型筛选 年卡/月卡/周卡
        if (!query.getBuyTypeList().isEmpty()) {
            List<Long> vipUserIds = pgVipService.list(new LambdaQueryWrapper<PgVip>()
                            .in(PgVip::getVipType, query.getBuyTypeList()))
                    .stream()
                    .map(PgVip::getUserId)
                    .toList();

            if (vipUserIds.isEmpty()) {
                return BaseResult.success(new Page<>());
            } else {
                lambdaQueryWrapper.in(PgUsers::getId, vipUserIds);
            }
        }

        // 购买次数 >=n次 筛选
        if (ObjectUtil.isNotNull(query.getBuyNum()) && query.getBuyNum() > 0) {
            // SELECT user_id, count(*) as num FROM "pg_vip" GROUP BY user_id HAVING count(*) >= 2
            // 映射 实体， 实体可以创建的，也可以是一个map
            // List<Map<String, Object>> listMaps = pgVipService.listMaps(new QueryWrapper<PgVip>()
            //         .select(FieldUtils.getFieldName(PgVip::getUserId), "COUNT(*) as num")
            //         .groupBy(FieldUtils.getFieldName(PgVip::getUserId))
            //         .having(StrUtil.format("COUNT(*) > {}", num))
            // );

            List<Long> userIds = pgVipService.list(new LambdaQueryWrapper<PgVip>()
                    .select(PgVip::getUserId)
                    .groupBy(PgVip::getUserId)
                    .having(StrUtil.format("COUNT(*) = {}", query.getBuyNum()))
            ).stream().map(PgVip::getUserId).toList();

            // 若为空
            if (userIds.isEmpty()) {
                return BaseResult.success(new Page<>());
            }
            // 加条件
            lambdaQueryWrapper.in(PgUsers::getId, userIds);
        }
        // 筛选购买次数等于0的用户
        else if (ObjectUtil.isNotNull(query.getBuyNum()) && query.getBuyNum() == 0) {

            // 获取所有用户的 ID
            List<Long> allUserIds = pgUsersService.list().stream().map(PgUsers::getId).toList();

            List<Long> vipUserIds = pgVipService.list(new LambdaQueryWrapper<PgVip>()
                    .select(PgVip::getUserId)
                    .groupBy(PgVip::getUserId)
                    .having(StrUtil.format("COUNT(*) >= {}", query.getBuyNum()))
            ).stream().map(PgVip::getUserId).toList();

            List<Long> resultUserIds = allUserIds.stream()
                    .filter(userId -> !vipUserIds.contains(userId))
                    .toList();

            if (resultUserIds.isEmpty()) {
                return BaseResult.success(new Page<>());
            }
            lambdaQueryWrapper.in(PgUsers::getId, resultUserIds);

        }


        IPage<ManageUserVO> page = pgUsersService.page(query.toMpPageSortByCreateTime(), lambdaQueryWrapper)
                .convert(user -> {
                    ManageUserVO usersVO = BeanUtil.copyProperties(user, ManageUserVO.class);
                    // 标签
                    usersVO.setTagList(
                            pgTagService.listByUserId(user.getId())
                    );
                    // 购买次数
                    usersVO.setBuyNum(
                            pgVipService.buyNum(user.getId())
                    );
                    // 会员类型 -- 以最后为准
                    PgVip vip = pgVipService.getOne(new LambdaQueryWrapper<PgVip>()
                            .eq(PgVip::getUserId, user.getId())
                            .orderByDesc(PgVip::getCreateTime)
                            .last("LIMIT 1"));
                    if (ObjectUtil.isNotNull(vip)) {
                        usersVO.setVipType(vip.getVipType());
                    }

                    return usersVO;
                });

        return BaseResult.success(page);
    }

    @Operation(summary = "单个用户删标签")
    @DeleteMapping("tag/single/{userId}/{tagId}")
    public BaseResult<Boolean> tagDelete(@PathVariable Long userId, @PathVariable Long tagId) {
        return BaseResult.success(
                pgTagUserService.remove(new LambdaQueryWrapper<PgTagUser>()
                        .eq(PgTagUser::getUserId, userId)
                        .eq(PgTagUser::getTagId, tagId)
                )
        );
    }


    public record TagSingleForm(List<Long> tagIds) {
    }

    @Operation(summary = "单个用户贴标签", description = "支持贴多个标签")
    @PostMapping("tag/single/{userId}")
    public BaseResult<Boolean> tagSingle(@PathVariable Long userId, @RequestBody TagSingleForm form) {
        return BaseResult.success(
                pgTagService.tagByUserId(userId, form.tagIds())
        );
    }


    @Operation(summary = "用户购买记录", description = "包括活动")
    @PostMapping("buyRecord/{userId}")
    public BaseResult<IPage<PgVipVO>> record(@PathVariable Long userId, @RequestBody PageQuery query) {

        IPage<PgVipVO> page = pgVipService.page(query.toMpPageSortByCreateTime(), new LambdaQueryWrapper<PgVip>()
                .eq(PgVip::getUserId, userId)
        ).convert(pgVip -> BeanUtil.copyProperties(pgVip, PgVipVO.class));
        return BaseResult.success(page);
    }

    @Operation(summary = "用户信息")
    @GetMapping("userInfo/{userId}")
    public BaseResult<ManageUserVO> userInfo(@PathVariable Long userId) {
        PgUsers user = pgUsersService.getById(userId);
        if (ObjectUtil.isNull(user)) {
            return BaseResult.code(GlobalCode.Item_Null);
        }
        ManageUserVO usersVO = BeanUtil.copyProperties(user, ManageUserVO.class);

        // 标签
        usersVO.setTagList(
                pgTagService.listByUserId(usersVO.getId())
        );

        return BaseResult.success(usersVO);
    }

    public record UserRemarkForm(String remark) {
    }

    @Operation(summary = "给用户备注信息")
    @PostMapping("remark/{userId}")
    public BaseResult<Boolean> remark(@PathVariable Long userId, @RequestBody UserRemarkForm form) {

        PgUsers users = pgUsersService.getById(userId);

        if (ObjectUtil.isNull(users)) {
            return BaseResult.code(GlobalCode.Item_Null);
        }

        // 备注信息
        users.setRemark(form.remark());

        return BaseResult.success(pgUsersService.updateById(users));
    }

}
