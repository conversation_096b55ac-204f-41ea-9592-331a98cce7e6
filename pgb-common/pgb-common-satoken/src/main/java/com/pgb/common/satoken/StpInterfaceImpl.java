package com.pgb.common.satoken;

import cn.dev33.satoken.stp.StpInterface;
import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.pgb.common.core.global.RoleConstants;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * 自定义权限加载接口实现类
 */
@Slf4j
@Component    // 保证此类被 SpringBoot 扫描，完成 Sa-Token 的自定义权限验证扩展
public class StpInterfaceImpl implements StpInterface {

    /**
     * 返回一个账号所拥有的权限码集合
     */
    @Override
    public List<String> getPermissionList(Object loginId, String loginType) {
        // 本 list 仅做模拟，实际项目中要根据具体业务逻辑来查询权限
        List<String> permissions = StrUtil.split(StpUtil.getSession().getString("permissions"), ",");
        if (ObjectUtil.isNull(permissions)) {
            permissions = new ArrayList<>();
        }
        return permissions;
    }

    /**
     * 返回一个账号所拥有的角色标识集合 (权限与角色可分开校验)
     */
    @Override
    public List<String> getRoleList(Object loginId, String loginType) {
        List<String> list = new ArrayList<>();

        String role = StpUtil.getSession().getString("role");

        if (role == null) {
            StpUtil.logout();
            log.info("当前用户无角色");
            return list;
        }

        // 如果是管理角色
        if (role.equals(RoleConstants.Manager)) {
            // 把其他几个角色都拿到
//            list.add(RoleConstants.Tenant);
//            list.add(RoleConstants.Account);
//            list.add(RoleConstants.User);
            list.add(RoleConstants.Manager);
        }

        // 默认添加原角色
        list.add(role);

        return list;
    }

}

