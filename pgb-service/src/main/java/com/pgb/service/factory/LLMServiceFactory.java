package com.pgb.service.factory;

import com.pgb.ai.LLMService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * Created by 2025/7/18 15:57
 */
@Component
public class LLMServiceFactory {

    private final LLMService aliLLMService;
    private final LLMService baiduLLMService;

    // 通过构造函数注入两个实现类
    @Autowired
    public LLMServiceFactory(
            @Qualifier("aliLLMService") LLMService aliLLMService,
            @Qualifier("baiduLLMService") LLMService baiduLLMService
    ) {
        this.aliLLMService = aliLLMService;
        this.baiduLLMService = baiduLLMService;
    }


    public LLMService getLLMService(String type) {

        return switch (type.toLowerCase()) {
            case "baidu" -> baiduLLMService;
            case "ali" -> aliLLMService;
            default -> throw new IllegalArgumentException("Unsupported LLM service type: " + type);
        };

    }

}
