package com.pgb.service.db.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.pgb.service.db.PgPopupUserService;
import com.pgb.service.domain.popup.PgPopupUser;
import com.pgb.service.mapper.PgPopupUserMapper;
import org.springframework.stereotype.Service;

import java.util.Date;

/**
 * <AUTHOR>
 * @description 针对表【pg_popups_user】的数据库操作Service实现
 * @createDate 2024-11-14 16:34:38
 */
@Service
public class PgPopupUserServiceImpl extends ServiceImpl<PgPopupUserMapper, PgPopupUser>
        implements PgPopupUserService {
    @Override
    public PgPopupUser createOrAddRecord(Long userId, Long popupId) {
        PgPopupUser popupUser = getOne(new LambdaQueryWrapper<PgPopupUser>()
                .eq(PgPopupUser::getPopupId, popupId)
                .eq(PgPopupUser::getUserId, userId)
                .last("LIMIT 1")
        );

        if (ObjectUtil.isNull(popupUser)) {
            popupUser = new PgPopupUser();
            popupUser.setPopupId(popupId);
            popupUser.setUserId(userId);
            popupUser.setActiveNum(1);
            popupUser.setCreateTime(new Date());
            popupUser.setUpdateTime(new Date());
            save(popupUser);
        }
        // 存在，次数+1
        else {
            popupUser.setActiveNum(
                    popupUser.getActiveNum() + 1
            );
            popupUser.setUpdateTime(new Date());
            updateById(popupUser);
        }

        return popupUser;
    }
}




