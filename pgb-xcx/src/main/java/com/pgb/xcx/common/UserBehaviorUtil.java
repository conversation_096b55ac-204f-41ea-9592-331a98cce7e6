package com.pgb.xcx.common;

import com.pgb.common.redis.RedisUtils;
import lombok.extern.slf4j.Slf4j;

import java.time.Duration;

/**
 * <AUTHOR>
 * Created by 2024/7/24 10:23
 */

@Slf4j
public class UserBehaviorUtil {


    /**
     * 判断某个行为是否超过限制
     *
     * @param redisKey
     * @return
     */
    public static Boolean isLimitOver(String redisKey, Integer overNum) {

        long atomicValue = RedisUtils.getAtomicValue(redisKey);

        return atomicValue >= overNum;
    }


    /**
     * 增加次数限制，线程安全
     *
     * @param redisKey
     * @return
     */
    public static Boolean addLimitOverNum(String redisKey, Duration duration) {

        // 判断是否存在
        boolean isExist = RedisUtils.isExistsObject(redisKey);

        RedisUtils.incrAtomicValue(redisKey);

        // 如果不存在，再设置 10分钟后过期
        if (!isExist) {
            RedisUtils.expire(redisKey, duration);
        }

        return true;
    }

    // 判断是否超出限制
    public static Boolean isLimit(String redisKey, Integer num, Duration duration) {

        // 判断是否超出限制
        if (isLimitOver(redisKey, num)) {
            return true;
        }
        // 没有超出限制
        else {
            addLimitOverNum(redisKey, duration);
            return false;
        }
    }
}
