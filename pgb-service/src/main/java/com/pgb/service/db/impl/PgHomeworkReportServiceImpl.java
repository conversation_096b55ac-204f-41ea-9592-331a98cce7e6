package com.pgb.service.db.impl;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.pgb.common.redis.QueueUtils;
import com.pgb.service.domain.GlobQueueConstants;
import com.pgb.service.domain.homework.PgHomeworkReport;
import com.pgb.service.db.PgHomeworkReportService;
import com.pgb.service.enums.CorrectStatusEnum;
import com.pgb.service.mapper.PgHomeworkReportMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
* <AUTHOR>
* @description 针对表【pg_homework_report】的数据库操作Service实现
* @createDate 2025-03-04 19:14:32
*/
@Service
@Slf4j
public class PgHomeworkReportServiceImpl extends ServiceImpl<PgHomeworkReportMapper, PgHomeworkReport>
    implements PgHomeworkReportService{

    @Override
    public Integer queryToExport() {
        // 7分钟之前的
        List<PgHomeworkReport> list = list(new LambdaQueryWrapper<PgHomeworkReport>()
                .ne(PgHomeworkReport::getStatus, CorrectStatusEnum.Corrected)
                .lt(PgHomeworkReport::getCreateTime, DateUtil.offsetMinute(new Date(), -7))
        );

        log.info("【待生成班级分析报告扫描】待生成班级分析报告数量:{}个", list.size());

        list.forEach(record -> {
            // 加入导出队列
            if (!QueueUtils.containsQueueObject(GlobQueueConstants.PGB_XCX_HOMEWORK_REPORT_QUEUE.name(), record.getId())) {
                QueueUtils.addQueueObject(GlobQueueConstants.PGB_XCX_HOMEWORK_REPORT_QUEUE.name(), record.getId());
            } else {
                log.info("当前作业报告已存在导出队列中：{}，跳过", record.getId());
            }
        });

        return list.size();
    }
}




