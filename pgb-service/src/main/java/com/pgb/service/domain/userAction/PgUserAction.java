package com.pgb.service.domain.userAction;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.util.Date;

import com.pgb.service.enums.UserActionType;
import lombok.Data;

/**
 * @TableName pg_user_action
 */
@TableName(value = "pg_user_action")
@Data
public class PgUserAction implements Serializable {
    /**
     * 用户行为状态
     */
    @TableId
    private Long id;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 行为类型
     */
    private UserActionType type;

    /**
     * 状态 0：未完成，1：已完成
     */
    private Integer status;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

}
