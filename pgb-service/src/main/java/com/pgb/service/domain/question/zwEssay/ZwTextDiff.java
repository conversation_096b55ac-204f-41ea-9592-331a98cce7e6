package com.pgb.service.domain.question.zwEssay;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class ZwTextDiff {
    @Schema(title = "源文本")
    private String source;

    @Schema(title = "目标文本")
    private String target;

    @Schema(title = "对比原文差异类型")
    private ZwTextDiffTypeEnum type;

    public enum ZwTextDiffTypeEnum {
        CHANGE,
        DELETE,
        INSERT,
        EQUAL;
    }
}
