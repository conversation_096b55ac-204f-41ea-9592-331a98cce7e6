package com.pgb.manage.controller.crm;

import cn.dev33.satoken.annotation.SaCheckRole;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.pgb.common.core.global.BaseResult;
import com.pgb.common.core.global.GlobalCode;
import com.pgb.common.core.global.RoleConstants;
import com.pgb.common.core.utils.Id2CodeUtil;
import com.pgb.manage.domain.query.CodeQuery;
import com.pgb.service.db.PgVipCodeService;
import com.pgb.service.domain.vip.PgVipCode;
import com.pgb.service.domain.vip.PgVipCodeVO;
import com.pgb.service.enums.ChannelTypeEnum;
import com.pgb.service.enums.VipTypeEnum;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * Created by 2025/2/6 10:32
 */
@Tag(name = "管理端/兑换码管理")
@RestController("ManageCodeController")
@Transactional(rollbackFor = Exception.class)
@RequestMapping("/manage/code")
@RequiredArgsConstructor
@Slf4j
@SaCheckRole(RoleConstants.Manager)
public class CodeController {

    private final PgVipCodeService pgVipCodeService;

    @Operation(summary = "兑换码列表")
    @PostMapping("page")
    public BaseResult<IPage<PgVipCodeVO>> page(@RequestBody CodeQuery query) {

        IPage<PgVipCodeVO> page = pgVipCodeService.page(query.toMpPageSortByCreateTime(), new LambdaQueryWrapper<PgVipCode>()
                .like(StrUtil.isNotBlank(query.getCode()), PgVipCode::getCode, query.getCode())
                .eq(ObjectUtil.isNotNull(query.getStatus()), PgVipCode::getStatus, query.getStatus())
        ).convert(code -> BeanUtil.copyProperties(code, PgVipCodeVO.class));

        return BaseResult.success(page);
    }

    @Operation(summary = "删除兑换码")
    @DeleteMapping("{id}")
    public BaseResult<Boolean> delete(@PathVariable Long id) {

        PgVipCode code = pgVipCodeService.getById(id);

        if (ObjectUtil.isNull(code)) {
            return BaseResult.code(GlobalCode.Item_Null);
        }

        if (code.getStatus() != 0) {
            return BaseResult.error("该兑换码已被使用");
        }

        return BaseResult.success(pgVipCodeService.removeById(id));
    }

    @Data
    @ColumnWidth(30)
    @EqualsAndHashCode
    public static class VipCodeExcel {
        @ExcelProperty("兑换码")
        private String code;

        @ExcelProperty("兑换码类型")
        private String type;
    }

    @Data
    public static class VipCodeDTO {

        @Schema(title = "会员类型")
        private VipTypeEnum vipType;

        @Schema(title = "兑换码数量")
        private Integer codeNum;
    }

    @Operation(summary = "生成兑换码并导出表")
    @PostMapping("generate/{key}")
    public void generate(HttpServletResponse response, @RequestBody VipCodeDTO dto, @PathVariable String key) throws IOException {

        if (!"generateVipCode".equals(key)) {
            return;
        }

        // 导出
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");
        response.setHeader("Access-Control-Expose-Headers", "Content-Disposition"); // 必须设置，否则无法获取文件名
        response.setHeader("Access-Control-Allow-Origin", "*");

        // 这里URLEncoder.encode可以防止中文乱码 当然和easyexcel没有关系
        String fileName = URLEncoder.encode(DateUtil.format(new Date(), "yyyy-MM-dd") + "兑换码表-" + dto.getVipType().desc + dto.getCodeNum() + "个", StandardCharsets.UTF_8);
        response.setHeader("Content-Disposition", "attachment;filename=" + fileName + ".xlsx");

        // 执行导出逻辑
        EasyExcel.write(response.getOutputStream(), VipCodeExcel.class).sheet("表格一").doWrite(() -> {

            List<VipCodeExcel> dataList = new ArrayList<>();

            for (int i = 0; i < dto.getCodeNum(); i++) {
                PgVipCode vipCode = new PgVipCode();
                vipCode.setCode(
                        Id2CodeUtil.encode(
                                IdUtil.getSnowflakeNextId()
                        )
                );
                vipCode.setStatus(0);
                vipCode.setCreateTime(new Date());
                vipCode.setChannelType(ChannelTypeEnum.SYSTEM);
                vipCode.setVipType(dto.getVipType());

                pgVipCodeService.save(vipCode);

                VipCodeExcel vip = new VipCodeExcel();
                vip.setCode(vipCode.getCode());
                vip.setType(vipCode.getVipType().desc);

                dataList.add(vip);
            }

            return dataList;
        });
    }
}
