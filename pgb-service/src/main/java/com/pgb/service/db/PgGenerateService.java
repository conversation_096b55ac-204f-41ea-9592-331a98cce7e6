package com.pgb.service.db;

import com.baomidou.mybatisplus.extension.service.IService;
import com.pgb.service.domain.generate.PgGenerate;
import com.pgb.service.domain.common.TodayNum;

/**
* <AUTHOR>
* @description 针对表【pg_generate(【万能写】记录表)】的数据库操作Service
* @createDate 2024-08-24 19:34:23
*/
public interface PgGenerateService extends IService<PgGenerate> {

    TodayNum getTodayUseNum(Long userId);
}
