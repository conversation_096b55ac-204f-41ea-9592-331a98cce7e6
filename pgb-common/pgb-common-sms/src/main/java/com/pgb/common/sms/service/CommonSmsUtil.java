package com.pgb.common.sms.service;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baidubce.auth.DefaultBceCredentials;
import com.baidubce.services.sms.SmsClient;
import com.baidubce.services.sms.SmsClientConfiguration;
import com.baidubce.services.sms.model.SendMessageV3Request;
import com.baidubce.services.sms.model.SendMessageV3Response;
import com.pgb.common.core.global.GlobalCode;
import com.pgb.common.sms.SmsResDTO;
import com.pgb.common.sms.SmsTemplateConfig;
import lombok.extern.slf4j.Slf4j;
import org.dromara.sms4j.api.SmsBlend;
import org.dromara.sms4j.api.entity.SmsResponse;
import org.dromara.sms4j.core.factory.SmsFactory;
import java.util.LinkedHashMap;

@Slf4j
public class CommonSmsUtil {
    /**
     * 发送短信
     *
     * @param template
     * @param phone
     * @param params
     * @return
     */
    public SmsResDTO sendSms(SmsTemplateConfig template, String phone, String... params) {
        // 将params转化为map
        LinkedHashMap<String, String> varMap = new LinkedHashMap<>();

        for (int i = 0; i < params.length; i++) {
            varMap.put("" + i, params[i]);
        }

        return sendSms(template, phone, varMap);
    }

    /**
     * 重载，默认不传参数
     *
     * @param phone
     * @return
     */
    public SmsResDTO sendSms(SmsTemplateConfig template, String phone) {
        return sendSms(template, phone, new LinkedHashMap<>());
    }

    /**
     * 发送短信
     *
     * @param phone
     * @param varMap
     * @return
     */
    public static SmsResDTO sendSmsV2(SmsTemplateConfig template, String phone, LinkedHashMap<String, String> varMap) {

        try {
            // 如： SmsBlend blend = SmsFactory.getSmsBlend("tencent");
            SmsBlend blend = SmsFactory.getSmsBlend(template.getConfigId());
            SmsResponse response = blend.sendMessage(phone, template.getTemplateId(), varMap);

            // 为空，发送失败
            if (!response.isSuccess()) {
                log.error("短信发送返回异常 => \n{} \n 【参数-{}】\n{} \n {}", JSONUtil.toJsonStr(response), phone, StrUtil.toString(template), StrUtil.toString(varMap));
                return SmsResDTO.builder()
                        .success(false)
                        .msg(response.getData().toString())
                        .errCode(GlobalCode.Sms_Code_Error)
                        .build();
            }
        } catch (Exception e) {
            log.error("短信发送异常 =>", e);
            return SmsResDTO.builder()
                    .success(false)
                    .msg("短信发送失败，请稍后再试")
                    .errCode(GlobalCode.Sms_Code_Error)
                    .build();
        }

        return SmsResDTO.builder()
                .success(true)
                .build();
    }

    // TODO 临时方案
    public static SmsResDTO sendSms(SmsTemplateConfig template, String phone, LinkedHashMap<String, String> varMap) {

        SmsClientConfiguration config = new SmsClientConfiguration();
        config.setCredentials(new DefaultBceCredentials("ALTAKKC0EdMoq61wyx0mi3L1sA", "224c72848ed64d6b8639e5fce9ac9a1c"));
        config.setEndpoint("http://sms.bj.baidubce.com");
        SmsClient client = new SmsClient(config);

        SendMessageV3Request request = new SendMessageV3Request();
        request.setMobile(phone);
        request.setSignatureId("sms-sign-dKFyGl83403");
        request.setTemplate(template.getTemplateId());
        request.setContentVar(varMap);
        SendMessageV3Response response = client.sendMessage(request);
        // 解析请求响应 response.isSuccess()为true 表示成功
        if (response != null && response.isSuccess()) {
            return SmsResDTO.builder()
                    .success(true)
                    .build();
        } else {
            log.error("短信发送返回异常 => \n{} \n 【参数-{}】\n{} \n {}", JSONUtil.toJsonStr(response), phone, StrUtil.toString(template), StrUtil.toString(varMap));
            return SmsResDTO.builder()
                    .success(false)
                    .msg("短信发送失败，请稍后再试")
                    .errCode(GlobalCode.Sms_Code_Error)
                    .build();
        }
    }

}
