package com.pgb.service.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;

/**
 * <AUTHOR>
 * Created by 2024/9/13 15:22
 */
@Schema(description = "时间 类型")
@AllArgsConstructor
public enum DurationEnum {

    @Schema(title = "今天")
    Today(0,"今天"),

    @Schema(title = "昨天")
    Yesterday(1,"昨天"),

    @Schema(title = "自定义")
    Custom(2,"自定义");
    /**
     * 枚举值对应的整数值
     */
    @EnumValue
    public final int value;

    /**
     * 文字说明
     */
    public final String desc;
}
