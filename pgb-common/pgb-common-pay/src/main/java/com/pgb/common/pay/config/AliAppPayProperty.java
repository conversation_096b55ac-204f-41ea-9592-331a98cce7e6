package com.pgb.common.pay.config;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.core.io.ClassPathResource;
import org.springframework.util.StreamUtils;

import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Paths;

@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode
@ToString
@Builder
@Schema(title = "支付宝APP支付配置")
@ConfigurationProperties(prefix = "ali.pay")
public class AliAppPayProperty {
    // 基础配置参数
    private String appId;              // 应用ID（从支付宝开放平台获取）
    // 密钥文件路径（推荐使用文件存储）
    private String privateKeyPath;     // 商户私钥文件路径（如 classpath:cert/private_key.pem）
    private String publicKeyPath;// 应用公钥文件路径
    // 支付宝公钥文件路径
    private String aliPublicKeyPath;

    public String getPrivateKey() {
        return readKeyFile(privateKeyPath);
    }

    public String getPublicKey() {
        return readKeyFile(publicKeyPath);
    }

    public String getAliPublicKey() {
        return readKeyFile(aliPublicKeyPath);
    }

    /**
     * 读取密钥文件内容（支持 classpath 和绝对路径）
     */
    private String readKeyFile(String path) {
        try {
            if (path.startsWith("classpath:")) {
                path = path.replace("classpath:", "");
                try (InputStream inputStream = new ClassPathResource(path).getInputStream()) {
                    return StreamUtils.copyToString(inputStream, StandardCharsets.UTF_8)
                            .replace("-----BEGIN PRIVATE KEY-----", "")
                            .replace("-----END PRIVATE KEY-----", "")
                            .replaceAll("\\s+", "");
                }
            } else {
                // 处理文件系统路径（如 /etc/certs/private_key.pem）
                return Files.readString(Paths.get(path));
            }
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }
}
