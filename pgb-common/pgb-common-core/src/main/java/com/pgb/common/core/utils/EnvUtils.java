package com.pgb.common.core.utils;

import cn.hutool.extra.spring.SpringUtil;

public class EnvUtils {

    /**
     * 是否是开发环境
     * @return
     */
    public static boolean isDev() {
        return SpringUtil.getActiveProfile().equals("dev");
    }

    /**
     * 是否是 测试环境
     * @return
     */
    public static boolean isTest() {
        return SpringUtil.getActiveProfile().equals("test");
    }

    /**
     * 是否是 负载均衡环境
     * @return
     */
    public static boolean isBalance() {
        return SpringUtil.getActiveProfile().equals("balance");
    }

    /**
     * 是否是 生产环境
     * @return
     */
    public static boolean isProd() {
        return SpringUtil.getActiveProfile().equals("prod");
    }
}
