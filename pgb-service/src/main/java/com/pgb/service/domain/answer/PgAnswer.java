package com.pgb.service.domain.answer;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;

import com.pgb.common.mybatis.handler.JsonbTypeHandler;
import com.pgb.service.enums.CorrectStatusEnum;
import lombok.Data;

/**
 *
 * @TableName pg_answer
 */
@TableName(value ="pg_answer")
@Data
public class PgAnswer implements Serializable {
    /**
     * 题库，做题记录
     */
    @TableId
    private Long id;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 用户本题作答，含题目及批改信息
     */
    @TableField(typeHandler = JsonbTypeHandler.class)
    private Object answer;

    /**
     * 批改状态
     */
    private CorrectStatusEnum status;

    /**
     * 本题消耗脑力值数量
     */
    private Long aiTokens;

    /**
     * 本题批改满意度，如果开启批改反馈
     */
    private Integer feedback;

    /**
     * 批改所需时长，单位：秒
     */
    private Integer correctDuration;

    /**
     * AI批改完成时间
     */
    private Date correctTime;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 批改结果
     */
    @TableField(typeHandler = JsonbTypeHandler.class)
    private Object correctResult;

    /**
     * 题目id
     */
    private Long questionId;

    /**
     * 备注名称
     */
    private String name;

    /**
     * 标记是否删除
     */
    private Boolean deleted;

    /**
     * 所属作业id
     */
    private Long homeworkId;

    /**
     * 所属学生id
     */
    private Long studentId;

    /**
     * 所属批次id
     */
    private Long batchId;

    /**
     * 是否归档，归档后批改结果不可修改，仅可查看或导出，默认180天后归档
     */
    private Boolean isArchive;

    /**
     * 归档后的批改结果json url，前端可直接获取，无需再调用接口获取
     * 注意：归档后，再次导出需要使用该内容，correctResult字段内容为null
     */
    private String archiveUrl;

}
