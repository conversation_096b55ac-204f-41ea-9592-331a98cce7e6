package com.pgb.common.oss.domain;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.RequiredArgsConstructor;

import java.util.Map;

@Data
@Builder
@RequiredArgsConstructor
@AllArgsConstructor
@Schema(title = "OSS 预签名实体")
public class OssPresignUrl {
    @Schema(title = "文件上传URL", description = "上传方法见method")
    private String uploadUrl;

    @Schema(title = "文件上传method")
    @Builder.Default
    private String method = "PUT";

    @Schema(title = "keyValue", description = "文件完整的key")
    private String keyValue;

    @Schema(title = "访问路径", description = "上传成功后，文件可访问路径")
    private String fileUrl;

    @Schema(title = "签名参数", description = "客户端直传使用")
    private Map<String, String> signature;

    @Schema(title = "内容类型")
    private String contentType;
}
