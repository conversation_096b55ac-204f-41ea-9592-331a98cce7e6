package com.pgb.xcx.controller.config;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import com.pgb.common.core.global.BaseResult;
import com.pgb.common.core.global.GlobalCode;
import com.pgb.service.db.PgUserConfigService;
import com.pgb.service.domain.userConfig.ExportConfigDTO;
import com.pgb.service.domain.userConfig.PgUserConfig;
import com.pgb.service.domain.userConfig.StudentReportConfigDTO;
import com.pgb.service.enums.UserConfigEnum;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.util.Date;

/**
 * <AUTHOR>
 * Created by 2025/3/11 16:50
 */
@Tag(name = "用户端/配置/学生端配置")
@RestController("UserConfigStudentController")
@Transactional(rollbackFor = Exception.class)
@RequestMapping("/user/config/student")
@RequiredArgsConstructor
@Slf4j
public class StudentController {

    private final PgUserConfigService pgUserConfigService;

    @Operation(summary = "获取学生端查看批改结果配置")
    @GetMapping
    public BaseResult<StudentReportConfigDTO> getConfig() {

        PgUserConfig config = pgUserConfigService.getByKey(UserConfigEnum.STUDENT_REPORT, StpUtil.getLoginIdAsLong());

        // 若没设置过
        if (ObjectUtil.isNull(config)) {
            return BaseResult.success(new StudentReportConfigDTO());
        }

        return BaseResult.success(
                BeanUtil.toBean(config.getValue(), StudentReportConfigDTO.class)
        );
    }


    @Operation(summary = "保存学生端查看批改结果配置")
    @PostMapping
    public BaseResult<Boolean> save(@RequestBody StudentReportConfigDTO reportConfig) {

        PgUserConfig config = pgUserConfigService.getByKey(UserConfigEnum.STUDENT_REPORT, StpUtil.getLoginIdAsLong());

        // 如果没设置过
        if (ObjectUtil.isNull(config)) {
            config = new PgUserConfig();
            config.setUserId(StpUtil.getLoginIdAsLong());
            config.setKey(UserConfigEnum.STUDENT_REPORT.name());
            config.setValue(reportConfig);
            config.setRemark("学生端查看批改结果配置");
            config.setIsValid(true);
            config.setCreateTime(new Date());
            pgUserConfigService.save(config);
        }

        // 如果设置过 则更新
        config.setValue(reportConfig);
        pgUserConfigService.updateById(config);

        return BaseResult.success(true);
    }

}
