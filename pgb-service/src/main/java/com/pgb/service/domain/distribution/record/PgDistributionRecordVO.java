package com.pgb.service.domain.distribution.record;

import java.io.Serializable;

import java.util.Date;

import cn.hutool.core.util.NumberUtil;
import com.fasterxml.jackson.annotation.JsonGetter;
import com.pgb.service.enums.ProfitStatusEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.ToString;
import lombok.Builder;

/**
 * @TableName pg_distribution_record
 */
@Schema(description = " VO")
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode
@ToString
@Builder
public class PgDistributionRecordVO implements Serializable {

    @Schema(title = "分销记录id")
    private Long id;

    @Schema(title = "邀请人id")
    private Long shareUserId;

    @Schema(title = "被邀请人id")
    private Long userId;

    @Schema(title = "支付金额")
    private Integer payAmount;

    @Schema(title = "佣金")
    private Integer payCommission;

    @Schema(title = "订单 id")
    private Long orderId;

    @Schema(title = "创建时间")
    private Date createTime;

    @Schema(title = "是否转账")
    private Boolean isTransfer;

    @Schema(title = "转账时间")
    private Date transferTime;

    @Schema(title = "批次关闭原因")
    private String closeReason;

    @Schema(title = "微信订单号")
    private String transactionId;

    @Schema(title = "商户分账单号")
    private String outOrderNo;

    @Schema(title = "分账单状态")
    private ProfitStatusEnum status;

    @Schema(title = "被邀请人昵称")
    private String nickName;

    @Schema(title = "被邀请人头像")
    private String avatarUrl;

    @Schema(title = "返现（元）")
    @JsonGetter
    public String getPayCommissionStr() {
        return NumberUtil.decimalFormat("0.00", this.payCommission / 100.0);
    }
}
