package com.pgb.common.pay.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;

@Schema(title = "全局 购买类型")
@AllArgsConstructor
public enum BuyTypeEnum {

    PRESENT(0, "系统赠送"),

    PAY(1, "支付购买"),

    FREE(2, "免费获得"),

    EXCHANGE(3, "兑换码获得");

    /**
     * 枚举值对应的整数值
     */
    @EnumValue
    public final int value;

    /**
     * 文字说明
     */

    public final String desc;
}
