package com.pgb.service.db;

import com.pgb.service.domain.answer.PgAnswer;
import com.pgb.service.domain.zc.answer.PgZcAnswer;
import com.baomidou.mybatisplus.extension.service.IService;
import com.pgb.service.domain.zc.question.chinese.ZcSubmitForm;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【pg_zc_answer】的数据库操作Service
* @createDate 2025-04-21 14:53:17
*/
public interface PgZcAnswerService extends IService<PgZcAnswer> {


    /**
     * 查询超时未批改的题目，并加入批改队列
     */
    public Integer queryToCorrect();


    /**
     * 查询超时未批改的题目，并决定是否加入批改队列
     * @param isReCorrect
     * @param overMinute
     * @return
     */
    public Integer queryToCorrect(Boolean isReCorrect, Integer overMinute);

    /**
     * 提交字词作业答案
     * @param form
     * @param userId
     * @return
     */
    PgZcAnswer submitZcHomeworkAnswer(ZcSubmitForm form, Long userId);

    /**
     * 将所有的批改结果图片累加，做 md5 操作
     *
     * @param ids
     * @return
     */
    public String getImgMd5(List<Long> ids, Long userId);

    /**
     * 重批指定批改结果
     * @param zcAnswer
     * @return
     */
    PgZcAnswer reCorrect(PgZcAnswer zcAnswer, boolean isCost);

}
