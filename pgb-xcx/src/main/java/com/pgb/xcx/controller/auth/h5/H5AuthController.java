package com.pgb.xcx.controller.auth.h5;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.URLUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.pgb.common.core.global.BaseResult;
import com.pgb.common.core.global.GlobalCode;
import com.pgb.common.satoken.LoginVO;
import com.pgb.service.db.PgUsersService;
import com.pgb.service.domain.user.PgUsers;
import com.pgb.service.domain.user.PgUsersVO;
import com.pgb.service.enums.DeviceTypeEnum;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.api.WxConsts;
import me.chanjar.weixin.common.bean.WxJsapiSignature;
import me.chanjar.weixin.common.bean.WxOAuth2UserInfo;
import me.chanjar.weixin.common.bean.oauth2.WxOAuth2AccessToken;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.mp.api.WxMpService;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

@Tag(name = "用户端/权限/登录/h5")
@RestController("UserAuthH5LoginController")
@Transactional(rollbackFor = Exception.class)
@RequestMapping("/user/auth/login")
@RequiredArgsConstructor
@Slf4j
public class H5AuthController {

    private final WxMpService wxMpService;

    private final PgUsersService pgUsersService;

    @Operation(summary = "获取微信js授权")
    @GetMapping("jsSignature")
    public BaseResult<WxJsapiSignature> getJsSignature(String url) {
        // 获取鉴权内容
        try {
            return BaseResult.success(
                    wxMpService.createJsapiSignature(url)
            );
        } catch (WxErrorException e) {
            log.error("获取微信公众号js授权失败", e);
            return BaseResult.error(e.getMessage());
        }
    }

    @Operation(summary = "微信授权跳转url")
    @GetMapping("wxJsUrl")
    public BaseResult<String> wxJsUrl(String url) {

        String redirectUrl = wxMpService.getOAuth2Service().buildAuthorizationUrl(url, WxConsts.OAuth2Scope.SNSAPI_USERINFO, null);

        return BaseResult.success("OK", redirectUrl);
    }

    @Operation(summary = "微信授权跳转url")
    @GetMapping("wxJsUrl/h5")
    public BaseResult<String> wxJsUrlH5(String url) {

        // 生成一个自定义令牌code
        String buildUrl = StrUtil.format("https://h5.pigaibang.com/login/wx/auth/{}",
                URLUtil.encodeAll(url)
        );
        String redirectUrl = wxMpService.getOAuth2Service().buildAuthorizationUrl(buildUrl, WxConsts.OAuth2Scope.SNSAPI_USERINFO, null);

        return BaseResult.success("OK", redirectUrl);
    }

    @Operation(summary = "根据自定义token，获取登录信息")
    @GetMapping("token/{code}")
    public BaseResult<LoginVO<PgUsersVO>> token(@PathVariable String code) {

        if (StrUtil.isBlank(code)) {
            return BaseResult.code(GlobalCode.Param_Wrong);
        }

        try {
            // 获取 access_token
            WxOAuth2AccessToken accessToken = wxMpService.getOAuth2Service().getAccessToken(code);

            // 拿授权信息
            WxOAuth2UserInfo result = wxMpService.getOAuth2Service().getUserInfo(accessToken, null);

            // 根据 unionId
            PgUsers user = pgUsersService.getOne(new LambdaQueryWrapper<PgUsers>()
                    .eq(PgUsers::getWxUnionId, result.getUnionId())
                    .last("LIMIT 1")
            );

            if (ObjectUtil.isNull(user)) {
                return BaseResult.code(GlobalCode.Login_Wx_Error);
            }

            // 执行登录
            LoginVO<PgUsersVO> loginVO = pgUsersService.loginSuccess(user, DeviceTypeEnum.WX_H5);

            loginVO.getUserInfo().setWxMpOpenId(result.getOpenid());

            // 返回登录信息
            return BaseResult.success(loginVO);
        } catch (WxErrorException e) {
            log.error("【微信H5】获取登录信息异常：{}", e.getError());
            return BaseResult.error("微信H5获取登录信息异常");
        }
    }
}
