package com.pgb.common.core.jackson;

import cn.hutool.json.JSONNull;
import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.fasterxml.jackson.databind.annotation.JacksonStdImpl;
import com.fasterxml.jackson.databind.ser.std.StdScalarSerializer;
import org.springframework.boot.jackson.JsonComponent;

import java.io.IOException;

// 兼容 hutool json
@JacksonStdImpl
public class JsonNullSerializer extends StdScalarSerializer<JSONNull> {

    protected JsonNullSerializer(Class<JSONNull> t) {
        super(t);
    }

    public static final JsonNullSerializer INSTANCE = new JsonNullSerializer(JSONNull.class);

    @Override
    public void serialize(JSONNull jsonNull, JsonGenerator jsonGenerator, SerializerProvider serializerProvider) throws IOException {
        jsonGenerator.writeNull();
    }
}
