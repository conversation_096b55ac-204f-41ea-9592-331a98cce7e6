package com.pgb.service.domain.zc.text;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * Created by 2025/4/28 10:50
 */
@Schema(title = "课文信息")
@Data
public class TextInfo {

    @Schema(title = "id")
    private Long id;

    @Schema(title = "第几课")
    private Integer sort;

    @Schema(title = "课文名称")
    private String textName;

    @Schema(title = "识字数量")
    private Integer knowWordCount;

    @Schema(title = "生字数量")
    private Integer newWordCount;

    @Schema(title = "词语数量")
    private Integer ciyuCount;

    @Schema(title = "读读写写数量", description = "七八九年级使用")
    private Integer readAndWriteCount;

}
