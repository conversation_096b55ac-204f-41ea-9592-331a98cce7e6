package com.pgb.service.domain.zc.common.textbook.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.pgb.service.enums.PronunciationTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * 单词发音实体
 *
 * <AUTHOR>
 * Created by 2025/7/25 21:00
 */
@Data
@Schema(description = "单词发音实体")
public class PgWordPronunciation {

    @Schema(description = "主键ID")
    @TableId(value = "id")
    private Long id;

    @Schema(description = "所属单词ID")
    private Long wordId;

    @Schema(description = "音标")
    private String phonetic;

    @Schema(description = "音频地址")
    private String audioUrl;

    @Schema(description = "发音类型：UK-英音，US-美音")
    private PronunciationTypeEnum type;

    @Schema(description = "排序值")
    private Integer sort;

    @Schema(description = "创建时间")
    private Date createTime;

    @Schema(description = "更新时间")
    private Date updateTime;
}
