package com.pgb.service.domain.zc.text;

import java.io.Serializable;

import java.util.Date;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.ToString;
import lombok.Builder;

/**
 * 课文表
 *
 * @TableName pg_text
 */
@Schema(description = "课文表 VO")
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode
@ToString
@Builder
public class PgZcTextVO implements Serializable {

    @Schema(title = "id")
    private Long id;

    @Schema(title = "课文名")
    private String name;

    @Schema(title = "字词ids")
    private String wordIds;

    @Schema(title = "创建时间")
    private Date createTime;

    @Schema(title = "更新时间")
    private Date updateTime;

    @Schema(title = "所属教材id")
    private Long textbookId;

    @Schema(title = "单元（1，2，3，4）")
    private Integer unit;

    @Schema(title = "排序（第几课）")
    private Integer sort;

}
