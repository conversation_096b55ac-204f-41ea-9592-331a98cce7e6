package com.pgb.ai.domain;

import lombok.Data;

/**
 * 获取回复，且返回消耗的总token
 */
@Data
public class GPTAnswer {

    /**
     * 请求id
     */
    String requestId;

    /**
     * 返回的答案
     */
    String answer;

    /**
     * 消耗的 tokens
     */
    int tokens;

    /**
     * 自定义错误code
     * 0:正常
     * 1: 敏感词，更换大模型来源
     * 2: 异常，无法批改
     */
    Integer code;

    public GPTAnswer(String answer, int tokens) {
        this.answer = answer;
        this.tokens = tokens;
        this.code = 0;
    }

    public GPTAnswer(String answer, int tokens, Integer code) {
        this.answer = answer;
        this.tokens = tokens;
        this.code = code;
    }

    public GPTAnswer(String answer, int tokens, String requestId) {
        this.answer = answer;
        this.tokens = tokens;
        this.code = 0;
        this.requestId = requestId;
    }

    public GPTAnswer() {
        this.tokens = 0;
    }
}
