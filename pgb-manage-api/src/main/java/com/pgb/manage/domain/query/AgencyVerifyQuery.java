package com.pgb.manage.domain.query;

import com.pgb.common.mybatis.global.PageQuery;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * Created by 2025/2/28 14:33
 */
@EqualsAndHashCode(callSuper = true)
@Schema(description = "代理分销记录 搜索实体")
@Data
public class AgencyVerifyQuery extends PageQuery {

    @Schema(title = "被邀请人手机号")
    private String phone;

}
