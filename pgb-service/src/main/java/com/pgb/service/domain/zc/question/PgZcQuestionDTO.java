package com.pgb.service.domain.zc.question;

import java.io.Serializable;

import java.util.Date;

import com.pgb.common.core.validate.UpdateGroup;
import com.pgb.service.enums.GradeEnum;
import com.pgb.service.enums.SubjectEnum;
import com.pgb.service.enums.ZcQuestionTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.ToString;
import lombok.Builder;
import org.springframework.validation.annotation.Validated;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;

/**
 * @TableName pg_zc_question
 */
@Schema(description = " DTO")
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode
@ToString
@Validated
@Builder
public class PgZcQuestionDTO<T> implements Serializable {

    @Schema(title = "默写题库表", type = "string")
    @NotNull(message = "[默写题库表]不能为空", groups = {UpdateGroup.class})
    private Long id;

    @Schema(title = "用户id")
    private Long userId;

    @Schema(title = "科目，注意，不同的科目，默写类型是不同的")
    private SubjectEnum subject;

    @Schema(title = "默写类型，0：通用，1：情景式填空")
    private ZcQuestionTypeEnum type;

    @Schema(title = "题目分数")
    private Integer score;

    @Schema(title = "题目名称")
    @Size(max = 255)
    private String name;

    @Schema(title = "年级，可选")
    private GradeEnum grade;

    @Schema(title = "是否官方题目")
    private Boolean isOfficial;

    @Schema(title = "排序")
    private Integer sort;

    @Schema(title = "题目内容")
    private T contentJson;

    @Schema(title = "创建时间")
    private Date createTime;

    @Schema(title = "修改时间")
    private Date updateTime;

}
