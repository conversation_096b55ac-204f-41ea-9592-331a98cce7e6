
## 一、技术架构说明
### 1. 数据库-postgresql
数据库使用postgresql数据库

#### 表命名规范
以`pg_`为开头，举例：`pg_users` 用户表

#### 主键规范
主键使用int8类型，对应java的long型

#### 注释
表结构，每个字段均需要注释

### 2. mybatis-plus
使用半自动ORM工具，注意：本项目不需要创建xml文件，全局使用方法进行数据库操作，易于维护。

#### 生成实体、service、mapper规范
例如有一个表为：pg_answer

##### 实体 pojo
命名应该为表名的全称，例如 PgAnswer
如果涉及到 jsonb 格式，则使用``@TableField(typeHandler = JsonbTypeHandler.class)``来处理jsonb字段
```java
@TableName(value ="pg_answer", autoResultMap = true)
@Data
public class PgAnswer {
    // 字段
    @TableField(typeHandler = JsonbTypeHandler.class)
    private Object info;
}
```

##### service 及 impl
例如：PgAnswerService 及 例如：PgAnswerServiceImpl
```java
public interface PgAnswerService extends IService<PgAnswer> {
    // 可自定义扩展方法，扩展的方法需加上注释
}
```
要求：自定义扩展方法，扩展的方法需加上注释

##### mapper
例如：PgAnswerMapper
```java
public interface PgAnswerMapper extends BaseMapper<PgAnswer> {
} 
```
注意：mapper不需要再加注释了，只需要继承即可

#### 枚举类 Enum
使用注解`@EnumValue`来进行类别转化，数据库使用int类型来保存类别，如下所示：
```java
@Schema(title = "这是描述")
@AllArgsConstructor
public enum MaterialTypeEnum {
    TEXT(1, "文字"),
    
    IMAGE(2, "图片");

    /**
     * 枚举值对应的整数值
     */
    @EnumValue
    public final int value;

    /**
     * 文字说明
     */
    public final String desc;
}
```
注意：返回给前端的枚举，是字符串形式的，如：TEXT，而不是数字

### 3. Sa-Token 权限认证框架
1. 对于需要鉴权的接口，使用 `@SaCheckLogin` 注解来判断是否需要登录
2. 对于已经登录的用户，使用`long userId = StpUtil.getLoginIdAsLong();`来获取当前登陆者的id
3. 方法内判断用户是否已经登录，使用`StpUtil.checkLogin()`
4. 使用`StpUtil.getSession().getModel(RoleConstants.User, T.class)`来获取对应登录的实体信息，括号内的参数根据角色来选择

### 4. 队列
使用Redisson进行队列，工具类是`[QueueUtils.java](/pgb-common/pgb-common-redis/src/main/java/com/pgb/common/redis/QueueUtils.java)`

## 二、项目规范

### 接口前缀
注意：当前项目已经统一设置前缀`/api/`因此不需要在路径上再加api了

### 实体注入最佳实践
应该尽量使用`@RequiredArgsConstructor`注解，然后使用`private final `形式来注入。

### 接口返回封装
统一使用`BaseResult<T>`进行封装返回

### 接口命名规则
- 分页：使用 post 请求，且以 page 为路径结尾，例如：/book/page
- 列表：使用 post 请求，且以 list 为路径结尾，例如：/list

### 日志打印
日志使用 @slf4j 注解，
- 消息：使用`log.info()`进行打印，注意：打印参数时，需要使用`{}`占位符，例如：`log.info("参数：{}", params)`
- 异常：使用`log.error()`进行打印，注意：打印异常时，需要使用`{}`占位符，例如：`log.error("获取用户信息失败，userId: {}", userId, e)`

## 三、项目目录结构说明

-- pgb-manage-api 后端springboot接口代码

## 四、其他注意事项
1. 除非特别指定，不然不需要生成数据迁移相关的内容
2. 除非特别指定，不然不需要生成测试相关的内容

