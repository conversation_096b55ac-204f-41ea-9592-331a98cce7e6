package com.pgb.service.domain.query;

import com.pgb.common.mybatis.global.PageQuery;
import com.pgb.service.enums.GradeEnum;
import com.pgb.service.enums.SubjectEnum;
import com.pgb.service.enums.ZcQuestionTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <AUTHOR>
 * Created by 2025/4/21 16:15
 */

@EqualsAndHashCode(callSuper = true)
@Schema(description = "题目 搜索实体")
@Data
public class ZcQuestionQuery extends PageQuery {

    @Schema(title = "题目名称")
    private String name;

    @Schema(title = "科目类型")
    private SubjectEnum subject;

    @Schema(title = "题目类型")
    private List<ZcQuestionTypeEnum> types;

    @Schema(title = "年级")
    private GradeEnum grade;


}
