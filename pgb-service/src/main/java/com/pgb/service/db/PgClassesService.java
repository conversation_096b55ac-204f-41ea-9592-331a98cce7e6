package com.pgb.service.db;

import com.baomidou.mybatisplus.extension.service.IService;
import com.pgb.service.domain.classes.PgClasses;
import com.pgb.service.enums.GradeEnum;

/**
 * <AUTHOR>
 * @description 针对表【pg_classes(班级表)】的数据库操作Service
 * @createDate 2024-12-10 16:37:13
 */
public interface PgClassesService extends IService<PgClasses> {

    /**
     * 创建班级
     * @param createUserId
     * @param name
     * @param classNum
     * @param grade
     * @return
     */
    PgClasses createClass(Long createUserId, String name, String classNum, GradeEnum grade);
}
