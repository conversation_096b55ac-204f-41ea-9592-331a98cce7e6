package com.pgb.service.domain.homework;

import java.io.Serializable;

import java.util.Date;
import java.util.List;

import cn.hutool.json.JSONArray;
import com.pgb.service.domain.answer.gridPaper.GridPaperResult;
import com.pgb.service.enums.CorrectStatusEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.ToString;

/**
*
* @TableName pg_homework_grid_paper
*/
@Schema(description = " VO")
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode
@ToString
public class PgHomeworkGridPaperVO implements Serializable {

    /**
    * 格子纸批量上传
    */
    @Schema(title = "格子纸批量上传")
    private Long id;

    /**
    * 用户id
    */
    @Schema(title = "用户id")
    private Long userId;

    /**
    * 作业id
    */
    @Schema(title = "作业id")
    private Long homeworkId;

    /**
    * 当前处理状态
    */
    @Schema(title = "当前处理状态")
    private CorrectStatusEnum status;

    /**
    * 检测及分割结果，对应哪个学生
    */
    @Schema(title = "检测及分割结果，对应哪个学生")
    private List<GridPaperResult> imgResultJson;

    /**
    * 创建时间
    */
    @Schema(title = "创建时间")
    private Date createTime;

    /**
    * 完成状态
    */
    @Schema(title = "完成状态")
    private Date completeTime;

    /**
    * 处理时长，秒
    */
    @Schema(title = "处理时长，秒")
    private Integer duration;

    /**
    * 消耗token
    */
    @Schema(title = "消耗token")
    private Integer tokens;

    @Schema(title = "识别成功")
    private Integer successNum;

    @Schema(title = "识别失败")
    private Integer failNum;

}
