package com.pgb.common.ocr.impl;

import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.baidu.aip.ocr.AipOcr;
import com.pgb.common.ocr.OCRService;
import com.pgb.common.ocr.domain.OCRResult;
import lombok.extern.slf4j.Slf4j;
import org.json.JSONObject;

import java.time.Duration;
import java.util.HashMap;

@Slf4j
public class OCRBaidu extends OCRService {

    private final AipOcr client;

    public OCRBaidu(String appId, String apiKey, String apiSecret) {
        this.client = new AipOcr(appId, apiKey, apiSecret);
        log.info("【OCR 配置-百度】初始化完成");
    }

    /**
     * 传入图片 url ，返回 OCR 识别结果
     * @param url
     * @return
     */
    @Override
    public OCRResult handWriting(String url, Integer retryNum) {

        // 超过10次，异常
        if (retryNum > 10) {
            throw new RuntimeException("OCR 识别重试超过10次，请检查");
        }

        // 构造参数
        HashMap<String, String> map = new HashMap<>();
        map.put("recognize_granularity", "small");
        map.put("detect_direction", "true");
        map.put("detect_alteration", "false");

        try {
            //if (!url.contains("x-oss")) {
            //    url = url + "?x-oss-process=image/resize,l_4000/format,png";
            //}

            JSONObject jsonObject = this.client.handwritingUrl(url, map);

//            log.info("ocr 结果：{}", jsonObject);

            //JSONObject jsonObject = this.client.accurateGeneral(url, map);

            String result = jsonObject.toString();

            OCRResult ocrResult = JSONUtil.toBean(result, OCRResult.class);

            if (ObjectUtil.isNull(ocrResult.getWords_result())) {
                log.error("ocr 异常：{}；url: {}", result, url);
                // 超时重试
                if(ObjectUtil.defaultIfNull(JSONUtil.parseObj(result).getInt("error_code"), 0) == 282112) {
                    log.info("超时重试");
                    return handWriting(url, retryNum + 1);
                }
                // URL 不合法
                else if (ObjectUtil.defaultIfNull(JSONUtil.parseObj(result).getInt("error_code"), 0) == 282111) {
                    log.info("URL不合法，进入重试");
                    return handWriting(url, retryNum + 1);
                }
                // 频率限制
                else if (ObjectUtil.defaultIfNull(JSONUtil.parseObj(result).getInt("error_code"), 0) == 18){
                    log.info("频率限制，等待1秒，进行重试");
                    ThreadUtil.sleep(Duration.ofSeconds(1).toMillis());
                    return handWriting(url, retryNum + 1);
                }
            }
            ocrResult.setToken(2000L);

            return ocrResult;
        } catch (NoSuchMethodError e) {
            // log.error("ocr 异常：{}", e.getMessage());

            // 进入重试
            ThreadUtil.sleep(Duration.ofSeconds(1).toMillis());
            log.info("OCR 【NoSuchMethodError】 已重试");
            return handWriting(url, retryNum + 1);
        }
    }
}
