package com.pgb.xcx.controller.auth;

import cn.binarywang.wx.miniapp.api.WxMaService;
import cn.binarywang.wx.miniapp.bean.WxMaCodeLineColor;
import cn.binarywang.wx.miniapp.bean.WxMaJscode2SessionResult;
import cn.binarywang.wx.miniapp.bean.WxMaPhoneNumberInfo;
import cn.binarywang.wx.miniapp.bean.urllink.GenerateUrlLinkRequest;
import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.img.ImgUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.digest.DigestUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.pgb.common.core.global.BaseResult;
import com.pgb.common.core.global.GlobalCode;
import com.pgb.common.core.global.GlobalConstants;
import com.pgb.common.core.utils.EnvUtils;
import com.pgb.common.redis.RedisUtils;
import com.pgb.common.satoken.LoginVO;
import com.pgb.common.sms.SmsResDTO;
import com.pgb.service.db.PgDistributionService;
import com.pgb.service.db.PgUsersService;
import com.pgb.xcx.common.SmsService;
import com.pgb.service.domain.GlobalXcxConstants;
import com.pgb.service.domain.distribution.PgDistribution;
import com.pgb.service.domain.login.LoginByPhoneFormDTO;
import com.pgb.service.domain.login.SmsFormDTO;
import com.pgb.service.domain.user.PcWxLoginCode;
import com.pgb.service.domain.user.PgUsers;
import com.pgb.service.domain.user.PgUsersVO;
import com.pgb.service.enums.DeviceTypeEnum;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.error.WxErrorException;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.File;
import java.time.Duration;
import java.util.Date;
import java.util.LinkedHashMap;

@Tag(name = "用户端/权限/登录")
@RestController("UserAuthLoginController")
@Transactional(rollbackFor = Exception.class)
@RequestMapping("/user/auth/login")
@RequiredArgsConstructor
@Slf4j
public class AuthController {

    private final WxMaService wxMaService;

    private final PgUsersService pgUsersService;

    public record LoginByPhoneForm(String code, String openId, String unionId, Long shareUserId,
                                   DeviceTypeEnum deviceType) {
    }

    private final PgDistributionService pgDistributionService;

    private final SmsService smsService;

    @Operation(summary = "PC 获取微信小程序登陆二维码")
    @GetMapping("pc/getQrCode/v2")
    public BaseResult<PcWxLoginCode> getWMQrCodeV2() {

        long uuid = IdUtil.getSnowflakeNextId();

        File codeFile = null;
        try {
            codeFile = wxMaService.getQrcodeService().createWxaCodeUnlimit(
                    "a_" + uuid,
                    "pages/index/index",
                    true,
                    EnvUtils.isDev() ? "develop" : "release",
                    430, false, (WxMaCodeLineColor) null, false
            );
        } catch (WxErrorException e) {
            return BaseResult.error(GlobalCode.Error, "小程序码生成异常");
        }

        String base64 = ImgUtil.toBase64DataUri(
                ImgUtil.read(codeFile), ImgUtil.IMAGE_TYPE_PNG
        );

        return BaseResult.success(
                PcWxLoginCode.builder()
                        .code(Long.toString(uuid))
                        .url(base64)
                        .build()
        );
    }

    @Operation(summary = "PC 获取微信小程序登陆二维码")
    @GetMapping("pc/getQrCode")
    public BaseResult<PcWxLoginCode> getWMQrCode() {
        String uuid = IdUtil.simpleUUID();

        File codeFile = null;
        try {
            codeFile = wxMaService.getQrcodeService().createWxaCodeUnlimit(
                    uuid,
                    "pages/index/index",
                    true,
                    EnvUtils.isDev() ? "develop" : "release",
                    430, false, (WxMaCodeLineColor) null, false
            );
        } catch (WxErrorException e) {
            return BaseResult.error(GlobalCode.Error, "小程序码生成异常");
        }

        String base64 = ImgUtil.toBase64DataUri(
                ImgUtil.read(codeFile), ImgUtil.IMAGE_TYPE_PNG
        );

        return BaseResult.success(
                PcWxLoginCode.builder()
                        .code(uuid)
                        .url(base64)
                        .build()
        );
    }

    @Operation(summary = "小程序 验证PC登录二维码")
    @GetMapping("wx/scanned/{codeId}")
    public BaseResult<Boolean> scanned(@PathVariable String codeId) {

        log.info("扫码登录：{}", codeId);

        String key = GlobalXcxConstants.XCX_LOGIN_CODE + StrUtil.replace(codeId, "a_", "");

        PcWxLoginCode code = PcWxLoginCode.builder()
                .code(codeId)
                .status(PcWxLoginCode.PcWxLoginCodeEnum.Scanned)
                .build();

        // 没有登录的时候，显示为扫码状态
        if (StpUtil.isLogin()) {
            code.setUserId(StpUtil.getLoginIdAsLong());
            code.setStatus(PcWxLoginCode.PcWxLoginCodeEnum.Logged);
        }

        RedisUtils.setCacheObject(key, code, Duration.ofMinutes(3));

        return BaseResult.success(
                StpUtil.isLogin()
        );
    }

    @Operation(summary = "PC 查询是否已用小程序登录")
    @GetMapping("pc/loginByWx/{codeId}")
    public BaseResult<PcWxLoginCode> getWMIsLogin(@PathVariable String codeId) {
        String key = GlobalXcxConstants.XCX_LOGIN_CODE + codeId;

        PcWxLoginCode loginCode = RedisUtils.getCacheObject(key);

        if (ObjectUtil.isNotNull(loginCode)) {

            // 登陆成功
            if (loginCode.getStatus().equals(PcWxLoginCode.PcWxLoginCodeEnum.Logged)) {
                PgUsers pgUsers = pgUsersService.getById(loginCode.getUserId());

                // 获取登录态
                LoginVO<PgUsersVO> loginVO = pgUsersService.loginSuccess(pgUsers, DeviceTypeEnum.WEB);

                loginCode.setLoginData(loginVO);

                return BaseResult.success(loginCode);
            }
            // 其他状态
            else {
                return BaseResult.success(loginCode);
            }
        } else {
            return BaseResult.success(
                    PcWxLoginCode.builder()
                            .status(PcWxLoginCode.PcWxLoginCodeEnum.UnScan)
                            .build()
            );
        }
    }

    @Operation(summary = "授权手机号", description = "根据phoneCode获取")
    @PostMapping("wx/loginByCode")
    public BaseResult<LoginVO<PgUsersVO>> loginByCode(@RequestBody @NotNull LoginByPhoneForm form) {

        if (ObjectUtil.isNull(form.code)) {
            return BaseResult.code(GlobalCode.Param_Wrong);
        }

        try {
            // 获取小程序用户手机号
            WxMaPhoneNumberInfo phoneNoInfo = wxMaService.getUserService().getPhoneNoInfo(form.code);

            PgUsers pgUsers;

            // 若无邀请人
            if (ObjectUtil.isNull(form.shareUserId)) {
                // 直接登录注册
                pgUsers = pgUsersService.getOrCreateUserByPhone(phoneNoInfo.getPurePhoneNumber());
            } else {
                // 关联关系
                PgDistribution distribution = new PgDistribution();

                // 先查有没有
                pgUsers = pgUsersService.getOne(new LambdaQueryWrapper<PgUsers>().eq(PgUsers::getPhone, phoneNoInfo.getPurePhoneNumber()), false);

                if (ObjectUtil.isNull(pgUsers)) {
                    pgUsers = pgUsersService.registerUser(phoneNoInfo.getPurePhoneNumber(), null, null, null);
                    distribution.setIsSuccess(true);
                } else {
                    distribution.setIsSuccess(false);
                    distribution.setReason("被邀请人不是新用户");
                    log.info("被邀请人不是新用户");
                }

                // 判断是否有关联关系，直接跳过
                if (!pgDistributionService.exists(new LambdaQueryWrapper<PgDistribution>()
                        .eq(PgDistribution::getUserId, pgUsers.getId())
                        .eq(PgDistribution::getShareUserId, form.shareUserId))
                        && !pgUsers.getId().equals(form.shareUserId)) {
                    // 邀请关系
                    distribution.setShareUserId(form.shareUserId);
                    distribution.setUserId(pgUsers.getId());
                    distribution.setCreateTime(new Date());

                    pgDistributionService.save(distribution);
                }
            }

            // 默认小程序用户
            pgUsers.setUserFrom(0);

            // 保存用户 openid、unionId
            if (ObjectUtil.isNotNull(form.openId)) pgUsers.setWxOpenId(form.openId);
            if (ObjectUtil.isNotNull(form.unionId)) pgUsers.setWxUnionId(form.unionId);

            // 获取登录态
            LoginVO<PgUsersVO> loginVO = pgUsersService.loginSuccess(pgUsers, form.deviceType);

            return BaseResult.success(loginVO);

        } catch (WxErrorException e) {
            throw new RuntimeException(e);
        }
    }

    @Operation(summary = "获取授权信息(openId)", description = "根据登录的code获取")
    @GetMapping("wx/info")
    public BaseResult<WxMaJscode2SessionResult> getWxInfo(String code) {
        // 获取openId等信息
        try {
            WxMaJscode2SessionResult sessionInfo = wxMaService.getUserService().getSessionInfo(code);

            return BaseResult.success(sessionInfo);
        } catch (WxErrorException e) {
            throw new RuntimeException(e);
        }
    }

    @Operation(summary = "获取加密URLLink")
    @GetMapping("link/{type}")
    public BaseResult<String> getLink(@PathVariable String type) {

        GenerateUrlLinkRequest request = new GenerateUrlLinkRequest();

        if ("home".equals(type)) {
            request.setPath("pages/index/index");
        } else if ("vip".equals(type)) {
            request.setPath("pages/my/vip/index");
        }

        request.setEnvVersion("release");

        // md5唯一值
        String md5 = DigestUtil.md5Hex(request.toString());

        String urlLink;
        try {

            String key = GlobalConstants.PGB_URL_LINK_KEY + md5;

            // 查缓存有没有
            urlLink = RedisUtils.getCacheObject(key);

            if (ObjectUtil.isNull(urlLink)) {
                urlLink = wxMaService.getLinkService().generateUrlLink(request);

                // 缓存3天
                RedisUtils.setCacheObject(key, urlLink, Duration.ofDays(3));
            }

            return BaseResult.success(urlLink);

        } catch (WxErrorException e) {
            throw new RuntimeException(e);
        }
    }


    @Operation(summary = "发送短信验证码")
    @PostMapping("sms")
    public BaseResult<String> sms(HttpServletRequest request, @Validated @RequestBody SmsFormDTO form) {

        String phone = form.getPhone();

        // 校验接口安全性
        // 判断是否是手机号加密
//        boolean isVerify = DigestUtils.md5DigestAsHex(("pgb-server-main-sms-key" + phone).getBytes()).equals(form.getVerifyKey());

//        // 滑动验证码校验
//        if (!isVerify) {
//            // 判断验证码是否已被验证
//            CaptchaSliderVO captchaSliderVO = RedisUtils.getCacheObject(GlobalConstants.CAPTCHA_CODE_KEY + form.getVerifyKey());
//
//            // 校验验证码
//            if (ObjectUtil.isNull(captchaSliderVO) || !captchaSliderVO.getIsVerify()) {
//                log.warn("【sendAuthSmsCode】{}/IP 发送短信拦截", JakartaServletUtil.getClientIP(request));
//                return BaseResult.code(GlobalCode.Login_Captcha_Error);
//            }
//        }

        // 调用腾讯云，发短信
        // -- 1. 生成验证码
        String verifyCode = RandomUtil.randomNumbers(4);

        // -- 2 发送验证码短信
        SmsResDTO smsRes = smsService.sendSms(smsService.getSmsConfigProperty().getVerifyCode(), phone, verifyCode);

        // 3. 判断短信验证结果
        if (!smsRes.getSuccess()) {
            return BaseResult.error(smsRes.getErrCode().value, smsRes.getMsg());
        }

        log.info("手机号登录：{}，验证码：{}", phone, verifyCode);

        // 4. 保存验证码信息
        JSONObject redisJson = JSONUtil.createObj()
                .putOnce("phone", phone)
                .putOnce("verifyCode", verifyCode)
                .putOnce("createTime", System.currentTimeMillis());

        //  5. Redis 执行保存，有效期 10分钟
        RedisUtils.setCacheObject(GlobalConstants.ADMIN_CAPTCHA_SMS_CODE_KEY + phone, redisJson.toString(), Duration.ofMinutes(10));

        return BaseResult.success("短信发送成功");
    }

    @Operation(summary = "使用手机登录账户")
    @PostMapping("phone")
    public BaseResult<LoginVO<PgUsersVO>> phone(@Validated @RequestBody LoginByPhoneFormDTO form) {

        // 校验短信验证码
        BaseResult<Boolean> result = smsService.smsIsSuccess(form.getPhone(), form.getSmsCode(), GlobalConstants.USER_CAPTCHA_SMS_CODE_KEY);

        // 验证失败
        if (result.getCode() != GlobalCode.Success.value) {
            return BaseResult.custom(result.getCode(), result.getMsg(), null);
        }

        // 4. 验证成功
        PgUsers pgUsers = pgUsersService.getOrCreateUserByPhone(form.getPhone());

        // 保存用户 openid、unionId
        if (ObjectUtil.isNotNull(form.getOpenId())) pgUsers.setWxOpenId(form.getOpenId());
        if (ObjectUtil.isNotNull(form.getUnionId())) pgUsers.setWxUnionId(form.getUnionId());

        // 获取登录态
        LoginVO<PgUsersVO> loginVO = pgUsersService.loginSuccess(pgUsers, form.getDeviceType());

        return BaseResult.success(loginVO);
    }

}
