package com.pgb.student.service;

import com.pgb.service.domain.student.StudentInfoDTO;
import com.pgb.student.domain.PgUserStudent;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【pg_user_student(用户学生表（一对多）)】的数据库操作Service
* @createDate 2025-03-10 16:49:43
*/
public interface PgUserStudentService extends IService<PgUserStudent> {

    /**
     * 根据用户id和学生姓名获取用户学生，如果没有则创建
     * @param userId
     * @param dto
     * @return
     */
    PgUserStudent getOrCreateUserStudent(Long userId, StudentInfoDTO dto);

    /**
     * 切换学生身份
     * @param userId
     */
    void switchStudent(Long userId);

    /**
     * 根据学生id切换学生身份
     * @param userStudentId
     */
    void switchByUserStudentId(Long userStudentId);

    /**
     * 根据用户id获取学生列表
     * @param userId
     * @return
     */
    List<PgUserStudent> getStudentList(Long userId);

    /**
     * 重置学生id会话
     */
    void resetStudentSession();
}
