package com.pgb.common.pay.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;

@Schema(description = "订单来源")
@AllArgsConstructor
public enum OrderTypeEnum {

    @Schema(title = "系统付费订单", description = "系统付费订单")
    Pay(0, "付费订单"),

    @Schema(title = "系统免费订单", description = "系统免费订单")
    Free(1, "免费订单"),

    @Schema(title = "兑换订单", description = "兑换订单")
    Redeem(2, "兑换订单"),

    @Schema(title = "渠道订单", description = "渠道订单")
    Channel(3, "渠道订单"),

    @Schema(title = "导入订单", description = "导入订单")
    Import(4, "导入订单");


    /**
     * 枚举值对应的整数值
     */
    @EnumValue
    public final int value;

    /**
     * 文字说明
     */
    public final String desc;
}
