package com.pgb.service.domain.question.zwEssay;

import com.pgb.service.domain.question.PgQuestion;
import com.pgb.service.domain.userConfig.CorrectConfigDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * Created by 2025/3/14 14:58
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ZwRequire extends PgQuestion {

    @Schema(title = "批改配置")
    private CorrectConfigDTO correctConfig;

}
