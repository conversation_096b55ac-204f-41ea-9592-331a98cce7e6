package com.pgb.service.domain.zc.word.chinese;

import java.io.Serializable;

import java.util.Date;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.ToString;
import lombok.Builder;

/**
 * 语文教材字词表
 *
 * @TableName pg_chinese_word
 */
@Schema(description = "语文教材字词表 VO")
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode
@ToString
@Builder
public class PgZcChineseWordVO implements Serializable {

    @Schema(title = "", type = "string")
    private Long id;

    @Schema(title = "字词")
    private String word;

    @Schema(title = "拼音")
    private String pinyin;

    @Schema(title = "创建时间")
    private Date createTime;

    @Schema(title = "更新时间")
    private Date updateTime;

    @Schema(title = "播报音频url")
    private String audioUrl;

}
