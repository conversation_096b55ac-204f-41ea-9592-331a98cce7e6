package com.pgb.service.db;

import com.pgb.service.domain.agent.cost.PgAgentCost;
import com.baomidou.mybatisplus.extension.service.IService;
import com.pgb.service.domain.agent.cost.TodayNum;

/**
 * <AUTHOR>
 * @description 针对表【pg_agent_cost(智能体会话生成记录表)】的数据库操作Service
 * @createDate 2025-02-14 16:40:29
 */
public interface PgAgentCostService extends IService<PgAgentCost> {

    /**
     * 获取今日智能体使用次数
     *
     * @param userId
     */
    TodayNum getTodayChatNum(Long userId, Integer totalUseNum);
}
