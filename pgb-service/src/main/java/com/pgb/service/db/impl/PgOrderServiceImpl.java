package com.pgb.service.db.impl;

import ch.qos.logback.core.util.EnvUtil;
import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.extra.servlet.JakartaServletUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.pgb.common.core.utils.EnvUtils;
import com.pgb.common.pay.enums.BuyTypeEnum;
import com.pgb.common.pay.enums.OrderStatusEnum;
import com.pgb.common.pay.enums.OrderTypeEnum;
import com.pgb.common.pay.enums.PayTypeEnum;

import com.pgb.common.redis.QueueUtils;
import com.pgb.service.db.PgOrderService;
import com.pgb.service.db.PgUsersService;
import com.pgb.service.db.PgVipService;
import com.pgb.service.domain.GlobalXcxConstants;
import com.pgb.service.domain.order.PgOrder;
import com.pgb.service.domain.user.PgUsers;
import com.pgb.service.domain.vip.PgVip;
import com.pgb.service.domain.vip.PgVipCode;
import com.pgb.service.enums.ChannelTypeEnum;
import com.pgb.service.enums.VipTypeEnum;
import com.pgb.service.mapper.PgOrderMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.util.Date;

import static com.pgb.service.custom.QwMsgService.sendPayMessage;

/**
 * <AUTHOR>
 * @description 针对表【pg_order(订单系统)】的数据库操作Service实现
 * @createDate 2024-06-25 16:26:25
 */
@Service
@RequiredArgsConstructor
public class PgOrderServiceImpl extends ServiceImpl<PgOrderMapper, PgOrder>
        implements PgOrderService {

    private final PgUsersService pgUsersService;

    private final PgVipService pgVipService;

    @Override
    public PgOrder generateOrder(VipTypeEnum vipType, PayTypeEnum payType, BuyTypeEnum buyType, String clientIp, Long userId) {
        PgOrder order = new PgOrder();
        // 获取订单号
        order.setOrderNo(IdUtil.getSnowflakeNextIdStr());
        // SKU 信息
        PgVip pgVip = new PgVip();
        pgVip.setDuration(vipType.dayNum);
        pgVip.setVipType(vipType);
        pgVip.setBuyType(buyType);
        order.setSku(pgVip);
        // 商品标题
        order.setTitle("批改邦VIP" + vipType.desc);
        // 商品描述
        order.setDescription("批改邦VIP" + vipType.desc);
        // 金额
        order.setTotalAmount(vipType.price);
        // 实际支付金额
        order.setPayAmount(vipType.price);
        // 是否已支付
        order.setIsPay(false);
        order.setIsRefund(false);
        // 订单类型
        order.setOrderType(OrderTypeEnum.Pay);
        // 订单状态
        order.setStatus(OrderStatusEnum.Generate);
        // 下单时间
        order.setCreateTime(new Date());
        // 订单过期时间
        order.setExpireTime(DateUtil.offsetMinute(new Date(), 60));
        // 保存用户信息
        order.setUserId(userId);
        // 获取ip
        order.setClientIp(clientIp);
        // 支付方式
        order.setPayType(payType);

        // 数据库，保存订单
        save(order);

        return order;
    }

    @Override
    public void paySuccess(Long orderId, Integer payAmount, Object platformData) {
        PgOrder order = getById(orderId);
        paySuccess(order, payAmount, platformData);
    }

    @Override
    public void paySuccess(Long orderId, Object platformData) {
        PgOrder order = getById(orderId);
        paySuccess(order, order.getTotalAmount(), platformData);
    }

    private void paySuccess(PgOrder order, Integer payAmount, Object platformData) {
        if (!order.getIsPay()) {
            order.setStatus(OrderStatusEnum.PaySuccess);
            order.setIsPay(true);
            order.setPayTime(new Date());
            order.setPlatformData(platformData);
            order.setPayAmount(payAmount);
            updateById(order);

            // 处理其他支付操作 延迟队列 30s
            QueueUtils.addDelayedQueueObjectInTransaction(GlobalXcxConstants.XCX_PAY_SUCCESS, order.getId(), Duration.ofSeconds(30));
        }

        // 走充值会员逻辑
        if (!pgVipService.exists(new LambdaQueryWrapper<PgVip>()
                .eq(PgVip::getOrderId, order.getId())
                .eq(PgVip::getBuyType, BuyTypeEnum.PAY)
                .eq(PgVip::getUserId, order.getUserId())
        )) {
            PgVip vip = JSONUtil.toBean(order.getSku().toString(), PgVip.class);
            vip.setUserId(order.getUserId());
            vip.setCreateTime(new Date());
            vip.setOrderId(order.getId());
            vip.setBuyType(BuyTypeEnum.PAY);
            // 自行开通
            vip.setOpenType(ChannelTypeEnum.PAY);

            pgVipService.save(vip);

            // 增加会员时间
            pgUsersService.addVipDay(order.getUserId(), vip.getDuration());
        }
    }

    @Override
    public PgOrder openVip(String phone, VipTypeEnum type, ChannelTypeEnum openType, OrderTypeEnum orderType) {
        // 查看用户是否已经存在
        PgUsers user = pgUsersService.getOne(new LambdaQueryWrapper<PgUsers>()
                .eq(PgUsers::getPhone, phone));

        if (ObjectUtil.isNull(user)) {
            // 生成用户
            user = new PgUsers();
            user.setPhone(phone);
            user.setNickName("邦友" + user.getPhone().substring(user.getPhone().length() - 4));
            // 用户来源
            user.setUserFrom(0);
            user.setCreateTime(new Date());
            user.setAvatarImgUrl("https://cdn.pigaibang.com/common/xcx-zw/avatar.png");
            pgUsersService.save(user);
        }

        return openVip(user.getId(), type, openType, orderType);
    }

    @Override
    public PgOrder openVip(Long userId, VipTypeEnum type, ChannelTypeEnum openType, OrderTypeEnum orderType) {
        // 生成订单
        PgOrder order = new PgOrder();
        // 订单号
        order.setOrderNo(IdUtil.getSnowflakeNextIdStr());
        // 订单类型
        order.setOrderType(orderType);
        // 订单状态
        order.setStatus(OrderStatusEnum.Generate);
        // 下单时间
        order.setCreateTime(new Date());
        // 订单过期时间
        order.setExpireTime(DateUtil.offsetMinute(new Date(), 60));
        // 支付方式
        order.setPayType(PayTypeEnum.WX_MINI);
        // 用户id
        order.setUserId(userId);
        // 是否已退款
        order.setIsRefund(false);

        PgVip vip = new PgVip();
        vip.setVipType(type);
        vip.setDuration(type.dayNum);
        vip.setBuyType(BuyTypeEnum.EXCHANGE);

        // SKU
        order.setSku(vip);

        order.setTotalAmount(type.price);
        order.setPayAmount(type.price);
        order.setTitle("批改邦VIP" + type.desc);
        order.setDescription("批改邦VIP" + type.desc);

        order.setIsPay(false);

        // 数据库，保存订单
        save(order);

        // ---- 走支付成功逻辑
        if (!order.getIsPay()) {
            order.setStatus(OrderStatusEnum.PaySuccess);
            order.setIsPay(true);
            order.setPayTime(new Date());
            updateById(order);
        }

        // 走充值会员逻辑
        if (!pgVipService.exists(new LambdaQueryWrapper<PgVip>()
                .eq(PgVip::getOrderId, order.getId())
                .eq(PgVip::getBuyType, BuyTypeEnum.PAY)
                .eq(PgVip::getUserId, order.getUserId())
        )) {
            vip.setUserId(order.getUserId());
            vip.setCreateTime(new Date());
            vip.setOrderId(order.getId());
            vip.setBuyType(BuyTypeEnum.EXCHANGE);
            // 开通方式
            vip.setOpenType(openType);

            pgVipService.save(vip);

            // 增加会员时间
            pgUsersService.addVipDay(order.getUserId(), vip.getDuration());

            // 发送通知
            // 生产环境
            if (EnvUtils.isProd()) {
                PgUsers users = pgUsersService.getById(order.getUserId());
                sendPayMessage(order, users.getPhone(), pgVipService.isNewBuy(users.getId()));
            }
        }

        return order;
    }

    @Override
    public PgOrder openActivityVip(Long userId, PgVipCode vipCode, PgOrder order) {

        PgVip vip = new PgVip();
        vip.setVipType(vipCode.getVipType());
        vip.setDuration(vipCode.getVipType().dayNum);
        vip.setBuyType(BuyTypeEnum.EXCHANGE);

        // 走充值会员逻辑
        if (!pgVipService.exists(new LambdaQueryWrapper<PgVip>()
                .eq(PgVip::getOrderId, order.getId())
                .eq(PgVip::getBuyType, BuyTypeEnum.PAY)
                .eq(PgVip::getUserId, order.getUserId())
        )) {
            vip.setUserId(order.getUserId());
            vip.setCreateTime(new Date());
            vip.setOrderId(order.getId());
            vip.setBuyType(BuyTypeEnum.EXCHANGE);
            // 开通方式
            vip.setOpenType(vipCode.getChannelType());

            pgVipService.save(vip);

            // 增加会员时间
            pgUsersService.addVipDay(order.getUserId(), vip.getDuration());
        }

        return order;
    }
}






