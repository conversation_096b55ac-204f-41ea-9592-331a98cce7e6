package com.pgb.service.domain.homework;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.pgb.common.mybatis.handler.JsonbTypeHandler;
import lombok.Data;

/**
 * 作业表
 *
 * @TableName pg_homework
 */
@TableName(value = "pg_homework", autoResultMap = true)
@Data
public class PgHomework implements Serializable {
    /**
     * 作业id
     */
    private Long id;

    /**
     * 创建人id
     */
    private Long creatorId;

    /**
     * 作业名称
     */
    private String name;

    /**
     * 作业题目信息
     */
    @TableField(typeHandler = JsonbTypeHandler.class)
    private Object questionInfo;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 班级id
     */
    private Long classId;

    /**
     * 作业类型 0：作文，1：字词
     */
    private Integer type;

    /**
     * 字词题目id
     */
    private Long zcQuestionId;

}
