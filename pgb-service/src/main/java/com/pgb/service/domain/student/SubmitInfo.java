package com.pgb.service.domain.student;

import com.pgb.service.domain.question.PgQuestionVO;
import com.pgb.service.domain.zc.question.PgZcQuestionVO;
import com.pgb.service.enums.CorrectStatusEnum;
import com.pgb.service.enums.GradeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.util.Date;

/**
 * <AUTHOR>
 * Created by 2024/12/20 11:09
 */
@Data
public class SubmitInfo {

    @Schema(title = "题目id")
    private Long answerId;

    @Schema(title = "批改状态")
    private CorrectStatusEnum status;

    @Schema(title = "作业提交时间")
    private Date submitTime;

    @Schema(title = "学生姓名")
    private String studentName;

    @Schema(title = "作业名称")
    private String homeworkName;

    @Schema(title = "班级名称")
    private String className;

    @Schema(title = "几年级")
    private GradeEnum grade;

    @Schema(title = "几班")
    private String classNum;

    @Schema(title = "作业发布时间")
    private Date homeworkCreateTime;

    @Schema(title = "作文题目信息")
    private PgQuestionVO questionInfo;

    @Schema(title = "班级口令")
    private String password;

}
