package com.pgb.service.db;

import com.pgb.service.domain.userAction.PgUserAction;
import com.baomidou.mybatisplus.extension.service.IService;
import com.pgb.service.enums.UserActionType;

/**
* <AUTHOR>
* @description 针对表【pg_user_action】的数据库操作Service
* @createDate 2025-01-16 14:42:02
*/
public interface PgUserActionService extends IService<PgUserAction> {

    /**
     * 创建示例班级和作业
     * @param userId
     * @return
     */
    boolean creatExampleClassHomework(Long userId);

    /**
     * 是否创建过示例班级
     * @param userId
     * @param type
     * @return
     */
    Boolean isCreateClassHomework(Long userId, UserActionType type);

    /**
     * 创建行为记录
     * @param userId
     * @param type
     * @return
     */
    void create(Long userId, UserActionType type);
}

