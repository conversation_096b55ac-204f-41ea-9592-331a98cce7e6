package com.pgb.service.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;

/**
 * <AUTHOR>
 * Created by 2024/11/14 14:45
 */
@Schema(description = "用户类型")
@AllArgsConstructor
public enum UserTypeEnum {

    @Schema(title = "所有用户")
    ALL(0, "所有用户"),

    @Schema(title = "会员用户")
    MEMBER(1, "会员用户"),

    @Schema(title = "会员过期用户")
    EXPIRED_MEMBER(2, "会员过期用户"),

    @Schema(title = "非会员用户")
    UN_MEMBER(3, "非会员用户");

    /**
     * 枚举值对应的整数值
     */
    @EnumValue
    public final int value;

    /**
     * 文字说明
     */
    public final String desc;
}
