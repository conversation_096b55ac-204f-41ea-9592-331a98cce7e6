package com.pgb.manage.domain.query;

import com.pgb.common.mybatis.global.PageQuery;
import com.pgb.service.domain.tag.PgTag;
import com.pgb.service.enums.VipTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * Created by 2024/8/5 13:58
 */

@EqualsAndHashCode(callSuper = true)
@Schema(description = "会员 搜索实体")
@Data
public class MemberQuery extends PageQuery {

    @Schema(title = "手机号")
    private String phone;

    @Schema(title = "会员类型")
    private VipTypeEnum type;

    @Schema(title = "会员开通时间范围", description = "长度为2，第一个开始时间，第二个结束时间")
    private List<Date> times;

    @Schema(title = "是否已过期")
    private Boolean isExpired;

    @Schema(title = "0：新购，1：续费")
    private Integer buyType;

    @Schema(title = "用户标签")
    private List<Long> tagList;

    @Schema(title = "是否是会员")
    private Boolean isVip;

    @Schema(title = "购买次数")
    private Integer buyNum;

    @Schema(title = "购买过的会员类型")
    private List<VipTypeEnum> buyTypeList;

}
