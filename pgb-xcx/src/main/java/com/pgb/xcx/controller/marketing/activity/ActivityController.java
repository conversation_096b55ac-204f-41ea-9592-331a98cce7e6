package com.pgb.xcx.controller.marketing.activity;

import cn.dev33.satoken.annotation.SaCheckLogin;
import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.extra.servlet.JakartaServletUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.binarywang.wxpay.bean.notify.SignatureHeader;
import com.github.binarywang.wxpay.bean.notify.WxPayNotifyV3Result;
import com.github.binarywang.wxpay.bean.request.WxPayUnifiedOrderV3Request;
import com.github.binarywang.wxpay.bean.result.WxPayOrderQueryV3Result;
import com.github.binarywang.wxpay.bean.result.WxPayUnifiedOrderV3Result;
import com.github.binarywang.wxpay.bean.result.enums.TradeTypeEnum;
import com.github.binarywang.wxpay.exception.WxPayException;
import com.github.binarywang.wxpay.service.WxPayService;
import com.pgb.common.core.global.BaseResult;
import com.pgb.common.core.global.GlobalCode;
import com.pgb.common.core.global.GlobalConstants;
import com.pgb.common.core.utils.EnvUtils;
import com.pgb.common.pay.enums.BuyTypeEnum;
import com.pgb.common.pay.enums.OrderStatusEnum;
import com.pgb.common.pay.enums.OrderTypeEnum;
import com.pgb.common.pay.enums.PayTypeEnum;
import com.pgb.common.redis.QueueUtils;
import com.pgb.service.db.*;
import com.pgb.service.domain.GlobalXcxConstants;
import com.pgb.service.domain.activity.*;
import com.pgb.service.domain.order.OrderPayParam;
import com.pgb.service.domain.order.PgOrder;
import com.pgb.service.domain.user.PgUsers;
import com.pgb.service.domain.vip.PgVip;
import com.pgb.service.domain.vip.PgVipCode;
import com.pgb.service.enums.ChannelTypeEnum;
import com.pgb.service.enums.VipTypeEnum;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.time.Duration;
import java.util.Date;
import java.util.List;


@Tag(name = "用户端/营销/活动")
@RestController("UserMarketingActivityController")
@Transactional(rollbackFor = Exception.class)
@RequestMapping("/user/marketing/activity")
@RequiredArgsConstructor
@Slf4j
public class ActivityController {

    private final PgActivityService pgActivityService;

    private final PgActivityUserService pgActivityUserService;

    private final PgUsersService pgUsersService;

    private final WxPayService wxPayService;

    private final PgOrderService pgOrderService;

    private final PgVipCodeService pgVipCodeService;

    private final PgVipService pgVipService;

    @Value("${pay.notifyUrl}")
    private String notifyUrl;

    @Operation(summary = "获取活动配置信息")
    @GetMapping("config/{id}")
    @SaCheckLogin
    public BaseResult<PgActivityVO> config(@PathVariable Long id) {

        PgActivity activity = pgActivityService.getById(id);

        if (ObjectUtil.isNull(activity)) {
            return BaseResult.code(GlobalCode.Item_Null);
        }

        // 查询是否已购买
        boolean isBuy = pgActivityUserService.exists(new LambdaQueryWrapper<PgActivityUser>()
                .eq(PgActivityUser::getActivityId, activity.getId())
                .eq(PgActivityUser::getUserId, StpUtil.getLoginIdAsLong())
                .eq(PgActivityUser::getIsBuy, true)
        );

        // 如果是立减活动，转换config
        ActivityDiscountConfig config = JSONUtil.toBean(activity.getConfig().toString(), ActivityDiscountConfig.class);

        // 复制属性
        PgActivityVO activityVO = BeanUtil.copyProperties(activity, PgActivityVO.class, "config");

        // 购买配置
        activityVO.setConfig(config);

        // 是否已购买
        activityVO.setIsBuy(isBuy);

        return BaseResult.success(activityVO);
    }

    @Operation(summary = "单个活动购买记录")
    @GetMapping("buyRecord/{activityId}")
    @SaCheckLogin
    public BaseResult<List<ActivityBuyRecord>> buyRecord(@PathVariable Long activityId) {
        PgActivity activity = pgActivityService.getById(activityId);

        if (ObjectUtil.isNull(activity)) {
            return BaseResult.code(GlobalCode.Item_Null);
        }

        // 获取全部购买记录
        List<ActivityBuyRecord> recordList = pgActivityUserService.list(new LambdaQueryWrapper<PgActivityUser>()
                        .eq(PgActivityUser::getActivityId, activityId)
                        .eq(PgActivityUser::getUserId, StpUtil.getLoginIdAsLong())
                        .eq(PgActivityUser::getIsBuy, true))
                .stream()
                .map(pgActivityUser -> {
                    // 获取购买记录及其兑换码
                    ActivityBuyRecord record = new ActivityBuyRecord();

                    // 获取兑换码
                    if (ObjectUtil.isNotNull(pgActivityUser.getCodeId())) {
                        PgVipCode code = pgVipCodeService.getById(pgActivityUser.getCodeId());
                        record.setCode(code.getCode());
                    }

                    // 获取订单信息
                    if (ObjectUtil.isNotNull(pgActivityUser.getOrderId())) {
                        PgOrder order = pgOrderService.getById(pgActivityUser.getOrderId());
                        record.setPrice(order.getPayAmount());
                    }

                    record.setBuyTime(pgActivityUser.getCreateTime());
                    return record;
                })
                .toList();

        return BaseResult.success(recordList);
    }

    @Operation(summary = "【小程序】活动获取支付信息")
    @GetMapping("pay/{activityId}")
    @SaCheckLogin
    public BaseResult<OrderPayParam<WxPayUnifiedOrderV3Result.JsapiResult>> pay(HttpServletRequest request, @PathVariable Long activityId) {

        PgUsers users = pgUsersService.getById(StpUtil.getLoginIdAsLong());

        if (ObjectUtil.isNull(users)) {
            return BaseResult.code(GlobalCode.Item_Null);
        }

        if (ObjectUtil.isNull(activityId)) {
            return BaseResult.code(GlobalCode.Param_Wrong);
        }

        // 拿到活动配置
        PgActivity activity = pgActivityService.getById(activityId);
        if (ObjectUtil.isNull(activity)) {
            return BaseResult.code(GlobalCode.Item_Null);
        }
        // 转换config
        ActivityDiscountConfig config = JSONUtil.toBean(activity.getConfig().toString(), ActivityDiscountConfig.class);

        VipTypeEnum vipType = config.getVipTypeEnum();
        String openId = users.getWxOpenId();

        // 生成订单信息
        PgOrder order = new PgOrder();
        // 获取订单号
        order.setOrderNo(IdUtil.getSnowflakeNextIdStr());
        // SKU 信息
        PgVip pgVip = new PgVip();
        pgVip.setDuration(vipType.dayNum + ObjectUtil.defaultIfNull(0, config.getPresentDays()));
        pgVip.setVipType(vipType);
        pgVip.setBuyType(BuyTypeEnum.PAY);
        order.setSku(pgVip);
        // 商品标题
        order.setTitle("批改邦批改" + vipType.desc + "（活动）");
        // 商品描述
        order.setDescription("批改邦批改" + vipType.desc + "（活动）");
        // 金额
        order.setTotalAmount(vipType.price);
        // 实际支付金额
        order.setPayAmount(config.getPrice());
        // 是否已支付
        order.setIsPay(false);
        order.setIsRefund(false);
        // 订单类型
        order.setOrderType(OrderTypeEnum.Pay);
        // 订单状态
        order.setStatus(OrderStatusEnum.Generate);
        // 下单时间
        order.setCreateTime(new Date());
        // 订单过期时间
        order.setExpireTime(DateUtil.offsetMinute(new Date(), 60));
        // 保存用户信息
        order.setUserId(StpUtil.getLoginIdAsLong());
        // 获取ip
        order.setClientIp(JakartaServletUtil.getClientIP(request, null));
        // 支付方式
        order.setPayType(PayTypeEnum.WX_MINI);

        // 数据库，保存订单
        pgOrderService.save(order);

        // 向redis 队列中，添加延迟队列
        // 30分钟后过期
        QueueUtils.addDelayedQueueObjectInTransaction(GlobalConstants.ORDER_EXPIRE_QUEUE_KEY + ":xcx", order.getId(), Duration.ofMinutes(60));

        WxPayUnifiedOrderV3Request orderV3Request = new WxPayUnifiedOrderV3Request();
        // 店铺商户号  xxxx_WX_MINI
        orderV3Request.setDescription(order.getDescription());
        orderV3Request.setOutTradeNo(order.getOrderNo().concat("_" + PayTypeEnum.WX_MINI.name()));
        orderV3Request.setTimeExpire(DateUtil.format(DateUtil.offsetMinute(order.getCreateTime(), 30), "yyyy-MM-dd'T'HH:mm:ssXXX"));
        orderV3Request.setAttach(order.getId().toString());
        // 回调地址
        orderV3Request.setNotifyUrl(
                notifyUrl + "/user/marketing/activity/wxPayNotify/" + activityId
        );
        // 金额
        orderV3Request.setAmount(
                new WxPayUnifiedOrderV3Request.Amount()
                        .setTotal(
                                EnvUtils.isDev() ? 1 : order.getPayAmount()
                        )
        );

        // 支付者
        orderV3Request.setPayer(
                new WxPayUnifiedOrderV3Request.Payer().setOpenid(openId)
        );
        orderV3Request.setSceneInfo(
                new WxPayUnifiedOrderV3Request.SceneInfo().setPayerClientIp(order.getClientIp())
        );

        // 获取支付信息
        try {
            WxPayUnifiedOrderV3Result.JsapiResult result = wxPayService.createOrderV3(
                    TradeTypeEnum.JSAPI,
                    orderV3Request
            );

            log.info("【微信小程序支付应答】: {}", result);

            OrderPayParam<WxPayUnifiedOrderV3Result.JsapiResult> payParam = new OrderPayParam<>();
            payParam.setOrderId(order.getId());
            payParam.setParams(result);

            return BaseResult.success(payParam);
        } catch (WxPayException e) {
            log.error("【微信小程序支付】异常：", e);
            return BaseResult.error("支付异常");
        }
    }

    @Operation(summary = "【小程序】活动获取支付信息")
    @GetMapping("order/query/{activityId}/{orderId}")
    @SaCheckLogin
    public BaseResult<Boolean> payQuery(@PathVariable Long activityId, @PathVariable Long orderId) {
        PgOrder order = pgOrderService.getById(orderId);
        if (ObjectUtil.isNull(order)) {
            return BaseResult.code(GlobalCode.Item_Null);
        }
        log.info("查询微信支付订单状态：{}", orderId);

        // 向微信支付查询订单状态
        try {
            WxPayOrderQueryV3Result result = wxPayService.queryOrderV3(null, order.getOrderNo().concat("_" + PayTypeEnum.WX_MINI.name()));
            if (result.getTradeState().equals("SUCCESS")) {
                paySuccess(order.getId(), result, activityId);
            }
            return BaseResult.success(true);
        } catch (WxPayException e) {
            log.error("微信支付查询订单错误", e);
            return BaseResult.success(false);
        }
    }

    @Operation(summary = "微信支付回调接口", description = "微信支付成功后，微信会调用此接口进行回调")
    @PostMapping("wxPayNotify/{activityId}")
    public void wxPayNotify(HttpServletRequest request, @RequestBody String data, @PathVariable Long activityId) {

        String timestamp = request.getHeader("Wechatpay-Timestamp");
        String nonce = request.getHeader("Wechatpay-Nonce");
        String serialNo = request.getHeader("Wechatpay-Serial");
        String signature = request.getHeader("Wechatpay-Signature");
        log.info("timestamp:{} nonce:{} serialNo:{} signature:{} data:{}", timestamp, nonce, serialNo, signature, data);

        SignatureHeader header = new SignatureHeader(timestamp, nonce, signature, serialNo);

        try {
            WxPayNotifyV3Result result = wxPayService.parseOrderNotifyV3Result(data, header);

            Long orderId = Long.valueOf(result.getResult().getAttach());

            // ---- 走支付成功逻辑
            paySuccess(orderId, BeanUtil.copyProperties(result.getResult(), WxPayOrderQueryV3Result.class), activityId);

            log.info("【微信支付回调】：{}", result);

        } catch (WxPayException e) {
            log.error("微信支付回调错误", e);
        }
    }

    private void paySuccess(Long orderId, WxPayOrderQueryV3Result result, Long activityId) {
        // ---- 走支付成功逻辑
        PgOrder order = pgOrderService.getById(orderId);

        if (!order.getIsPay()) {
            order.setStatus(OrderStatusEnum.PaySuccess);
            order.setIsPay(true);
            order.setPayTime(new Date());
            order.setPlatformData(result);
            order.setPayAmount(result.getAmount().getTotal());
            pgOrderService.updateById(order);
        }

        // 走生成兑换码逻辑
        if (!pgActivityUserService.exists(new LambdaQueryWrapper<PgActivityUser>()
                .eq(PgActivityUser::getOrderId, orderId)
                .eq(PgActivityUser::getUserId, order.getUserId())
                .eq(PgActivityUser::getActivityId, activityId)
        )) {

            PgVip vip = JSONUtil.toBean(order.getSku().toString(), PgVip.class);

            PgActivity activity = pgActivityService.getById(activityId);

            // 保存活动参与信息
            PgActivityUser activityUser = new PgActivityUser();
            activityUser.setUserId(order.getUserId());
            activityUser.setActivityId(activityId);
            activityUser.setOrderId(orderId);
            if (ObjectUtil.isNotNull(activity)) {
                activityUser.setActivityType(activity.getType());
            }
            activityUser.setCreateTime(new Date());
            activityUser.setIsBuy(true);

            // 【兑换码兑换】
            if (activity.getBuyType() == 1) {
                // 生成兑换码
                PgVipCode vipCode = pgVipCodeService.createVipCode(ChannelTypeEnum.ACTIVITY, vip.getVipType(), activityId, orderId);
                activityUser.setCodeId(vipCode.getId());
            }
            // 【直购】
            else if (activity.getBuyType() == 0) {

                // 走充值会员逻辑
                if (!pgVipService.exists(new LambdaQueryWrapper<PgVip>()
                        .eq(PgVip::getOrderId, orderId)
                        .eq(PgVip::getBuyType, BuyTypeEnum.PAY)
                        .eq(PgVip::getUserId, order.getUserId())
                )) {
                    vip.setUserId(order.getUserId());
                    vip.setCreateTime(new Date());
                    vip.setOrderId(orderId);
                    vip.setBuyType(BuyTypeEnum.PAY);
                    // 活动开通 -- 直购
                    vip.setOpenType(ChannelTypeEnum.ACTIVITY);

                    pgVipService.save(vip);

                    // 增加会员时间
                    pgUsersService.addVipDay(order.getUserId(), vip.getDuration());
                }
            }
            pgActivityUserService.save(activityUser);

            // 处理其他支付操作 延迟队列 5s
            QueueUtils.addDelayedQueueObjectInTransaction(GlobalXcxConstants.XCX_ACTIVITY_PAY_SUCCESS, activityUser.getId(), Duration.ofSeconds(5));
        }
    }
}
