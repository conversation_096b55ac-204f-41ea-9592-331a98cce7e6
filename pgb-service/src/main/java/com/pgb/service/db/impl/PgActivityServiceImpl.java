package com.pgb.service.db.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.pgb.service.db.PgActivityService;
import com.pgb.service.db.PgActivityUserService;
import com.pgb.service.domain.activity.PgActivity;
import com.pgb.service.domain.activity.PgActivityUser;
import com.pgb.service.mapper.PgActivityMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【pg_activity】的数据库操作Service实现
* @createDate 2024-12-23 16:21:29
*/
@Service
@RequiredArgsConstructor
public class PgActivityServiceImpl extends ServiceImpl<PgActivityMapper, PgActivity>
    implements PgActivityService {

    private final PgActivityUserService pgActivityUserService;

    @Override
    public boolean isParticipate(Long userId, Long activityId) {
        return pgActivityUserService.exists(new LambdaQueryWrapper<PgActivityUser>()
                .eq(PgActivityUser::getActivityId, activityId)
                .eq(PgActivityUser::getUserId, userId)
                .eq(PgActivityUser::getIsBuy, true)
        );
    }
}




