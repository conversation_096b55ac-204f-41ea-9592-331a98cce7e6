package com.pgb.service.domain.zc.homework;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 字词作业表
 * @TableName pg_zc_homework
 */
@TableName(value ="pg_zc_homework")
@Data
public class PgZcHomework implements Serializable {
    /**
     * 字词作业id
     */
    @TableId
    private Long id;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 作业名称
     */
    private String name;

    /**
     * 题目内容
     */
    private Object questionInfo;

    /**
     * 关联的字词题目id
     */
    private Long zcQuestionId;

    /**
     * 班级id
     */
    private Long classId;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

}
