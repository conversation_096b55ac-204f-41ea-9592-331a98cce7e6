package com.pgb.xcx;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.TimeInterval;
import cn.hutool.core.img.ImgUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.lang.TypeReference;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.json.JSONUtil;
import com.pgb.common.core.global.BaseResult;
import com.pgb.common.core.global.exception.BaseException;
import com.pgb.service.domain.common.fabric.FabricJson;
import com.pgb.service.domain.common.image.FilePaperImg;
import com.pgb.service.domain.question.mx.MxCommonQuestion;
import com.pgb.service.domain.question.zwEssay.ZwEssayQuestion;
import lombok.extern.slf4j.Slf4j;

import java.awt.image.BufferedImage;
import java.io.File;
import java.util.List;

@Slf4j
public class PoTest {

    public static void main(String[] args) {
        MxCommonQuestion question = new MxCommonQuestion();
        FilePaperImg answer = new FilePaperImg();
        FilePaperImg user = new FilePaperImg();
        FilePaperImg empty = new FilePaperImg();

        answer.setImgUrl("https://cdn.pigaibang.com/answer3.jpg");
        user.setImgUrl("https://cdn.pigaibang.com/user3.jpg");
        empty.setImgUrl("https://cdn.pigaibang.com/empty4.jpg");

        question.setQuesAnswerList(List.of(answer));
        question.setUserImgAnswerList(List.of(user));
        //question.setQuesEmptyList(List.of(empty));

        TimeInterval interval = DateUtil.timer();
        ZwEssayQuestion correctEssay = mxCorrect(question);

        log.info(
                JSONUtil.toJsonStr(correctEssay)
        );
    }


    public static void renderImg(FabricJson markJson, int imgHs, int imgWs, String name) {
        HttpResponse response = HttpRequest.post("http://127.0.0.1:9000")
                .body(JSONUtil.createObj()
                        // imgHs * (800. / imgWs) + 100
                        .putOnce("height", imgHs)
                        .putOnce("width", imgWs)
                        .putOnce("markJson", markJson).toString())
                .timeout(5 * 60 * 1000)
                .execute();

        if (response.isOk()) {
            BufferedImage image = ImgUtil.read(response.bodyStream());
            FileUtil.del("C:\\Users\\<USER>\\Desktop\\" + name);
            // 保存到桌面
            File touch = FileUtil.touch("C:\\Users\\<USER>\\Desktop\\" + name);
            ImgUtil.write(image, touch);
            log.info("图片保存成功，路径：{}", touch.toURI());
        }
    }

    /**
     * 英文默写卡批改
     *
     * @param question
     * @return
     */
    public static ZwEssayQuestion mxCorrect(MxCommonQuestion question) {
        // 发送请求
        String resultStr = HttpRequest.post(StrUtil.format("{}/api/correct/mx/common/{}", "http://127.0.0.1:8081", "pgb-api-correct-v2"))
                .body(JSONUtil.parseObj(question).toString())
                .timeout(20 * 60 * 1000)
                .execute()
                .body();

        BaseResult<ZwEssayQuestion> result = Convert.convert(new TypeReference<BaseResult<ZwEssayQuestion>>() {
        }, JSONUtil.parseObj(resultStr));

        if (result.isSuccess()) {
            return result.getData();
        } else {
            log.error("默写批改失败，错误信息：{}", result);
            throw new BaseException("默写批改失败");
        }
    }
}

