package com.pgb.service.domain.zc.question.chinese;

import com.pgb.service.domain.common.image.FilePaperImg;
import com.pgb.service.enums.SubjectEnum;
import com.pgb.service.enums.ZcQuestionTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * Created by 2025/5/14 10:46
 */
@Data
@Schema(title = "字词题")
public class ZcSubmitForm {

    @Schema(title = "科目")
    private SubjectEnum subject;

    @Schema(title = "题目类型")
    private ZcQuestionTypeEnum questionType;

    @Schema(title = "用户作答图片 URL")
    private List<FilePaperImg> userImgAnswerList;

    @Schema(title = "提交时的题目json的url", description = "zc/question/userId/questionId/md5.json")
    private String questionJsonUrl;

    @Schema(title = "备注名称")
    private String name;

    @Schema(title = "作业id")
    private Long zcHomeworkId;

    @Schema(title = "学生id")
    private Long studentId;

    @Schema(title = "题目分数")
    private Double score;

}
