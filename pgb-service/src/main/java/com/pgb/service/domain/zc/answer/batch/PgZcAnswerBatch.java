package com.pgb.service.domain.zc.answer.batch;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 默写批量上传表
 * @TableName pg_zc_answer_batch
 */
@TableName(value ="pg_zc_answer_batch")
@Data
public class PgZcAnswerBatch implements Serializable {
    /**
     * 批次id
     */
    @TableId
    private Long id;

    /**
     * 用户id
     */
    private Long userId;

    /**
     *
     */
    private Long zcQuestionId;

    /**
     * 创建时间
     */
    private Date createTime;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
