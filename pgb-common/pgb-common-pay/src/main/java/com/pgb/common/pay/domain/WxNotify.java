package com.pgb.common.pay.domain;

import com.github.binarywang.wxpay.bean.notify.WxPayNotifyV3Result;
import com.github.binarywang.wxpay.constant.WxPayConstants;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.Date;

/**
 * {"mchid":"**********",
 * "appid":"wxce81cadb7db6eeea",
 * "out_trade_no":"d4d1e365bcea4416a29b49d0b70af0db",
 * "transaction_id":"4200001920202308240381463296",
 * "trade_type":"NATIVE",
 * "trade_state":"SUCCESS",
 * "trade_state_desc":"支付成功",
 * "bank_type":"OTHERS",
 * "attach":"1694410811239391233",
 * "success_time":"2023-08-24T02:30:42+08:00",
 * "payer":{"openid":"olKDV5lZUdpy7DcxlDeyj4YCVi4c"},
 * "amount":{"total":1,"payer_total":1,"currency":"CNY","payer_currency":"CNY"}}
 */

/**
 * 微信回调实体类
 */
@Data
@AllArgsConstructor
@EqualsAndHashCode
@ToString
public class WxNotify {
    String mchid;
    String appid;
    String outTradeNo;
    String transactionId;
    WxPayConstants.TradeType tradeType;
    String tradeState;
    String tradeStateDesc;
    String bankType;
    String attach;
    Date successTime;
    WxPayNotifyV3Result.Payer payer;
    WxPayNotifyV3Result.Amount amount;
}
