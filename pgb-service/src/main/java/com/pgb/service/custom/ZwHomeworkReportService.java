package com.pgb.service.custom;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.TimeInterval;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.pgb.service.db.PgAnswerService;
import com.pgb.service.db.PgHomeworkReportService;
import com.pgb.service.db.PgHomeworkService;
import com.pgb.service.domain.answer.PgAnswer;
import com.pgb.service.domain.homework.PgHomework;
import com.pgb.service.domain.homework.PgHomeworkReport;
import com.pgb.service.domain.question.PgQuestion;
import com.pgb.service.domain.question.homework.HomeworkStatistic;
import com.pgb.service.domain.question.zwEssay.ZwEssayQuestion;
import com.pgb.service.enums.CorrectStatusEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;

@Component("ZwStatisticService")
@RequiredArgsConstructor
@Slf4j
public class ZwHomeworkReportService {
    private final CorrectService correctService;

    private final PgHomeworkService pgHomeworkService;

    private final PgAnswerService pgAnswerService;

    private final PgHomeworkReportService pgHomeworkReportService;

    public void statistic(PgHomeworkReport report, boolean isReCorrect) {

        if (ObjectUtil.isNull(report)) {
            log.info("无效的班级作文报告-为空");
        }

        if (!isReCorrect && report.getStatus().equals(CorrectStatusEnum.Corrected)) {
            log.info("无效的班级作文报告-已批改：{}", report.getId());
        }

        // 拿作业
        PgHomework homework = pgHomeworkService.getById(report.getHomeworkId());

        // 空判断
        if (ObjectUtil.isNull(homework)) {
            log.info("无效的班级作文报告-作业不存在：{}", report.getId());
            return;
        }

        TimeInterval interval = DateUtil.timer();

        // 根据作业id，拿作业全班内容
        List<PgAnswer> questionList = pgAnswerService.list(new LambdaQueryWrapper<PgAnswer>()
                .eq(PgAnswer::getHomeworkId, homework.getId())
                .and(i -> i.eq(PgAnswer::getStatus, CorrectStatusEnum.Corrected)
                        .or()
                        .eq(PgAnswer::getStatus, CorrectStatusEnum.Checked)
                )
                .eq(PgAnswer::getDeleted, false)
        );

        // 判断空
        if (questionList.isEmpty()) {
            log.info("无效的班级作文报告：当前作文提交记录为空{}", homework.getId());
            return;
        }

        log.info("【接收生成班级分析报告消息】：{}", report.getId());

        // 拿批改要求
        PgQuestion require = JSONUtil.toBean(JSONUtil.toJsonStr(homework.getQuestionInfo()), PgQuestion.class);

        // 拿做题记录
        List<ZwEssayQuestion> answerList = questionList.stream().map(answer -> JSONUtil.toBean(JSONUtil.toJsonStr(answer.getCorrectResult()), ZwEssayQuestion.class)).toList();
        List<String> nameList = questionList.stream().map(PgAnswer::getName).toList();

        // 开始统计分析
        HomeworkStatistic statistic = new HomeworkStatistic();
        statistic.setNameList(nameList);
        statistic.setRequire(require);
        statistic.setQuestionList(answerList);

        // 执行分析
        HomeworkStatistic homeworkStatistic = correctService.zwStatistic(statistic);

        // 设置统计结果
        report.setStatus(CorrectStatusEnum.Corrected);
        report.setReport(homeworkStatistic.getSectionList());
        report.setCompleteTime(new Date());
        report.setTokens(Math.toIntExact(homeworkStatistic.getAiTokens()));
        report.setDuration((int) interval.intervalSecond());

        pgHomeworkReportService.updateById(report);

        log.info("【班级分析报告消息】-结束：{}，用时：{}", report.getId(), interval.intervalPretty());
    }
}
