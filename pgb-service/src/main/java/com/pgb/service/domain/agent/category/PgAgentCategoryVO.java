package com.pgb.service.domain.agent.category;

import java.io.Serializable;
import java.util.List;

import com.pgb.service.domain.agent.PgAgent;
import com.pgb.service.domain.agent.PgAgentVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.ToString;
import lombok.Builder;

/**
* 分组表
* @TableName pg_agent_category
*/
@Schema(description = "分组表 VO")
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode
@ToString
@Builder
public class PgAgentCategoryVO implements Serializable {

    @Schema(title = "分组id")
    private Long id;

    @Schema(title = "分组名称")
    private String name;

    @Schema(title = "同级排序")
    private Integer sort;

    @Schema(title = "关联的智能体id")
    private String agentIds;

    @Schema(title = "关联的智能体")
    private List<PgAgentVO> agentList;

}
