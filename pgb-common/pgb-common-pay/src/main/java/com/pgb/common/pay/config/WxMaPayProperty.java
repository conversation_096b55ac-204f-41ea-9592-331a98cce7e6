package com.pgb.common.pay.config;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import org.springframework.boot.context.properties.ConfigurationProperties;

@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode
@ToString
@Builder
@Schema(title = "微信小程序支付配置")
@ConfigurationProperties(prefix = "wx.pay.ma")
public class WxMaPayProperty {
    @Schema(title = "app Id", description = "微信服务商 appid")
    private String appId;

    @Schema(title = "app Id", description = "微信服务商 商户id")
    private String mchId;

    @Schema(title = "apiV3Key")
    private String apiV3Key;

    @Schema(title = "服务商模式下的子商户公众账号ID")
    private String subAppId;

    @Schema(title = "服务商模式下的子商户号")
    private String subMchId;

    @Schema(title = "apiclient_cert.p12文件的绝对路径，或者如果放在项目中，请以classpath:开头指定")
    private String keyPath;

    @Schema(title = "privateKeyPath")
    private String privateKeyPath;

    @Schema(title = "privateCertPath")
    private String privateCertPath;
}
