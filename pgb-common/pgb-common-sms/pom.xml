<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.pgb</groupId>
        <artifactId>pgb-common</artifactId>
        <version>${revision}</version>
    </parent>

    <artifactId>pgb-common-sms</artifactId>
    <description>短信模块</description>

    <dependencies>
        <dependency>
            <groupId>com.pgb</groupId>
            <artifactId>pgb-common-core</artifactId>
        </dependency>

        <!--  sms4j  https://sms4j.com/ -->
        <dependency>
            <groupId>org.dromara.sms4j</groupId>
            <artifactId>sms4j-spring-boot-starter</artifactId>
        </dependency>

        <!--  百度 -->
        <dependency>
            <groupId>com.baidubce</groupId>
            <artifactId>bce-java-sdk</artifactId>
            <version>${baidu.oss.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>org.apache.hbase</groupId>
                    <artifactId>hbase-client</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
    </dependencies>

</project>
