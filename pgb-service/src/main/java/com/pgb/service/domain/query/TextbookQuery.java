package com.pgb.service.domain.query;

import com.pgb.common.mybatis.global.PageQuery;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * Created by 2025/4/27 18:49
 */
@EqualsAndHashCode(callSuper = true)
@Schema(description = "题目 搜索实体")
@Data
public class TextbookQuery extends PageQuery {

    @Schema(title = "学制")
    private String system;

    @Schema(title = "年级")
    private String grade;

    @Schema(title = "册别")
    private String volume;
}
