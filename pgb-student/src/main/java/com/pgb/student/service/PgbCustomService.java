package com.pgb.student.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.pgb.service.domain.classes.PgClasses;
import com.pgb.service.domain.classes.PgClassesVO;
import com.pgb.service.domain.student.PgStudent;
import com.pgb.service.domain.student.StudentInfoDTO;
import com.pgb.student.domain.PgUserStudent;

import java.util.List;

public interface PgbCustomService {

    /**
     * 根据班级id获取学生列表
     *
     * @param classId
     * @param userStudentIds
     * @return
     */
    List<PgStudent> getStudentsByClassId(Long classId, List<Long> userStudentIds);

    /**
     * 根据学生用户id获取学生列表
     *
     * @param studentUserId
     * @return
     */
    List<PgStudent> getStudentListByStudentUserId(Long studentUserId);

    List<PgStudent> students();

    List<PgStudent> students(Long userId);

    void update(PgStudent student);


    /**
     * 加入班级
     *
     * @param userId
     * @param classId
     * @param dto
     */
    PgUserStudent joinClass(Long userId, Long classId, StudentInfoDTO dto);


    /**
     * 根据班级id获取班级信息
     *
     * @param classId
     * @return
     */
    PgClasses getClassById(Long classId);

    /**
     * 根据班级口令获取班级信息
     *
     * @param password
     * @return
     */
    PgClasses getClassByPassword(String password);

    /**
     * 根据班级id获取学生信息
     *
     * @param classId
     * @param userStudentId
     * @return
     */
    PgStudent getStudentByClassId(Long classId, Long userStudentId);


    /**
     * 根据条件获取班级列表
     *
     * @param queryWrapper
     * @return
     */
    List<PgClassesVO> pgClasses(LambdaQueryWrapper<PgClasses> queryWrapper);

    /**
     * 退出班级
     *
     * @param classId
     * @param userStudentId
     */
    void quitClass(Long classId, Long userStudentId);

    /**
     * 解除学生绑定
     * @param studentId
     */
    void unbind(Long studentId);
}
