package com.pgb.service.domain.answer.answerCost;

import java.io.Serializable;

import java.util.Date;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.ToString;
import lombok.Builder;

/**
* 作文批改明细表
* @TableName pg_answer_cost
*/
@Schema(description = "作文批改明细表 VO")
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode
@ToString
@Builder
public class PgAnswerCostVO implements Serializable {

    @Schema(title = "消耗明细 id", type = "string")
    private Long id;

    @Schema(title = "创建时间")
    private Date createTime;

    @Schema(title = "用户id")
    private Long userId;

    @Schema(title = "关联的批改id")
    private Long answerId;

}
