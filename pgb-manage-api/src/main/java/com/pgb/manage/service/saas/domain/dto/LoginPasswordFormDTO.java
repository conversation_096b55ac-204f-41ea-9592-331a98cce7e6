package com.pgb.manage.service.saas.domain.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.hibernate.validator.constraints.Length;
import org.springframework.validation.annotation.Validated;

@Data
@Schema(description = "系统登录表单实体 DTO")
@Validated
@ToString
@EqualsAndHashCode
public class LoginPasswordFormDTO {
    @Schema(title = "手机号", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "手机号不能为空")
    @Length(min = 11, max = 11, message = "手机号长度是11")
    String phone;

    @Schema(title = "密码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "密码不能为空")
    @Length(min = 6, message = "密码长度不低于6位")
    String password;

    @Schema(title = "验证码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "验证码不能为空")
    String code;

    @Schema(title = "验证码唯一标识", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank
    String verifyKey;
}
