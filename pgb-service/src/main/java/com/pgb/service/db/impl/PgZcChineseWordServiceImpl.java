package com.pgb.service.db.impl;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.houbb.pinyin.util.PinyinHelper;
import com.pgb.ai.domain.doubao.DoubaoTTSProperty;
import com.pgb.ai.domain.doubao.DoubaoTTSResult;
import com.pgb.ai.models.DoubaoTTSService;
import com.pgb.common.oss.service.OssService;
import com.pgb.service.domain.zc.word.chinese.PgZcChineseWord;
import com.pgb.service.db.PgZcChineseWordService;
import com.pgb.service.mapper.PgZcChineseWordMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.io.File;
import java.util.Date;

/**
 * <AUTHOR>
 * @description 针对表【pg_chinese_word(语文教材字词表)】的数据库操作Service实现
 * @createDate 2025-04-27 15:16:24
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class PgZcChineseWordServiceImpl extends ServiceImpl<PgZcChineseWordMapper, PgZcChineseWord>
        implements PgZcChineseWordService {

    private final DoubaoTTSService doubaoTTSService = new DoubaoTTSService();

    private final DoubaoTTSProperty doubaoTTSProperty;

    private final OssService ossService;

    private static final String TEST_OUTPUT_DIR = "test-audio-output";

    @Override
    public PgZcChineseWord getOrCreateWordInfo(String word) {

        // 1. 查询已存在的记录
        PgZcChineseWord existWord = getOne(new LambdaQueryWrapper<PgZcChineseWord>()
                .eq(PgZcChineseWord::getWord, word)
                .last("limit 1"));

        // 2. 不存在则新建记录
        if (ObjectUtil.isNull(existWord)) {
            existWord = new PgZcChineseWord();
            existWord.setWord(word);
            existWord.setPinyin(PinyinHelper.toPinyin(word));
            existWord.setCreateTime(new Date());
            save(existWord); // 先保存
        }

        // 3. 统一处理音频生成逻辑
        if (StrUtil.isBlank(existWord.getAudioUrl())) {
            String audioUrl = generateAndUploadAudio(word);
            if (StrUtil.isNotBlank(audioUrl)) {
                existWord.setAudioUrl(audioUrl);
                updateById(existWord); // 统一使用更新操作
            }
        }

        return existWord;
    }

    private String generateAndUploadAudio(String word) {

        DoubaoTTSResult doubaoTTSResult = doubaoTTSService.textToSpeech(word, doubaoTTSProperty);

        if (doubaoTTSResult.getSuccess()) {

//            // 保存音频文件
//            String savedPath = saveAudioFile(doubaoTTSResult, "basic_test.mp3");
//            if (savedPath != null) {
//                log.info("基本测试音频已保存到: {}", savedPath);
//            }

            byte[] audioData = doubaoTTSResult.getAudioData();

//            String encode = URLEncoder.encode(word, StandardCharsets.UTF_8);

            String key = StrUtil.format("resource/ch/audio/{}/{}.mp3", word, word);

            File tempFile = FileUtil.createTempFile("mp3", true);
            FileUtil.writeBytes(audioData, tempFile);

            String audioUrl = ossService.putFile(key, tempFile);
            FileUtil.del(tempFile);

            log.info("获取词语：{}，音频：{}", word, audioUrl);

            return audioUrl;

        } else {
            log.error("获取词语：{}，音频失败：{}", word, doubaoTTSResult.getErrorMessage());
        }
        return "";
    }


    private String saveAudioFile(DoubaoTTSResult result, String fileName) {
        if (result == null || !result.getSuccess() || result.getAudioData() == null) {
            return null;
        }

        try {
            String filePath = TEST_OUTPUT_DIR + File.separator + fileName;
            boolean saved = doubaoTTSService.saveAudioToFile(result.getAudioData(), filePath);

            if (saved) {
                File savedFile = new File(filePath);
                log.info("音频文件保存成功: {}", savedFile.getAbsolutePath());
                log.info("文件大小: {} KB", savedFile.length() / 1024.0);
                return filePath;
            } else {
                log.error("音频文件保存失败: {}", filePath);
                return null;
            }
        } catch (Exception e) {
            log.error("保存音频文件时发生异常: {}", fileName, e);
            return null;
        }
    }
}

