package com.pgb.service.domain.question;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;

import com.pgb.service.enums.GradeEnum;
import com.pgb.service.enums.SubjectEnum;
import com.pgb.service.enums.WritingStyleEnum;
import lombok.Data;

/**
 * 题目表
 * @TableName pg_question
 */
@TableName(value ="pg_question")
@Data
public class PgQuestion implements Serializable {
    /**
     * 题目id
     */
    private Long id;

    /**
     * 题目名称
     */
    private String name;

    /**
     * 批改要求
     */
    private String correctRequest;

    /**
     * 题干
     */
    private String writingRequest;

    /**
     * 年级
     */
    private GradeEnum grade;

    /**
     * 上册/下册 xx单元
     */
    private String unit;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 题目类型
     */
    private SubjectEnum subject;

    /**
     * 字数要求
     */
    private Integer wordNum;

    /**
     * 是否是官方题目
     */
    private Boolean isOfficial;

    /**
     * 写作文体
     */
    private WritingStyleEnum style;

    /**
     * 好标题
     */
    private String headline;

    /**
     * 好词
     */
    private String word;

    /**
     * 好开头
     */
    private String opening;

    /**
     * 好结尾
     */
    private String ending;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 参考分数
     */
    private Integer score;

    /**
     * 评价单
     */
    private String evaluation;

    /**
     * 封面
     */
    private String cover;

    /**
     * 学期（上下册）
     */
    private String semester;

}
