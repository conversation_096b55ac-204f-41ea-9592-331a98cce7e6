package com.pgb.service.domain.agent.collect;

import java.io.Serializable;

import java.util.Date;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.ToString;
import lombok.Builder;
import org.springframework.validation.annotation.Validated;
import jakarta.validation.constraints.NotNull;
import com.pgb.common.core.validate.UpdateGroup;

/**
*
* @TableName pg_agent_collect
*/
@Schema(description = " DTO")
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode
@ToString
@Validated
@Builder
public class PgAgentCollectDTO implements Serializable {

    @Schema(title = "智能体收藏", type = "string")
    @NotNull(message="[智能体收藏]不能为空", groups = {UpdateGroup.class})
    private Long id;

    @Schema(title = "用户id")
    private Long userId;

    @Schema(title = "智能体id")
    private Long agentId;

    @Schema(title = "收藏时间")
    private Date createTime;

    @Schema(title = "排序")
    private Integer sort;

}
