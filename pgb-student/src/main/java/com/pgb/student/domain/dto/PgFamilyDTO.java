package com.pgb.student.domain.dto;

import java.io.Serializable;

import java.util.Date;

import com.pgb.service.enums.IdentityTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.ToString;
import lombok.Builder;
import org.springframework.validation.annotation.Validated;

/**
 * 用户家庭表
 *
 * @TableName pg_family
 */
@Schema(description = "用户家庭表 DTO")
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode
@ToString
@Validated
@Builder
public class PgFamilyDTO implements Serializable {

    @Schema(title = "用户家庭id", type = "string")
    private Long id;

    @Schema(title = "用户id")
    private Long userId;

    @Schema(title = "被邀请人（子号）用户id")
    private Long inviteUserId;

    @Schema(title = "学生id")
    private Long studentId;

    @Schema(title = "身份关系")
    private IdentityTypeEnum identity;

    @Schema(title = "创建时间")
    private Date createTime;

}
