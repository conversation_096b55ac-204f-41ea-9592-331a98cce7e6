<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.pgb</groupId>
        <artifactId>pgb-common</artifactId>
        <version>${revision}</version>
    </parent>

    <artifactId>pgb-common-wx</artifactId>
    <description>微信相关</description>

    <dependencies>
        <dependency>
            <groupId>com.pgb</groupId>
            <artifactId>pgb-common-core</artifactId>
        </dependency>

        <!-- 微信公众号开发工具 -->
        <dependency>
            <groupId>com.github.binarywang</groupId>
            <artifactId>wx-java-mp-spring-boot-starter</artifactId>
        </dependency>
        <!--微信开放平台-->
        <dependency>
            <groupId>com.github.binarywang</groupId>
            <artifactId>weixin-java-open</artifactId>
        </dependency>
        <!-- 微信支付服务商 -->
        <dependency>
            <groupId>com.github.binarywang</groupId>
            <artifactId>weixin-java-pay</artifactId>
        </dependency>
        <!--微信小程序-->
        <dependency>
            <groupId>com.github.binarywang</groupId>
            <artifactId>weixin-java-miniapp</artifactId>
        </dependency>
    </dependencies>

</project>
