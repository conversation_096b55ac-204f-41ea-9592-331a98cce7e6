package com.pgb.manage.config;

import com.pgb.common.ocr.OCRService;
import com.pgb.common.ocr.impl.OCRBaidu;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class OCRConfig {

    @Value("${baidu.ocr.api-id}")
    private String appId;

    @Value("${baidu.ocr.api-key}")
    private String apiKey;

    @Value("${baidu.ocr.api-secret}")
    private String apiSecret;

    @Bean
    public OCRService ocrService() {
        return new OCRBaidu(appId, apiKey, apiSecret);
    }
}
