package com.pgb.service.domain.common.fabric;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.util.List;

@Schema(title = "组类型")
@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MarkObjectGroup extends MarkObject {
    @Schema(title = "类型，Image、txt、line")
    private final MarkObjectEnum type = MarkObjectEnum.Group;

    @Schema(title = "left 位置")
    private Double left;

    @Schema(title = "top 位置")
    private Double top;

    @Schema(title = "标注对象 列表")
    private List<MarkObject> objects;
}
