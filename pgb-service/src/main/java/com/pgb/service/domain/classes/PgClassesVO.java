package com.pgb.service.domain.classes;

import java.io.Serializable;

import java.util.Date;

import cn.hutool.core.util.ObjectUtil;
import com.fasterxml.jackson.annotation.JsonGetter;
import com.pgb.service.enums.GradeEnum;
import com.pgb.service.enums.SubjectEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.ToString;
import lombok.Builder;

/**
* 班级表
* @TableName pg_classes
*/
@Schema(description = "班级表 VO")
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode
@ToString
@Builder
public class PgClassesVO implements Serializable {

    @Schema(title = "班级id")
    private Long id;

    @Schema(title = "班级名称")
    private String name;

    @Schema(title = "创建者id")
    private Long userId;

    @Schema(title = "年级")
    private GradeEnum grade;

    @Schema(title = "班级口令")
    private String password;

    @Schema(title = "创建时间")
    private Date createTime;

    @Schema(title = "班级")
    private String classNum;

    @Schema(title = "学生人数")
    private Integer studentNum;

    @Schema(title = "是否是班级创建者")
    private Boolean isCreator;

    @Schema(title = "作业数量")
    private Integer homeworkNum;

    @Schema(title = "年级名称")
    @JsonGetter("gradeStr")
    public String getGradeStr() {
        return ObjectUtil.isNotNull(this.grade) ? this.grade.desc : null;
    }

}
