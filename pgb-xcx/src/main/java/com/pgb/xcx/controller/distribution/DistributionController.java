package com.pgb.xcx.controller.distribution;

import cn.binarywang.wx.miniapp.api.WxMaService;
import cn.dev33.satoken.annotation.SaCheckLogin;
import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.img.ImgUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.lang.UUID;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.digest.DigestUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fasterxml.jackson.annotation.JsonGetter;
import com.pgb.common.core.global.BaseResult;
import com.pgb.common.core.global.GlobalCode;
import com.pgb.common.core.global.GlobalConstants;
import com.pgb.common.core.utils.EnvUtils;
import com.pgb.common.mybatis.global.PageQuery;
import com.pgb.common.oss.service.OssService;
import com.pgb.common.redis.RedisUtils;
import com.pgb.service.db.*;
import com.pgb.service.domain.distribution.PgDistribution;
import com.pgb.service.domain.distribution.PgDistributionVO;
import com.pgb.service.domain.distribution.agency.CommissionInfoDTO;
import com.pgb.service.domain.distribution.agency.PgDistributionAgency;
import com.pgb.service.domain.distribution.agency.VerifyRecord;
import com.pgb.service.domain.distribution.record.PgDistributionRecord;
import com.pgb.service.domain.distribution.record.PgDistributionRecordVO;
import com.pgb.service.domain.distribution.verify.PgDistributionVerify;
import com.pgb.service.domain.order.PgOrder;
import com.pgb.service.domain.user.PgUsers;
import com.pgb.service.domain.vip.PgVip;
import com.pgb.service.enums.ProfitStatusEnum;
import com.pgb.service.enums.VipTypeEnum;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.error.WxErrorException;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.IOException;
import java.net.URL;
import java.time.Duration;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * Created by 2024/7/4 15:18
 */
@Tag(name = "用户端/分销/信息")
@RestController("UserDistributionController")
@Transactional(rollbackFor = Exception.class)
@RequestMapping("/user/distribution/info")
@RequiredArgsConstructor
@Slf4j
public class DistributionController {

    private final PgDistributionService pgDistributionService;

    private final PgDistributionRecordService pgDistributionRecordService;

    private final PgUsersService pgUsersService;

    private final WxMaService wxMaService;

    private final OssService ossService;

    private final PgDistributionVerifyService pgDistributionVerifyService;

    private final PgOrderService pgOrderService;

    private final PgDistributionAgencyService pgDistributionAgencyService;


    @Operation(summary = "已登录邀请人回调")
    @GetMapping("invite/{shareUserId}")
    public BaseResult<Boolean> invite(@PathVariable Long shareUserId) {

        log.info("用户回调邀请人：" + shareUserId);

        if (!StpUtil.isLogin()) {
            return BaseResult.success(false);
        }

        if (StpUtil.getLoginIdAsLong() == shareUserId) {
            return BaseResult.success(true);
        }

        PgDistribution distribution = new PgDistribution();
        // 邀请关系
        distribution.setShareUserId(shareUserId);
        distribution.setUserId(StpUtil.getLoginIdAsLong());
        distribution.setCreateTime(new Date());
        distribution.setIsSuccess(false);
        distribution.setReason("被邀请人不是新用户");

        // 判断是否有关联关系，直接跳过
        if (!pgDistributionService.exists(new LambdaQueryWrapper<PgDistribution>()
                .eq(PgDistribution::getUserId, StpUtil.getLoginIdAsLong())
                .eq(PgDistribution::getShareUserId, shareUserId))) {
            pgDistributionService.save(distribution);
        }

        return BaseResult.success(StpUtil.isLogin());
    }

    @Operation(summary = "邀请用户列表")
    @PostMapping("share")
    @SaCheckLogin
    public BaseResult<IPage<PgDistributionVO>> share(@RequestBody PageQuery query) {

        IPage<PgDistributionVO> page = pgDistributionService.page(query.toMpPageSortByCreateTime(), new LambdaQueryWrapper<PgDistribution>()
                .eq(PgDistribution::getShareUserId, StpUtil.getLoginIdAsLong())
        ).convert(pgDistribution -> {
            PgDistributionVO pgDistributionVO = BeanUtil.copyProperties(pgDistribution, PgDistributionVO.class);

            // 查被邀请人信息
            PgUsers users = pgUsersService.getById(pgDistribution.getUserId());
            if (ObjectUtil.isNotNull(users)) {
                pgDistributionVO.setNickName(users.getNickName());
                pgDistributionVO.setAvatarUrl(users.getAvatarImgUrl());
            }
            return pgDistributionVO;
        });

        return BaseResult.success(page);
    }

    @Operation(summary = "分销记录列表")
    @PostMapping("record")
    @SaCheckLogin
    public BaseResult<IPage<PgDistributionRecordVO>> record(@RequestBody PageQuery query) {
        IPage<PgDistributionRecordVO> page = pgDistributionRecordService.page(query.toMpPageSortByCreateTime(), new LambdaQueryWrapper<PgDistributionRecord>()
                .eq(PgDistributionRecord::getShareUserId, StpUtil.getLoginIdAsLong())
        ).convert(pgDistributionRecord -> {
            PgDistributionRecordVO recordVO = BeanUtil.copyProperties(pgDistributionRecord, PgDistributionRecordVO.class);

            // 查被邀请人信息
            PgUsers users = pgUsersService.getById(pgDistributionRecord.getUserId());
            if (ObjectUtil.isNotNull(users)) {
                recordVO.setNickName(users.getNickName());
                recordVO.setAvatarUrl(users.getAvatarImgUrl());
            }
            return recordVO;
        });
        return BaseResult.success(page);
    }

    @Operation(summary = "获取佣金统计信息")
    @GetMapping("commission")
    @SaCheckLogin
    public BaseResult<CommissionInfoDTO> commission() {

        CommissionInfoDTO commissionInfo = new CommissionInfoDTO();

        // 绑定人数
        long count = pgDistributionService.count(new LambdaQueryWrapper<PgDistribution>()
                .eq(PgDistribution::getShareUserId, StpUtil.getLoginIdAsLong())
                .eq(PgDistribution::getIsSuccess, true));

        commissionInfo.setBindUserNum((int) count);

        List<PgDistributionRecord> records = pgDistributionRecordService.list(new LambdaQueryWrapper<PgDistributionRecord>()
                .eq(PgDistributionRecord::getShareUserId, StpUtil.getLoginIdAsLong()));

        // 代入账（分）
        int unsettledCommission = records.stream().filter
                // 转帐中
                        (record -> record.getStatus() == ProfitStatusEnum.PENDING)
                .mapToInt(PgDistributionRecord::getPayCommission)
                .sum();
        commissionInfo.setUnsettledCommission(unsettledCommission);

        // 累计佣金（分） 转账成功的
        int totalCommission = records.stream()
                .filter(record -> record.getIsTransfer().equals(true))
                .mapToInt(PgDistributionRecord::getPayCommission)
                .sum();
        commissionInfo.setTotalCommission(totalCommission);

        return BaseResult.success(commissionInfo);
    }


    @Operation(summary = "分销核销记录")
    @PostMapping("verifyRecord")
    public BaseResult<IPage<VerifyRecord>> verifyRecord(@RequestBody PageQuery query) {

        LambdaQueryWrapper<PgOrder> queryWrapper = new LambdaQueryWrapper<PgOrder>()
                .eq(PgOrder::getIsPay, true);

        // 查找代理人
        PgDistributionAgency agency = pgDistributionAgencyService.getOne(new LambdaQueryWrapper<PgDistributionAgency>()
                .eq(PgDistributionAgency::getUserId, StpUtil.getLoginIdAsLong()));

        // 查用户邀请用户列表
        List<Long> userIds = pgDistributionService.list(new LambdaQueryWrapper<PgDistribution>()
                        .eq(PgDistribution::getIsSuccess, true)
                        .eq(PgDistribution::getShareUserId, StpUtil.getLoginIdAsLong()))
                .stream()
                .map(PgDistribution::getUserId)
                .toList();

        if (userIds.isEmpty()) {
            return BaseResult.success(new Page<>());
        } else {
            queryWrapper.in(PgOrder::getUserId, userIds);
        }

        IPage<VerifyRecord> page = pgOrderService.page(query.toMpPageSortByCreateTime(), queryWrapper
        ).convert(pgOrder -> {

            PgVip vip = JSONUtil.toBean(pgOrder.getSku().toString(), PgVip.class);
            // 排除周卡类型
            if (vip.getVipType().equals(VipTypeEnum.WEEK)) {
                return null;
            }
            VerifyRecord record = new VerifyRecord();

            // 用户信息
            // 查被邀请人信息
            PgUsers users = pgUsersService.getById(pgOrder.getUserId());
            if (ObjectUtil.isNotNull(users)) {
                record.setNickName(users.getNickName());
                record.setAvatarUrl(users.getAvatarImgUrl());
            }

            // 下单时间
            record.setCreateTime(pgOrder.getCreateTime());
            // 实际支付金额
            record.setPayAmount(pgOrder.getPayAmount());
            record.setOrderId(pgOrder.getId());

            // 若已处理 则是已转账
            PgDistributionVerify verify = pgDistributionVerifyService.getOne(new LambdaQueryWrapper<PgDistributionVerify>()
                    .eq(PgDistributionVerify::getOrderId, pgOrder.getId())
                    .last("LIMIT 1"));
            // 订单分销金额
            if (ObjectUtil.isNotNull(verify)) {
                record.setDistributeAmount(verify.getDistributeAmount());
                record.setStatus(ProfitStatusEnum.SUCCESS);
            } else {
                // 待转账金额
                record.setDistributeAmount(ObjectUtil.isNotNull(agency) ? (int) Math.ceil((double) (pgOrder.getPayAmount() * agency.getRate()) / 100) : 0);
                record.setStatus(ProfitStatusEnum.PENDING);
            }
            return record;
        });
        return BaseResult.success(page);
    }

    @Operation(summary = "分销核销佣金统计信息")
    @GetMapping("verifyCommission")
    public BaseResult<CommissionInfoDTO> verifyCommission() {

        // 查找代理人
        PgDistributionAgency agency = pgDistributionAgencyService.getOne(new LambdaQueryWrapper<PgDistributionAgency>()
                .eq(PgDistributionAgency::getUserId, StpUtil.getLoginIdAsLong()));

        CommissionInfoDTO commissionInfo = new CommissionInfoDTO();

        // 绑定人数
        long count = pgDistributionService.count(new LambdaQueryWrapper<PgDistribution>()
                .eq(PgDistribution::getShareUserId, StpUtil.getLoginIdAsLong())
                .eq(PgDistribution::getIsSuccess, true));

        commissionInfo.setBindUserNum((int) count);

        // 累计佣金（分） 转账成功的
        List<PgDistributionVerify> verifyList = pgDistributionVerifyService.list(new LambdaQueryWrapper<PgDistributionVerify>()
                .eq(PgDistributionVerify::getShareUserId, StpUtil.getLoginIdAsLong())
                .eq(PgDistributionVerify::getStatus, 1));

        int totalCommission = verifyList.stream()
                .mapToInt(PgDistributionVerify::getDistributeAmount)
                .sum();
        commissionInfo.setTotalCommission(totalCommission);

        // 待入账
        // 查用户邀请用户列表
        List<Long> userIds = pgDistributionService.list(new LambdaQueryWrapper<PgDistribution>()
                        .eq(PgDistribution::getIsSuccess, true)
                        .eq(PgDistribution::getShareUserId, StpUtil.getLoginIdAsLong()))
                .stream()
                .map(PgDistribution::getUserId)
                .toList();

        if (!userIds.isEmpty()) {

            // 已核销的orderIds
            List<Long> orderIds = verifyList.stream().map(PgDistributionVerify::getOrderId).toList();

            int unsettledCommission = pgOrderService.list(new LambdaQueryWrapper<PgOrder>()
                            .in(PgOrder::getUserId, userIds)
                            .eq(PgOrder::getIsPay, true))
                    .stream()
                    // 排除掉 已经在verify表中的orderId
                    .filter(pgOrder -> !orderIds.contains(pgOrder.getId()))
                    // 排除掉周卡类型的
                    .filter(pgOrder -> {
                        PgVip vip = JSONUtil.toBean(pgOrder.getSku().toString(), PgVip.class);
                        return !vip.getVipType().equals(VipTypeEnum.WEEK);
                    })
                    .mapToInt(PgOrder::getPayAmount)
                    .sum();
            commissionInfo.setUnsettledCommission(ObjectUtil.isNotNull(agency) ? (int) Math.ceil((double) (unsettledCommission * agency.getRate()) / 100) : 0);
        }
        return BaseResult.success(commissionInfo);
    }


    @Operation(summary = "生成分享海报")
    @GetMapping("poster")
    @SaCheckLogin
    public BaseResult<String> poster() {

        String userId = StpUtil.getLoginIdAsString();

        // 开始计时
        long startTime = System.currentTimeMillis();

        // 查海报缓存
        String posterUrl = RedisUtils.getCacheObject(GlobalConstants.POSTER_CACHE_KEY + userId);

        // 若不存在 则生成海报并上传oss
        if (ObjectUtil.isNull(posterUrl)) {

            String bgImgUrl = "https://cdn.pigaibang.com/common/xcx-zw/poster/fx_share.jpg";

            // 获取背景图片
            BufferedImage bgImg = null;
            try {
                bgImg = ImageIO.read(new URL(bgImgUrl));

                // 获取二维码
                File qrCode = FileUtil.createTempFile();
//                qrCode.deleteOnExit();

                try {
                    qrCode = wxMaService.getQrcodeService().createWxaCodeUnlimit(
                            "b_" + userId,
                            "pages/index/index",
                            true,
                            EnvUtils.isDev() ? "develop" : "release",
                            430,
                            false,
                            null,
                            false);

                } catch (WxErrorException e) {
                    return BaseResult.error(GlobalCode.Error, "小程序码生成异常");
                }

                BufferedImage avatarImage = ImgUtil.read(qrCode);

                // 背景图尺寸
                int width = 1242;
                int height = 2134;

                // 二维码尺寸
                int avatarWidth = 260;
                int avatarHeight = 260;

                // 创建画布
                BufferedImage canvas = new BufferedImage(width, height, BufferedImage.TYPE_INT_RGB);
                Graphics2D g = (Graphics2D) canvas.getGraphics();
                g.setBackground(Color.WHITE);   //  设置背景色
                g.clearRect(0, 0, width, height);

                // 设置文字抗锯齿
                g.setRenderingHint(RenderingHints.KEY_TEXT_ANTIALIASING, RenderingHints.VALUE_TEXT_ANTIALIAS_ON);

                // 画背景
                g.drawImage(bgImg.getScaledInstance(width, height, Image.SCALE_SMOOTH), 0, 0, null);
                g.drawImage(avatarImage.getScaledInstance(avatarWidth, avatarHeight, Image.SCALE_SMOOTH), 518, 1753, null);

                // 生成图片
                String uuid = UUID.randomUUID().toString().replaceAll("-", "");
                File file = null;
                file = File.createTempFile(uuid, ".png");
                ImageIO.write(canvas, "png", file);

                // 结束计时
                log.info("【分享专属福利】海报生成成功！" + "【耗时:" + (System.currentTimeMillis() - startTime) / 1000.0 + "s】");

                BufferedImage image = ImgUtil.read(file);

                // 图片生成唯一值
                String md5 = DigestUtil.md5Hex(file);

                String path = StrUtil.format("zw/user/{}/poster/{}.png", userId, md5);

                // 上传图片
                posterUrl = ossService.putCdnImg(path, image);

                // 删除本地临时文件
                FileUtil.del(qrCode);

                // 删除海报文件
                FileUtil.del(file);

                // 缓存3天 开发环境不缓存数据
                if (!EnvUtils.isDev()) {
                    String key = GlobalConstants.POSTER_CACHE_KEY + userId;

                    RedisUtils.setCacheObject(key, posterUrl, Duration.ofDays(3));
                }
            } catch (IOException e) {

                log.error("【分享专属福利海报】生成异常！", e);
            }
        }
        log.info("【分享专属福利海报】{}", posterUrl);
        return BaseResult.success(posterUrl);
    }
}
