package com.pgb.service.db;

import com.pgb.ai.domain.ChatRecord;
import com.pgb.service.domain.chat.PgChat;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【pg_chat(AI会话表)】的数据库操作Service
* @createDate 2025-02-08 14:28:51
*/
public interface PgChatService extends IService<PgChat> {
    public List<ChatRecord> getHistory(PgChat chat);
}
