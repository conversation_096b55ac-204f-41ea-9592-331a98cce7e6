package com.pgb.service.db.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.pgb.service.db.PgDistributionRecordService;
import com.pgb.service.domain.distribution.record.PgDistributionRecord;
import com.pgb.service.mapper.PgDistributionRecordMapper;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【pg_distribution_record】的数据库操作Service实现
* @createDate 2024-07-04 16:57:31
*/
@Service
public class PgDistributionRecordServiceImpl extends ServiceImpl<PgDistributionRecordMapper, PgDistributionRecord>
    implements PgDistributionRecordService {

}




