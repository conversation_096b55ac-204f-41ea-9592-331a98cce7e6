package com.pgb.service.domain;

import com.pgb.common.core.global.GlobalConstants;

public interface GlobalXcxConstants {
    /**
     * 小程序· 登录code
     */
    String XCX_LOGIN_CODE = GlobalConstants.GLOBAL_REDIS_KEY + ":xcx:login:code:";

    /**
     * 小程序· 支付成功
     */
    String XCX_PAY_SUCCESS = GlobalConstants.GLOBAL_REDIS_KEY + ":xcx:pay:success:";

    /**
     * 小程序· 活动支付成功
     */
    String XCX_ACTIVITY_PAY_SUCCESS = GlobalConstants.GLOBAL_REDIS_KEY + ":xcx:activity:pay:success:";

    /**
     * 小红书· access_token
     */
    String XCX_XHS_ACCESS_TOKEN = GlobalConstants.GLOBAL_REDIS_KEY + ":xcx:xhs:access_token:";

    /**
     * 小红书· refresh_token
     */
    String XCX_XHS_REFRESH_TOKEN = GlobalConstants.GLOBAL_REDIS_KEY + ":xcx:xhs:refresh_token:";

    /**
     * 小红书· 兑换码限流
     */
    String XCX_VIP_EXCHANGE_CODE_LIMIT = GlobalConstants.GLOBAL_REDIS_KEY + "xcx:vip:exchange:code:limit:";

    /**
     * 小程序· 接口调用防抖
     */
    String XCX_DEBOUNCE = GlobalConstants.GLOBAL_REDIS_KEY + "xcx:debounce:";

    /**
     * 小程序 作文批改中状态
     */
    String XCX_ZW_CORRECTING = GlobalConstants.GLOBAL_REDIS_KEY + "xcx:zw:correcting:";

    /**
     * 小程序 字词默写批改中状态
     */
    String XCX_ZC_CORRECTING = GlobalConstants.GLOBAL_REDIS_KEY + "xcx:zc:correcting:";

    /**
     * 小程序 字词PDF渲染中状态
     */
    String XCX_ZC_QUES_PDF_RENDERING = GlobalConstants.GLOBAL_REDIS_KEY + "xcx:zc:ques:pdf:rendering:";
}
