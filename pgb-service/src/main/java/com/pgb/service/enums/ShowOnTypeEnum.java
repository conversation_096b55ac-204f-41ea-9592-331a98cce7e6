package com.pgb.service.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;

/**
 * <AUTHOR>
 * Created by 2024/12/24 11:33
 */
@Schema(title = "显示时机类型")
@AllArgsConstructor
public enum ShowOnTypeEnum {

    ALWAYS(0, "总是弹窗"),

    TODAY_FIRST(1, "当天第一次进入"),

    ONLY_ONCE(2, "永久一次");

    /**
     * 枚举值对应的整数值
     */
    @EnumValue
    public final int value;

    /**
     * 文字说明
     */
    public final String desc;
}
