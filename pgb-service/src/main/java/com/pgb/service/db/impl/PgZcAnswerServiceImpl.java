package com.pgb.service.db.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.crypto.digest.DigestUtil;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.pgb.common.core.global.exception.BaseException;
import com.pgb.common.core.utils.EnvUtils;
import com.pgb.common.redis.QueueUtils;
import com.pgb.common.redis.RedisUtils;
import com.pgb.service.db.*;
import com.pgb.service.domain.GlobQueueConstants;
import com.pgb.service.domain.GlobalXcxConstants;
import com.pgb.service.domain.homework.PgHomework;
import com.pgb.service.domain.student.PgStudent;
import com.pgb.service.domain.zc.answer.PgZcAnswer;
import com.pgb.service.domain.zc.question.PgZcQuestion;
import com.pgb.service.domain.zc.question.chinese.ZcSubmitForm;
import com.pgb.service.domain.zc.question.chinese.pinyinAndWord.PinyinAndWordResult;
import com.pgb.service.enums.CorrectStatusEnum;
import com.pgb.service.mapper.PgHomeworkMapper;
import com.pgb.service.mapper.PgZcAnswerMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【pg_zc_answer】的数据库操作Service实现
 * @createDate 2025-04-21 14:53:17
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class PgZcAnswerServiceImpl extends ServiceImpl<PgZcAnswerMapper, PgZcAnswer>
        implements PgZcAnswerService {

    private final PgStudentService pgStudentService;

    private final PgHomeworkMapper pgHomeworkMapper;

    private final PgZcQuestionService pgZcQuestionService;

    private final PgAnswerCostService pgAnswerCostService;

    @Override
    public Integer queryToCorrect() {
        return queryToCorrect(true, EnvUtils.isDev() ? 0 : 7);
    }

    @Override
    public Integer queryToCorrect(Boolean isReCorrect, Integer overMinute) {

        // 扫描当前需要批改的题目列表
        // 7分钟之前的
        List<PgZcAnswer> allList = list(new LambdaQueryWrapper<PgZcAnswer>()
                .eq(PgZcAnswer::getDeleted, false)
                .eq(PgZcAnswer::getStatus, CorrectStatusEnum.Uploaded)
                .lt(PgZcAnswer::getCreateTime, DateUtil.offsetMinute(new Date(), -overMinute))
        );

        List<PgZcAnswer> list = new ArrayList<>();

        // 排除掉正在批改的
        for (PgZcAnswer zcAnswer : allList) {
            // 是否批改中
            if (RedisUtils.isExistsObject(GlobalXcxConstants.XCX_ZC_CORRECTING + zcAnswer.getId())) {
                log.info("当前字词题目正在批改中：{}，跳过", zcAnswer.getId());
                continue;
            }

            // 是否已存在队列
            if (QueueUtils.containsQueueObject(GlobQueueConstants.PGB_XCX_ZC_CORRECT_QUEUE.name(), zcAnswer.getId())
            ) {
                log.info("当前字词题目已存在批改队列中：{}，跳过", zcAnswer.getId());
                continue;
            }

            list.add(zcAnswer);
        }

        log.info("【待批改字词题目扫描】超过7分钟未批改题目数量:{}个", list.size());

        if (isReCorrect) {
            list.forEach(zcAnswer -> {
                // 加入批改队列
                QueueUtils.addQueueObject(GlobQueueConstants.PGB_XCX_ZC_CORRECT_QUEUE.name(), zcAnswer.getId());
            });
        }
        return list.size();
    }

    @Override
    public PgZcAnswer submitZcHomeworkAnswer(ZcSubmitForm form, Long userId) {

        // 提交班级默写作业
        PgZcAnswer zcAnswer = new PgZcAnswer();
        zcAnswer.setUserId(userId);
        zcAnswer.setDeleted(false);
        zcAnswer.setStatus(CorrectStatusEnum.Uploaded);
        zcAnswer.setCreateTime(new Date());
        zcAnswer.setAiTokens(0L);

        // 关联学生
        if (ObjectUtil.isNotNull(form.getStudentId())) {
            PgStudent student = pgStudentService.getById(form.getStudentId());
            if (ObjectUtil.isNull(student)) {
                throw new BaseException("当前学生不存在");
            }
            zcAnswer.setName(student.getName());
            zcAnswer.setStudentId(student.getId());
            form.setName(student.getName());
        }

        // 关联作业提交
        if (ObjectUtil.isNotNull(form.getZcHomeworkId())) {
            PgHomework homework = pgHomeworkMapper.selectById(form.getZcHomeworkId());
            if (ObjectUtil.isNull(homework)) {
                throw new BaseException("作业不存在");
            }
            zcAnswer.setZcHomeworkId(form.getZcHomeworkId());

            // 关联题目
            if (ObjectUtil.isNotNull(homework.getZcQuestionId())) {

                PgZcQuestion zcQuestion = pgZcQuestionService.getById(homework.getZcQuestionId());
                // 题目类型
                form.setSubject(zcQuestion.getSubject());
                // 分数
                if (ObjectUtil.isNotNull(zcQuestion.getScore())) {
                    form.setScore(Double.valueOf(zcQuestion.getScore()));
                }
            }
            zcAnswer.setAnswer(form);
        }

        save(zcAnswer);

        return zcAnswer;
    }

    @Override
    public String getImgMd5(List<Long> ids, Long userId) {
        StringBuilder result = new StringBuilder();

        if (!ids.isEmpty()) {
            List<PgZcAnswer> pgZcAnswers = listByIds(ids);
            for (PgZcAnswer answer : pgZcAnswers) {

                PinyinAndWordResult correctResult = JSONUtil.toBean(JSONUtil.toJsonStr(answer.getCorrectResult()), PinyinAndWordResult.class);
                result.append(correctResult);
            }
        }
        return DigestUtil.md5Hex(String.valueOf(result));
    }

    @Override
    public PgZcAnswer reCorrect(PgZcAnswer zcAnswer, boolean isCost) {

        Long userId = zcAnswer.getUserId();

        zcAnswer.setStatus(CorrectStatusEnum.Uploaded);

        updateById(zcAnswer);

        // 新增消耗情况
        if (isCost) {
            pgAnswerCostService.addAnswerCost(userId, zcAnswer.getId(), 1);
        }

        // 加入字词批改队列
        QueueUtils.addQueueObjectInTransaction(
                GlobQueueConstants.PGB_XCX_ZC_CORRECT_QUEUE.name(), zcAnswer.getId()
        );
        return zcAnswer;
    }
}




