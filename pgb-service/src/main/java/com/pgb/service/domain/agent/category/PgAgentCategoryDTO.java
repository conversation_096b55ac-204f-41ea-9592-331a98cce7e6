package com.pgb.service.domain.agent.category;

import java.io.Serializable;
import java.util.List;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.ToString;
import lombok.Builder;
import org.springframework.validation.annotation.Validated;
import jakarta.validation.constraints.Size;

/**
* 分组表
* @TableName pg_agent_category
*/
@Schema(description = "分组表 DTO")
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode
@ToString
@Validated
@Builder
public class PgAgentCategoryDTO implements Serializable {

    @Schema(title = "分组id", type = "string")
    private Long id;

    @Schema(title = "分组名称")
    @Size(max= 255)
    private String name;

    @Schema(title = "同级排序")
    private Integer sort;

    @Schema(title = "关联的智能体id")
    private List<Long> agentIds;
}
