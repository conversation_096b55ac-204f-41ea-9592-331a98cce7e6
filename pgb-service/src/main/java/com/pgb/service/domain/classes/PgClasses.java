package com.pgb.service.domain.classes;

import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.util.Date;
import com.pgb.service.enums.GradeEnum;
import com.pgb.service.enums.SubjectEnum;
import lombok.Data;

/**
 * 班级表
 *
 * @TableName pg_classes
 */
@TableName(value = "pg_classes")
@Data
public class PgClasses implements Serializable {
    /**
     * 班级id
     */
    private Long id;

    /**
     * 班级名称
     */
    private String name;

    /**
     * 创建者id
     */
    private Long userId;

    /**
     * 年级
     */
    private GradeEnum grade;

    /**
     * 班级口令
     */
    private String password;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 班级
     */
    private String classNum;

}
