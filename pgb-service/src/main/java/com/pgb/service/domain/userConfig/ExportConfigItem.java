package com.pgb.service.domain.userConfig;

import cn.hutool.core.util.ObjectUtil;
import com.fasterxml.jackson.annotation.JsonGetter;
import com.pgb.service.domain.userConfig.enums.ExportConfigItemEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema(title = "导出配置项")
@Data
public class ExportConfigItem {
    @Schema(title = "是否启用")
    private Boolean enable;

    @Schema(title = "所属字段")
    private ExportConfigItemEnum field;

    @Schema(title = "所属字段名称")
    @JsonGetter
    public String getName() {
        if (ObjectUtil.isNull(this.field)) {
            return "";
        }

        return this.field.desc;
    }
}
