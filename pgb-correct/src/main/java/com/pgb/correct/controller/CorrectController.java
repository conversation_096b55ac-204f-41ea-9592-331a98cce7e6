package com.pgb.correct.controller;

import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.pgb.common.core.global.BaseResult;
import com.pgb.correct.script.ImgClearBootTest;
import com.pgb.service.custom.ZcCorrectService;
import com.pgb.service.custom.ZwCorrectService;
import com.pgb.service.custom.ZwHomeworkReportService;
import com.pgb.service.db.PgAnswerService;
import com.pgb.service.db.PgHomeworkReportService;
import com.pgb.service.db.PgZcAnswerService;
import com.pgb.service.domain.answer.PgAnswer;
import com.pgb.service.domain.homework.PgHomeworkReport;
import com.pgb.service.domain.zc.answer.PgZcAnswer;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.Duration;

@RestController("CorrectController")
@RequestMapping("/correct")
@RequiredArgsConstructor
@Slf4j
public class CorrectController {

    private final ZwCorrectService zwCorrectService;

    private final PgAnswerService pgAnswerService;

    private final PgHomeworkReportService pgHomeworkReportService;

    private final ZwHomeworkReportService zwHomeworkReportService;

    private final PgZcAnswerService pgZcAnswerService;

    private final ZcCorrectService zcCorrectService;

    @GetMapping("test")
    public BaseResult<String> test() {
        return BaseResult.success("当前为【自有】阿里云云函数");
    }

    // 构造get请求，更新status参数
    // http://127.0.0.1:8082/api/correct/status
    @GetMapping("/status")
    public BaseResult<String> status() {
        ImgClearBootTest test = SpringUtil.getBean(ImgClearBootTest.class);
        test.setStatus(false);
        log.info("【接收到状态更新请求】：已关闭");
        return BaseResult.success("已关闭");
    }

    @GetMapping("zw/{zwId}")
    public BaseResult<Boolean> zwCorrect(@PathVariable Long zwId) {
        log.info("【接收到作文批改监听信息】：{}", zwId);

        try {
            // 获取已上传作业
            PgAnswer answer = pgAnswerService.getById(zwId);

            // 如果为空
            if (ObjectUtil.isNull(answer)) {
                log.warn("【作文批改队列】：{}，记录不存在，等待2秒重试", zwId);
                // 执行2秒后重试，保证数据库一定插入完毕了
                ThreadUtil.safeSleep(Duration.ofSeconds(2).toMillis());
                answer = pgAnswerService.getById(zwId);
            }

            zwCorrectService.correct(answer);
        } catch (Exception e) {
            log.error("【作文批改队列异常】：", e);

            return BaseResult.error("云函数 作文批改队列异常");
        }

        return BaseResult.success(true);
    }

    @GetMapping("report/{homeworkReportId}")
    public BaseResult<Boolean> hmReport(@PathVariable Long homeworkReportId) {
        log.info("【接收到班级报告监听信息】：{}", homeworkReportId);

        try {
            // 获取已上传作业
            PgHomeworkReport report = pgHomeworkReportService.getById(homeworkReportId);

            // 执行生成
            zwHomeworkReportService.statistic(report, false);
        } catch (Exception e) {
            log.error("【班级报告队列异常】：", e);

            return BaseResult.error("云函数 班级报告队列异常");
        }

        return BaseResult.success(true);
    }


    @Operation(summary = "字词批改")
    @GetMapping("zc/{zcId}")
    public BaseResult<Boolean> zcCorrect(@PathVariable Long zcId) {

        log.info("【接收到字词批改监听信息】：{}", zcId);

        try {
            PgZcAnswer zcAnswer = pgZcAnswerService.getById(zcId);

            if (ObjectUtil.isNull(zcAnswer)){
                log.warn("【字词批改队列】：{}，记录不存在，等待2秒重试", zcId);
                // 执行2秒后重试，保证数据库一定插入完毕了
                ThreadUtil.safeSleep(Duration.ofSeconds(2).toMillis());
                zcAnswer = pgZcAnswerService.getById(zcId);
            }

            zcCorrectService.correct(zcAnswer);
        } catch (Exception e) {
            log.error("【字词批改队列异常】：", e);

            return BaseResult.error("云函数 字词批改队列异常");
        }

        return BaseResult.success(true);
    }

}
