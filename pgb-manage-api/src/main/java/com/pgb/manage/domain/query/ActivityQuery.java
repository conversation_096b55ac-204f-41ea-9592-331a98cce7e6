package com.pgb.manage.domain.query;

import com.pgb.common.mybatis.global.PageQuery;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * Created by 2024/12/23 17:26
 */
@EqualsAndHashCode(callSuper = true)
@Schema(description = "活动 搜索实体")
@Data
public class ActivityQuery extends PageQuery {

    @Schema(title = "活动名称")
    private String name;
}
