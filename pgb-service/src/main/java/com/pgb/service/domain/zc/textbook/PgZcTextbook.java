package com.pgb.service.domain.zc.textbook;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;

import com.pgb.common.mybatis.handler.JsonbTypeHandler;
import lombok.Data;

/**
 * 教材表
 * @TableName pg_textbook
 */
@TableName(value ="pg_zc_textbook")
@Data
public class PgZcTextbook implements Serializable {
    /**
     *
     */
    @TableId
    private Long id;

    /**
     * 学制
     */
    private String system;

    /**
     * 年级
     */
    private String grade;

    /**
     * 册别
     */
    private String volume;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 所含单元
     */
    @TableField(typeHandler = JsonbTypeHandler.class)
    private Object units;

    /**
     * 排序
     */
    private Integer sort;
}
