package com.pgb.service.domain.popup;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 *
 * @TableName pg_popup_user
 */
@TableName(value ="pg_popup_user")
@Data
public class PgPopupUser implements Serializable {
    /**
     * 弹窗-用户关联id
     */
    private Long id;

    /**
     * 弹窗id
     */
    private Long popupId;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 激活次数，n次
     */
    private Integer activeNum;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

}
