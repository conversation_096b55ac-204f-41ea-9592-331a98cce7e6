package com.pgb.common.core.utils;

import cn.hutool.core.lang.func.Func1;
import cn.hutool.core.lang.func.LambdaUtil;
import cn.hutool.core.util.StrUtil;

public class FieldUtils {

    /**
     * 通过 lamda 表达式，获取类指定属性的名字
     * @param func
     * @return
     * @param <T>
     * @param <R>
     */
    public static <T, R> String getFieldName(Func1<T, R> func) {
        // 获取 lambda 表达式实现方法的名称
        return LambdaUtil.getFieldName(func);
        //String fieldName = LambdaUtils.extract().getImplMethodName();
        //// 去掉前缀：get，is
        //fieldName = PropertyNamer.methodToProperty(fieldName);
        //// 首字母小写
        //return Introspector.decapitalize(fieldName);
    }

    public static <T, R> String getUnderlineName(Func1<T, R> func) {
        return StrUtil.toUnderlineCase(getFieldName(func));
    }
}
