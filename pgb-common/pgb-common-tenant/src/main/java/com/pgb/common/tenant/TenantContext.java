package com.pgb.common.tenant;

import cn.hutool.core.collection.ListUtil;
import com.baomidou.mybatisplus.extension.plugins.handler.TenantLineHandler;
import lombok.extern.slf4j.Slf4j;
import net.sf.jsqlparser.expression.Expression;
import net.sf.jsqlparser.expression.LongValue;

import java.util.List;

@Slf4j
public class TenantContext implements TenantLineHandler {

    // 店铺，多租户id
    public static final ThreadLocal<Long> TENANT_ID = new ThreadLocal<>();

    // 判断是否是管理员
    public static final ThreadLocal<Boolean> IS_MANAGE = new ThreadLocal<>();

    public static void setTenantId(Long tenantId) {
        TENANT_ID.set(tenantId);
    }

    public static Long getTenantIdAsLong() {
        Long tenantId = TENANT_ID.get();
        return tenantId == null ? -1 : tenantId;
    }

    @Override
    public Expression getTenantId() {
        Long tenantId = TENANT_ID.get();
        return new LongValue(tenantId == null ? -1 : tenantId);
    }

    @Override
    public String getTenantIdColumn() {
        return "tenant_id";
    }

    @Override
    public boolean ignoreTable(String tableName) {

        // 如果是系统管理员，则无需多租户
        if (IS_MANAGE.get() != null && IS_MANAGE.get()) {
            return true;
        }

        // 忽略表
        List<String> ignoreTables = ListUtil.toLinkedList(
                "pg_account",
                "pg_tenant",
                "pg_fk_law",
                "system_admin",
                "system_dict",
                "system_config",
                "system_role",
                "system_permission",
                "system_notice",
                "system_services",
                "pg_question"
        );

        return ignoreTables.contains(tableName);
    }

    // 设置是否是管理员
    public static void setIsManage(Boolean isManage) {
        IS_MANAGE.set(isManage);
    }
}
