package com.pgb.service.domain.generate;

import com.pgb.service.enums.GenerateTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import org.springframework.validation.annotation.Validated;

/**
 * <AUTHOR>
 * Created by 2024/8/24 19:38
 */

@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode
@ToString
@Validated
@Builder
@Data
@Schema(title = "表单内容")
public class GenerateForm {

    @Schema(title = "关联作文题目id")
    private Long quesId;

    @Schema(title = "生成类型")
    private GenerateTypeEnum type;

    @Schema(title = "作文标题")
    private String title;

    @Schema(title = "写作要求")
    private String requirement;

    @Schema(title = "作文体裁")
    private String style;

    @Schema(title = "字数要求")
    private String wordNum;

    @Schema(title = "写哪段")
    private String paragraph;

    @Schema(title = "写作手法")
    private String method;

    // ----------- 关联作文库  -------------
    @Schema(title = "年级")
    private String grade;
}
