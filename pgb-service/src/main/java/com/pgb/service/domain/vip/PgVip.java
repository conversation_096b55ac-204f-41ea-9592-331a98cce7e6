package com.pgb.service.domain.vip;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.pgb.common.pay.enums.BuyTypeEnum;
import com.pgb.service.enums.ChannelTypeEnum;
import com.pgb.service.enums.VipTypeEnum;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 *
 * @TableName pg_vip
 */
@TableName(value ="pg_vip")
@Data
public class PgVip implements Serializable {
    /**
     * 会员充值表
     */
    @TableId
    private Long id;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 会员类型，0：年卡，1：月卡，2：周卡
     */
    private VipTypeEnum vipType;

    /**
     * 购买类型
     */
    private BuyTypeEnum buyType;

    /**
     * 购买天数
     */
    private Integer duration;

    /**
     * 订单id
     */
    private Long orderId;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 会员开通方式，0：小程序开通，1：后台开通，2：小红书发货
     */
    private ChannelTypeEnum openType;
}
