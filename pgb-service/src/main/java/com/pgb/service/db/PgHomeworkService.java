package com.pgb.service.db;

import com.pgb.service.domain.homework.PgHomework;
import com.baomidou.mybatisplus.extension.service.IService;
import com.pgb.service.domain.homework.PgHomeworkVO;

/**
 * <AUTHOR>
 * @description 针对表【pg_homework(作业表)】的数据库操作Service
 * @createDate 2024-12-10 19:36:38
 */
public interface PgHomeworkService extends IService<PgHomework> {

    /**
     * 创建示例作业
     * @param userId
     * @param classId
     */
    void createSampleHomework(Long userId, Long classId);

    /**
     * 将作业对象转换为作业VO对象，并填充相关信息
     * @param homework
     * @return
     */
    PgHomeworkVO convertToHomeworkVO(PgHomework homework);
}
