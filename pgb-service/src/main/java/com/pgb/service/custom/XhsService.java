package com.pgb.service.custom;

import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.SecureUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.pgb.common.core.global.exception.BaseException;
import com.pgb.common.core.utils.EnvUtils;
import com.pgb.common.redis.RedisUtils;
import com.pgb.service.domain.GlobalXcxConstants;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.util.Date;

import static com.pgb.service.custom.QwMsgService.sendErrorMessage;

/**
 * 批改服务
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class XhsService {

    private final String baseApi = "https://ark.xiaohongshu.com/ark/open_api/v3/common_controller";

    private final String appId = "51f0bd3592c34fdda1f6";

    private final String appSecret = "5228e70ab04444ee07d88cbb1ba0597c";

    private JSONObject postNoAccessToken(String method, JSONObject body) {

        Long timestamp = DateUtil.currentSeconds();

        String path = StrUtil.format(
                "{}?appId={}&timestamp={}&version=2.0{}",
                method, this.appId, timestamp, this.appSecret
        );

        String sign = SecureUtil.md5(path);

        // 构建通用体
        body.putOnce("sign", sign)
                .putOnce("appId", appId)
                .putOnce("timestamp", timestamp)
                .putOnce("version", "2.0")
                .putOnce("method", method);

        // 请求
        String response = HttpRequest.post(baseApi)
                .body(body.toString())
                .execute()
                .body();

        JSONObject json = JSONUtil.parseObj(response);
        if (json.getInt("error_code") != 0) {
            log.info("""
                    小红书接口
                    【{}】：调用失败
                    【错误信息】：{}
                    【请求参数】：{}
                    """, method, json, body);
            throw new BaseException("小红书接口调用失败：" + method);
        }

        if (EnvUtils.isDev()) {
            log.info("小红书接口：{}，返回数据：{}", method, json.get("data"));
        }

        if (JSONUtil.isTypeJSON(json.get("data").toString())) {
            return json.getJSONObject("data");
        } else {
            return json;
        }
    }

    private JSONObject post(String method, JSONObject body) {
        return this.postNoAccessToken(method, body.putOnce("accessToken", getAccessToken()));
    }

    /**
     * 授权后，获取accessToken和refreshToken
     *
     * @param code
     */
    public void setAccessToken(String code) {

        JSONObject data = this.postNoAccessToken("oauth.getAccessToken",
                JSONUtil.createObj()
                        .putOnce("code", code)
        );

        // 缓存token
        setTokenCache(data);
    }

    /**
     * 获取，如果没有，则用refresh刷新
     *
     * @return
     */
    private String getAccessToken() {
        String accessToken = RedisUtils.getCacheObject(GlobalXcxConstants.XCX_XHS_ACCESS_TOKEN);

        if (StrUtil.isNotBlank(accessToken)) {
            return accessToken;
        }

        String refreshToken = RedisUtils.getCacheObject(GlobalXcxConstants.XCX_XHS_REFRESH_TOKEN);

        // 如果授权过期了，需要提醒重新授权
        if (StrUtil.isBlank(refreshToken)) {
            // 企微提醒群，提醒
            sendErrorMessage("小红书授权已过期，需要重新授权");
            //
            throw new BaseException("小红书授权已过期，请重新授权");
        }

        JSONObject data = this.post("oauth.refreshToken",
                JSONUtil.createObj()
                        .putOnce("refreshToken", refreshToken)
        );

        // 缓存token
        setTokenCache(data);

        return data.getStr("accessToken");
    }

    /**
     * 缓存token
     *
     * @param data
     */
    private void setTokenCache(JSONObject data) {
        // 缓存accessToken
        RedisUtils.setCacheObject(
                GlobalXcxConstants.XCX_XHS_ACCESS_TOKEN,
                data.getStr("accessToken"),
                Duration.ofSeconds(
                        DateUtil.between(
                                new Date(),
                                DateUtil.date(data.getLong("accessTokenExpiresAt")),
                                DateUnit.SECOND
                        ) - 30
                )
        );

        // 缓存 refreshToken
        RedisUtils.setCacheObject(
                GlobalXcxConstants.XCX_XHS_REFRESH_TOKEN,
                data.getStr("refreshToken"),
                Duration.ofSeconds(
                        DateUtil.between(
                                new Date(),
                                DateUtil.date(data.getLong("refreshTokenExpiresAt")),
                                DateUnit.SECOND
                        ) - 30
                )
        );
    }

    /**
     * 获取订单详情
     *
     * @param orderId
     * @return
     */
    public JSONObject getOrderDetails(String orderId) {

        JSONObject data = this.post("order.getOrderDetail",
                JSONUtil.createObj()
                        .putOnce("orderId", orderId)
        );

        return data;
    }

    /**
     * 获取订单收货人信息
     * 注意，该接口仅订单未“待发货”的时候，才能用
     * https://open.xiaohongshu.com/document/api?apiNavigationId=185&id=27&gatewayId=103&gatewayVersionId=1661&apiId=27240&apiParentNavigationId=15
     *
     * @param orderId
     * @param openAddressId
     * @return
     */
    public JSONObject getOrderReceiverInfo(String orderId, String openAddressId) {
        // 注意，该接口仅订单未“待发货”的时候，才能用
        JSONArray receiverQueries = JSONUtil.createArray();
        receiverQueries.add(
                JSONUtil.createObj()
                        .putOnce("orderId", orderId)
                        .putOnce("openAddressId", openAddressId)
        );

        // 获取加密数据
        JSONObject data = this.post("order.getOrderReceiverInfo",
                JSONUtil.createObj()
                        .putOnce("receiverQueries", receiverQueries)
                        .putOnce("isReturn", false)
        );

        // 获取收件人
        JSONObject receiverInfo = data.getJSONArray("receiverInfos").getJSONObject(0);

        // 进行解密
        batchDecrypt(orderId, receiverInfo);

        log.info("小红书解密信息：{}", receiverInfo);

        return receiverInfo;
    }

    /**
     * 批量解密
     * @param orderId
     * @param info
     */
    public void batchDecrypt(String orderId, JSONObject info) {

        JSONArray baseInfos = JSONUtil.createArray();
        baseInfos.add(
                JSONUtil.createObj()
                        .putOnce("dataTag", orderId)
                        .putOnce("encryptedData", info.getStr("receiverAddress"))
        );
        baseInfos.add(
                JSONUtil.createObj()
                        .putOnce("dataTag", orderId)
                        .putOnce("encryptedData", info.getStr("receiverPhone"))
        );
        baseInfos.add(
                JSONUtil.createObj()
                        .putOnce("dataTag", orderId)
                        .putOnce("encryptedData", info.getStr("receiverName"))
        );

        JSONObject data = this.post("data.batchDecrypt", JSONUtil.createObj()
                .putOnce("baseInfos", baseInfos)
                .putOnce("actionType", "1")
                .putOnce("appUserId", IdUtil.fastSimpleUUID())
        );

        info.set("receiverAddress", data.getJSONArray("dataInfoList").getJSONObject(0).getStr("decryptedData"));
        info.set("receiverPhone", data.getJSONArray("dataInfoList").getJSONObject(1).getStr("decryptedData"));
        info.set("receiverName", data.getJSONArray("dataInfoList").getJSONObject(2).getStr("decryptedData"));
    }

    /**
     * 无物流发货
     *
     * @param orderId
     * @param expressNo
     */
    public void orderDeliver(String orderId, String expressNo) {
        this.post("order.orderDeliver",
                JSONUtil.createObj()
                        .putOnce("orderId", orderId)
                        .putOnce("expressNo", expressNo)
                        .putOnce("expressCompanyCode", "selfdelivery")
        );
    }
}
