package com.pgb.manage.controller.textbook;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.pgb.common.core.global.BaseResult;
import com.pgb.service.custom.textbook.WordService;
import com.pgb.service.domain.zc.common.textbook.dto.*;
import com.pgb.service.domain.zc.common.textbook.entity.PgWordExample;
import com.pgb.service.domain.zc.common.textbook.entity.PgWordPronunciation;
import com.pgb.service.domain.zc.common.textbook.entity.PgWordTranslation;
import com.pgb.service.domain.zc.common.textbook.vo.*;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 单词管理Controller
 *
 * <AUTHOR>
 * Created by 2025/7/25 18:34
 */
@Tag(name = "管理端/单词管理", description = "单词、翻译、发音、例句管理接口")
@Slf4j
@RestController("ManageWordController")
@RequestMapping("/manage/word")
@RequiredArgsConstructor
//@SaCheckRole(RoleConstants.Manager)
public class WordController {

    private final WordService wordService;

    // ==================== 单词相关接口 ====================

    @Operation(summary = "分页获取单词列表", description = "获取单词列表，包含完整的翻译、发音、例句等关联数据")
    @PostMapping("/page")
    public BaseResult<Page<WordPageVO>> getWordPage(@RequestBody @Validated WordPageDTO dto) {
        return wordService.getWordPage(dto);
    }

    @Operation(summary = "获取单词详情")
    @GetMapping("/{id}")
    public BaseResult<WordDetailVO> getWordDetail(
            @Parameter(description = "单词ID") @PathVariable Long id) {
        return wordService.getWordDetail(id);
    }

    @Operation(summary = "创建单词")
    @PostMapping
    public BaseResult<IdResultVO> createWord(@RequestBody @Validated PgWordDTO dto) {
        return wordService.createWord(dto);
    }

    @Operation(summary = "更新单词")
    @PutMapping("/{id}")
    public BaseResult<Boolean> updateWord(
            @Parameter(description = "单词ID") @PathVariable Long id,
            @RequestBody @Validated WordUpdateDTO dto) {
        return wordService.updateWord(id, dto);
    }

    @Operation(summary = "删除单词")
    @DeleteMapping("/{id}")
    public BaseResult<Boolean> deleteWord(
            @Parameter(description = "单词ID") @PathVariable Long id) {
        return wordService.deleteWord(id);
    }

    @Operation(summary = "批量录入单词")
    @PostMapping("/batch")
    public BaseResult<BatchWordResultVO> batchCreateWords(@RequestBody @Validated BatchWordDTO dto) {
        return wordService.batchCreateWords(dto);
    }

    @Operation(summary = "单词排序")
    @PostMapping("/sort")
    public BaseResult<Boolean> sortWords(@RequestBody @Validated List<SortItemDTO> sortList) {
        return wordService.sortWords(sortList);
    }

    // ==================== 单词翻译相关接口 ====================

    @Operation(summary = "获取单词翻译列表")
    @GetMapping("/translation/list")
    public BaseResult<List<PgWordTranslation>> getWordTranslationList(
            @Parameter(description = "单词ID") @RequestParam Long wordId) {
        return wordService.getWordTranslationList(wordId);
    }

    @Operation(summary = "创建单词翻译")
    @PostMapping("/translation")
    public BaseResult<IdResultVO> createWordTranslation(@RequestBody @Validated PgWordTranslationDTO dto) {
        return wordService.createWordTranslation(dto);
    }

    @Operation(summary = "更新单词翻译")
    @PutMapping("/translation/{id}")
    public BaseResult<Boolean> updateWordTranslation(
            @Parameter(description = "翻译ID") @PathVariable Long id,
            @RequestBody @Validated PgWordTranslationDTO dto) {
        return wordService.updateWordTranslation(id, dto);
    }

    @Operation(summary = "删除单词翻译")
    @DeleteMapping("/translation/{id}")
    public BaseResult<Boolean> deleteWordTranslation(
            @Parameter(description = "翻译ID") @PathVariable Long id) {
        return wordService.deleteWordTranslation(id);
    }

    // ==================== 单词发音相关接口 ====================

    @Operation(summary = "获取单词发音列表")
    @GetMapping("/pronunciation/list")
    public BaseResult<List<PgWordPronunciation>> getWordPronunciationList(
            @Parameter(description = "单词ID") @RequestParam Long wordId) {
        return wordService.getWordPronunciationList(wordId);
    }

    @Operation(summary = "创建单词发音")
    @PostMapping("/pronunciation")
    public BaseResult<IdResultVO> createWordPronunciation(@RequestBody @Validated PgWordPronunciationDTO dto) {
        return wordService.createWordPronunciation(dto);
    }

    @Operation(summary = "更新单词发音")
    @PutMapping("/pronunciation/{id}")
    public BaseResult<Boolean> updateWordPronunciation(
            @Parameter(description = "发音ID") @PathVariable Long id,
            @RequestBody @Validated PgWordPronunciation dto) {
        return wordService.updateWordPronunciation(id, dto);
    }

    @Operation(summary = "删除单词发音")
    @DeleteMapping("/pronunciation/{id}")
    public BaseResult<Boolean> deleteWordPronunciation(
            @Parameter(description = "发音ID") @PathVariable Long id) {
        return wordService.deleteWordPronunciation(id);
    }

    // ==================== 单词例句相关接口 ====================

    @Operation(summary = "获取单词例句列表")
    @GetMapping("/example/list")
    public BaseResult<List<PgWordExample>> getWordExampleList(
            @Parameter(description = "单词ID") @RequestParam Long wordId) {
        return wordService.getWordExampleList(wordId);
    }

    @Operation(summary = "创建单词例句")
    @PostMapping("/example")
    public BaseResult<IdResultVO> createWordExample(@RequestBody @Validated PgWordExampleDTO dto) {
        return wordService.createWordExample(dto);
    }

    @Operation(summary = "更新单词例句")
    @PutMapping("/example/{id}")
    public BaseResult<Boolean> updateWordExample(
            @Parameter(description = "例句ID") @PathVariable Long id,
            @RequestBody @Validated PgWordExampleDTO dto) {
        return wordService.updateWordExample(id, dto);
    }

    @Operation(summary = "删除单词例句")
    @DeleteMapping("/example/{id}")
    public BaseResult<Boolean> deleteWordExample(
            @Parameter(description = "例句ID") @PathVariable Long id) {
        return wordService.deleteWordExample(id);
    }

    // ==================== 图片识别相关接口 ====================

    @Operation(
            summary = "直接上传并识别图片中的英语单词",
            description = """
                    上传图片文件并直接识别其中的英语单词，返回详细的识别结果。

                    **功能说明：**
                    - 支持JPG、PNG、BMP、GIF格式的图片文件
                    - 文件大小限制：10MB以内
                    - 使用AI大模型进行图片识别，提供高精度的单词识别
                    - 返回每个单词的详细信息：原文、中文翻译、音标、词性

                    **返回字段说明：**
                    - words: 识别出的单词列表（字符串数组）
                    - confidence: 整体识别置信度（0-1之间的浮点数）
                    - processTime: 处理耗时（毫秒）
                    - imageUrl: 图片的Base64数据URL
                    - fileName: 原始文件名
                    - fileSize: 文件大小（字节）
                    - details: 单词详细信息数组
                    """
    )
    @PostMapping("/image/direct-ocr")
    public BaseResult<DirectOcrResponseVO> directOcr(
            @Parameter(description = "图片文件，支持JPG、PNG、BMP、GIF格式，大小限制10MB")
            @RequestParam("file") MultipartFile file,
            @Parameter(description = "单元ID，用于关联识别的单词到指定教学单元")
            @RequestParam("unitId") Long unitId) {
        return wordService.directOcr(file, unitId);
    }

    @Operation(
            summary = "确认录入识别的单词",
            description = """
                    确认录入图片识别出的单词，支持完整的单词信息录入。

                    **功能说明：**
                    - 支持录入单词的完整信息：单词原文、中文翻译、音标、词性
                    - 自动创建单词主记录、翻译记录、发音记录
                    - 支持批量录入多个单词
                    - 音标默认设置为英音类型
                    - 翻译和发音信息为可选，如果为空则不创建对应记录
                    """
    )
    @PostMapping("/image/confirm")
    public BaseResult<BatchWordResultVO> confirmWords(@RequestBody @Validated ImageWordConfirmDTO dto) {
        return wordService.confirmWords(dto);
    }
}
