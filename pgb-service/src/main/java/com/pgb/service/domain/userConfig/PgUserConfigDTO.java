package com.pgb.service.domain.userConfig;

import java.io.Serializable;

import java.util.Date;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.ToString;
import lombok.Builder;
import org.springframework.validation.annotation.Validated;
import jakarta.validation.constraints.Size;

/**
* 用户配置表
* @TableName pg_user_config
*/
@Schema(description = "用户配置表 DTO")
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode
@ToString
@Validated
@Builder
public class PgUserConfigDTO implements Serializable {

    @Schema(title = "用户配置", type = "string")
    private Long id;

    @Schema(title = "用户id")
    private Long userId;

    @Schema(title = "key值")
    private String key;

    @Schema(title = "配置value值")
    private Object value;

    @Schema(title = "备注说明")
    private String remark;

    @Schema(title = "是否启用")
    private Boolean isValid;

    @Schema(title = "创建时间")
    private Date createTime;

}
