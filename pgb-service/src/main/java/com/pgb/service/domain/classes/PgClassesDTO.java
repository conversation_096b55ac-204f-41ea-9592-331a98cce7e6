package com.pgb.service.domain.classes;

import java.io.Serializable;

import java.util.Date;
import com.pgb.service.enums.GradeEnum;
import com.pgb.service.enums.SubjectEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.ToString;
import lombok.Builder;
import org.springframework.validation.annotation.Validated;
import jakarta.validation.constraints.Size;

/**
* 班级表
* @TableName pg_classes
*/
@Schema(description = "班级表 DTO")
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode
@ToString
@Validated
@Builder
public class PgClassesDTO implements Serializable {

    @Schema(title = "班级id")
    private Long id;

    @Schema(title = "班级名称")
    @Size(max= 255)
    private String name;

    @Schema(title = "创建者id")
    private Long userId;

    @Schema(title = "年级")
    private GradeEnum grade;

    @Schema(title = "班级口令")
    @Size(max= 255)
    private String password;

    @Schema(title = "创建时间")
    private Date createTime;

    @Schema(title = "几班")
    private String classNum;

}
