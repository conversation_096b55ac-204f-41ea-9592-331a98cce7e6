package com.pgb.manage.config;

import com.pgb.common.oss.property.OssAliProperty;
import com.pgb.common.oss.service.OssAliService;
import com.pgb.common.oss.service.OssService;
import io.swagger.v3.oas.annotations.media.Schema;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
@EnableConfigurationProperties(OssAliProperty.class)
public class OssConfiguration {

    @Schema(title = "注入 阿里云 OSS")
    @Bean
    public OssService ossService(OssAliProperty property) {
        return new OssAliService(property);
    }

}
