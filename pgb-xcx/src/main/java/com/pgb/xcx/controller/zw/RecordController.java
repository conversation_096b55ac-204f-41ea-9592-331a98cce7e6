package com.pgb.xcx.controller.zw;

import cn.dev33.satoken.annotation.SaCheckLogin;
import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.*;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.pgb.common.core.global.BaseResult;
import com.pgb.common.core.global.GlobalCode;

import com.pgb.common.mybatis.global.PageQuery;
import com.pgb.common.oss.service.OssService;
import com.pgb.service.custom.CorrectService;
import com.pgb.service.db.*;
import com.pgb.service.domain.answer.AnswerNumStatistic;
import com.pgb.service.domain.answer.answerBatch.PgAnswerBatch;
import com.pgb.service.domain.answer.answerBatch.PgAnswerBatchVO;
import com.pgb.service.domain.answer.answerCost.PgAnswerCost;
import com.pgb.service.domain.answer.model.PgAnswerModel;
import com.pgb.service.domain.common.image.FilePaperImg;
import com.pgb.service.domain.answer.PgAnswer;
import com.pgb.service.domain.answer.PgAnswerDTO;
import com.pgb.service.domain.answer.PgAnswerVO;
import com.pgb.service.domain.common.TodayNum;
import com.pgb.service.domain.homework.PgHomework;
import com.pgb.service.domain.query.AnswerQuery;
import com.pgb.service.domain.query.PcAnswerQuery;
import com.pgb.service.domain.question.PgQuestion;
import com.pgb.service.domain.question.zwEssay.ZwEssayQuestion;
import com.pgb.service.domain.student.PgStudent;
import com.pgb.service.enums.CorrectStatusEnum;
import com.pgb.service.util.ZwUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;


@Tag(name = "用户端/批改/批改记录")
@RestController("UserRecordController")
@Transactional(rollbackFor = Exception.class)
@RequestMapping("/user/record/info")
@RequiredArgsConstructor
@Slf4j
public class RecordController {
    private final PgAnswerService pgAnswerService;

    private final CorrectService correctService;

    private final OssService ossService;

    private final PgAnswerCostService pgAnswerCostService;

    private final PgAnswerBatchService pgAnswerBatchService;

    private final PgQuestionService pgQuestionService;

    private final PgAnswerModelService pgAnswerModelService;

    private final PgHomeworkService pgHomeworkService;

    private final PgStudentService pgStudentService;


    @Operation(summary = "获取今日批改次数")
    @GetMapping("today/submit")
    @SaCheckLogin
    public BaseResult<TodayNum> getTodaySubmitNum() {

        return BaseResult.success(
                pgAnswerCostService.getTodaySubmitNum(StpUtil.getLoginIdAsLong())
        );
    }

    @Operation(summary = "查看总批改次数")
    @GetMapping("total/correct")
    @SaCheckLogin
    public BaseResult<Long> getTotalCorrectNum() {
        long count = pgAnswerCostService.count(new LambdaQueryWrapper<PgAnswerCost>()
                .eq(PgAnswerCost::getUserId, StpUtil.getLoginIdAsLong()));

        return BaseResult.success(count);

    }

    @Operation(summary = "查看提交作文记录")
    @PostMapping("page")
    @SaCheckLogin
    public BaseResult<IPage<PgAnswerVO>> page(@RequestBody AnswerQuery query) {

        IPage<PgAnswerVO> page = pgAnswerService.page(query.toMpPageSortByCreateTime(), new LambdaQueryWrapper<PgAnswer>()
                // 根据备注名称筛选
                .like(StrUtil.isNotBlank(query.getName()), PgAnswer::getName, query.getName())
                .eq(PgAnswer::getUserId, StpUtil.getLoginIdAsLong())
                .eq(PgAnswer::getDeleted, false)
        ).convert(item -> {
            ZwEssayQuestion userAnswer = JSONUtil.toBean(item.getAnswer().toString(), ZwEssayQuestion.class);

            PgAnswerVO answerVO = BeanUtil.copyProperties(item, PgAnswerVO.class, "answer");
            answerVO.setAnswer(userAnswer);

            return answerVO;
        });

        return BaseResult.success(page);
    }

    @Operation(summary = "【PC】查看提交作文记录")
    @PostMapping("pc/page")
    @SaCheckLogin
    public BaseResult<IPage<PgAnswerVO>> pcPage(@RequestBody PcAnswerQuery query) {

        IPage<PgAnswerVO> page = pgAnswerService.page(query.toMpPageSortByCreateTime(), new LambdaQueryWrapper<PgAnswer>()
                // 根据备注名称筛选
                .like(StrUtil.isNotBlank(query.getName()), PgAnswer::getName, query.getName())
                // 状态
                .eq(ObjectUtil.isNotNull(query.getStatus()), PgAnswer::getStatus, query.getStatus())
                .eq(PgAnswer::getUserId, StpUtil.getLoginIdAsLong())
                .eq(PgAnswer::getDeleted, false)
        ).convert(item -> {
            ZwEssayQuestion userAnswer = JSONUtil.toBean(item.getAnswer().toString(), ZwEssayQuestion.class);

            PgAnswerVO answerVO = BeanUtil.copyProperties(item, PgAnswerVO.class, "answer");
            answerVO.setAnswer(userAnswer);

            return answerVO;
        });

        return BaseResult.success(page);
    }

    @Operation(summary = "查看批量提交批次记录")
    @PostMapping("batch/page")
    @SaCheckLogin
    public BaseResult<IPage<PgAnswerBatchVO>> batchPage(@RequestBody PageQuery query) {

        IPage<PgAnswerBatchVO> page = pgAnswerBatchService.page(query.toMpPageSortByCreateTime(), new LambdaQueryWrapper<PgAnswerBatch>()
                .eq(PgAnswerBatch::getUserId, StpUtil.getLoginIdAsLong())
        ).convert(batch -> {

            PgAnswerBatchVO batchVO = BeanUtil.copyProperties(batch, PgAnswerBatchVO.class);

            List<PgAnswer> answers = pgAnswerService.list(new LambdaQueryWrapper<PgAnswer>()
                    .select(PgAnswer::getId, PgAnswer::getStatus)
                    .eq(PgAnswer::getBatchId, batch.getId())
                    .eq(PgAnswer::getDeleted, false)
            );

            // 总数
            long totalNum = answers.size();
            batchVO.setTotalNum((int) totalNum);

            // 已批改数量
            long correctedNum = CollUtil.count(answers, answer -> answer.getStatus() == CorrectStatusEnum.Corrected);
            batchVO.setCorrectedNum((int) correctedNum);

            // 【批量提交】题目信息
            if (ObjectUtil.isNotNull(batch.getQuestionId())) {
                PgQuestion question = pgQuestionService.getById(batch.getQuestionId());
                if (ObjectUtil.isNotNull(question)) {
                    batchVO.setQuestionName(question.getName());
                }
            }
            return batchVO;
        });

        return BaseResult.success(page);

    }

    @Operation(summary = "查看单个作文记录批次进度", description = "用于轮询查进度")
    @GetMapping("batch/{id}")
    @SaCheckLogin
    public BaseResult<PgAnswerBatchVO> batch(@PathVariable Long id) {

        PgAnswerBatch batch = pgAnswerBatchService.getById(id);

        if (ObjectUtil.isNull(batch)) {
            return BaseResult.code(GlobalCode.Item_Null);
        }

        PgAnswerBatchVO batchVO = BeanUtil.copyProperties(batch, PgAnswerBatchVO.class);

        LambdaQueryWrapper<PgAnswer> wrapper = new LambdaQueryWrapper<PgAnswer>()
                .eq(PgAnswer::getBatchId, id)
                .and(i -> i.ne(PgAnswer::getDeleted, true)
                        .or()
                        .isNull(PgAnswer::getDeleted));

        // 总数
        long totalNum = pgAnswerService.count(wrapper);
        batchVO.setTotalNum((int) totalNum);

        // 已批改数量
        long correctedNum = pgAnswerService.count(wrapper
                .ne(PgAnswer::getStatus, CorrectStatusEnum.Uploaded)
        );
        batchVO.setCorrectedNum((int) correctedNum);

        return BaseResult.success(batchVO);
    }

    @Operation(summary = "根据批次查提交作文记录")
    @GetMapping("page/{batchId}")
    @SaCheckLogin
    public BaseResult<List<PgAnswerVO>> pageByBatchId(@PathVariable Long batchId) {

        List<PgAnswerVO> list = pgAnswerService.list(new LambdaQueryWrapper<PgAnswer>()
                        .eq(PgAnswer::getBatchId, batchId)
                        .eq(PgAnswer::getUserId, StpUtil.getLoginIdAsLong())
                        .and(i -> i.ne(PgAnswer::getDeleted, true)
                                .or()
                                .isNull(PgAnswer::getDeleted)
                        )
                        .orderByAsc(PgAnswer::getCreateTime)
                )
                .stream()
                .map(item -> {
                    ZwEssayQuestion userAnswer = JSONUtil.toBean(item.getAnswer().toString(), ZwEssayQuestion.class);

                    PgAnswerVO answerVO = BeanUtil.copyProperties(item, PgAnswerVO.class, "answer");
                    answerVO.setAnswer(userAnswer);

                    return answerVO;
                }).toList();

        return BaseResult.success(list);
    }

    @Operation(summary = "获取单个记录批改状态")
    @GetMapping("status/{id}")
    @SaCheckLogin
    public BaseResult<CorrectStatusEnum> getStatus(@PathVariable Long id) {

        PgAnswer answer = pgAnswerService.getById(id);

        if (ObjectUtil.isNull(answer)) {
            return BaseResult.code(GlobalCode.Item_Null);
        }

        return BaseResult.success(answer.getStatus());
    }

    @Data
    public static class ScoreStatusInfo {

        @Schema(title = "状态")
        private CorrectStatusEnum status;

        @Schema(title = "用户分数")
        private Double userScore;
    }

    @Operation(summary = "获取记录的批改状态和分数")
    @GetMapping("scoreStatus/{id}")
    @SaCheckLogin
    public BaseResult<ScoreStatusInfo> getScoreStatus(@PathVariable Long id) {

        ScoreStatusInfo info = new ScoreStatusInfo();

        PgAnswer answer = pgAnswerService.getById(id);

        info.setStatus(answer.getStatus());

        if (ObjectUtil.isNotNull(answer.getCorrectResult())) {
            ZwEssayQuestion result = JSONUtil.toBean(answer.getCorrectResult().toString(), ZwEssayQuestion.class);
            info.setUserScore(Double.valueOf(result.getUserScore()));
        }

        return BaseResult.success(info);
    }


    @Operation(summary = "查看提交详情")
    @GetMapping("detail/{id}")
    @SaCheckLogin
    public BaseResult<PgAnswerDTO> detail(@PathVariable Long id) {

        PgAnswer answer = pgAnswerService.getById(id);

        PgAnswerDTO answerDTO = JSONUtil.toBean(JSONUtil.toJsonStr(answer), PgAnswerDTO.class);

        // 如果归档了，则获取归档内容
        if (ObjectUtil.defaultIfNull(answer.getIsArchive(), false)) {
            if (StrUtil.isBlank(answer.getArchiveUrl())) {
                return BaseResult.error("当前批改内容不存在");
            }

            // hutool 下载批改结果
            try {
                String jsonContent = HttpUtil.get(answer.getArchiveUrl());
                // 将下载的内容转为JSONObject
                ZwEssayQuestion correctResult = JSONUtil.toBean(jsonContent, ZwEssayQuestion.class);
                // 将归档内容设置到DTO中
                answerDTO.setCorrectResult(
                        correctResult
                );
            } catch (Exception e) {
                return BaseResult.error("获取归档内容失败: " + e.getMessage());
            }
        }
        // 正常状态
        else {
            if (ObjectUtil.isNull(answer.getCorrectResult())) {
                return BaseResult.error("当前批改内容不存在，请批改完成后查看");
            }

            ZwEssayQuestion correctResult = JSONUtil.toBean(answer.getCorrectResult().toString(), ZwEssayQuestion.class);

            answerDTO.setCorrectResult(
                    correctResult
            );
        }

        // 是否是范文
        answerDTO.setIsModel(
                pgAnswerModelService.exists(new LambdaQueryWrapper<PgAnswerModel>()
                        .eq(PgAnswerModel::getAnswerId, id))
        );

        return BaseResult.success(answerDTO);
    }

    @Operation(summary = "查看批改结果", description = "直接返回生成好的图片")
    @GetMapping("result/{id}")
    public BaseResult<PgAnswerDTO> result(@PathVariable Long id) {

        PgAnswer answer = pgAnswerService.getById(id);

        if (ObjectUtil.isNull(answer)) {
            return BaseResult.code(GlobalCode.Item_Null);
        }

        if (answer.getStatus().equals(CorrectStatusEnum.Uploaded)) {
            return BaseResult.error("当前作文正在批改中，请稍后查看");
        }

        PgAnswerDTO answerDTO = JSONUtil.toBean(JSONUtil.toJsonStr(answer), PgAnswerDTO.class);

        // 统一返回
        ZwEssayQuestion correctAnswer = JSONUtil.toBean(answer.getCorrectResult().toString(), ZwEssayQuestion.class);

        // 清除
        correctAnswer.setMarkJsonList(null);

        // 替代
        answerDTO.setCorrectResult(correctAnswer);

        // 是否是范文
        answerDTO.setIsModel(
                pgAnswerModelService.exists(new LambdaQueryWrapper<PgAnswerModel>()
                        .eq(PgAnswerModel::getAnswerId, id))
        );

        // 用户答案
        ZwEssayQuestion userAnswer = JSONUtil.toBean(answer.getAnswer().toString(), ZwEssayQuestion.class);

        answerDTO.setAnswer(userAnswer);

        return BaseResult.success(answerDTO);
    }

    @Operation(summary = "保存批改结果")
    @PostMapping("save/{id}")
    @SaCheckLogin
    public BaseResult<Boolean> saveZw(@PathVariable Long id, @RequestBody @Validated ZwEssayQuestion question) {

        PgAnswer answer = pgAnswerService.getById(id);

        if (!answer.getUserId().equals(StpUtil.getLoginIdAsLong())) {
            return BaseResult.error("当前无权限修改作文内容");
        }

        // 得对照，现在的json和之前渲染好的json，有没有不同，如果不同，就重新渲染
        // 遍历 markJSON
        question = correctService.renderImg(question.getUserImgAnswerList(), question, answer.getUserId(), true, true);

        // 保存题目批改信息
        answer.setCorrectResult(question);

        // 更新保存
        pgAnswerService.updateById(answer);

        return BaseResult.success(true);
    }

    public record SaveComment(String comment, String overallComment) {
    }

    @Operation(summary = "保存点评")
    @PostMapping("saveComment/{id}")
    @SaCheckLogin
    public BaseResult<Boolean> saveFeedback(@PathVariable Long id, @RequestBody SaveComment data) {

        PgAnswer answer = pgAnswerService.getById(id);

        if (!answer.getUserId().equals(StpUtil.getLoginIdAsLong())) {
            return BaseResult.error("当前无权限修改作文内容");
        }

        ZwEssayQuestion correctAnswer = JSONUtil.toBean(answer.getCorrectResult().toString(), ZwEssayQuestion.class);

        correctAnswer.setComment(data.comment);
        correctAnswer.setOverallComment(data.overallComment);

        answer.setCorrectResult(correctAnswer);

        pgAnswerService.updateById(answer);

        return BaseResult.success(true);
    }

    public record SavePolish(String polish) {
    }

    @Operation(summary = "保存润色")
    @PostMapping("savePolish/{id}")
    @SaCheckLogin
    public BaseResult<Boolean> savePolish(@PathVariable Long id, @RequestBody SavePolish data) {

        PgAnswer answer = pgAnswerService.getById(id);

        if (!answer.getUserId().equals(StpUtil.getLoginIdAsLong())) {
            return BaseResult.error("当前无权限修改作文内容");
        }

        ZwEssayQuestion correctAnswer = JSONUtil.toBean(answer.getCorrectResult().toString(), ZwEssayQuestion.class);

        correctAnswer.setPolish(data.polish);

        // 获取润色和原文的差异
        correctAnswer.setPolishDiffList(
                ZwUtil.getPolishDiff(correctAnswer.getFullText(), correctAnswer.getPolish())
        );

        answer.setCorrectResult(correctAnswer);

        pgAnswerService.updateById(answer);

        return BaseResult.success(true);
    }

    @Operation(summary = "删除作文提交记录")
    @DeleteMapping("{id}")
    @SaCheckLogin
    public BaseResult<Boolean> delete(@PathVariable Long id) {
        PgAnswer answer = pgAnswerService.getById(id);

        if (!answer.getUserId().equals(StpUtil.getLoginIdAsLong())) {
            return BaseResult.error("当前无权限删除作文内容");
        }

        if (ObjectUtil.isNull(answer)) {
            return BaseResult.code(GlobalCode.Item_Null);
        }

        pgAnswerService.deleteAnswer(answer);

        return BaseResult.success(true);
    }

    @Operation(summary = "根据批次删除作文提交记录")
    @DeleteMapping("batch/{id}")
    @SaCheckLogin
    public BaseResult<Boolean> deleteBatch(@PathVariable Long id) {

        PgAnswerBatch batch = pgAnswerBatchService.getById(id);

        if (ObjectUtil.isNull(batch)) {
            return BaseResult.code(GlobalCode.Item_Null);
        }

        List<PgAnswer> answers = pgAnswerService.list(new LambdaQueryWrapper<PgAnswer>()
                .select(PgAnswer::getId, PgAnswer::getBatchId, PgAnswer::getDeleted)
                .eq(PgAnswer::getBatchId, id));

        answers.forEach(answer -> answer.setDeleted(true));

        pgAnswerService.updateBatchById(answers);

        return BaseResult.success(
                pgAnswerBatchService.removeById(id)
        );
    }

    @Deprecated
    public BaseResult<Boolean> deleteV1(Long id) {

        PgAnswer answer = pgAnswerService.getById(id);
        if (ObjectUtil.isNull(answer)) {
            return BaseResult.code(GlobalCode.Item_Null);
        }

        if (!answer.getStatus().equals(CorrectStatusEnum.Corrected)) {
            return BaseResult.error("当前题目批改中，无法删除");
        }

        // 删除用户上传的图片
        ZwEssayQuestion userAnswer = JSONUtil.toBean(answer.getAnswer().toString(), ZwEssayQuestion.class);

        deleteImages(userAnswer.getUserImgAnswerList());

        // 删除渲染后的图片
        ZwEssayQuestion correctAnswer = JSONUtil.toBean(answer.getCorrectResult().toString(), ZwEssayQuestion.class);

        deleteImages(correctAnswer.getUserImgAnswerList());

        pgAnswerService.removeById(id);

        return BaseResult.success(true);
    }

    /**
     * 删除OSS图片
     *
     * @param imgAnswerList
     */
    private void deleteImages(List<FilePaperImg> imgAnswerList) {
//        imgAnswerList.stream()
//                .filter(Objects::nonNull)
//                .filter(img -> ObjectUtil.isNotNull(img.getImgUrl()))
//                .forEach(img -> ossService.delete(URLUtil.getPath(img.getImgUrl())));

        imgAnswerList.forEach(
                img -> {
                    if (ObjectUtil.isNotNull(img.getImgUrl())) {
                        ossService.delete(URLUtil.getPath(img.getImgUrl()));
                    }
                }
        );
    }


    public record SavaScore(Double score, Integer scoreLevel) {
    }

    @Operation(summary = "手动打分")
    @PostMapping("score/{id}")
    @SaCheckLogin
    public BaseResult<SavaScore> score(@PathVariable Long id, @RequestBody SavaScore data) {

        PgAnswer answer = pgAnswerService.getById(id);

        if (ObjectUtil.defaultIfNull(answer.getIsArchive(), false)) {
            return BaseResult.error("180天前的作文仅可导出，无法修改");
        }

        ZwEssayQuestion correctAnswer = JSONUtil.toBean(answer.getCorrectResult().toString(), ZwEssayQuestion.class);

        // 分数 ---> 更改评级
        if (ObjectUtil.isNotNull(data.score)) {
            if (data.score < 0) {
                return BaseResult.error("分数不能小于0");
            }
            correctAnswer.setUserScore(data.score.intValue());
        }
        // 评级 ---> 更改分数
        else {
            if (data.scoreLevel < 1 || data.scoreLevel > 4) {
                return BaseResult.error("评级类型错误，请重新选择");
            }
            correctAnswer.handleSetScoreLevel(data.scoreLevel);
        }

        answer.setCorrectResult(correctAnswer);

        pgAnswerService.updateById(answer);

        return BaseResult.success(
                new SavaScore(correctAnswer.getUserScore().doubleValue(), correctAnswer.getScoreLevel())
        );
    }


    public record ScoreStyle(Integer style) {
    }

    @Operation(summary = "修改分数样式", description = "0：评级，1：分数")
    @PostMapping("scoreStyle/{id}")
    @SaCheckLogin
    public BaseResult<Integer> scoreStyle(@PathVariable Long id, @RequestBody ScoreStyle form) {

        PgAnswer answer = pgAnswerService.getById(id);

        if (ObjectUtil.isNull(answer)) {
            return BaseResult.code(GlobalCode.Item_Null);
        }
        ZwEssayQuestion correctAnswer = JSONUtil.toBean(answer.getCorrectResult().toString(), ZwEssayQuestion.class);

        if (ObjectUtil.isNotNull(form.style)) {
            correctAnswer.setScoreStyle(form.style);
        }
        answer.setCorrectResult(correctAnswer);

        pgAnswerService.updateById(answer);

        return BaseResult.success(form.style == 0 ? correctAnswer.getScoreLevel() : correctAnswer.getUserScore());
    }

    @Operation(summary = "【全部批改记录】根据状态统计数量")
    @GetMapping("countByStatus")
    @SaCheckLogin
    public BaseResult<AnswerNumStatistic> countByStatus() {

        // 获取用户的提交记录
        List<PgAnswer> answers = pgAnswerService.list(new LambdaQueryWrapper<PgAnswer>()
                .eq(PgAnswer::getUserId, StpUtil.getLoginIdAsLong())
                .and(i -> i.ne(PgAnswer::getDeleted, true)
                        .or()
                        .isNull(PgAnswer::getDeleted)
                )
        );

        AnswerNumStatistic statistic = new AnswerNumStatistic();

        if (answers.isEmpty()) {
            return BaseResult.success(statistic);
        }

        // 总数量
        statistic.setTotalNum(answers.size());
        // 已批改数量
        statistic.setCorrectedNum(CollUtil.count(answers, answer -> answer.getStatus().equals(CorrectStatusEnum.Corrected)));
        // 已审核数量
        statistic.setCheckedNum(CollUtil.count(answers, answer -> answer.getStatus().equals(CorrectStatusEnum.Checked)));

        return BaseResult.success(statistic);
    }

    @Operation(summary = "【作业批改记录】根据状态统计数量")
    @GetMapping("countByStatus/{homeworkId}")
    @SaCheckLogin
    public BaseResult<AnswerNumStatistic> countByStatus(@PathVariable Long homeworkId) {

        PgHomework homework = pgHomeworkService.getById(homeworkId);

        if (ObjectUtil.isNull(homework)) {
            return BaseResult.code(GlobalCode.Item_Null);
        }

        AnswerNumStatistic statistic = new AnswerNumStatistic();

        List<Long> studentIds = pgStudentService.list(new LambdaQueryWrapper<PgStudent>()
                        .eq(PgStudent::getClassId, homework.getClassId())
                )
                .stream()
                .map(PgStudent::getId)
                .toList();

        if (studentIds.isEmpty()) {
            return BaseResult.success(statistic);
        }

        List<PgAnswer> answers = pgAnswerService.list(new LambdaQueryWrapper<PgAnswer>()
                .select(PgAnswer::getId, PgAnswer::getStatus, PgAnswer::getStudentId)
                .eq(PgAnswer::getDeleted, false)
                .eq(PgAnswer::getHomeworkId, homeworkId)
                .in(PgAnswer::getStudentId, studentIds)
        );

        if (answers.isEmpty()) {
            return BaseResult.success(statistic);
        }

        answers = CollUtil.distinct(answers, PgAnswer::getStudentId, false);

        // 总数
        statistic.setTotalNum(studentIds.size());

        // 已批改数量
        statistic.setCorrectedNum(CollUtil.count(answers, answer -> answer.getStatus().equals(CorrectStatusEnum.Corrected)));

        // 已审核数量
        statistic.setCheckedNum(CollUtil.count(answers, answer -> answer.getStatus().equals(CorrectStatusEnum.Checked)));

        return BaseResult.success(statistic);
    }
}

