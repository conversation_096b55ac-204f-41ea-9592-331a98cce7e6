package com.pgb.common.core.utils;

import org.apache.commons.lang3.StringUtils;

/**
 * 62进制转换
 */
public class Id2CodeUtil {
    /**
     * 初始化 62 进制数据，索引位置代表字符的数值，比如 A代表10，z代表61等
     */
    private static final String chars = "abcdefghijklmnopqrstuvwxyz0123456789";
    private static final int scale = 36;

    /**
     * 将数字转为36进制
     *
     * @param num    Long 型数字
     * @return 36进制字符串
     */
    public static String encode(long num) {
        // 转换后的字符串长度，不足则左侧补0
        int length = 11;
        StringBuilder sb = new StringBuilder();
        int remainder = 0;
        while (num > scale - 1) {
            /*
             * 对 scale 进行求余，然后将余数追加至 sb 中，由于是从末位开始追加的，因此最后需要反转（reverse）字符串
             */
            remainder = Long.valueOf(num % scale).intValue();
            sb.append(chars.charAt(remainder));
            num = num / scale;
        }
        sb.append(chars.charAt(Long.valueOf(num).intValue()));
        String value = sb.reverse().toString();
        return StringUtils.leftPad(value, length, '0');
    }

    /**
     * 62进制字符串转为数字 * * @param str 编码后的62进制字符串 * @return 解码后的 10 进制字符串
     */
    public static long decode(String str) {
        /*
         *  将 0 开头的字符串进行替换
         */
        str = str.replace("^0*", "");
        long num = 0;
        int index = 0;
        for (int i = 0; i < str.length(); i++) {
            /*
             *  查找字符的索引位置
             */
            index = chars.indexOf(str.charAt(i));
            /*
             * 索引位置代表字符的数值
             */
            num += (long) (index * (Math.pow(scale, str.length() - i - 1)));
        }
        return num;
    }

    public static void main(String[] args) {
        //System.out.println("36进制：" + encode(1716378262614679553L, 11));
        System.out.println("10进制：" + decode("m4stxxlfamqb"));
        //String str = "bZf567itGB3";
        //str = str.replaceFirst("app", "");
        //str = str.replaceAll("^app", "");
        //System.out.println(str);
    }
}
