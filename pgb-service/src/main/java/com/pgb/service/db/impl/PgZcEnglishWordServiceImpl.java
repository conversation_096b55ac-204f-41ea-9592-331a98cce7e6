package com.pgb.service.db.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.pgb.service.db.PgZcEnglishWordService;
import com.pgb.service.domain.zc.word.english.PgZcEnglishWord;
import com.pgb.service.mapper.PgZcEnglishWordMapper;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【pg_english_words(英语单词表)】的数据库操作Service实现
* @createDate 2025-04-25 19:05:07
*/
@Service
public class PgZcEnglishWordServiceImpl extends ServiceImpl<PgZcEnglishWordMapper, PgZcEnglishWord>
    implements PgZcEnglishWordService {

}




