package com.pgb.xcx.controller.pay;

import cn.dev33.satoken.annotation.SaCheckLogin;
import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.pgb.common.core.global.BaseResult;
import com.pgb.common.core.global.GlobalCode;
import com.pgb.common.pay.enums.OrderTypeEnum;
import com.pgb.common.redis.RedisUtils;
import com.pgb.service.db.PgOrderService;
import com.pgb.service.db.PgVipChannelService;
import com.pgb.service.db.PgVipCodeService;
import com.pgb.service.domain.GlobalXcxConstants;
import com.pgb.service.domain.order.PgOrder;
import com.pgb.service.domain.vip.PgVipChannel;
import com.pgb.service.domain.vip.PgVipCode;
import com.pgb.service.enums.ChannelTypeEnum;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.time.Duration;
import java.util.Date;
import java.util.concurrent.TimeUnit;

@Tag(name = "用户端/订单/会员兑换")
@RestController("UserOrderExchangeController")
@Transactional(rollbackFor = Exception.class)
@RequestMapping("/user/order/exchange")
@RequiredArgsConstructor
@Slf4j
public class ExchangeController {

    private final PgVipCodeService pgVipCodeService;

    private final PgOrderService pgOrderService;

    private final PgVipChannelService pgVipChannelService;

    @Operation(summary = "兑换码")
    @GetMapping("code")
    @SaCheckLogin
    public BaseResult<Boolean> exchange(String code) {
        // 校验
        if (StrUtil.isBlank(code)) {
            return BaseResult.code(GlobalCode.Param_Wrong);
        }

        // 原子值
        long atomicValue = RedisUtils.getAtomicValue(GlobalXcxConstants.XCX_VIP_EXCHANGE_CODE_LIMIT + StpUtil.getLoginIdAsLong());

        if (atomicValue > 20) {
            return BaseResult.error(GlobalCode.Permission_Not, "兑换码兑换次数过多，请稍后再试");
        }

        // 加锁
        RLock lock = RedisUtils.getClient().getLock(GlobalXcxConstants.XCX_VIP_EXCHANGE_CODE_LIMIT + StpUtil.getLoginIdAsLong() + code);
        try {

            // 上锁，阻塞，超时时间为100秒，100秒后上锁失败，直接 false
            lock.tryLock(10, TimeUnit.SECONDS);

            // 判断 code 是否成功
            PgVipCode vipCode = pgVipCodeService.getOne(new LambdaQueryWrapper<PgVipCode>()
                    .eq(PgVipCode::getCode, code)
                    .last("LIMIT 1")
            );

            if (ObjectUtil.isNull(vipCode)) {
                // 缓存加1
                addExchangeNum();
                return BaseResult.error(GlobalCode.Item_Null, "兑换码不存在");
            }

            // 不是未使用
            if (vipCode.getStatus() != 0) {
                // 缓存加1
                addExchangeNum();
                return BaseResult.error(GlobalCode.Limit_Over.value, "兑换码已使用");
            }

            // 未使用
            vipCode.setStatus(1);
            vipCode.setExchangeTime(new Date());
            vipCode.setUserId(StpUtil.getLoginIdAsLong());

            // 兑换
            PgOrder order = null;
            if (vipCode.getChannelType().equals(ChannelTypeEnum.ACTIVITY)) {
                // 按照活动的订单来走
                if (ObjectUtil.isNotNull(vipCode.getOrderId())) {
                    // 拿订单
                    order = pgOrderService.getById(vipCode.getOrderId());

                    // 开通会员
                    pgOrderService.openActivityVip(StpUtil.getLoginIdAsLong(), vipCode, order);
                }
            }

            // 如果没走活动订单
            if (ObjectUtil.isNull(order)) {
                order = pgOrderService.openVip(StpUtil.getLoginIdAsLong(), vipCode.getVipType(), vipCode.getChannelType(), OrderTypeEnum.Redeem);
            }

            // 回调订单id
            vipCode.setOrderId(order.getId());

            // 更新
            pgVipCodeService.updateById(vipCode);

            // 更新用户信息
            if (ObjectUtil.isNotNull(vipCode.getChannelId())) {
                PgVipChannel channel = pgVipChannelService.getById(vipCode.getChannelId());

                if (ObjectUtil.isNotNull(channel)) {
                    channel.setUserId(StpUtil.getLoginIdAsLong());
                    pgVipChannelService.updateById(channel);
                }
            }

            // 重置原子值
            RedisUtils.deleteObject(GlobalXcxConstants.XCX_VIP_EXCHANGE_CODE_LIMIT + StpUtil.getLoginIdAsLong());

        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        } finally {
            // 无论如何都要确保锁最终被释放
            if (lock.isLocked() && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }

        return BaseResult.success(true);
    }

    private void addExchangeNum() {
        // 增加原子值
        RedisUtils.incrAtomicValue(
                GlobalXcxConstants.XCX_VIP_EXCHANGE_CODE_LIMIT + StpUtil.getLoginIdAsLong()
        );

        // 过期
        RedisUtils.expire(
                GlobalXcxConstants.XCX_VIP_EXCHANGE_CODE_LIMIT + StpUtil.getLoginIdAsLong(),
                Duration.ofMinutes(10)
        );
    }

}

