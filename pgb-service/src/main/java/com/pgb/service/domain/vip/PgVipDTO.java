package com.pgb.service.domain.vip;

import java.io.Serializable;

import java.util.Date;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.ToString;
import lombok.Builder;
import org.springframework.validation.annotation.Validated;
import jakarta.validation.constraints.NotNull;
import com.pgb.common.core.validate.UpdateGroup;

/**
*
* @TableName pg_vip
*/
@Schema(description = " DTO")
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode
@ToString
@Validated
@Builder
public class PgVipDTO implements Serializable {

    @Schema(title = "会员充值表", type = "string")
    @NotNull(message="[会员充值表]不能为空", groups = {UpdateGroup.class})
    private Long id;

    @Schema(title = "用户id")
    private Long userId;

    @Schema(title = "会员类型，0：年卡，1：月卡，2：周卡")
    private Integer vipType;

    @Schema(title = "购买类型")
    private Integer buyType;

    @Schema(title = "购买天数")
    private Integer duration;

    @Schema(title = "订单id")
    private Long orderId;

    @Schema(title = "生效时间")
    private Date effectTime;

    @Schema(title = "过期时间")
    private Date expireTime;

    @Schema(title = "创建时间")
    private Date createTime;

    @Schema(title = "会员开通方式，0：自行开通，1：后台开通")
    private Integer openType;

}
