package com.pgb.xcx.controller.classes;

import cn.dev33.satoken.annotation.SaCheckLogin;
import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.pgb.common.core.global.BaseResult;
import com.pgb.common.core.global.GlobalCode;
import com.pgb.service.db.*;
import com.pgb.service.domain.answer.PgAnswer;
import com.pgb.service.domain.classes.PgClasses;
import com.pgb.service.domain.classes.PgClassesDTO;
import com.pgb.service.domain.classes.PgClassesVO;
import com.pgb.service.domain.homework.PgHomework;
import com.pgb.service.domain.query.ClassQuery;
import com.pgb.service.domain.student.PgStudent;
import com.pgb.service.domain.user.PgUsers;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * Created by 2024/12/10 16:35
 */
@Tag(name = "用户端/班级/班级管理")
@RestController("UserClassManageController")
@Transactional(rollbackFor = Exception.class)
@RequestMapping("/user/class/manage")
@RequiredArgsConstructor
@Slf4j
public class ClassController {

    private final PgClassesService pgClassesService;

    private final PgStudentService pgStudentService;

    private final PgHomeworkService pgHomeworkService;

    private final PgAnswerService pgAnswerService;

    private final PgUsersService pgUsersService;

    @Operation(summary = "创建班级")
    @PostMapping("create")
    @SaCheckLogin
    public BaseResult<PgClasses> create(@RequestBody PgClassesDTO classesDTO) {
        PgClasses classes = pgClassesService.createClass(
                StpUtil.getLoginIdAsLong(),
                classesDTO.getName(),
                classesDTO.getClassNum(),
                classesDTO.getGrade()
        );

        return BaseResult.success(classes);
    }

    @Operation(summary = "查看班级信息")
    @GetMapping("{id}")
    @SaCheckLogin
    public BaseResult<PgClassesVO> getInfo(@PathVariable Long id) {

        PgClasses classes = pgClassesService.getById(id);

        if (ObjectUtil.isNull(classes)) {
            return BaseResult.code(GlobalCode.Item_Null);
        }

        PgClassesVO pgClassesVO = BeanUtil.copyProperties(classes, PgClassesVO.class);

        // 获取班级人数
        long studentNum = pgStudentService.count(new LambdaQueryWrapper<PgStudent>()
                .eq(PgStudent::getClassId, id));

        pgClassesVO.setStudentNum((int) studentNum);

        return BaseResult.success(pgClassesVO);
    }

    @Operation(summary = "删除班级")
    @DeleteMapping("{id}")
    @SaCheckLogin
    public BaseResult<Boolean> delete(@PathVariable Long id) {

        PgClasses pgClasses = pgClassesService.getById(id);

        // 只有创建者才能删除
        if (!pgClasses.getUserId().equals(StpUtil.getLoginIdAsLong())) {
            return BaseResult.error("只有创建者才能删除班级");
        }

        if (ObjectUtil.isNull(pgClasses)) {
            return BaseResult.code(GlobalCode.Item_Null);
        }

        // 删除班级-学生信息
        pgStudentService.remove(new LambdaQueryWrapper<PgStudent>()
                .eq(PgStudent::getClassId, id)
        );

        // 获取班级-作业信息
        List<Long> homeworkIds = pgHomeworkService.list(new LambdaQueryWrapper<PgHomework>()
                        .eq(PgHomework::getClassId, id))
                .stream()
                .map(PgHomework::getId)
                .toList();

        if (!homeworkIds.isEmpty()) {
            // 删除作业-提交记录信息 -- 假删除
            List<PgAnswer> answers = pgAnswerService.list(new LambdaQueryWrapper<PgAnswer>()
                    .select(PgAnswer::getId, PgAnswer::getDeleted, PgAnswer::getHomeworkId)
                    .in(PgAnswer::getHomeworkId, homeworkIds));
            answers.forEach(answer -> answer.setDeleted(true));
            pgAnswerService.updateBatchById(answers);

            // 删除班级-作业信息
            pgHomeworkService.removeBatchByIds(homeworkIds);
        }

        // 删除班级
        pgClassesService.removeById(id);

        return BaseResult.success(true);
    }

    @Operation(summary = "修改班级信息")
    @PutMapping("{id}")
    @SaCheckLogin
    public BaseResult<Boolean> update(@PathVariable Long id, @RequestBody PgClassesDTO pgClassesDTO) {

        PgClasses pgClasses = pgClassesService.getById(id);

        if (ObjectUtil.isNull(pgClasses)) {
            return BaseResult.code(GlobalCode.Item_Null);
        }

        // 只有创建者才能删除
        if (!pgClasses.getUserId().equals(StpUtil.getLoginIdAsLong())) {
            return BaseResult.error("只有创建者才能编辑");
        }

        PgClasses pgClass = BeanUtil.copyProperties(pgClassesDTO, PgClasses.class);

        pgClassesService.updateById(pgClass);

        return BaseResult.success(true);
    }

    @Operation(summary = "查看班级列表")
    @PostMapping("classes")
    @SaCheckLogin
    public BaseResult<IPage<PgClassesVO>> list(@RequestBody @Validated ClassQuery query) {

        // 获取用当前户创建的班级
        LambdaQueryWrapper<PgClasses> queryWrapper = new LambdaQueryWrapper<PgClasses>()
                // 根据班级名称筛选
                .like(StrUtil.isNotEmpty(query.getName()), PgClasses::getName, query.getName())
                .eq(PgClasses::getUserId, StpUtil.getLoginIdAsLong());

        IPage<PgClassesVO> page = pgClassesService.page(query.toMpPageSortByCreateTime(), queryWrapper
        ).convert(pgClasses -> {

            PgClassesVO classesVO = BeanUtil.copyProperties(pgClasses, PgClassesVO.class);

            // 获取班级人数
            long studentNum = pgStudentService.count(new LambdaQueryWrapper<PgStudent>()
                    .eq(PgStudent::getClassId, pgClasses.getId()));

            classesVO.setStudentNum((int) studentNum);

            // 作业数量
            long homeworkNum = pgHomeworkService.count(new LambdaQueryWrapper<PgHomework>()
                    .eq(PgHomework::getClassId, pgClasses.getId()));
            classesVO.setHomeworkNum((int) homeworkNum);

            // 判断当前登录用户是否是班级的创建者
            classesVO.setIsCreator(pgClasses.getUserId().equals(StpUtil.getLoginIdAsLong()));

            return classesVO;
        });

        return BaseResult.success(page);
    }

    @Operation(summary = "获取班级列表,只能看到自己创建的", description = "发布作业时使用")
    @PostMapping("list")
    @SaCheckLogin
    public BaseResult<List<PgClassesVO>> list() {

        // 获取当前用户创建的班级
        List<PgClassesVO> classes = pgClassesService.list(new LambdaQueryWrapper<PgClasses>()
                        .select(PgClasses::getId, PgClasses::getName, PgClasses::getGrade, PgClasses::getClassNum)
                        .eq(PgClasses::getUserId, StpUtil.getLoginIdAsLong())
                        .orderByDesc(PgClasses::getCreateTime)
                ).stream()
                .map(pgClasses -> {
                            PgClassesVO classesVO = BeanUtil.copyProperties(pgClasses, PgClassesVO.class);

                            // 获取班级人数
                            long studentNum = pgStudentService.count(new LambdaQueryWrapper<PgStudent>()
                                    .eq(PgStudent::getClassId, pgClasses.getId()));

                            classesVO.setStudentNum((int) studentNum);

                            return classesVO;
                        }
                )
                .toList();

        return BaseResult.success(classes);
    }

    @Operation(summary = "查看当前用户是否创建过班级")
    @GetMapping("isCreator")
    @SaCheckLogin
    public BaseResult<Boolean> isCreator() {

        long count = pgClassesService.count(new LambdaQueryWrapper<PgClasses>()
                .eq(PgClasses::getUserId, StpUtil.getLoginIdAsLong()));

        return BaseResult.success(count > 0);
    }

    @Operation(summary = "根据classId查老师信息")
    @GetMapping("getTeacherInfo/{classId}")
    public BaseResult<PgUsers> getTeacherInfo(@PathVariable Long classId) {

        PgClasses pgClasses = pgClassesService.getById(classId);

        if (ObjectUtil.isNull(pgClasses)) {
            return BaseResult.error(GlobalCode.Item_Null, "班级不存在");
        }

        PgUsers users = pgUsersService.getById(pgClasses.getUserId());

        return BaseResult.success(users);
    }

}
