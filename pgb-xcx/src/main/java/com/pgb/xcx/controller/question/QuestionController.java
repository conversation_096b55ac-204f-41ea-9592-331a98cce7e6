package com.pgb.xcx.controller.question;

import cn.dev33.satoken.annotation.SaCheckLogin;
import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.URLUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.pgb.common.core.global.BaseResult;
import com.pgb.common.core.global.GlobalCode;
import com.pgb.common.core.global.GlobalConstants;
import com.pgb.common.core.utils.EnvUtils;
import com.pgb.common.ocr.OCRService;
import com.pgb.common.ocr.domain.OCRResult;
import com.pgb.common.ocr.domain.OCRWordResult;
import com.pgb.common.oss.service.OssService;
import com.pgb.service.custom.CorrectService;
import com.pgb.service.db.PgQuestionService;
import com.pgb.service.domain.common.image.FilePaperImg;
import com.pgb.service.domain.question.UnitQuestionForm;
import com.pgb.xcx.common.UserBehaviorUtil;
import com.pgb.service.domain.question.PgQuestionDTO;
import com.pgb.service.domain.query.QuestionQuery;
import com.pgb.service.domain.question.PgQuestion;
import com.pgb.service.domain.question.PgQuestionVO;
import com.pgb.service.enums.GradeEnum;
import com.pgb.service.enums.SubjectEnum;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.time.Duration;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * Created by 2024/7/17 10:29
 */

@Tag(name = "用户端/题目/信息")
@RestController("UserQuestionController")
@Transactional(rollbackFor = Exception.class)
@RequestMapping("/user/question/info")
@RequiredArgsConstructor
@Slf4j
public class QuestionController {

    private final PgQuestionService pgQuestionService;

    private final OCRService ocrService;

    private final OssService ossService;

    private final CorrectService correctService;


    @Operation(summary = "根据指定年级学期单元，获取单元作文")
    @PostMapping("unitQuestion")
    @SaCheckLogin
    public BaseResult<List<PgQuestionVO>> unitQuestion(@RequestBody UnitQuestionForm form) {

        List<PgQuestionVO> list = pgQuestionService.list(new LambdaQueryWrapper<PgQuestion>()
                        // 教材作文
                        .eq(PgQuestion::getIsOfficial, true)
                        .eq(ObjectUtil.isNotNull(form.getGrade()), PgQuestion::getGrade, form.getGrade())
                        .eq(StrUtil.isNotBlank(form.getSemester()), PgQuestion::getSemester, form.getSemester())
                        .eq(StrUtil.isNotBlank(form.getUnit()), PgQuestion::getUnit, form.getUnit())
                        .orderByAsc(PgQuestion::getSort)
                )
                .stream()
                .map(question -> BeanUtil.copyProperties(question, PgQuestionVO.class))
                .toList();

        return BaseResult.success(list);
    }

    @Operation(summary = "新增题目")
    @PostMapping("save")
    @SaCheckLogin
    public BaseResult<PgQuestionDTO> save(@RequestBody PgQuestionDTO pgQuestionDTO) {

        // 保存题目基本信息
        PgQuestion pgQuestion = BeanUtil.copyProperties(pgQuestionDTO, PgQuestion.class,"name");

        // 名称保留前十个字
        pgQuestion.setName(StrUtil.sub(pgQuestion.getName(), 0, 20));

        // 用户id
        pgQuestion.setUserId(StpUtil.getLoginIdAsLong());

        // 默认为自定义作文题目
        pgQuestion.setIsOfficial(false);

        pgQuestion.setCreateTime(new Date());

        pgQuestionService.save(pgQuestion);

        // 设置id
        pgQuestionDTO.setId(pgQuestion.getId());

        return BaseResult.success(pgQuestionDTO);
    }

    @Operation(summary = "编辑题目")
    @PutMapping("update/{id}")
    @SaCheckLogin
    public BaseResult<Long> update(@RequestBody PgQuestionDTO pgQuestionDTO, @PathVariable Long id) {
        // 获取题目
        PgQuestion question = pgQuestionService.getById(id);

        if (ObjectUtil.isNull(question)) {
            return BaseResult.code(GlobalCode.Item_Null);
        }

        // 新增到自定义题目
        PgQuestion pgQuestion = new PgQuestion();
        // 若是官方例题
        if (question.getIsOfficial()) {

            BeanUtil.copyProperties(pgQuestionDTO, pgQuestion, "id", "isOfficial", "userId", "sort");
            pgQuestion.setIsOfficial(false);
            pgQuestion.setUserId(StpUtil.getLoginIdAsLong());
            pgQuestion.setCreateTime(new Date());
            pgQuestionService.save(pgQuestion);
        } else {
            // 判断是否为当前用户
            if (StpUtil.getLoginIdAsLong() != question.getUserId()) {
                return BaseResult.code(GlobalCode.Permission_Not);
            }

            // 保存题目基本信息
            pgQuestion = BeanUtil.copyProperties(pgQuestionDTO, PgQuestion.class);

            pgQuestionService.updateById(pgQuestion);
        }

        return BaseResult.success(pgQuestion.getId());
    }

    @Operation(summary = "删除题目")
    @DeleteMapping("delete/{id}")
    @SaCheckLogin
    public BaseResult<Boolean> delete(@PathVariable Long id) {

        // 获取题目
        PgQuestion pgQuestion = pgQuestionService.getById(id);

        if (ObjectUtil.isNull(pgQuestion)) {
            return BaseResult.code(GlobalCode.Item_Null);
        }

        // 教材题目不允许删除
        if (!pgQuestion.getIsOfficial()) {

            pgQuestionService.removeById(id);
            return BaseResult.success(true);
        } else {
            return BaseResult.code(GlobalCode.Permission_Not);
        }
    }

    @Operation(summary = "获取题目")
    @GetMapping("get/{id}")
    @SaCheckLogin
    public BaseResult<PgQuestionVO> get(@PathVariable Long id) {

        PgQuestion pgQuestion = pgQuestionService.getById(id);

        PgQuestionVO pgQuestionVO = BeanUtil.copyProperties(pgQuestion, PgQuestionVO.class);

        return BaseResult.success(pgQuestionVO);
    }

    @Operation(summary = "获取自定义作文题目列表")
    @PostMapping("list")
    @SaCheckLogin
    public BaseResult<IPage<PgQuestionVO>> list(@RequestBody @Validated QuestionQuery query) {

        LambdaQueryWrapper<PgQuestion> wrapper = new LambdaQueryWrapper<PgQuestion>()
                // 当前用户
                .eq(PgQuestion::getUserId, StpUtil.getLoginIdAsLong())
                // 自定义作文题目
                .eq(PgQuestion::getIsOfficial, false)
                // 根据年级筛选
                .eq(ObjectUtil.isNotNull(query.getGrade()), PgQuestion::getGrade, query.getGrade())
                // 根据科目筛选
                .eq(ObjectUtil.isNotNull(query.getSubject()), PgQuestion::getSubject, query.getSubject())
                // 根据关键词筛选
                .and(StrUtil.isNotBlank(query.getKeyword()),
                        i -> i.like(ObjectUtil.isNotNull(query.getKeyword()), PgQuestion::getName, query.getKeyword())
                                .or()
                                .like(ObjectUtil.isNotNull(query.getKeyword()), PgQuestion::getWritingRequest, query.getKeyword())
                );

        IPage<PgQuestionVO> page = pgQuestionService.page(query.toMpPageSortByCreateTime(), wrapper)
                .convert(pgQuestion -> {
                            PgQuestionVO questionVO = BeanUtil.copyProperties(pgQuestion, PgQuestionVO.class);

                            // 已批改次数
                            //long correctNum = pgAnswerService.count(new LambdaQueryWrapper<PgAnswer>()
                            //        .eq(PgAnswer::getQuestionId, pgQuestion.getId())
                            //        .ne(PgAnswer::getStatus, CorrectStatusEnum.Uploaded));
                            //
                            //questionVO.setCorrectNum((int) correctNum);
                            return questionVO;
                        }
                );

        return BaseResult.success(page);
    }

    @Operation(summary = "获取单元习作列表")
    @PostMapping("official/list")
    public BaseResult<IPage<PgQuestionVO>> official(@RequestBody @Validated QuestionQuery query) {

        LambdaQueryWrapper<PgQuestion> wrapper = new LambdaQueryWrapper<PgQuestion>()
                // 教材作文题目
                .eq(PgQuestion::getIsOfficial, true)
                // 根据科目筛选
                .eq(ObjectUtil.isNotNull(query.getSubject()), PgQuestion::getSubject, query.getSubject())
                // 根据年级筛选
                .eq(ObjectUtil.isNotNull(query.getGrade()), PgQuestion::getGrade, query.getGrade())
                // 上下册
                .eq(StrUtil.isNotBlank(query.getSemester()), PgQuestion::getSemester, query.getSemester())
                // 关键词
                .and(StrUtil.isNotBlank(query.getKeyword()),
                        i -> i.like(ObjectUtil.isNotNull(query.getKeyword()), PgQuestion::getName, query.getKeyword())
                                .or()
                                .like(ObjectUtil.isNotNull(query.getKeyword()), PgQuestion::getWritingRequest, query.getKeyword())
                );

        IPage<PgQuestionVO> page = pgQuestionService.page(query.toMpPageSortByCreateTime(), wrapper)
                .convert(pgQuestion -> BeanUtil.copyProperties(pgQuestion, PgQuestionVO.class));

        return BaseResult.success(page);
    }

    public record GradeQueryDTO(GradeEnum grade, SubjectEnum subject) {
    }

    //    @Deprecated
    @Operation(summary = "根据【年级】获取教材作文题目列表")
    @PostMapping("officialByGrade/list")
    public BaseResult<List<PgQuestionVO>> officialByGrade(@RequestBody @Validated GradeQueryDTO dto) {

        List<PgQuestionVO> list = pgQuestionService.list(new LambdaQueryWrapper<PgQuestion>()
                        // 教材作文题目
                        .eq(PgQuestion::getIsOfficial, true)
                        // 根据科目筛选
                        .eq(ObjectUtil.isNotNull(dto.subject()), PgQuestion::getSubject, dto.subject())
                        // 根据年级筛选
                        .eq(ObjectUtil.isNotNull(dto.grade()), PgQuestion::getGrade, dto.grade())
                        // 根据sort排序
                        .orderByAsc(PgQuestion::getSort)
                )
                .stream()
                .map(pgQuestion -> BeanUtil.copyProperties(pgQuestion, PgQuestionVO.class)).toList();

        return BaseResult.success(list);
    }


    public record GradeQuesCountDTO(GradeEnum grade, long count, String gradeName) {
    }

    @Operation(summary = "获取年级列表（语文）")
    @PostMapping("grades")
    public BaseResult<List<GradeQuesCountDTO>> grade() {

        // 获取所有年级
        List<GradeEnum> grades = Arrays.stream(GradeEnum.values()).toList();

        List<GradeQuesCountDTO> result = grades.stream()
                .map(grade -> {
                    // 查询符合条件的作文题目数量
                    long questionCount = pgQuestionService.count(new LambdaQueryWrapper<PgQuestion>()
                            .eq(PgQuestion::getIsOfficial, true)
                            .eq(PgQuestion::getGrade, grade));
                    // 如果有作文题目，则创建并返回DTO对象；否则返回null
                    return questionCount > 0 ? new GradeQuesCountDTO(grade, questionCount, grade.desc) : null;
                })
                .filter(Objects::nonNull) // 过滤掉null值
                .collect(Collectors.toList());

        return BaseResult.success(result);
    }

//    @Operation(summary = "上传图片识别文字")
//    @PostMapping(value = "ocr", headers = "content-type=multipart/form-data")
//    public BaseResult<String> ocr(@RequestParam("file") MultipartFile file) {
//
//        OCRResult result = ocrService.handWriting(file);
//
//        String answers = CollUtil.join(result.getWords_result().stream().map(OCRWordResult::getWords).toList(), "");
//
//        log.info("识别结果：{}", answers);
//
//        return BaseResult.success(answers);
//    }

    @Operation(summary = "上传图片识别文字")
    @GetMapping("ocr")
    @SaCheckLogin
    public BaseResult<String> ocr(@RequestParam String url) {

        // TODO 频率 一个人用这个接口100次 超过发报警 加账户id

        long userId = StpUtil.getLoginIdAsLong();
//        long userId = 1810610447493398529L;

        String redisKey = GlobalConstants.TENANT_OCR_LIMIT_REDIS_KEY + userId;

        // 判断是否超过限制
        if (isOcrLimited(redisKey)) {

            sendMessage("用户ocr识别次数超过50次！需重视！", userId);

            log.warn("当前用户：{}，ocr识别次数超过限制！", userId);
            return BaseResult.code(GlobalCode.Limit_Over);
        } else {
            // 兼容各种图片
            FilePaperImg img = new FilePaperImg();
            img.setImgUrl(url);
            correctService.processImgV2(img);

            OCRResult result = ocrService.handWriting(img.getImgUrl());

            String answers = CollUtil.join(result.getWords_result().stream().map(OCRWordResult::getWords).toList(), "");

            if (EnvUtils.isDev()) {
                log.info("识别结果：{}", answers);
            }

            // 删除
            ossService.delete(URLUtil.getPath(url));
            ossService.delete(URLUtil.getPath(img.getImgUrl()));

            if (EnvUtils.isDev()) {
                log.info("删除图片 {}", URLUtil.getPath(url));
            }

            return BaseResult.success(answers);
        }
    }

    // 判断是否超出限制
    private boolean isOcrLimited(String redisKey) {

        // 判断是否超出限制
        if (UserBehaviorUtil.isLimitOver(redisKey, 100)) {
            return true;
        } else {
            UserBehaviorUtil.addLimitOverNum(redisKey, Duration.ofDays(1));
            return false;
        }
    }

    /**
     * 企微 发异常报警通知
     */
    public void sendMessage(String action, Long userId) {

        String msg = EnvUtils.isDev() ? "小程序测试" : "小程序线上";
        // 发送企微机器人通知 技术反馈群
        try {
            String result = HttpRequest.post(
                            "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=295eae4d-ef26-4382-930e-0090c30ca9c7"
                    ).body(JSONUtil.createObj()
                            .putOnce("msgtype", "markdown")
                            .putOnce("markdown", JSONUtil.createObj()
                                    .putOnce("content",
                                            StrUtil.format(
                                                    """
                                                            小程序OCR频率报警\n
                                                            环境：{}\n
                                                            时间：{}\n
                                                            用户：{}\n
                                                            操作：{}
                                                            """, msg, DateUtil.format(new Date(), "yyyy-MM-dd HH:mm:ss"), userId, action
                                            )
                                    )
                            ).toString()
                    ).timeout(3 * 60 * 1000)
                    .execute()
                    .body();

            log.info("企微发送通知成功 - {}", result);
        } catch (Exception e) {
            log.error("企微发送通知失败 - {}", e.getMessage());
        }
    }

}
