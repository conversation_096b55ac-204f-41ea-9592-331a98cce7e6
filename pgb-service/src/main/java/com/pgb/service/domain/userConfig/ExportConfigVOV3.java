package com.pgb.service.domain.userConfig;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Schema(title = "前端导出配置内容")
@Data
public class ExportConfigVOV3 {
    @Schema(title = "导出原文对比样式", description = "0：普通导出，1：分栏导出")
    private Integer polishCompareStyle = 0;

    @Schema(title = "导出分数样式", description = "0:评级,1:分数")
    private Integer scoreStyle = 0;

    @Schema(title = "批量导出作文格式", description = "是否导出至一个文档中，0:压缩包（一篇一个），1：单个word")
    private Integer exportFileStyle = 0;

    @Schema(title = "是否导出作文报告标题")
    private Boolean reportTitle = true;

    @Schema(title = "是否导出分数")
    private Boolean score = true;

    @Schema(title = "排序的导出项")
    private List<ExportConfigItem> exportItemList;
}
