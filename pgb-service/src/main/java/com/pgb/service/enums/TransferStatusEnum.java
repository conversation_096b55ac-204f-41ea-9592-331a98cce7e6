package com.pgb.service.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;

/**
 * <AUTHOR>
 * Created by 2024/7/5 20:43
 */
@Schema(description = "转账 状态")
@AllArgsConstructor
public enum TransferStatusEnum {

    @Schema(title = "已受理")
    ACCEPTED(0, "已受理"),

    @Schema(title = "转账中")
    PROCESSING(1, "转账中"),

    @Schema(title = "已完成")
    FINISHED(2, "已完成"),

    @Schema(title = "已关闭")
    CLOSED(3, "已关闭");


    /**
     * 枚举值对应的整数值
     */
    @EnumValue
    public final int value;

    /**
     * 文字说明
     */
    public final String desc;
}
