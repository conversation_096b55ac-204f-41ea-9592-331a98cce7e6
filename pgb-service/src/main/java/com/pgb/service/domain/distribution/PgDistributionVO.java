package com.pgb.service.domain.distribution;

import java.io.Serializable;

import java.util.Date;

import com.pgb.service.domain.user.PgUsers;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.ToString;
import lombok.Builder;

/**
* 分销关系表
* @TableName pg_distribution
*/
@Schema(description = "分销关系表 VO")
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode
@ToString
@Builder
public class PgDistributionVO implements Serializable {

    @Schema(title = "分销 id", type = "string")
    private Long id;

    @Schema(title = "邀请人id")
    private Long shareUserId;

    @Schema(title = "被邀请人 id")
    private Long userId;

    @Schema(title = "邀请是否成功")
    private Boolean isSuccess;

    @Schema(title = "邀请失败的原因")
    private String reason;

    @Schema(title = "创建时间")
    private Date createTime;

    @Schema(title = "被邀请人昵称")
    private String nickName;

    @Schema(title = "被邀请人头像")
    private String avatarUrl;

    @Schema(title = "被邀请人信息")
    private PgUsers userInfo;
}
