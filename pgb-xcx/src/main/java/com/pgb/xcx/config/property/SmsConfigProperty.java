package com.pgb.xcx.config.property;

import com.pgb.common.sms.SmsTemplateConfig;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.NestedConfigurationProperty;

@Data
@ConfigurationProperties(prefix = "sms.open-config")
public class SmsConfigProperty {
    @NestedConfigurationProperty
    private SmsTemplateConfig verifyCode;

    @NestedConfigurationProperty
    private SmsTemplateConfig exchangeCode;

    @NestedConfigurationProperty
    private SmsTemplateConfig vipExpired;
}
