package com.pgb.service.domain.answer.polish;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * Created by 2025/3/19 17:03
 */
@Schema(title = "润色要求")
@Data
public class PolishRequire {

    @Schema(title = "作文标题")
    private String title;

    @Schema(title = "重新润色要求（选填）")
    private String rePolishRequire;

    @Schema(title = "润色幅度", description = "1：低，2：中，3：高")
    private Integer polishRange;

    @Schema(title = "字数要求")
    private Integer wordNum;

}
