package com.pgb.service.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;

/**
 * <AUTHOR>
 * Created by 2024/8/12 12:03
 */
@Schema(description = "年级 阶段")
@AllArgsConstructor
public enum VolumnEnum {

    @Schema(title = "小班")
    SMALL(1, "小班"),

    @Schema(title = "中班")
    MIDDLE(2, "中班"),

    @Schema(title = "大班")
    BIG(3, "大班"),

    @Schema(title = "一年级上册")
    FIRST_UP(4, "一年级上册"),

    @Schema(title = "一年级下册")
    FIRST_DOWN(5, "一年级下册"),

    @Schema(title = "二年级上册")
    SECOND_UP(6, "二年级上册"),

    @Schema(title = "二年级下册")
    SECOND_DOWN(7, "二年级下册"),

    @Schema(title = "三年级上册")
    THIRD_UP(8, "三年级上册"),

    @Schema(title = "三年级下册")
    THIRD_DOWN(9, "三年级下册"),

    @Schema(title = "四年级上册")
    FOURTH_UP(10, "四年级上册"),

    @Schema(title = "四年级下册")
    FOURTH_DOWN(11, "四年级下册"),

    @Schema(title = "五年级上册")
    FIFTH_UP(12, "五年级上册"),

    @Schema(title = "五年级下册")
    FIFTH_DOWN(13, "五年级下册"),

    @Schema(title = "六年级上册")
    SIXTH_UP(14, "六年级上册"),

    @Schema(title = "六年级下册")
    SIXTH_DOWN(15, "六年级下册"),

    @Schema(title = "七年级上册")
    SEVENTH_UP(16, "七年级上册"),

    @Schema(title = "七年级下册")
    SEVENTH_DOWN(17, "七年级下册"),

    @Schema(title = "八年级上册")
    EIGHTH_UP(18, "八年级上册"),

    @Schema(title = "八年级下册")
    EIGHTH_DOWN(19, "八年级下册"),

    @Schema(title = "九年级上册")
    NINTH_UP(20, "九年级上册"),

    @Schema(title = "九年级下册")
    NINTH_DOWN(21, "九年级下册"),

    @Schema(title = "高一上")
    HIGH_FIRST_UP(22, "高一上"),

    @Schema(title = "高一下")
    HIGH_FIRST_DOWN(23, "高一下"),

    @Schema(title = "高二上")
    HIGH_SECOND_UP(24, "高二上"),

    @Schema(title = "高二下")
    HIGH_SECOND_DOWN(25, "高二下"),

    @Schema(title = "高三上")
    HIGH_THIRD_UP(26, "高三上"),

    @Schema(title = "高三下")
    HIGH_THIRD_DOWN(27, "高三下"),

    @Schema(title = "基础模块上册")
    BASIC_UP(28, "基础模块上册"),

    @Schema(title = "基础模块下册")
    BASIC_DOWN(29, "基础模块下册"),

    @Schema(title = "拓展模块上册")
    EXTENSION_UP(30, "拓展模块上册"),

    @Schema(title = "拓展模块下册")
    EXTENSION_DOWN(31, "拓展模块下册"),

    @Schema(title = "必修课")
    REQUIRED(32, "必修课"),

    @Schema(title = "选修课")
    OPTIONAL(33, "选修课"),

    @Schema(title = "其他")
    OTHER(34, "其他");


    /**
     * 枚举值对应的整数值
     */
    @EnumValue
    public final int value;

    /**
     * 文字说明
     */
    public final String desc;
}
