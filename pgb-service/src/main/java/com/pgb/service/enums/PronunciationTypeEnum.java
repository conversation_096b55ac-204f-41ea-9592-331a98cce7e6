package com.pgb.service.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;

/**
 * <AUTHOR>
 * Created by 2025/7/29 19:04
 */
@Schema(title = "单词发音类型枚举")
@AllArgsConstructor
public enum PronunciationTypeEnum {

    UK(1, "英音"),

    US(2, "美音");

    /**
     * 枚举值对应的整数值
     */
    @EnumValue
    public final int value;

    /**
     * 文字说明
     */
    public final String desc;

}
