package com.pgb.service.domain.answer.polish;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 作文润色记录表
 * @TableName pg_answer_polish
 */
@TableName(value ="pg_answer_polish")
@Data
public class PgAnswerPolish implements Serializable {
    /**
     * 润色记录id
     */
    private Long id;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 关联作答id
     */
    private Long answerId;

    /**
     * 润色内容
     */
    private String content;

    /**
     * 润色要求
     */
    private Object require;

    /**
     * 版本排序
     */
    private Integer sort;

    /**
     * 创建时间
     */
    private Date createTime;

}
