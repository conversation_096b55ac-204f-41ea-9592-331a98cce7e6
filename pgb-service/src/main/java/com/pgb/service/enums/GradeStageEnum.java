package com.pgb.service.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;

/**
 * <AUTHOR>
 * Created by 2024/8/12 12:03
 */
@Schema(description = "年级 阶段")
@AllArgsConstructor
public enum GradeStageEnum {
    @Schema(title = "幼儿园")
    KINDERGARTEN(1, "幼儿园"),

    @Schema(title = "小学")
    PRIMARY(2, "小学"),

    @Schema(title = "初中")
    MIDDLE(3, "初中"),

    @Schema(title = "高中")
    HIGH(4, "高中"),

    @Schema(title = "大学")
    UNIVERSITY(5, "大学"),

    @Schema(title = "其他")
    OTHER(6, "其他");

    /**
     * 枚举值对应的整数值
     */
    @EnumValue
    public final int value;

    /**
     * 文字说明
     */
    public final String desc;
}
