# 数据库
spring:
  datasource:
    driver-class-name: org.postgresql.Driver
#    url: **************************************************
    url: **********************************************
    username: postgres
    password: Snros2022..

# 批改模型接口
correct:
  # 批改邦内网，无需更换 vpc
  base-url: https://model-ocnnmkezye.cn-beijing-vpc.fcapp.run
  api-key: pgb-api-correct-v2
  # 批改 url
  render-url: https://fabric-hjhjjmlqyb.cn-beijing-vpc.fcapp.run
  # 用户端保存，使用高并发
  render-url-user: http://zw-render-url.pigaibang.com

# textbook服务配置
textbook:
  base-url: http://81.70.132.52:8080/api
