package com.pgb.service.domain.agent.collect;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import lombok.Data;

/**
 *
 * @TableName pg_agent_collect
 */
@TableName(value ="pg_agent_collect")
@Data
public class PgAgentCollect {
    /**
     * 智能体收藏
     */
    @TableId
    private Long id;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 智能体id
     */
    private Long agentId;

    /**
     * 收藏时间
     */
    private Date createTime;

    /**
     * 排序
     */
    private Integer sort;
}
