package com.pgb.service.db.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.pgb.service.domain.zc.answer.batch.PgZcAnswerBatch;
import com.pgb.service.db.PgZcAnswerBatchService;
import com.pgb.service.mapper.PgZcAnswerBatchMapper;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【pg_zc_answer_batch(默写批量上传表)】的数据库操作Service实现
* @createDate 2025-04-21 17:07:01
*/
@Service
public class PgZcAnswerBatchServiceImpl extends ServiceImpl<PgZcAnswerBatchMapper, PgZcAnswerBatch>
    implements PgZcAnswerBatchService{

}




