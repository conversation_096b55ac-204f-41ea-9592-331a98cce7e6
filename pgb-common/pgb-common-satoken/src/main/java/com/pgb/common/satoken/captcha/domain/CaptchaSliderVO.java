package com.pgb.common.satoken.captcha.domain;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.awt.*;
import java.util.Date;

@NoArgsConstructor // 生成无参构造方法
@AllArgsConstructor // 生成有参构造方法
@Schema(title = "滑动图片 验证码实体 VO")
@Builder
@EqualsAndHashCode
@Data
public class CaptchaSliderVO {

    @Schema(title = "唯一标识", description = "验证码的唯一标识。通常为uuid")
    public String verifyKey;

    @Schema(title = "创建时间")
    public Date createTime;

    @Schema(title = "是否已验证")
    public Boolean isVerify;

    @Schema(title = "验证时间")
    public Date verifyTime;

    @Schema(title = "验证码图片", description = "base64位的字符串")
    private String img;

    @Schema(title = "【滑动验证码】滑块坐标信息")
    private Point point;

    @Schema(title = "【滑动验证码】滑块图片", description = "base64位")
    private String sliderImg;
}
