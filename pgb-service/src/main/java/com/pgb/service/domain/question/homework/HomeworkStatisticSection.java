package com.pgb.service.domain.question.homework;

import cn.hutool.core.util.ObjectUtil;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class HomeworkStatisticSection {
    private String title;

    private List<HomeworkStatisticSectionItem> itemList;

    public void addItem(HomeworkStatisticSectionItem item) {
        if (ObjectUtil.isNull(this.itemList)) {
            this.itemList = new ArrayList<>();
        }

        this.itemList.add(item);
    }
}
