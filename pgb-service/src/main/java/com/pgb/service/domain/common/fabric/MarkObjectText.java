package com.pgb.service.domain.common.fabric;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

@Schema(title = "文字类型")
@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MarkObjectText extends MarkObject{
    @Schema(title = "类型，Image、txt、line")
    private final MarkObjectEnum type = MarkObjectEnum.Textbox;

    @Builder.Default
    private String textAlign = "left";

    @Builder.Default
    private Double fontSize = 24.;

    @Schema(title = "left 位置")
    private Double left;

    @Schema(title = "top 位置")
    private Double top;

    private String text;

    private Double width;

    private Double height;

    // 新叶念体 or Times New Roman
    @Builder.Default
    // private String fontFamily = "新叶念体";
    private String fontFamily = "AliDaKai";

    @Builder.Default
    private String fill = "#ef4444";

    @Builder.Default
    private Float lineHeight = 1.2f;

    @Builder.Default
    private String fontWeight = "normal";

    @Builder.Default
    @Schema(title = "是否自动换行")
    private Boolean splitByGrapheme = true;

    @Builder.Default
    private String originX = "left";

    @Builder.Default
    private String originY = "top";

}
