package com.pgb.service.domain.zc.common.textbook.vo;

import com.pgb.service.enums.PronunciationTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 单词分页VO
 *
 * <AUTHOR>
 * Created by 2025/7/25 21:00
 */
@Schema(description = "单词分页查询结果VO")
@Data
public class WordPageVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "单词ID")
    private String id;

    @Schema(description = "单词")
    private String word;

    @Schema(description = "所属单元ID")
    private String unitId;

    @Schema(description = "排序值")
    private Integer sort;

    @Schema(description = "单词翻译列表")
    private List<WordTranslationVO> translations;

    @Schema(description = "单词发音列表")
    private List<WordPronunciationVO> pronunciations;

    @Schema(description = "单词例句列表")
    private List<WordExampleVO> examples;

    /**
     * 单词翻译VO
     */
    @Schema(description = "单词翻译VO")
    @Data
    public static class WordTranslationVO implements Serializable {

        private static final long serialVersionUID = 1L;

        @Schema(description = "翻译ID")
        private String id;

        @Schema(description = "单词ID")
        private String wordId;

        @Schema(description = "翻译内容")
        private String translation;

        @Schema(description = "词性")
        private String partOfSpeech;

        @Schema(description = "排序值")
        private Integer sort;
    }

    /**
     * 单词发音VO
     */
    @Schema(description = "单词发音VO")
    @Data
    public static class WordPronunciationVO implements Serializable {

        private static final long serialVersionUID = 1L;

        @Schema(description = "发音ID")
        private String id;

        @Schema(description = "单词ID")
        private String wordId;

        @Schema(description = "音标")
        private String phonetic;

        @Schema(description = "音频地址")
        private String audioUrl;

        @Schema(description = "发音类型：uk-英音，us-美音")
        private PronunciationTypeEnum type;

        @Schema(description = "排序值")
        private Integer sort;
    }

    /**
     * 单词例句VO
     */
    @Schema(description = "单词例句VO")
    @Data
    public static class WordExampleVO implements Serializable {

        private static final long serialVersionUID = 1L;

        @Schema(description = "例句ID")
        private String id;

        @Schema(description = "单词ID")
        private String wordId;

        @Schema(description = "例句内容")
        private String example;

        @Schema(description = "例句翻译")
        private String translation;

        @Schema(description = "排序值")
        private Integer sort;
    }
}
