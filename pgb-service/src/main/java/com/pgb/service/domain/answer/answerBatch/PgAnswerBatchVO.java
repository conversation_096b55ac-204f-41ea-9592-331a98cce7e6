package com.pgb.service.domain.answer.answerBatch;

import java.io.Serializable;

import java.util.Date;

import com.pgb.service.domain.question.zwEssay.ZwEssayTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.ToString;
import lombok.Builder;

/**
 * 批次提交表
 *
 * @TableName pg_answer_batch
 */
@Schema(description = "批次提交表 VO")
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode
@ToString
@Builder
public class PgAnswerBatchVO implements Serializable {

    @Schema(title = "批次id", type = "string")
    private Long id;

    @Schema(title = "创建时间")
    private Date createTime;

    @Schema(title = "用户id")
    private Long userId;

    @Schema(title = "题目id")
    private Long questionId;

    @Schema(title = "已批改数量")
    private Integer correctedNum;

    @Schema(title = "总数")
    private Integer totalNum;

    @Schema(title = "题目名称")
    private String questionName;

    @Schema(title = "班级名称")
    private String className;
}
