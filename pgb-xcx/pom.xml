<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.pgb</groupId>
        <artifactId>pgb-server</artifactId>
        <version>${revision}</version>
    </parent>

    <artifactId>pgb-xcx</artifactId>
    <packaging>jar</packaging>

    <description>
        小程序端口
    </description>

    <dependencies>

        <!--  db  -->
        <dependency>
            <groupId>com.pgb</groupId>
            <artifactId>pgb-service</artifactId>
        </dependency>

        <!--  短信  -->
        <dependency>
            <groupId>com.pgb</groupId>
            <artifactId>pgb-common-sms</artifactId>
        </dependency>

        <!-- OCR 识别 -->
        <dependency>
            <groupId>com.pgb</groupId>
            <artifactId>pgb-common-ocr</artifactId>
        </dependency>

        <!-- Redis 缓存-->
        <dependency>
            <groupId>com.pgb</groupId>
            <artifactId>pgb-common-redis</artifactId>
        </dependency>

        <!-- ai -->
        <dependency>
            <groupId>com.pgb</groupId>
            <artifactId>pgb-common-llm</artifactId>
        </dependency>

        <!--微信服务号-->
        <dependency>
            <groupId>com.pgb</groupId>
            <artifactId>pgb-common-wx</artifactId>
        </dependency>

        <!--微信小程序-->
        <dependency>
            <groupId>com.github.binarywang</groupId>
            <artifactId>weixin-java-miniapp</artifactId>
        </dependency>

        <!-- EasyExcel   -->
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>easyexcel</artifactId>
        </dependency>


<!--        <dependency>-->
<!--            <groupId>io.github.java-diff-utils</groupId>-->
<!--            <artifactId>java-diff-utils</artifactId>-->
<!--            <version>4.12</version>-->
<!--        </dependency>-->

<!--        <dependency>-->
<!--            <groupId>fr.opensagres.xdocreport</groupId>-->
<!--            <artifactId>org.apache.poi.xwpf.converter.pdf</artifactId>-->
<!--            <version>1.0.6</version>-->
<!--        </dependency>-->

        <!-- SpringBoot Web容器 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>spring-boot-starter-tomcat</artifactId>
                    <groupId>org.springframework.boot</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- web 容器使用 undertow 性能更强 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-undertow</artifactId>
        </dependency>

        <!--  springboot 测试  -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>

        <!--  markdown 转 html      -->
        <dependency>
            <groupId>org.commonmark</groupId>
            <artifactId>commonmark</artifactId>
            <version>0.22.0</version>
        </dependency>

        <!-- websocket -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-websocket</artifactId>
        </dependency>

    </dependencies>

    <build>
        <!--<finalName>pgb-xcx-1.0.3</finalName>-->
        <plugins>
            <!-- 可生成可执行的jar包或war包。插件的核心goal  -->
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>${spring-boot.version}</version>
                <configuration>
                    <includeSystemScope>true</includeSystemScope>
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <!--   打包jar包  -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-jar-plugin</artifactId>
                <version>${maven-jar-plugin.version}</version>
            </plugin>
        </plugins>
    </build>

</project>
