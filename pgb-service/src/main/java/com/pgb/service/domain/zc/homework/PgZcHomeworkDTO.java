package com.pgb.service.domain.zc.homework;

import java.io.Serializable;

import java.util.Date;

import com.pgb.common.core.validate.UpdateGroup;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.ToString;
import lombok.Builder;
import org.springframework.validation.annotation.Validated;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;

/**
* 字词作业表
* @TableName pg_zc_homework
*/
@Schema(description = "字词作业表 DTO")
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode
@ToString
@Validated
@Builder
public class PgZcHomeworkDTO implements Serializable {

    @Schema(title = "字词作业id", type = "string")
    @NotNull(message="[字词作业id]不能为空", groups = {UpdateGroup.class})
    private Long id;

    @Schema(title = "用户id")
    private Long userId;

    @Schema(title = "作业名称")
    @Size(max= 255)
    private String name;

    @Schema(title = "题目内容")
    private Object questionInfo;

    @Schema(title = "关联的字词题目id")
    private Long zcQuestionId;

    @Schema(title = "班级id")
    private Long classId;

    @Schema(title = "创建时间")
    private Date createTime;

    @Schema(title = "更新时间")
    private Date updateTime;

}
