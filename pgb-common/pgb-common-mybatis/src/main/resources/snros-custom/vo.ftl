package ${baseInfo.packageName};

import java.io.Serializable;
<#list tableClass.importList as fieldType>${"\n"}import ${fieldType};</#list>
import org.hibernate.validator.constraints.Length;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.ToString;
import lombok.Builder;

/**
* ${tableClass.remark!}
* @TableName ${tableClass.tableName}
*/
@Schema(description = "${tableClass.remark!} VO")
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode
@ToString
@Builder
public class ${baseInfo.fileName} implements Serializable {

<#list tableClass.allFields as field>
    @Schema(title = "${field.remark!}"<#if field.fieldName=="id">, type = "string"</#if>)
    private ${field.shortTypeName} ${field.fieldName};

</#list>
}
