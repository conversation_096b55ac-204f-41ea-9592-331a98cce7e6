package com.pgb.service.domain.distribution.agency;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 分销代理人员表
 * @TableName pg_distribution_agency
 */
@TableName(value ="pg_distribution_agency")
@Data
public class PgDistributionAgency implements Serializable {
    /**
     * 分销代理人员id
     */
    private Long id;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 分销比例 整数百分比
     */
    private Integer rate;

    /**
     * 创建时间
     */
    private Date createTime;

}
