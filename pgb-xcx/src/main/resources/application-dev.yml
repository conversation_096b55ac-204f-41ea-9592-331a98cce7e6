# 数据库
# http://***************:21356/a27f5d41
spring:
  datasource:
    driver-class-name: org.postgresql.Driver
    url: *************************************************
#    url: **************************************************
    username: postgres
    password: Snros2022..

  data:
    redis:
      #      host: *************
      host: 127.0.0.1
      port: 6379
  #      password: Snros2022..

#  data:
#    redis:
##      host: *************
#      host: **************
#      port: 6379
#      password: Snros2022..
#      database: 1

#  data:
#    redis:
##      host: *************
#      host: **************
#      port: 6379
#      password: Snros2022..
#      database: 1

# 接口文档
springdoc:
  swagger-ui:
    enabled: true
  api-docs:
    enabled: true

# 支付回调
pay:
  notifyUrl: https://test-api-2.pigaibang.com/api
#  notifyUrl: https://8q791634f5.goho.co/api

# 批改模型接口
correct:
  base-url: http://127.0.0.1:8081
#  base-url: http://model-java.pigaibang.com
  api-key: pgb-api-correct-v2
  # 批改 url
#  render-url: http://*************:9000
  render-url: http://127.0.0.1:9000
  # 用户端保存，使用高并发
  render-url-user: http://zw-render-url.pigaibang.com


# redisson
redisson:
  # redis key前缀
  keyPrefix:
  # 线程池数量
  threads: 128
  # Netty线程池数量
  nettyThreads: 128
  # 单节点配置
  singleServerConfig:
    # 客户端名称
    clientName: pgb-server
    # 最小空闲连接数
    connectionMinimumIdleSize: 32
    # 连接池大小
    connectionPoolSize: 128
    # 连接空闲超时，单位：毫秒
    idleConnectionTimeout: 10000
    # 命令等待超时，单位：毫秒
    timeout: 3000
    # 发布和订阅连接池大小
    subscriptionConnectionPoolSize: 128

# textbook服务配置
textbook:
  base-url: http://127.0.0.1:8080/api
