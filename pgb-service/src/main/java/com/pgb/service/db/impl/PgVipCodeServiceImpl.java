package com.pgb.service.db.impl;

import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.pgb.common.core.utils.Id2CodeUtil;
import com.pgb.service.db.PgVipCodeService;
import com.pgb.service.domain.vip.PgVipCode;
import com.pgb.service.enums.ChannelTypeEnum;
import com.pgb.service.enums.VipTypeEnum;
import com.pgb.service.mapper.PgVipCodeMapper;
import org.springframework.stereotype.Service;

import java.util.Date;

/**
* <AUTHOR>
* @description 针对表【pg_vip_code】的数据库操作Service实现
* @createDate 2024-10-21 23:50:52
*/
@Service
public class PgVipCodeServiceImpl extends ServiceImpl<PgVipCodeMapper, PgVipCode>
    implements PgVipCodeService {

    @Override
    public PgVipCode createVipCode(ChannelTypeEnum channelType, VipTypeEnum vipType, Long channelId, Long orderId) {
        PgVipCode vipCode = new PgVipCode();
        vipCode.setCode(
                Id2CodeUtil.encode(
                        IdUtil.getSnowflakeNextId()
                )
        );
        vipCode.setStatus(0);
        vipCode.setCreateTime(new Date());
        vipCode.setChannelType(channelType);
        vipCode.setVipType(vipType);
        if (ObjectUtil.isNotNull(channelId)) {
            vipCode.setChannelId(channelId);
        }
        if (ObjectUtil.isNotNull(orderId)) {
            vipCode.setOrderId(orderId);
        }

        save(vipCode);

        return vipCode;
    }
}




