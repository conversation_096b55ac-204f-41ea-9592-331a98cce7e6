package com.pgb.ai.domain.doubao;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * 豆包TTS配置参数
 *
 * <AUTHOR>
 */
@Schema(title = "豆包TTS配置参数")
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode
@ToString
@Builder(toBuilder = true)
@ConfigurationProperties(prefix = "doubao.tts")
public class DoubaoTTSProperty {

    @Schema(title = "应用ID", description = "豆包TTS应用ID")
    private String appId;

    @Schema(title = "访问密钥", description = "豆包TTS访问密钥")
    private String accessKey;

    @Schema(title = "资源ID", description = "豆包TTS资源ID")
    private String resourceId;

    @Schema(title = "应用密钥", description = "豆包TTS应用密钥，默认值")
    private String appKey = "aGjiRDfUWi";

    @Schema(title = "请求地址", description = "豆包TTS API地址")
    private String apiUrl = "https://openspeech.bytedance.com/api/v3/tts/unidirectional";

    @Schema(title = "用户ID", description = "用户标识")
    private String userId = "12345";

    @Schema(title = "音色", description = "语音合成音色")
    private String speaker = "zh_male_jieshuonansheng_mars_bigtts";

    @Schema(title = "音频格式", description = "输出音频格式")
    private String audioFormat = "mp3";

    @Schema(title = "采样率", description = "音频采样率")
    private Integer sampleRate = 24000;

    @Schema(title = "禁用Markdown过滤", description = "是否禁用Markdown过滤")
    private Boolean disableMarkdownFilter = true;

    @Schema(title = "启用语言检测", description = "是否启用语言检测")
    private Boolean enableLanguageDetector = true;

    @Schema(title = "启用LaTeX转换", description = "是否启用LaTeX转换")
    private Boolean enableLatexTn = true;

    @Schema(title = "禁用默认比特率", description = "是否禁用默认比特率")
    private Boolean disableDefaultBitRate = true;

    @Schema(title = "过滤括号的最大长度", description = "过滤括号内容的最大长度")
    private Integer maxLengthToFilterParenthesis = 0;

    @Schema(title = "使用缓存", description = "是否使用缓存")
    private Boolean useCache = true;

    @Schema(title = "文本类型", description = "缓存配置中的文本类型")
    private Integer textType = 1;

    @Schema(title = "请求超时时间", description = "HTTP请求超时时间（毫秒）")
    private Integer timeout = 60000;

}
