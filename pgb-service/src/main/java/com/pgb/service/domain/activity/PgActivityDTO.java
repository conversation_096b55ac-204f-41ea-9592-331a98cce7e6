package com.pgb.service.domain.activity;

import com.pgb.common.core.validate.UpdateGroup;
import com.pgb.service.enums.ActivityTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.*;
import org.springframework.validation.annotation.Validated;

import java.io.Serializable;
import java.util.Date;

/**
*
* @TableName pg_activity
*/
@Schema(description = " DTO")
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode
@ToString
@Validated
@Builder
public class PgActivityDTO implements Serializable {

    @Schema(title = "活动表", type = "string")
    @NotNull(message="[活动表]不能为空", groups = {UpdateGroup.class})
    private Long id;

    @Schema(title = "活动名称")
    @Size(max= 255)
    private String name;

    @Schema(title = "活动类型，0：年卡立减，1：买多少送多少（天）")
    private ActivityTypeEnum type;

    @Schema(title = "购买类型，0：直购，1：兑换码")
    private Integer buyType;

    @Schema(title = "活动配置，对应不同实体")
    private ActivityDiscountConfig config;

    @Schema(title = "创建时间")
    private Date createTime;

    @Schema(title = "开始时间")
    private Date startTime;

    @Schema(title = "结束时间")
    private Date endTime;

}
