
执行下面的请求，可以停止批改
http://127.0.0.1:8084/api/user/zw/info/setCorrectStatus/setCorrectStatus/false

#### 安卓APP打包
生成签名证书
```bash
keytool -genkey -alias pigaibang -keyalg RSA -keysize 2048 -validity 36500 -keystore app.keystore
```
查看公钥(window 系统中双击 XXX.cer，将证书打开，切换到详细信息，然后就可以看到公钥)
```bash
keytool -list -v -keystore app.keystore
```
导出APP公钥方法
```bash
keytool -export -alias pigaibang -file certificate.cer -keystore app.keystore
```

#### 数据库-数据分析SQL
筛选无效的题目-试卷关联信息
```sql
SELECT * FROM "pg_paper_ques" where ques_id not in (SELECT id FROM pg_question)
```

删筛选无效作答记录
```sql
SELECT * FROM "pg_question_answer" where user_id not in (SELECT id FROM pg_users)
```

```sql
SELECT * FROM "pg_goods_user" where user_id not in (SELECT id FROM pg_users)
```

删除作业无效关联
```sql
SELECT * FROM "pg_homework_relations" where goods_id not in (SELECT id FROM "pg_goods")
```

#### 阿里云的 maven 镜像库：
https://developer.aliyun.com/mvn/search

在里面可以搜索当前最新的版本使用。


#### 微信第三方平台 调试工具
https://developers.weixin.qq.com/apiExplorer?apiName=modifyThirdpartyServerDomain&plat=thirdparty

在这里，添加业务域名、服务器域名
```json

```
