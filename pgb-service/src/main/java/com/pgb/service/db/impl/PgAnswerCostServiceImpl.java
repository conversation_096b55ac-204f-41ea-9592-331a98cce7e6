package com.pgb.service.db.impl;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.pgb.service.db.PgAnswerCostService;
import com.pgb.service.db.PgUsersService;
import com.pgb.service.domain.answer.answerCost.PgAnswerCost;
import com.pgb.service.domain.user.PgUsers;
import com.pgb.service.domain.common.TodayNum;
import com.pgb.service.mapper.PgAnswerCostMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Date;

/**
 * <AUTHOR>
 * @description 针对表【pg_answer_cost(作文批改明细表)】的数据库操作Service实现
 * @createDate 2024-09-04 15:58:56
 */
@Service
@RequiredArgsConstructor
public class PgAnswerCostServiceImpl extends ServiceImpl<PgAnswerCostMapper, PgAnswerCost>
        implements PgAnswerCostService {

    private final PgUsersService pgUsersService;

    @Override
    public TodayNum getTodaySubmitNum(Long userId) {
        // 判断当前用户是否是会员
        PgUsers users = pgUsersService.getById(userId);

        int total = users.getIsVip() ? 200 : 3;

        long todayNum = count(new LambdaQueryWrapper<PgAnswerCost>()
                .eq(PgAnswerCost::getUserId, userId)
                .between(PgAnswerCost::getCreateTime, DateUtil.beginOfDay(new Date()), DateUtil.endOfDay(new Date()))
        );

        return new TodayNum((int) todayNum, total);
    }

    @Override
    public PgAnswerCost addAnswerCost(Long userId, Long answerId, Integer type) {
        // 保存关联明细数据
        PgAnswerCost cost = new PgAnswerCost();
        cost.setUserId(userId);
        cost.setAnswerId(answerId);
        cost.setCreateTime(new Date());
        cost.setType(type);
        save(cost);
        return cost;
    }
}




