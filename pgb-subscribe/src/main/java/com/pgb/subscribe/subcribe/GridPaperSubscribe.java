package com.pgb.subscribe.subcribe;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.TimeInterval;
import cn.hutool.core.img.Img;
import cn.hutool.core.img.ImgUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.IORuntimeException;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.URLUtil;
import cn.hutool.core.util.ZipUtil;
import cn.hutool.crypto.digest.DigestUtil;
import cn.hutool.http.HttpUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.pgb.common.core.utils.EnvUtils;
import com.pgb.common.oss.domain.MaterialTypeEnum;
import com.pgb.common.oss.service.OssService;
import com.pgb.common.redis.QueueUtils;
import com.pgb.service.custom.CorrectService;
import com.pgb.service.db.*;
import com.pgb.service.domain.answer.PgAnswer;
import com.pgb.service.domain.answer.gridPaper.GridPaperResult;
import com.pgb.service.domain.common.image.FilePaperImg;
import com.pgb.service.domain.homework.PgHomework;
import com.pgb.service.domain.homework.PgHomeworkGridPaper;
import com.pgb.service.domain.question.zwEssay.ZwEssayQuestion;
import com.pgb.service.domain.student.PgStudent;
import com.pgb.service.enums.CorrectStatusEnum;
import com.pgb.service.domain.GlobQueueConstants;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.pdfbox.Loader;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.rendering.PDFRenderer;
import org.redisson.api.RBlockingQueue;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;

import java.awt.image.BufferedImage;
import java.io.File;
import java.io.IOException;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.CompletableFuture;

@Schema(title = "标准格子纸批量上传监听队列")
@Component("GridPaperSubscribe")
@Slf4j
@RequiredArgsConstructor
public class GridPaperSubscribe implements CommandLineRunner {

    private final CorrectService correctService;

    private final PgHomeworkGridPaperService pgHomeworkGridPaperService;

    private final PgHomeworkService pgHomeworkService;

    private final PgStudentService pgStudentService;

    private final PgAnswerService pgAnswerService;

    private final PgAnswerCostService pgAnswerCostService;

    private final OssService ossService;

    @Override
    public void run(String... args) throws Exception {

        // 监听队列 两个处理
        // PgHomeworkGridPaper record = pgHomeworkGridPaperService.getById(1950735855681638402L);
        // PgHomework homework = pgHomeworkService.getById(record.getHomeworkId());
        // // 执行分析
        // doDetect(record, homework);

        /**
         * https://cdn.pigaibang.com/tmp/user/1869749468023459841/IMG/3f0fbc89cfd9498aba050f3cd035339b.jpg
         * https://cdn.pigaibang.com/zw/user/1869749468023459841/IMG/401b7fd9616d0feb527a6ff7c15194d8.jpg
         */
         //GridPaperResult result = correctService.gridPaperResult("https://cdn.pigaibang.com/tmp/user/1869749468023459841/IMG/6f26031a04a74e4cb85401db8037f400.jpg");
         //log.info("回复内容：{}，{}，第{}页",result.getName(), result.getStudentNo(), result.getPageNo());

        // queryToExport();

        if (EnvUtils.isProd() || EnvUtils.isDev()) {
            // 监听队列 两个处理
            reExport();

            reExport();

            // 立即执行一次
            queryToExport();

            log.info("监听格子纸上传队列 --> 启动");
        }
    }

    private void reExport() {

        // 获取导出队列
        RBlockingQueue<Long> queue = QueueUtils.getClient().getBlockingQueue(GlobQueueConstants.PGB_XCX_GRID_PAPER_DETECTION_QUEUE.name());

        // 全局异步监听导出word
        CompletableFuture<Void> f = CompletableFuture.runAsync(() -> {
            try {
                // 阻塞获取格子纸任务
                Long recordId = queue.take();

                log.info("【接收格子纸-监听信息】：{}", recordId);

                PgHomeworkGridPaper record = pgHomeworkGridPaperService.getById(recordId);

                if (ObjectUtil.isNull(record) || record.getStatus().equals(CorrectStatusEnum.Corrected)) {
                    log.info("无效的格子纸任务-已处理状态：{}", record.getId());
                    return;
                }

                PgHomework homework = pgHomeworkService.getById(record.getHomeworkId());

                if (ObjectUtil.isNull(homework)) {
                    log.info("格子纸对应的作业不存在：{}", record.getId());
                    // 删除
                    pgHomeworkGridPaperService.removeById(record.getId());
                    return;
                }

                // 执行分析
                doDetect(record, homework);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
        });

        f.whenComplete((v, e) -> {
            if (e != null) {
                // 如果是 RedissonShutdownException 则直接跳过
                if (e.getCause().getMessage().contains("Redisson is shutdown")) {
                    log.error("格子纸队列监听异常 --> redisson 已中断！");
                    return;
                }

                log.error("格子纸队列监听异常 -->  异常", e);
            }

            this.reExport();
        });
    }

    private void doDetect(PgHomeworkGridPaper record, PgHomework homework) {
        TimeInterval interval = DateUtil.timer();

        // 判断是否是压缩包
        zipFile(record);

        // 判断是否是一个pdf
        pdfFile(record);

        List<String> userImgList = StrUtil.split(record.getUserImgList(), StrUtil.COMMA);

        // 拿班级学生
        List<PgStudent> studentList = pgStudentService.list(new LambdaQueryWrapper<PgStudent>()
                .eq(PgStudent::getClassId, homework.getClassId())
        );

        // 班级学生待提交表单
        Map<Long, List<GridPaperResult>> studentFormMap = new HashMap<>();

        // 合并排序
        List<GridPaperResult> resultList = new ArrayList<>();

        for (String userImg : userImgList) {
            GridPaperResult result = correctService.gridPaperResult(userImg);
            if (ObjectUtil.isNull(result)) {
                log.info("格子纸图片处理失败：{}", userImg);
                continue;
            }
            result.setImgUrl(userImg);
            result.setUserImgList(List.of(userImg));
            record.setTokens((int) (record.getTokens() + result.getAiTokens()));
            // 先查学号
            boolean isFind = false;
            if (StrUtil.isNotBlank(result.getStudentNo())) {
                PgStudent one = CollUtil.findOne(studentList, student -> StrUtil.isNotEmpty(student.getStudentNo()) && student.getStudentNo().equals(result.getStudentNo()));
                if (ObjectUtil.isNotNull(one)) {
                    addStudentMap(studentFormMap, result, one);
                    isFind = true;
                } else {
                    // 再查名称
                    if (StrUtil.isNotBlank(result.getName())) {
                        one = CollUtil.findOne(studentList, student -> StrUtil.isNotEmpty(student.getName()) && student.getName().equals(result.getName()));
                        if (ObjectUtil.isNotNull(one)) {
                            addStudentMap(studentFormMap, result, one);
                            isFind = true;
                        }
                    }
                }
            } else {
                // 查名称
                if (StrUtil.isNotBlank(result.getName())) {
                    PgStudent one = CollUtil.findOne(studentList, student -> StrUtil.isNotEmpty(student.getName()) && student.getName().equals(result.getName()));
                    if (ObjectUtil.isNotNull(one)) {
                        addStudentMap(studentFormMap, result, one);
                        isFind = true;
                    }
                }
            }

            // 没有识别出来
            if (!isFind) {
                GridPaperResult failResult = new GridPaperResult();
                failResult.setImgUrl(userImg);
                failResult.setStatus(0);
                resultList.add(failResult);
            }
        }

        // 遍历form
        for (Map.Entry<Long, List<GridPaperResult>> entry : studentFormMap.entrySet()) {
            // 只有一个，直接添加
            if (entry.getValue().size() == 1) {
                resultList.add(
                        entry.getValue().get(0)
                );
                continue;
            }

            // 处理序号问题，默认使用填充方式页码
            processPageNo(entry);

            // 根据pageNo 排序，正序
            entry.getValue().sort(Comparator.comparingInt(GridPaperResult::getPageNo));

            log.info("【识别结果：{}", entry);

            // 合并，规则：同一份
            GridPaperResult result = entry.getValue().get(0);
            for (int i = 1; i < entry.getValue().size(); i++) {
                result.getSplitImgList().addAll(
                        entry.getValue().get(i).getSplitImgList()
                );
            }
            resultList.add(result);
        }

        // 开始上传作文
        for (GridPaperResult result : resultList) {
            if (ObjectUtil.isNotNull(result.getStudentId())) {
                submitZw(result, homework, record);
            }
        }

        // 将之前的图片，设置为删除标签
        for (String userImgUrl : userImgList) {
            ossService.setTagList(
                    URLUtil.getPath(userImgUrl),
                    "Deleted"
            );
        }

        // 清空图片
        for (GridPaperResult result : resultList) {
            result.setSplitImgList(null);
        }

        // 保存状态
        record.setStatus(CorrectStatusEnum.Corrected);
        // 保存时间
        record.setCompleteTime(new Date());
        // 用时
        record.setDuration((int) interval.intervalSecond());
        // 保存检测结果
        record.setImgResultJson(resultList);
        // 保存
        pgHomeworkGridPaperService.updateById(record);

        log.info("格子纸任务-完成：{}，用时：{}", record.getId(), interval.intervalPretty());
    }

    // 处理序号问题，优先占位排序
    private void processPageNo(Map.Entry<Long, List<GridPaperResult>> entry) {
        // 根据大小，设置需要占位的内容
        Boolean[] isNull = new Boolean[entry.getValue().size()];
        // 默认false
        Arrays.fill(isNull, false);

        // 如果有页码，就将其占位为 true
        for (int i = 0; i < entry.getValue().size(); i++) {
            GridPaperResult result = entry.getValue().get(i);
            if (ObjectUtil.isNotNull(result.getPageNo()) && result.getPageNo() > 0 && result.getPageNo() < entry.getValue().size()) {
                isNull[result.getPageNo() - 1] = true;
            }
        }

        for (int i = 0; i < entry.getValue().size(); i++) {
            GridPaperResult result = entry.getValue().get(i);
            if (ObjectUtil.isNull(result.getPageNo())) {
                // 先找到第一个为false的，并且当前没有页码能占位上
                for (int j = 0; j < isNull.length; j++) {
                    if (!isNull[j]) {
                        result.setPageNo(j + 1);
                        isNull[j] = true;
                        break;
                    }
                }
            }
        }
    }

    private void zipFile(PgHomeworkGridPaper record) {
        if (!StrUtil.contains(record.getUserImgList(), StrUtil.COMMA) && StrUtil.endWith(record.getUserImgList(), "zip")) {
            // 下载文件到本地
            File zip = FileUtil.createTempFile(".zip", true);
            // 解压文件 防止内有中文名文件夹时报错
            File unzip = null;
            String zipUrl = record.getUserImgList();
            // 先解压缩
            try {
                // 下载文件
                HttpUtil.downloadFile(record.getUserImgList(), zip);
                try {
                    unzip = ZipUtil.unzip(zip, Charset.forName("GBK"));
                } catch (IORuntimeException e) {
                    log.info("解压失败，尝试使用UTF-8解压");
                    unzip = ZipUtil.unzip(zip, StandardCharsets.UTF_8);
                }
                // 获取压缩包中的所有文件
                List<File> files = FileUtil.loopFiles(unzip.getPath(), file -> {
                    // 过滤隐藏文件
                    return !file.getName().startsWith(".");
                });
                // 遍历上传
                List<String> userImgList = new ArrayList<>();
                for (File img : files) {
                    try {
                        // 上传图片
                        String key = StrUtil.format(
                                "zw/user/{}/{}/{}.jpg",
                                record.getUserId(),
                                MaterialTypeEnum.IMG.name(),
                                DigestUtil.md5Hex(
                                        FileUtil.readBytes(img)
                                )
                        );
                        // 压缩并提交
                        File tempFile = FileUtil.createTempFile(".jpg", true);
                        Img.from(img).setQuality(0.8).write(tempFile);
                        String imgUrl = ossService.putFile(key, tempFile);
                        userImgList.add(imgUrl);
                        FileUtil.del(tempFile);
                    } catch (Exception e) {
                        log.error("上传图片失败：{}，失败原因：{}", img.getPath(), e.getMessage());
                        continue;
                    }
                }
                // 反向赋值
                record.setUserImgList(
                        StrUtil.join(StrUtil.COMMA, userImgList)
                );
                pgHomeworkGridPaperService.updateById(record);
                // 删除文件
                ossService.delete(
                        URLUtil.getPath(zipUrl)
                );
            } finally {
                FileUtil.del(zip);
                FileUtil.del(unzip);
            }
        }

    }

    private void pdfFile(PgHomeworkGridPaper record) {
        // 判断是否是一个pdf
        if (!StrUtil.contains(record.getUserImgList(), StrUtil.COMMA) && StrUtil.endWith(record.getUserImgList(), ".pdf")) {
            File pdf = FileUtil.createTempFile(".pdf", true);
            String pdfUrl = record.getUserImgList();
            try {
                HttpUtil.downloadFile(record.getUserImgList(), pdf);
                PDDocument doc = Loader.loadPDF(FileUtil.readBytes(pdf));
                PDFRenderer renderer = new PDFRenderer(doc);
                // 遍历上传
                List<String> userImgList = new ArrayList<>();
                for (int i = 0; i < doc.getNumberOfPages(); i++) {

                    // 生成 PDF 截取的图片，dpi越高，越大
                    BufferedImage image = renderer.renderImageWithDPI(i, 200);

                    // 压缩
                    File tempFile = FileUtil.createTempFile(".jpg", true);
                    Img.from(image).setQuality(0.8).write(tempFile);

                    // 上传图片
                    String key = StrUtil.format(
                            "zw/user/{}/{}/{}.jpg",
                            record.getUserId(),
                            MaterialTypeEnum.IMG.name(),
                            DigestUtil.md5Hex(
                                    FileUtil.readBytes(tempFile)
                            )
                    );

                    // 提交
                    String imgUrl = ossService.putFile(key, tempFile);
                    userImgList.add(imgUrl);
                    FileUtil.del(tempFile);
                }

                // 反向赋值
                record.setUserImgList(
                        StrUtil.join(StrUtil.COMMA, userImgList)
                );
                pgHomeworkGridPaperService.updateById(record);
                // 删除文件
                ossService.delete(
                        URLUtil.getPath(pdfUrl)
                );
            } catch (IOException e) {
                log.error("加载pdf失败：{}，失败原因：{}", record.getUserImgList(), e.getMessage());
                e.printStackTrace();
            } finally {
                FileUtil.del(pdf);
            }
        }
    }

    private void addStudentMap(Map<Long, List<GridPaperResult>> studentFormMap, GridPaperResult result, PgStudent one) {
        if (ObjectUtil.isNotNull(one)) {
            result.setStudentNo(one.getStudentNo());
            result.setName(one.getName());
            result.setStudentId(one.getId());
            // 如果不存在
            if (!studentFormMap.containsKey(one.getId())) {
                studentFormMap.put(one.getId(), new ArrayList<>());
            }
            // 保存内容
            studentFormMap.get(one.getId()).add(result);
        }
    }

    private void submitZw(GridPaperResult userSubmit, PgHomework homework, PgHomeworkGridPaper record) {
        // 判断学生是否已经有提交的，有提交的跳过
        if (pgAnswerService.exists(new LambdaQueryWrapper<PgAnswer>()
                .eq(PgAnswer::getHomeworkId, homework.getId())
                .eq(PgAnswer::getStudentId, userSubmit.getStudentId())
                .eq(PgAnswer::getDeleted, false)
        )) {
            userSubmit.setStatus(2);
            log.info("学生已经提交过，跳过：{}，题目：{}", userSubmit.getName(), homework.getName());
            return;
        }

        // 逐项提交分割的图片
        for (String base64 : userSubmit.getSplitImgList()) {
            BufferedImage image = ImgUtil.toImage(base64);
            // 压缩并提交
            File tempFile = FileUtil.createTempFile(".jpg", true);
            Img.from(image).setQuality(0.8).write(tempFile);
            // 上传图片
            String key = StrUtil.format(
                    "zw/user/{}/{}/{}.jpg",
                    record.getUserId(),
                    MaterialTypeEnum.IMG.name(),
                    DigestUtil.md5Hex(
                            ImgUtil.toBytes(image, ImgUtil.IMAGE_TYPE_JPG)
                    )
            );
            String url = ossService.putFile(key, tempFile);
            log.info("格子纸批量任务，上传分割图片：{}", url);
            FileUtil.del(tempFile);
            // 添加上传的图片
            userSubmit.addSplitImgUrl(url);
            userSubmit.setStatus(1);
        }

        // 移除base64
        userSubmit.setSplitImgList(null);

        // 构建提交表单
        ZwEssayQuestion form = new ZwEssayQuestion();
        form.setStudentId(userSubmit.getStudentId());
        form.setHomeworkId(homework.getId());
        form.setUserImgAnswerList(
                userSubmit.getSplitImgUrlList().stream().map(imgUrl -> {
                    FilePaperImg img = new FilePaperImg();
                    img.setImgUrl(imgUrl);
                    return img;
                }).toList()
        );

        // 提交班级作文
        PgAnswer answer = pgAnswerService.submitHomeworkZwAnswer(form, record.getUserId());

        // 新增消耗情况
        pgAnswerCostService.addAnswerCost(record.getUserId(), answer.getId(), 0);

        // 提交批改队列
        QueueUtils.addQueueObjectInTransaction(
                GlobQueueConstants.PGB_XCX_CORRECT_QUEUE_VIP.name(),
                answer.getId()
        );
    }

    private void queryToExport() {
        // 查询超过7分钟没有处理的
        List<PgHomeworkGridPaper> list = pgHomeworkGridPaperService.list(new LambdaQueryWrapper<PgHomeworkGridPaper>()
                .eq(PgHomeworkGridPaper::getStatus, CorrectStatusEnum.Uploaded)
                .le(PgHomeworkGridPaper::getCreateTime, DateUtil.offsetMinute(new Date(), -7))
        );

        for (PgHomeworkGridPaper record : list) {
            // 加入队列
            if (!QueueUtils.containsQueueObject(GlobQueueConstants.PGB_XCX_GRID_PAPER_DETECTION_QUEUE.name(), record.getId())) {
                QueueUtils.addQueueObject(GlobQueueConstants.PGB_XCX_GRID_PAPER_DETECTION_QUEUE.name(), record.getId());
                log.info("当前格子纸记录加入队列：{}", record.getId());
            } else {
                log.info("当前格子纸记录存在队列中：{}，跳过", record.getId());
            }

        }
    }
}
