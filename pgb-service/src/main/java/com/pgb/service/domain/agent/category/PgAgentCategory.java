package com.pgb.service.domain.agent.category;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 分组表
 * @TableName pg_agent_category
 */
@TableName(value ="pg_agent_category")
@Data
public class PgAgentCategory {
    /**
     * 分组id
     */
    private Long id;

    /**
     * 分组名称
     */
    private String name;

    /**
     * 同级排序
     */
    private Integer sort;

    /**
     * 关联的智能体id
     */
    private String agentIds;
}
