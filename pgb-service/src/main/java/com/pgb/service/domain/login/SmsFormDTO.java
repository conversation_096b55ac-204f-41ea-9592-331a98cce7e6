package com.pgb.service.domain.login;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import org.springframework.validation.annotation.Validated;

import java.io.Serializable;

/**
 * <AUTHOR>
 * Created by 2024/11/6 17:39
 */
@Data
@Schema(description = "短信验证码表单实体")
@Validated
public class SmsFormDTO implements Serializable {

    @Schema(title = "手机号", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "手机号不能为空")
    private String phone;

//    @Schema(title = "验证码唯一标识", requiredMode = Schema.RequiredMode.REQUIRED)
//    @NotBlank
//    private String verifyKey;
}
