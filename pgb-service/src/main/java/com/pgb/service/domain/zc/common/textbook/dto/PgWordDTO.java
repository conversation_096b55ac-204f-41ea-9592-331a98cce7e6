package com.pgb.service.domain.zc.common.textbook.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * 单词DTO
 *
 * <AUTHOR>
 * Created by 2025/7/25 21:00
 */
@Data
@Schema(description = "单词DTO")
public class PgWordDTO {

    @Schema(description = "主键ID")
    private Long id;

    @Schema(description = "单词")
    private String word;

    @Schema(description = "所属单元ID")
    private Long unitId;

    @Schema(description = "排序值")
    private Integer sort;

    @Schema(description = "创建时间")
    private Date createTime;

    @Schema(description = "更新时间")
    private Date updateTime;
}
