package com.pgb.subscribe.subcribe;

import cn.hutool.core.compress.ZipWriter;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.TimeInterval;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.ReUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.pgb.common.core.global.exception.BaseException;
import com.pgb.common.core.utils.EnvUtils;
import com.pgb.common.oss.service.OssService;
import com.pgb.common.redis.QueueUtils;
import com.pgb.service.db.*;
import com.pgb.service.domain.GlobQueueConstants;
import com.pgb.service.domain.answer.PgAnswer;
import com.pgb.service.domain.export.PgExportRecord;
import com.pgb.service.domain.homework.PgHomework;
import com.pgb.service.domain.question.zwEssay.ZwEssayQuestion;
import com.pgb.service.domain.student.PgStudent;
import com.pgb.service.domain.userConfig.ExportConfigDTO;
import com.pgb.service.enums.ExportStatusEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.checkerframework.checker.units.qual.A;
import org.redisson.api.RBlockingQueue;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;

import java.io.File;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.CompletableFuture;

/**
 * <AUTHOR>
 * Created by 2025/5/23 15:33
 */
@Component("ExportStudentRecordQueueSubscribe")
@Slf4j
@RequiredArgsConstructor
public class ExportStudentRecordQueueSubscribe implements CommandLineRunner {

    private final PgAnswerService pgAnswerService;

    private final PgExportRecordService pgExportRecordService;

    private final PgUserConfigService pgUserConfigService;

    private final PgStudentService pgStudentService;

    private final PgHomeworkService pgHomeworkService;

    private final OssService ossService;

    @Override
    public void run(String... args) throws Exception {

        if (EnvUtils.isProd() || EnvUtils.isDev()) {

            for (int i = 0; i < 5; i++) {
                // 导出队列
                reExport();
            }

            log.info("监听导出学生作文集队列 --> 启动");

            // 立即执行一次
            queryToExport();
        }

//        doExport(1927629109974069250L);
    }

    private void reExport() {

        // 获取导出队列
        RBlockingQueue<Long> queue = QueueUtils.getClient().getBlockingQueue(GlobQueueConstants.PGB_XCX_EXPORT_STUDENT_WORD_QUEUE.name());

        // 全局异步监听导出word
        CompletableFuture<Void> f = CompletableFuture.runAsync(() -> {

            try {
                // 阻塞获取md5任务
                Long recordId = queue.take();

                log.info("【接收导出学生作文集队列-监听信息】：{}", recordId);

                // 执行导出
                doExport(recordId);

            } catch (InterruptedException e) {
                e.printStackTrace();
            }
        });

        f.whenComplete((v, e) -> {
            if (e != null) {

                // 如果是 RedissonShutdownException 则直接跳过
                if (e.getCause().getMessage().contains("Redisson is shutdown")) {
                    log.error("导出学生作文集队列监听异常 --> redisson 已中断！");
                    return;
                }

                log.error("导出学生作文集队列监听异常 -->  异常", e);
            }

            this.reExport();
        });

    }

    private void doExport(Long recordId) {

        TimeInterval timer = DateUtil.timer();

        // 拿任务
        PgExportRecord record = pgExportRecordService.getById(recordId);

        // 任务有效性判断
        if (ObjectUtil.isNull(record) || record.getStatus().equals(ExportStatusEnum.Completed)) {
            log.info("导出学生作文集任务跳过：{}", recordId);
            return;
        }

        // 构建进度数据
        log.info("开始导出学生作文集任务：{}", recordId);

        List<Long> studentIds = StrUtil.split(record.getStudentIds(), ";").stream().map(Long::valueOf).toList();
        List<Long> homeworkIds = StrUtil.split(record.getHomeworkIds(), ";").stream().map(Long::valueOf).toList();

        // 获取导出配置
        ExportConfigDTO exportConfig = pgUserConfigService.getExportConfig(record.getUserId());

        File zipFile = FileUtil.createTempFile(".zip", false);

        ZipWriter zipWriter = ZipWriter.of(zipFile, StandardCharsets.UTF_8);

        try {
            // 导出至一个word
            if (exportConfig.getExportFileStyle().equals(1)) {
                singleExport(zipWriter, studentIds, homeworkIds, exportConfig);
            }
            // 单篇导出
            else {
                multiExport(zipWriter, studentIds, homeworkIds);
            }
            // 关闭
            zipWriter.close();

            String path = StrUtil.format("zw/studentZip/{}/{}.zip", record.getUserId(), record.getMd5());

            // 上传
            String zipUrl = ossService.putFile(path, zipFile);

            log.info("压缩包：{}", zipUrl);

            // 更新上传状态
            record.setStatus(ExportStatusEnum.Completed);
            record.setZipUrl(zipUrl);
            record.setExportTime(new Date());
            pgExportRecordService.updateById(record);

            log.info("【导出学生作文集队列】结束：{}，耗时：{}",
                    record.getId(),
                    timer.intervalPretty()
            );
        } catch (Exception e) {
            log.error("导出作文集异常:{}", e.getMessage());
            e.printStackTrace();
        }
    }

    private void singleExport(ZipWriter zipWriter, List<Long> studentIds, List<Long> homeworkIds, ExportConfigDTO exportConfig) {

        for (Long studentId : studentIds) {

            PgStudent student = pgStudentService.getById(studentId);

            if (ObjectUtil.isNull(student)) {
                continue;
            }
            List<Long> answerIds = pgAnswerService.list(new LambdaQueryWrapper<PgAnswer>()
                            .eq(PgAnswer::getStudentId, studentId)
                            .in(PgAnswer::getHomeworkId, homeworkIds)
                            .and(i -> i.ne(PgAnswer::getDeleted, true)
                                    .or()
                                    .isNull(PgAnswer::getDeleted)
                            )
                    ).stream()
                    .map(PgAnswer::getId)
                    .toList();

            if (!answerIds.isEmpty()) {
                // 生成文档
                File exportOneDoc = pgExportRecordService.getExportOneDoc(answerIds, exportConfig);

                String filePath = studentIds.indexOf(studentId) + 1 + "_" + (StrUtil.isNotBlank(student.getName()) ? student.getName() : "学生") + "/作文批改报告.docx";

                zipWriter.add(filePath, FileUtil.getInputStream(exportOneDoc));

                FileUtil.del(exportOneDoc);
            }
        }
    }

    private void multiExport(ZipWriter zipWriter, List<Long> studentIds, List<Long> homeworkIds) {

        for (Long studentId : studentIds) {

            List<PgAnswer> answers = pgAnswerService.list(new LambdaQueryWrapper<PgAnswer>()
                    .eq(PgAnswer::getStudentId, studentId)
                    .in(PgAnswer::getHomeworkId, homeworkIds)
                    .and(i -> i.ne(PgAnswer::getDeleted, true)
                            .or()
                            .isNull(PgAnswer::getDeleted)
                    )
            );

            if (answers.isEmpty()) {
                continue;
            }

            for (PgAnswer answer : answers) {

                String fileName;

                PgHomework homework = pgHomeworkService.getById(answer.getHomeworkId());

                // 若备注不为空
                if (StrUtil.isNotBlank(homework.getName())) {
                    String name = ReUtil.delAll("[^\\u4e00-\\u9fa5a-zA-Z\\d_《》]", homework.getName());
                    fileName = answers.indexOf(answer) + 1 + "_" + StrUtil.subWithLength(name, 0, 10) + "_" + DateUtil.format(new Date(), "MM-dd") + "_作文批改报告";
                } else {
                    // 导出的文件名
                    fileName = answers.indexOf(answer) + 1 + "_" + DateUtil.format(new Date(), "MM-dd") + "_作文批改报告";
                }

                File tempWord = pgExportRecordService.getExportWord(answer, fileName);

                String dirName = answers.indexOf(answer) + 1 + "_" + (StrUtil.isNotBlank(answer.getName()) ? answer.getName() : "学生") + "作文集";

                // 添加到临时文件中
                String filePath = dirName + "/" + fileName + "." + FileUtil.getSuffix(tempWord);

                zipWriter.add(filePath, FileUtil.getInputStream(tempWord));

                FileUtil.del(tempWord);
            }
        }
    }

    private void queryToExport() {
        // 7分钟之前的
        List<PgExportRecord> list = pgExportRecordService.list(new LambdaQueryWrapper<PgExportRecord>()
                .ne(PgExportRecord::getStatus, ExportStatusEnum.Completed)
                // 作文集类型
                .eq(PgExportRecord::getType, 2)
        );

        log.info("【待导出作文集扫描】待导出作文集数量:{}个", list.size());

        list.forEach(record -> {
            // 加入导出队列
            if (!QueueUtils.containsQueueObject(GlobQueueConstants.PGB_XCX_EXPORT_STUDENT_WORD_QUEUE.name(), record.getId())) {
                QueueUtils.addQueueObject(GlobQueueConstants.PGB_XCX_EXPORT_STUDENT_WORD_QUEUE.name(), record.getId());
            } else {
                log.info("当前作文集已存在导出队列中：{}，跳过", record.getId());
            }
        });
    }
}
