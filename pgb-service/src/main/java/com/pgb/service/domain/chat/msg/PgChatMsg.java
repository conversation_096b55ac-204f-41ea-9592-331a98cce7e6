package com.pgb.service.domain.chat.msg;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;

import com.pgb.common.mybatis.handler.JsonbTypeHandler;
import com.pgb.service.enums.LLMTypeEnum;
import com.pgb.service.enums.RoleTypeEnum;
import lombok.Data;

/**
 * 会话消息内容记录表
 * @TableName pg_chat_msg
 */
@TableName(value ="pg_chat_msg")
@Data
public class PgChatMsg implements Serializable {
    /**
     * 会话消息内容记录id
     */
    private Long id;

    /**
     * 所属会话id
     */
    private Long chatId;

    /**
     * 用户类型（用户、机器人）
     */
    private RoleTypeEnum role;

    /**
     * 消息内容
     */
    private String content;

    /**
     * 文件
     */
    private String files;


    /**
     * 排序
     */
    private Integer sort;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 深度思考内容
     */
    private String reason;

    /**
     *  内容类型 0：普通文本，1：文件，2：图片
     */
    private Integer contentType;

    /**
     * 内容json（用于保存附件）
     */
    @TableField(typeHandler = JsonbTypeHandler.class)
    private Object contentJson;
}
