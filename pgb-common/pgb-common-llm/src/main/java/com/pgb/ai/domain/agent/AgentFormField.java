package com.pgb.ai.domain.agent;

import com.pgb.ai.enums.AgentFormFieldType;
import com.pgb.ai.enums.FileTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Schema(title = "智能体表单数据项")
@Data
public class AgentFormField {
    @Schema(title = "数据类型")
    private AgentFormFieldType type;

    @Schema(title = "字段名称")
    private String title;

    @Schema(title = "字段内容")
    private String content;

    @Schema(title = "文件列表")
    private List<FileItem> fileList;

}
