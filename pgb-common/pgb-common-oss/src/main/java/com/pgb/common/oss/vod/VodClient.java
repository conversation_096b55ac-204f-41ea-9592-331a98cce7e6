package com.pgb.common.oss.vod;

import cn.hutool.core.util.StrUtil;
import com.pgb.common.oss.cloud.TencentCloudUtils;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

import java.time.Duration;
import java.util.Random;

/**
 * Vod 客户端
 */
@Getter
@Slf4j
public class VodClient {

    private final String configKey;

    private final VodProperties properties;

    /**
     * 初始化构造VOD客户端
     * @param configKey
     * @param properties
     */
    public VodClient(String configKey, VodProperties properties) {
        this.configKey = configKey;
        this.properties = properties;
        // 设置事件回调
        String notificationUrl = TencentCloudUtils.getVodNotificationUrl(properties.getAccessKey(), properties.getSecretKey(), properties.getAppId());
        if (!StrUtil.equals(properties.getCallbackUrl(), notificationUrl)) {
            String requestId = TencentCloudUtils.setVodNotificationUrl(properties.getAccessKey(), properties.getSecretKey(), properties.getAppId(), properties.getCallbackUrl());
            log.info("设置VOD回调URL => {} [{}]", properties.getCallbackUrl(), requestId);
        }
    }

    /**
     * 检查配置是否相同
     */
    public boolean checkPropertiesSame(VodProperties properties) {
        return this.properties.equals(properties);
    }

    // 获取视频上传密钥
    public String getUploadSign() {
        return TencentCloudUtils.getVodSign(
                this.properties.getAccessKey(),
                this.properties.getSecretKey(),
                this.properties.getAppId(),
                Duration.ofHours(6).toSeconds(),
                new Random().nextInt(Integer.MAX_VALUE),
                "封面图截取"
        );
    }
}
