# 数据库
# http://192.168.231.128:21356/a27f5d41
spring:
  datasource:
    driver-class-name: org.postgresql.Driver
    url: *************************************************
#    url: **************************************************
    username: postgres
    password: Snros2022..

# 批改模型接口
correct:
  base-url: http://127.0.0.1:8081
#  base-url: http://model-java.pigaibang.com
  api-key: pgb-api-correct-v2
  # 批改 url
#  render-url: http://47.98.182.152:9000
#  render-url: http://49.232.245.85:9000
  render-url: http://127.0.0.1:9000
  # 用户端保存，使用高并发
  render-url-user: http://fabric.fcv3.1466332595565727.cn-beijing.fc.devsapp.net

# textbook服务配置
textbook:
  base-url: http://127.0.0.1:8080/api
