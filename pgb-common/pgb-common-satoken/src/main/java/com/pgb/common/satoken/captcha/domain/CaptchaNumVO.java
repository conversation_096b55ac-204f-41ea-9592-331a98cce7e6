package com.pgb.common.satoken.captcha.domain;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.util.Date;

@Data
@NoArgsConstructor // 生成无参构造方法
@AllArgsConstructor // 生成有参构造方法
@EqualsAndHashCode
@Schema(title = "数字 验证码实体 VO")
@Builder
public class CaptchaNumVO {

    @Schema(title = "唯一标识", description = "验证码的唯一标识。通常为uuid")
    public String verifyKey;

    @Schema(title = "验证码图片", description = "base64位的字符串")
    private String img;

    @Schema(title = "创建时间")
    public Date createTime;

    @Schema(title = "是否已验证")
    public Boolean isVerify;

    @Schema(title = "验证时间")
    public Date verifyTime;
}
