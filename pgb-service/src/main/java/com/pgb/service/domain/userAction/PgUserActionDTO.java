package com.pgb.service.domain.userAction;

import java.io.Serializable;

import java.util.Date;

import com.pgb.common.core.validate.UpdateGroup;
import com.pgb.service.enums.UserActionType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.ToString;
import lombok.Builder;
import org.springframework.validation.annotation.Validated;
import jakarta.validation.constraints.NotNull;

/**
*
* @TableName pg_user_action
*/
@Schema(description = " DTO")
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode
@ToString
@Validated
@Builder
public class PgUserActionDTO implements Serializable {

    @Schema(title = "用户行为状态", type = "string")
    @NotNull(message="[用户行为状态]不能为空", groups = {UpdateGroup.class})
    private Long id;

    @Schema(title = "用户id")
    private Long userId;

    @Schema(title = "行为类型")
    private UserActionType type;

    @Schema(title = "状态")
    private Integer status;

    @Schema(title = "创建时间")
    private Date createTime;

    @Schema(title = "更新时间")
    private Date updateTime;

}
