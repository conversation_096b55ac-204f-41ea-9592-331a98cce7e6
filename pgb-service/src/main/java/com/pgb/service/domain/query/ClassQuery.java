package com.pgb.service.domain.query;

import com.pgb.common.mybatis.global.PageQuery;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * Created by 2024/12/10 17:25
 */
@EqualsAndHashCode(callSuper = true)
@Schema(description = "班级 搜索实体")
@Data
public class ClassQuery extends PageQuery {

    @Schema(title = "班级名称")
    private String name;
}
