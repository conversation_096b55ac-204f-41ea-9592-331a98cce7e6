package com.pgb.common.tenant.config;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.InnerInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.TenantLineInnerInterceptor;
import com.pgb.common.tenant.TenantContext;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.ArrayList;
import java.util.List;

@Configuration
public class TenantConfig {
    @Bean
    public boolean tenantInit(MybatisPlusInterceptor interceptor) {

        // ------- 多租户插件 -------
        List<InnerInterceptor> interceptors = new ArrayList<>();
        // 务必先添加 租户上下文，否则不生效
        interceptors.add(new TenantLineInnerInterceptor(new TenantContext()));
        // 添加其他插件
        interceptors.addAll(interceptor.getInterceptors());

        interceptor.setInterceptors(interceptors);
        return true;
    }
}
