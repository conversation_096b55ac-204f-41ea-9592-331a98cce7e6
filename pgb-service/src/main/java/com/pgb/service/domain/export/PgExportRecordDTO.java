package com.pgb.service.domain.export;

import java.io.Serializable;

import java.util.Date;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.ToString;
import lombok.Builder;
import org.springframework.validation.annotation.Validated;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import com.pgb.common.core.validate.UpdateGroup;

/**
*
* @TableName pg_export_record
*/
@Schema(description = " DTO")
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode
@ToString
@Validated
@Builder
public class PgExportRecordDTO implements Serializable {

    @Schema(title = "导出记录", type = "string")
    @NotNull(message="[导出记录]不能为空", groups = {UpdateGroup.class})
    private Long id;

    @Schema(title = "用户id")
    private Long userId;

    @Schema(title = "导出篇数")
    private Integer totalNum;

    @Schema(title = "创建时间")
    private Date createTime;

    @Schema(title = "导出时间")
    private Date exportTime;

    @Schema(title = "导出状态")
    private Integer status;

    @Schema(title = "压缩包地址")
    @Size(max= 255)
    private String zipUrl;

    @Schema(title = "导出md5")
    @Size(max= 255)
    private String md5;

    @Schema(title = "导出id列表，用分号分隔")
    @Size(max= -1)
    private String answerIds;

}
