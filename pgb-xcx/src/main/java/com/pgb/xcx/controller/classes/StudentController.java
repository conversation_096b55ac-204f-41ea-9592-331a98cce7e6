package com.pgb.xcx.controller.classes;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.comparator.PinyinComparator;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.ReUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.pgb.common.core.global.BaseResult;
import com.pgb.common.core.global.GlobalCode;
import com.pgb.service.db.PgAnswerService;
import com.pgb.service.db.PgClassesService;
import com.pgb.service.db.PgStudentService;
import com.pgb.service.domain.answer.PgAnswer;
import com.pgb.service.domain.classes.JoinClassForm;
import com.pgb.service.domain.classes.PgClasses;
import com.pgb.service.domain.query.StudentQuery;
import com.pgb.service.domain.student.PgStudent;
import com.pgb.service.domain.student.PgStudentDTO;
import com.pgb.service.domain.student.PgStudentVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * Created by 2024/12/10 18:17
 */

@Tag(name = "用户端/班级/学生管理")
@RestController("UserClassStudentController")
@Transactional(rollbackFor = Exception.class)
@RequestMapping("/user/class/student")
@RequiredArgsConstructor
@Slf4j
public class StudentController {

    private final PgClassesService pgClassesService;

    private final PgStudentService pgStudentService;

    private final PgAnswerService pgAnswerService;

    @Operation(summary = "查看班级学生列表")
    @PostMapping("list/{classId}")
    public BaseResult<List<PgStudentVO>> list(@PathVariable Long classId, @RequestBody StudentQuery query) {

        // 判断班级是否存在
        PgClasses pgClasses = pgClassesService.getById(classId);

        if (ObjectUtil.isNull(pgClasses)) {
            // 班级不存在
            return BaseResult.error("班级不存在");
        }
        List<PgStudentVO> list = pgStudentService.list(new LambdaQueryWrapper<PgStudent>()
                        .like(StrUtil.isNotBlank(query.getStudentName()), PgStudent::getName, query.getStudentName())
                        .eq(PgStudent::getClassId, classId)
                        .orderByDesc(PgStudent::getCreateTime))
                .stream()
                .map(pgStudent -> BeanUtil.copyProperties(pgStudent, PgStudentVO.class))
                .toList();

        // 执行排序
        return BaseResult.success(
                pgStudentService.sortByNoName(list)
        );

    }

    @Operation(summary = "添加班级学生")
    @PostMapping("addStudent/{classId}")
    public BaseResult<Boolean> addStudent(@PathVariable Long classId, @RequestBody PgStudentDTO studentDTO) {

        // 判断班级是否存在
        PgClasses pgClasses = pgClassesService.getById(classId);

        if (ObjectUtil.isNull(pgClasses)) {
            return BaseResult.code(GlobalCode.Item_Null);
        }

        // 学号唯一
        if (StrUtil.isNotBlank(studentDTO.getStudentNo())) {
            if (pgStudentService.exists(new LambdaQueryWrapper<PgStudent>()
                    .eq(PgStudent::getClassId, classId)
                    .eq(PgStudent::getStudentNo, studentDTO.getStudentNo()))) {
                return BaseResult.error("当前学号已存在");
            }
        }

        // 名称
//        if (StrUtil.isNotBlank(studentDTO.getName())) {
//            studentDTO.setName(ReUtil.replaceAll(studentDTO.getName(), "[^a-zA-Z0-9\\u4e00-\\u9fa5]", ""));
//        }

        if (StrUtil.isBlank(studentDTO.getName())) {
            return BaseResult.error("学生姓名必填，仅支持汉字、数字和字母");
        }

        // 姓名去掉首尾空格
        studentDTO.setName(StrUtil.trim(studentDTO.getName()));
        PgStudent student = BeanUtil.copyProperties(studentDTO, PgStudent.class);

        student.setCreateTime(new Date());
        student.setClassId(classId);
        pgStudentService.save(student);

        return BaseResult.success(true);
    }

    @Operation(summary = "查看学生详情")
    @PostMapping("{id}")
    public BaseResult<PgStudentDTO> get(@PathVariable Long id) {

        PgStudent pgStudent = pgStudentService.getById(id);

        if (ObjectUtil.isNull(pgStudent)) {
            return BaseResult.code(GlobalCode.Item_Null);
        }
        PgStudentDTO studentDTO = BeanUtil.copyProperties(pgStudent, PgStudentDTO.class);

        return BaseResult.success(studentDTO);
    }

    @Operation(summary = "删除学生")
    @DeleteMapping("{id}")
    public BaseResult<Boolean> delete(@PathVariable Long id) {

        PgStudent pgStudent = pgStudentService.getById(id);

        if (ObjectUtil.isNull(pgStudent)) {
            return BaseResult.code(GlobalCode.Item_Null);
        }

        // 假删除学生对应的作文
        List<PgAnswer> answers = pgAnswerService.list(new LambdaQueryWrapper<PgAnswer>()
                .select(PgAnswer::getId, PgAnswer::getDeleted)
                .eq(PgAnswer::getStudentId, id));
        answers.forEach(answer -> answer.setDeleted(true));
        pgAnswerService.updateBatchById(answers);

        // 删除学生
        pgStudentService.removeById(id);

        return BaseResult.success(true);
    }

    @Operation(summary = "修改学生信息")
    @PutMapping("{id}")
    public BaseResult<Boolean> update(@PathVariable Long id, @RequestBody PgStudentDTO pgStudentDTO) {

        PgStudent pgStudent = pgStudentService.getById(id);

        if (ObjectUtil.isNull(pgStudent)) {
            return BaseResult.code(GlobalCode.Item_Null);
        }

        // 学号唯一
        if (StrUtil.isNotBlank(pgStudentDTO.getStudentNo())) {
            if (pgStudentService.exists(new LambdaQueryWrapper<PgStudent>()
                    .ne(PgStudent::getId, id)
                    .eq(PgStudent::getClassId, pgStudent.getClassId())
                    .eq(PgStudent::getStudentNo, pgStudentDTO.getStudentNo()))) {
                return BaseResult.error("当前学号已存在");
            }
        }

        // 姓名去掉首尾空格
        if (StrUtil.isNotBlank(pgStudentDTO.getName())){
            pgStudentDTO.setName(StrUtil.trim(pgStudentDTO.getName()));
        }

        pgStudent = BeanUtil.copyProperties(pgStudentDTO, PgStudent.class);

        pgStudentService.updateById(pgStudent);

        return BaseResult.success(true);
    }

    public record AddStudentBatchForm(String studentsInfo) {
    }

    @Operation(summary = "批量添加学生", description = "格式：每一行一个学生，姓名[空格]学号，学号选填")
    @PostMapping("addStudentBatch/{classId}")
    public BaseResult<Boolean> addStudentBatch(@PathVariable Long classId, @RequestBody AddStudentBatchForm form) {

        List<PgStudent> students = new ArrayList<>();
        // 按换行符分割出一个个学生
        String[] split = form.studentsInfo().split("\n");

        for (String studentInfo : split) {

            String[] parts = studentInfo.split("\\s+");

            // 跳过格式不正确的
            if (parts.length < 1 || parts.length > 2) {
                continue;
            }

            PgStudent student = new PgStudent();
            // 姓名
            student.setName(parts[0].trim());

            if (parts.length == 2) {

                // 学号唯一
                if (pgStudentService.exists(new LambdaQueryWrapper<PgStudent>()
                        .eq(PgStudent::getClassId, classId)
                        .eq(PgStudent::getStudentNo, parts[1]))) {

                    continue;
                } else {
                    // 学号
                    student.setStudentNo(parts[1]);
                }
            }
            student.setCreateTime(new Date());
            student.setClassId(classId);

            students.add(student);

        }
        pgStudentService.saveBatch(students);

        return BaseResult.success(true);
    }

}
