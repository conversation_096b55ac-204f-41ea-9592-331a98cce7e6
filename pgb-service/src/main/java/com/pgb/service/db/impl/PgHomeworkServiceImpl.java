package com.pgb.service.db.impl;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.pgb.service.db.*;
import com.pgb.service.domain.answer.PgAnswer;
import com.pgb.service.domain.classes.PgClasses;
import com.pgb.service.domain.homework.PgHomework;
import com.pgb.service.domain.homework.PgHomeworkVO;
import com.pgb.service.domain.question.PgQuestionDTO;
import com.pgb.service.domain.student.PgStudent;
import com.pgb.service.domain.zc.answer.PgZcAnswer;
import com.pgb.service.enums.CorrectStatusEnum;
import com.pgb.service.enums.GradeEnum;
import com.pgb.service.enums.SubjectEnum;
import com.pgb.service.enums.WritingStyleEnum;
import com.pgb.service.mapper.PgAnswerMapper;
import com.pgb.service.mapper.PgHomeworkMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【pg_homework(作业表)】的数据库操作Service实现
 * @createDate 2024-12-10 19:36:38
 */
@Service
@RequiredArgsConstructor
public class PgHomeworkServiceImpl extends ServiceImpl<PgHomeworkMapper, PgHomework>
        implements PgHomeworkService {

    private final PgAnswerMapper pgAnswerMapper;

    private final PgZcAnswerService pgZcAnswerService;

    private final PgClassesService pgClassesService;

    private final PgStudentService pgStudentService;

    @Override
    public void createSampleHomework(Long userId, Long classId) {

        PgHomework homework = new PgHomework();
        homework.setCreatorId(userId);
        homework.setName("示例作业");

        // 默认题目
        PgQuestionDTO question = new PgQuestionDTO();
        question.setName("默认");
        question.setCorrectRequest("默认");
        question.setWritingRequest("默认");
        question.setGrade(GradeEnum.GRADE_3);
        question.setSubject(SubjectEnum.Chinese);
        question.setWordNum(300);
        question.setStyle(WritingStyleEnum.Other);
        question.setScore(30);
        homework.setQuestionInfo(question);

        homework.setCreateTime(new Date());
        homework.setClassId(classId);
        save(homework);

    }


    @Override
    public PgHomeworkVO convertToHomeworkVO(PgHomework homework) {
        PgHomeworkVO homeworkVO = BeanUtil.copyProperties(homework, PgHomeworkVO.class);

        // 所属班级信息
        PgClasses classes = pgClassesService.getById(homework.getClassId());
        if (ObjectUtil.isNotNull(classes)) {
            homeworkVO.setClassName(classes.getName());
            homeworkVO.setGrade(classes.getGrade());
            homeworkVO.setClassNum(classes.getClassNum());
        }

        List<Long> studentIds = pgStudentService.list(new LambdaQueryWrapper<PgStudent>()
                        .eq(PgStudent::getClassId, homework.getClassId())
                )
                .stream()
                .map(PgStudent::getId)
                .toList();

        if (studentIds.isEmpty()) {
            return homeworkVO;
        }

        // 字词
        if (homework.getType() == 1) {
            List<PgZcAnswer> zcAnswers = pgZcAnswerService.list(new LambdaQueryWrapper<PgZcAnswer>()
                    .select(PgZcAnswer::getId, PgZcAnswer::getStatus, PgZcAnswer::getStudentId)
                    .eq(PgZcAnswer::getDeleted, false)
                    .eq(PgZcAnswer::getZcHomeworkId, homework.getId())
                    .in(PgZcAnswer::getStudentId, studentIds));
            zcAnswers = CollUtil.distinct(zcAnswers, PgZcAnswer::getStudentId, false);
            List<Long> submitStudentIds = zcAnswers.stream().map(PgZcAnswer::getStudentId).toList();

            // 【已交】
            homeworkVO.setSubmitNum(zcAnswers.size());
            // 【未交】
            homeworkVO.setUnSubmitNum(
                    (int) studentIds.stream().filter(id -> !submitStudentIds.contains(id)).count()
            );
            //【已批阅】
            homeworkVO.setCorrectedNum((int) zcAnswers.stream().filter(zcAnswer -> !zcAnswer.getStatus().equals(CorrectStatusEnum.Uploaded)).count());
        }
        // 作文
        else {
            List<PgAnswer> answers = pgAnswerMapper.selectList(new LambdaQueryWrapper<PgAnswer>()
                    .select(PgAnswer::getId, PgAnswer::getStatus, PgAnswer::getStudentId)
                    .eq(PgAnswer::getDeleted, false)
                    .eq(PgAnswer::getHomeworkId, homework.getId())
                    .in(PgAnswer::getStudentId, studentIds)
            );

            answers = CollUtil.distinct(answers, PgAnswer::getStudentId, false);
            List<Long> submitStudentIds = answers.stream().map(PgAnswer::getStudentId).toList();

            // 【已交】
            homeworkVO.setSubmitNum(answers.size());
            // 【未交】
            homeworkVO.setUnSubmitNum(
                    (int) studentIds.stream().filter(id -> !submitStudentIds.contains(id)).count()
            );
            //【已批阅】
            homeworkVO.setCorrectedNum((int) answers.stream().filter(answer -> !answer.getStatus().equals(CorrectStatusEnum.Uploaded)).count());
        }

        // 【总数】
        homeworkVO.setTotalNum(studentIds.size());

        return homeworkVO;
    }

}




