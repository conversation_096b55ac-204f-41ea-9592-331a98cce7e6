package com.pgb.service.domain.homework;

import java.io.Serializable;

import java.util.Date;

import cn.hutool.json.JSONObject;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.ToString;
import lombok.Builder;
import org.springframework.validation.annotation.Validated;
import jakarta.validation.constraints.NotNull;
import com.pgb.common.core.validate.UpdateGroup;

/**
*
* @TableName pg_homework_report
*/
@Schema(description = " DTO")
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode
@ToString
@Validated
@Builder
public class PgHomeworkReportDTO implements Serializable {

    @Schema(title = "班级作业报告", type = "string")
    @NotNull(message="[班级作业报告]不能为空", groups = {UpdateGroup.class})
    private Long id;

    @Schema(title = "作业id")
    private Long homeworkId;

    @Schema(title = "生成用户id")
    private Long userId;

    @Schema(title = "生成状态")
    private Integer status;

    @Schema(title = "生成的报告内容")
    private JSONObject report;

    @Schema(title = "创建时间")
    private Date createTime;

    @Schema(title = "完成时间")
    private Date completeTime;

    @Schema(title = "生成时间，单位：秒")
    private Integer duration;

}
