package com.pgb.service.db.impl;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.pgb.common.redis.QueueUtils;
import com.pgb.service.domain.GlobQueueConstants;
import com.pgb.service.domain.homework.PgHomeworkGridPaper;
import com.pgb.service.db.PgHomeworkGridPaperService;
import com.pgb.service.enums.CorrectStatusEnum;
import com.pgb.service.mapper.PgHomeworkGridPaperMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
* <AUTHOR>
* @description 针对表【pg_homework_grid_paper】的数据库操作Service实现
* @createDate 2025-03-09 19:32:27
*/
@Service
@Slf4j
public class PgHomeworkGridPaperServiceImpl extends ServiceImpl<PgHomeworkGridPaperMapper, PgHomeworkGridPaper>
    implements PgHomeworkGridPaperService{

    @Override
    public Integer queryToExport() {
        // 查询超过7分钟没有处理的
        List<PgHomeworkGridPaper> todoList = list(new LambdaQueryWrapper<PgHomeworkGridPaper>()
                .eq(PgHomeworkGridPaper::getStatus, CorrectStatusEnum.Uploaded)
                .le(PgHomeworkGridPaper::getCreateTime, DateUtil.offsetMinute(new Date(), -7))
        );

        for (PgHomeworkGridPaper record : todoList) {
            // 加入队列
            if (!QueueUtils.containsQueueObject(GlobQueueConstants.PGB_XCX_GRID_PAPER_DETECTION_QUEUE.name(), record.getId())) {
                QueueUtils.addQueueObject(GlobQueueConstants.PGB_XCX_GRID_PAPER_DETECTION_QUEUE.name(), record.getId());
                log.info("当前格子纸记录加入队列：{}", record.getId());
            } else {
                log.info("当前格子纸记录存在队列中：{}，跳过", record.getId());
            }
        }

        return todoList.size();
    }
}




