package com.pgb.service.domain.homework;

import java.io.Serializable;

import java.util.Date;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.ToString;
import org.springframework.validation.annotation.Validated;
import jakarta.validation.constraints.NotNull;

/**
* 
* @TableName pg_homework_grid_paper
*/
@Schema(description = " DTO")
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode
@ToString
@Validated
public class PgHomeworkGridPaperDTO implements Serializable {

    @Schema(title = "格子纸批量上传")
    @NotNull(message="[格子纸批量上传]不能为空")
    private Long id;

    @Schema(title = "用户id")
    private Long userId;

    @Schema(title = "作业id")
    private Long homeworkId;

    @Schema(title = "当前处理状态")
    private Integer status;

    @Schema(title = "检测及分割结果，对应哪个学生")
    private Object imgResultJson;

    @Schema(title = "创建时间")
    private Date createTime;

    @Schema(title = "完成状态")
    private Date completeTime;

    @Schema(title = "处理时长，秒")
    private Integer duration;

    @Schema(title = "消耗token")
    private Integer tokens;

}
