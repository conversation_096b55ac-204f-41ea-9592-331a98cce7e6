package com.pgb.manage.service.saas.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 *
 * @TableName system_role
 */
@TableName(value ="system_role")
@Data
public class SystemRole implements Serializable {
    /**
     * 角色id
     */
    @TableId
    private Long id;

    /**
     * 角色名称
     */
    private String name;

    /**
     * 权限列表，用，分割权限id
     */
    private String permissions;

    /**
     * 是否是超级管理员
     */
    private Boolean isSuper;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 是否是系统预设角色
     */
    private Boolean isSystem;

    /**
     * 角色介绍
     */
    private String description;

    /**
     * 系统分类，0：管理系统，1：SASS系统
     */
    private Integer systemType;
}
