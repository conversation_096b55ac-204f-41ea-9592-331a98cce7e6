package com.pgb.service.domain.distribution.agency;

import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import com.fasterxml.jackson.annotation.JsonGetter;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * Created by 2025/2/28 14:43
 */
@Schema(title = " 佣金统计情况 ")
@Data
public class CommissionInfoDTO {

    @Schema(title = "绑定人数")
    private Integer bindUserNum;

    @Schema(title = "待入账", description = "单位是 分")
    private Integer unsettledCommission;

    @Schema(title = "累计佣金", description = "单位是 分")
    private Integer totalCommission;

    @Schema(title = "待入账（元）")
    @JsonGetter
    public String getUnsettledCommissionStr() {
        return NumberUtil.decimalFormat("0.00", ObjectUtil.isNotNull(this.unsettledCommission) ? this.unsettledCommission / 100.0 : 0);
    }

    @Schema(title = "累计佣金（元）")
    @JsonGetter
    public String getTotalCommissionStr() {
        return NumberUtil.decimalFormat("0.00", ObjectUtil.isNotNull(this.totalCommission) ? this.totalCommission / 100.0 : 0);
    }
}
