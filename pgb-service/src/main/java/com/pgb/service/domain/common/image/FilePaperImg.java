package com.pgb.service.domain.common.image;

import cn.hutool.core.img.ImgUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.URLUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.fasterxml.jackson.annotation.JsonGetter;
import com.pgb.common.oss.service.OssService;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import org.springframework.validation.annotation.Validated;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.IOException;

@Schema(description = "用户 文档试卷 单个图片/页面 DTO")
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode
@ToString
@Validated
@Builder
public class FilePaperImg {
    @Schema(title = "上传的图片url")
    private String imgUrl;

    @Schema(title = "图片高度属性", description = "注意，这个一定是原图的高度")
    private Integer imgH;

    @Schema(title = "图片所属属性", description = "注意，这个一定是原图的宽度")
    private Integer imgW;

    @Schema(title = "朝向", description = "-1:未定义，0:正向，1: 逆时针90度，2:逆时针180度，3:逆时针270度")
    private Integer direction;

    @Schema(title = "获取朝向正常信息")
    @JsonGetter
    public String getImgRotateUrl() {

        if (ObjectUtil.isNull(this.direction)) {
            return this.imgUrl;
        }

        if (this.direction.equals(0) || this.direction.equals(-1)) {
            return this.imgUrl;
        }

        if (this.direction.equals(1)) {
            return this.imgUrl + "?imageMogr2/rotate/270";
        }
        else if (this.direction.equals(2)) {
            return this.imgUrl + "?imageMogr2/rotate/180";
        }
        else if (this.direction.equals(3)) {
            return this.imgUrl + "?imageMogr2/rotate/90";
        }

        return this.imgUrl;
    }

    @Schema(title = "获取压缩后的url")
    @JsonGetter
    public String getResizeUrl() {
        OssService service = SpringUtil.getBean(OssService.class);
        return service.getResizeUrl(this.imgUrl);
    }

    // ----- 渲染相关
    @Schema(title = "画布高度", description = "这是根据旁批，原图高度等算出来的")
    private Integer canvasH;

    @Schema(title = "画布宽度", description = "是一个固定宽度，规则设置为固定1300")
    private Integer canvasW = 1300;
}
