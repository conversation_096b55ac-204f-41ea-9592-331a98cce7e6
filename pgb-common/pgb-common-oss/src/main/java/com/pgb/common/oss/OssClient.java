package com.pgb.common.oss;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.img.ImgUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.io.file.FileNameUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.URLUtil;
import com.amazonaws.ClientConfiguration;
import com.amazonaws.HttpMethod;
import com.amazonaws.Protocol;
import com.amazonaws.auth.*;
import com.amazonaws.client.builder.AwsClientBuilder;
import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.AmazonS3Client;
import com.amazonaws.services.s3.AmazonS3ClientBuilder;
import com.amazonaws.services.s3.model.*;
import com.pgb.common.core.global.MaterialProperty;
import com.pgb.common.core.utils.FileUtils;
import com.pgb.common.oss.constant.OssConstant;
import com.pgb.common.oss.domain.*;
import com.pgb.common.oss.enumd.AccessPolicyType;
import com.pgb.common.oss.enumd.PolicyType;
import com.pgb.common.oss.exception.OssException;
import lombok.Getter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.multipart.MultipartFile;

import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.InputStream;
import java.net.URL;
import java.util.Date;

/**
 * S3 存储协议 所有兼容S3协议的云厂商均支持
 * 阿里云 腾讯云 七牛云 minio
 *
 * <AUTHOR> Li
 */
@Getter
public class OssClient {

    private static final Logger log = LoggerFactory.getLogger(OssClient.class);
    private final String configKey;

    private final OssProperties properties;

    private final AmazonS3 client;

    public OssClient(String configKey, OssProperties ossProperties) {
        this.configKey = configKey;
        this.properties = ossProperties;
        try {
            AwsClientBuilder.EndpointConfiguration endpointConfig =
                new AwsClientBuilder.EndpointConfiguration(properties.getEndpoint(), properties.getRegion());

            // 构建访问凭证
            AWSCredentials credentials = new BasicAWSCredentials(properties.getAccessKey(), properties.getSecretKey());
            AWSCredentialsProvider credentialsProvider = new AWSStaticCredentialsProvider(credentials);

            // 配置
            ClientConfiguration clientConfig = new ClientConfiguration();
            if (properties.getIsHttps()) {
                // - SSL 协议
                clientConfig.setProtocol(Protocol.HTTPS);
            } else {
                clientConfig.setProtocol(Protocol.HTTP);
            }
            AmazonS3ClientBuilder build = AmazonS3Client.builder()
                .withEndpointConfiguration(endpointConfig)
                .withClientConfiguration(clientConfig)
                .withCredentials(credentialsProvider)
                .disableChunkedEncoding();

            // 桶放前面的域名，都是虚拟托管URL，minio是路径样式访问
            if (!StrUtil.containsAny(properties.getEndpoint(), OssConstant.CLOUD_SERVICE)) {
                // minio 设置路径访问样式
                build.enablePathStyleAccess();
            }

            this.client = build.build();

            // 预创建，以防没有创建桶
            createBucket();
        } catch (Exception e) {
            if (e instanceof OssException) {
                throw e;
            }
            throw new OssException("配置错误! 请检查系统配置:[" + e.getMessage() + "]");
        }
    }

    public void createBucket() {
        try {
            String bucketName = properties.getBucketName();
            if (client.doesBucketExistV2(bucketName)) {
                return;
            }
            CreateBucketRequest createBucketRequest = new CreateBucketRequest(bucketName);
            AccessPolicyType accessPolicy = getAccessPolicy();
            createBucketRequest.setCannedAcl(accessPolicy.getAcl());
            client.createBucket(createBucketRequest);
            client.setBucketPolicy(bucketName, getPolicy(bucketName, accessPolicy.getPolicyType()));
        } catch (Exception e) {
            throw new OssException("创建Bucket失败, 请核对配置信息:[" + e.getMessage() + "]");
        }
    }

    public UploadResult uploadFile(byte[] data, String fullPath, String contentType) {
        return uploadFile(new ByteArrayInputStream(data), fullPath, contentType);
    }

    private UploadResult uploadFile(InputStream inputStream, String fullPath, String contentType) {
        if (!(inputStream instanceof ByteArrayInputStream)) {
            inputStream = new ByteArrayInputStream(IoUtil.readBytes(inputStream));
        }
        try {
            ObjectMetadata metadata = new ObjectMetadata();
            metadata.setContentType(contentType);
            metadata.setContentLength(inputStream.available());
            PutObjectRequest putObjectRequest = new PutObjectRequest(properties.getBucketName(), fullPath, inputStream, metadata);
            // 设置上传对象的 Acl 为公共读
            putObjectRequest.setCannedAcl(getAccessPolicy().getAcl());
            client.putObject(putObjectRequest);
        } catch (Exception e) {
            throw new OssException("上传文件失败，请检查配置信息:[" + e.getMessage() + "]");
        }
        return UploadResult.builder().url(getUrl() + "/" + fullPath).filename(fullPath).build();
    }

    private UploadResult uploadFile(File file, String fullPath) {
        try {
            PutObjectRequest putObjectRequest = new PutObjectRequest(properties.getBucketName(), fullPath, file);
            // 设置上传对象的 Acl 为公共读
            putObjectRequest.setCannedAcl(getAccessPolicy().getAcl());
            client.putObject(putObjectRequest);
        } catch (Exception e) {
            throw new OssException("上传文件失败，请检查配置信息:[" + e.getMessage() + "]");
        }
        return UploadResult.builder().url(getUrl() + "/" + fullPath).filename(fullPath).build();
    }

    /**
     *
     * @param bytes
     * @param prefix
     * @param suffix
     * @return
     */
    public UploadFileDTO uploadAudio(byte[] bytes, String prefix, String suffix) {
        // 拼接路径
        String fullPath;
        if (prefix.endsWith("/")) {
            fullPath = prefix + IdUtil.fastSimpleUUID() + "." + suffix;
        } else {
            fullPath = prefix + "/" + IdUtil.fastSimpleUUID() + "." + suffix;
        }

        String contentType = "audio/wav";

        if (suffix.equals("mp3")) {
            contentType = "audio/mpeg";
        }

        // 执行上传
        UploadResult result = uploadFile(bytes, fullPath, contentType);

        // 文件属性
        MaterialProperty materialProperty = new MaterialProperty();
        materialProperty.setSuffix(suffix);

        // 组装返回
        return UploadFileDTO.builder()
                .url(result.getUrl())
                .size((long) bytes.length)
                .keyValue(fullPath)
                .configKey(this.configKey)
                .materialProperty(materialProperty)
                .cdnUrl(URLUtil.completeUrl(properties.getDomain(), fullPath))
                .build();
    }


    /**
     * 根据 prefix 上传 buffer 内存缓冲区图片
     * @param image
     * @param prefix
     * @return
     */
    public UploadFileDTO upload(BufferedImage image, String prefix) {

        String suffix = "png";

        // 拼接路径
        String fullPath;
        if (prefix.endsWith("/")) {
            fullPath = prefix + IdUtil.fastSimpleUUID() + "." + suffix;
        } else {
            fullPath = prefix + "/" + IdUtil.fastSimpleUUID() + "." + suffix;
        }

        byte[] bytes = ImgUtil.toBytes(image, "png");

        // 执行上传
        UploadResult result = uploadFile(bytes, fullPath, "image/png");

        // 文件属性
        MaterialProperty materialProperty = new MaterialProperty();

        // 图片形式，自动获取属性
        materialProperty.setHeight((long) image.getHeight());
        materialProperty.setWidth((long) image.getWidth());

        // 组装返回
        return UploadFileDTO.builder()
                .url(result.getUrl())
                .size((long) bytes.length)
                .keyValue(fullPath)
                .configKey(this.configKey)
                .materialProperty(materialProperty)
                .cdnUrl(URLUtil.completeUrl(properties.getDomain(), fullPath))
                .build();
    }

    /**
     * 根据 prefix，uuid 拼接，上传保存的文件
     * @param multipartFile
     * @param prefix
     * @return
     */
    public UploadFileDTO upload(MultipartFile multipartFile, String prefix) {
        // 转换为 File 类型
        return upload(FileUtils.transToFile(multipartFile), prefix);
    }

    /**
     * 根据 prefix，uuid 拼接，上传保存的文件
     * @param file
     * @param prefix
     * @return
     */
    public UploadFileDTO upload(File file, String prefix) {
        // 获取文件后缀
        String suffix = FileNameUtil.getSuffix(file);

        // 拼接路径
        String fullPath;
        if (prefix.endsWith("/")) {
            fullPath = prefix + IdUtil.fastSimpleUUID() + "." + suffix;
        } else {
            fullPath = prefix + "/" + IdUtil.fastSimpleUUID() + "." + suffix;
        }

        // 执行上传
        UploadResult result = uploadFile(file, fullPath);

        // 文件属性
        MaterialProperty materialProperty = new MaterialProperty();

        // 如果是图片形式，自动获取属性
        if(FileNameUtil.isType(file.getName(), "png", "jpg", "jpeg")) {
            BufferedImage image = ImgUtil.read(file);
            materialProperty.setHeight((long) image.getHeight());
            materialProperty.setWidth((long) image.getWidth());
        }

        // 组装返回
        return UploadFileDTO.builder()
                .url(result.getUrl())
                .size(FileUtil.size(file))
                .keyValue(fullPath)
                .configKey(this.configKey)
                .materialProperty(materialProperty)
                .cdnUrl(URLUtil.completeUrl(properties.getDomain(), fullPath))
                .build();
    }

    /**
     * 删除文件
     * @param path
     */
    public void delete(String path) {
        path = path.replace(getUrl() + "/", "");
        try {
            client.deleteObject(properties.getBucketName(), path);
        } catch (Exception e) {
            throw new OssException("删除文件失败，请检查配置信息:[" + e.getMessage() + "]");
        }
    }

    public String getUrl() {
        String domain = properties.getDomain();
        String endpoint = properties.getEndpoint();
        String header = properties.getIsHttps() ? "https://" : "http://";

        // 云服务商直接返回
        if (StrUtil.containsAny(endpoint, OssConstant.CLOUD_SERVICE)) {
            return header + properties.getBucketName() + "." + endpoint;
        }

        // minio 单独处理
        if (StrUtil.isNotBlank(domain)) {
            return header + domain + "/" + properties.getBucketName();
        }

        return header + endpoint + "/" + properties.getBucketName();
    }

    /**
     * 获取私有URL链接
     *
     * @param objectKey 对象KEY
     * @param second    授权时间
     */
    public String getPrivateUrl(String objectKey, Integer second) {
        GeneratePresignedUrlRequest generatePresignedUrlRequest =
            new GeneratePresignedUrlRequest(properties.getBucketName(), objectKey)
                .withMethod(HttpMethod.GET)
                .withExpiration(new Date(System.currentTimeMillis() + 1000L * second));
        URL url = client.generatePresignedUrl(generatePresignedUrlRequest);
        return url.toString();
    }

    /**
     * 检查配置是否相同
     */
    public boolean checkPropertiesSame(OssProperties properties) {
        return this.properties.equals(properties);
    }

    /**
     * 获取当前桶权限类型
     *
     * @return 当前桶权限类型code
     */
    public AccessPolicyType getAccessPolicy() {
        return properties.getAccessPolicy();
    }

    private static String getPolicy(String bucketName, PolicyType policyType) {
        StringBuilder builder = new StringBuilder();
        builder.append("{\n\"Statement\": [\n{\n\"Action\": [\n");
        builder.append(switch (policyType) {
            case WRITE -> "\"s3:GetBucketLocation\",\n\"s3:ListBucketMultipartUploads\"\n";
            case READ_WRITE -> "\"s3:GetBucketLocation\",\n\"s3:ListBucket\",\n\"s3:ListBucketMultipartUploads\"\n";
            default -> "\"s3:GetBucketLocation\"\n";
        });
        builder.append("],\n\"Effect\": \"Allow\",\n\"Principal\": \"*\",\n\"Resource\": \"arn:aws:s3:::");
        builder.append(bucketName);
        builder.append("\"\n},\n");
        if (policyType == PolicyType.READ) {
            builder.append("{\n\"Action\": [\n\"s3:ListBucket\"\n],\n\"Effect\": \"Deny\",\n\"Principal\": \"*\",\n\"Resource\": \"arn:aws:s3:::");
            builder.append(bucketName);
            builder.append("\"\n},\n");
        }
        builder.append("{\n\"Action\": ");
        builder.append(switch (policyType) {
            case WRITE -> "[\n\"s3:AbortMultipartUpload\",\n\"s3:DeleteObject\",\n\"s3:ListMultipartUploadParts\",\n\"s3:PutObject\"\n],\n";
            case READ_WRITE -> "[\n\"s3:AbortMultipartUpload\",\n\"s3:DeleteObject\",\n\"s3:GetObject\",\n\"s3:ListMultipartUploadParts\",\n\"s3:PutObject\"\n],\n";
            default -> "\"s3:GetObject\",\n";
        });
        builder.append("\"Effect\": \"Allow\",\n\"Principal\": \"*\",\n\"Resource\": \"arn:aws:s3:::");
        builder.append(bucketName);
        builder.append("/*\"\n}\n],\n\"Version\": \"2012-10-17\"\n}\n");
        return builder.toString();
    }

    /**
     * 获取预签名上传URL，指定上传路径，包含上传参数
     * @param fullPath
     * @return
     */
    public URL getPreSignedUrl(String fullPath) {
        return client.generatePresignedUrl(properties.getBucketName(), fullPath, DateUtil.offsetHour(new Date(), 3), HttpMethod.PUT);
    }

}
