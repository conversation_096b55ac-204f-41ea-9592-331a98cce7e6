package com.pgb.student.controller.student;

import cn.dev33.satoken.annotation.SaCheckLogin;
import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.URLUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.pgb.common.core.global.BaseResult;
import com.pgb.common.core.global.GlobalCode;
import com.pgb.common.oss.service.OssService;
import com.pgb.service.db.PgStudentService;
import com.pgb.service.domain.student.PgStudent;
import com.pgb.service.enums.IdentityTypeEnum;
import com.pgb.student.domain.PgFamily;
import com.pgb.student.domain.PgUserStudent;
import com.pgb.student.domain.dto.PgUserStudentDTO;
import com.pgb.student.domain.vo.PgUserStudentVO;
import com.pgb.student.service.PgFamilyService;
import com.pgb.student.service.PgUserStudentService;
import com.pgb.student.service.PgbCustomService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * Created by 2025/2/26 17:20
 */
@Tag(name = "学生端/学生/管理")
@RestController("StudentManageController")
//@Transactional(rollbackFor = Exception.class)
@RequestMapping("/student/manage")
@RequiredArgsConstructor
@Slf4j
public class StudentController {

    private final PgUserStudentService pgUserStudentService;

    private final PgbCustomService pgbCustomService;

    private final PgFamilyService pgFamilyService;
    private final OssService ossService;

    @Operation(summary = "获取当前用户在某班级下的孩子列表")
    @PostMapping("students/{classId}")
    @SaCheckLogin
    public BaseResult<List<PgUserStudent>> list(@PathVariable Long classId) {

        // 获取绑定亲情号的学生id
        List<Long> studentIds = pgFamilyService.list(new LambdaQueryWrapper<PgFamily>()
                        .eq(PgFamily::getInviteUserId, StpUtil.getLoginIdAsLong()))
                .stream()
                .map(PgFamily::getStudentId)
                .toList();

        // 获取当前用户关联的学生
        List<Long> userStudentIds = pgUserStudentService.list(new LambdaQueryWrapper<PgUserStudent>()
                        .and(i -> i.eq(PgUserStudent::getUserId, StpUtil.getLoginIdAsLong())
                                .or()
                                .in(CollUtil.isNotEmpty(studentIds), PgUserStudent::getId, studentIds))

                )
                .stream()
                .map(PgUserStudent::getId)
                .toList();

        if (userStudentIds.isEmpty()) {
            return BaseResult.success(new ArrayList<>());
        }

        List<Long> studentUserIds = pgbCustomService.getStudentsByClassId(classId, userStudentIds)
                .stream()
                .map(PgStudent::getStudentUserId)
                .toList();

        if (studentUserIds.isEmpty()) {
            return BaseResult.success(new ArrayList<>());
        }

        List<PgUserStudent> list = pgUserStudentService.list(new LambdaQueryWrapper<PgUserStudent>()
                .in(PgUserStudent::getId, studentUserIds)
        );

        return BaseResult.success(list);
    }

    @Operation(summary = "根据班级id查看当前用户的学生信息")
    @GetMapping("info/{classId}")
    public BaseResult<PgStudent> info(@PathVariable Long classId) {

        long userStudentId = StpUtil.getSession().getLong("studentId");

        PgStudent student = pgbCustomService.getStudentByClassId(classId, userStudentId);

        if (ObjectUtil.isNull(student)) {
            return BaseResult.code(GlobalCode.Item_Null);
        }

        return BaseResult.success(student);

    }

    @Operation(summary = "获取当前登录用户的学生信息")
    @GetMapping("info")
    @SaCheckLogin
    public BaseResult<PgUserStudent> info() {

        long studentId = StpUtil.getSession().getLong("studentId");

        // 如果不存在
        if (studentId == 0) {
            // 解除session student
            pgUserStudentService.resetStudentSession();
            studentId = StpUtil.getSession().getLong("studentId");
        }

        PgUserStudent student = pgUserStudentService.getById(studentId);

        if (ObjectUtil.isNull(student)) {
            // 判断是否当前学生已经不属于了
            List<PgUserStudent> studentList = pgUserStudentService.getStudentList(StpUtil.getLoginIdAsLong());
            if (studentList.isEmpty()) {
                return BaseResult.code(GlobalCode.Item_Null);
            }
            StpUtil.getSession().set("studentId", studentList.get(0).getId());
            return BaseResult.success(studentList.get(0));
        }

        // 判断是否当前学生已经不属于了
        List<PgUserStudent> studentList = pgUserStudentService.getStudentList(StpUtil.getLoginIdAsLong());
        if (!studentList.contains(student)) {
            StpUtil.getSession().delete("studentId");
            return BaseResult.code(GlobalCode.Item_Null);
        }

        StpUtil.getSession().set("studentId", student.getId());
        return BaseResult.success(student);
    }

    @Operation(summary = "获取指定学生的信息")
    @GetMapping("info/student/{studentId}")
    @SaCheckLogin
    public BaseResult<PgUserStudent> infoById(@PathVariable Long studentId) {
        PgUserStudent student = pgUserStudentService.getById(studentId);

        if (ObjectUtil.isNull(student)) {
            return BaseResult.code(GlobalCode.Item_Null);
        }

        return BaseResult.success(student);
    }

    @Operation(summary = "修改孩子信息")
    @PostMapping("info")
    @SaCheckLogin
    public BaseResult<Boolean> updateInfo(@RequestBody PgUserStudentDTO dto) {

        long studentId = StpUtil.getSession().getLong("studentId");

        PgUserStudent student = pgUserStudentService.getById(studentId);

        if (ObjectUtil.isNull(student)) {
            return BaseResult.code(GlobalCode.Item_Null);
        }

        // 如果头像换了
        if (!student.getAvatar().equals(dto.getAvatar())) {
            String sourceKey = URLUtil.getPath(student.getAvatar());
            String targetKey = sourceKey.replaceAll("tmp/", "student/avatar/");
            ossService.renameFile(sourceKey, targetKey);
        }

        // 复制修改信息
        BeanUtil.copyProperties(dto, student, "id");

        return BaseResult.success(
                pgUserStudentService.updateById(student)
        );
    }


    @Operation(summary = "获取学生账号列表", description = "多个学生账号时使用")
    @PostMapping("list")
    @SaCheckLogin
    public BaseResult<List<PgUserStudentVO>> list() {

        List<PgUserStudentVO> list = pgUserStudentService.getStudentList(StpUtil.getLoginIdAsLong()).stream()
                .map(pgUserStudent -> BeanUtil.copyProperties(pgUserStudent, PgUserStudentVO.class))
                .toList();

        return BaseResult.success(list);
    }

    public record SwitchStudentForm(Long studentId) {
    }

    @Operation(summary = "切换账号身份")
    @PostMapping("switch")
    @SaCheckLogin
    public BaseResult<Boolean> switchStudent(@RequestBody SwitchStudentForm form) {

        if (ObjectUtil.isNull(form.studentId())) {
            return BaseResult.error("请选择身份");
        }
        StpUtil.getSession().set("studentId", form.studentId);
//        StpUtil.switchTo(studentId);

        return BaseResult.success(true);
    }

    @Operation(summary = "删除学生")
    @DeleteMapping("{studentId}")
    public BaseResult<Boolean> delete(@PathVariable Long studentId) {

        PgUserStudent student = pgUserStudentService.getById(studentId);

        if (ObjectUtil.isNull(student)) {
            return BaseResult.code(GlobalCode.Item_Null);
        }
        pgUserStudentService.removeById(studentId);

        // 解绑教师端学生关联
        pgbCustomService.unbind(studentId);

        return BaseResult.success(true);
    }

}
