package com.pgb.xcx.controller.config;

import cn.dev33.satoken.annotation.SaCheckLogin;
import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import com.pgb.common.core.global.BaseResult;
import com.pgb.common.core.global.GlobalCode;
import com.pgb.service.db.PgUserConfigService;
import com.pgb.service.domain.userConfig.CorrectConfigDTO;
import com.pgb.service.domain.userConfig.ExportConfigDTO;
import com.pgb.service.domain.userConfig.PgUserConfig;
import com.pgb.service.enums.UserConfigEnum;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.util.Date;

/**
 * <AUTHOR>
 * Created by 2025/3/14 14:47
 */
@Tag(name = "用户端/配置/批改配置")
@RestController("UserConfigCorrectController")
@Transactional(rollbackFor = Exception.class)
@RequestMapping("/user/config/correct")
@RequiredArgsConstructor
@Slf4j
public class CorrectController {

    private final PgUserConfigService pgUserConfigService;

    @Operation(summary = "获取批改配置")
    @GetMapping
    @SaCheckLogin
    public BaseResult<CorrectConfigDTO> getConfig() {

        PgUserConfig config = pgUserConfigService.getByKey(UserConfigEnum.CORRECT, StpUtil.getLoginIdAsLong());

        // 若没设置过
        if (ObjectUtil.isNull(config)) {
            return BaseResult.success(new CorrectConfigDTO());
        }

        return BaseResult.success(
                BeanUtil.toBean(config.getValue(), CorrectConfigDTO.class)
        );
    }

    @Operation(summary = "保存批改配置")
    @PostMapping
    @SaCheckLogin
    public BaseResult<Boolean> save(@RequestBody CorrectConfigDTO correctConfig) {

        PgUserConfig config = pgUserConfigService.getByKey(UserConfigEnum.CORRECT, StpUtil.getLoginIdAsLong());

        if (ObjectUtil.isNotNull(config) && !config.getUserId().equals(StpUtil.getLoginIdAsLong())) {
            return BaseResult.error("无保存权限");
        }

        // 如果没设置过
        if (ObjectUtil.isNull(config)) {
            config = new PgUserConfig();
            config.setUserId(StpUtil.getLoginIdAsLong());
            config.setKey(UserConfigEnum.CORRECT.name());
            // 校验是否为数字格式
            if (!NumberUtil.isNumber(String.valueOf(correctConfig.getScoreStandard()))) {
                return BaseResult.code(GlobalCode.Param_Wrong);
            }
            config.setValue(correctConfig);
            config.setRemark("批改配置");
            config.setIsValid(true);
            config.setCreateTime(new Date());
            pgUserConfigService.save(config);
        }

        // 如果设置过 则更新
        config.setValue(correctConfig);
        pgUserConfigService.updateById(config);

        return BaseResult.success(true);
    }

}
