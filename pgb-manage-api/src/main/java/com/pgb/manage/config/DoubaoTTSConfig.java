package com.pgb.manage.config;

import com.pgb.ai.domain.doubao.DoubaoTTSProperty;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 豆包TTS配置参数
 *
 * <AUTHOR>
 */
@Slf4j
@Configuration
@RequiredArgsConstructor
@EnableConfigurationProperties(DoubaoTTSProperty.class)
public class DoubaoTTSConfig {
}
