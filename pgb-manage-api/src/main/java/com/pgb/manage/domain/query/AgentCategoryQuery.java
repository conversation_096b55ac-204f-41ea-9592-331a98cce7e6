package com.pgb.manage.domain.query;

import com.pgb.common.mybatis.global.PageQuery;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Min;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * Created by 2025/2/14 14:42
 */
@EqualsAndHashCode(callSuper = true)
@Schema(description = "智能体分类 搜索实体")
@Data
public class AgentCategoryQuery extends PageQuery {

    @Schema(title = "智能体分类名称")
    String name;
}
