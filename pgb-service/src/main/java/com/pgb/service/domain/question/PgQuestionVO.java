package com.pgb.service.domain.question;

import java.io.Serializable;

import java.util.Date;

import cn.hutool.core.util.ObjectUtil;
import com.fasterxml.jackson.annotation.JsonGetter;
import com.pgb.service.enums.GradeEnum;
import com.pgb.service.enums.SubjectEnum;
import com.pgb.service.enums.WritingStyleEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.ToString;
import lombok.Builder;

/**
* 题目表
* @TableName pg_question
*/
@Schema(description = "题目表 VO")
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode
@ToString
@Builder
public class PgQuestionVO implements Serializable {

    @Schema(title = "题目id", type = "string")
    private Long id;

    @Schema(title = "题目名称")
    private String name;

    @Schema(title = "批改要求")
    private String correctRequest;

    @Schema(title = "写作要求")
    private String writingRequest;

    @Schema(title = "年级")
    private GradeEnum grade;

    @Schema(title = "xx册xx单元")
    private String unit;

    @Schema(title = "创建时间")
    private Date createTime;

    @Schema(title = "题目类型")
    private SubjectEnum subject;

    @Schema(title = "字数要求")
    private Integer wordNum;

    @Schema(title = "是否是官方题目")
    private Boolean isOfficial;

    @Schema(title = "已批改次数")
    private Integer correctNum;

    @Schema(title = "写作文体")
    private WritingStyleEnum style;

    @Schema(title = "好标题")
    private String headline;

    @Schema(title = "好词")
    private String word;

    @Schema(title = "好开头")
    private String opening;

    @Schema(title = "好结尾")
    private String ending;

    @Schema(title = "用户id")
    private Long userId;

    @Schema(title = "排序")
    private Integer sort;

    @Schema(title = "分数")
    private Integer score;

    @Schema(title = "学期（上下册）")
    private String semester;

    @Schema(title = "写作文体名称")
    @JsonGetter("styleStr")
    public String getStyleStr() {
        return ObjectUtil.isNotNull(this.style) ? this.style.desc : null;
    }


    @Schema(title = "年级名称")
    @JsonGetter("gradeStr")
    public String getGradeStr() {
        return ObjectUtil.isNotNull(this.grade) ? this.grade.desc : null;
    }
}
