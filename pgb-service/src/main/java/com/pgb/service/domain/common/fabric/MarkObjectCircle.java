package com.pgb.service.domain.common.fabric;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

@Schema(title = "圆圈类型")
@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MarkObjectCircle extends MarkObject{

    @Schema(title = "类型，Image、txt、line")
    private final MarkObjectEnum type = MarkObjectEnum.Circle;

    @Schema(title = "left 位置")
    private Double left;

    @Schema(title = "top 位置")
    private Double top;

    @Schema(title = "top 位置")
    private Double radius;

    @Schema(title = "top 位置")
    private String fill;
}
