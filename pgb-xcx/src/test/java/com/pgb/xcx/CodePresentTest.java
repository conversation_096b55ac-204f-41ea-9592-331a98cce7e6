package com.pgb.xcx;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.pgb.XcxApplication;
import com.pgb.service.db.PgUsersService;
import com.pgb.service.db.PgVipCodeService;
import com.pgb.service.db.PgVipService;
import com.pgb.service.domain.user.PgUsers;
import com.pgb.service.domain.vip.PgVip;
import com.pgb.service.domain.vip.PgVipCode;
import com.pgb.service.enums.ChannelTypeEnum;
import com.pgb.service.enums.VipTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.List;

@SpringBootTest(classes = XcxApplication.class, webEnvironment = SpringBootTest.WebEnvironment.DEFINED_PORT)
@Slf4j
public class CodePresentTest {

    @Autowired
    private PgUsersService pgUsersService;

    @Autowired
    private PgVipService pgVipService;

    @Autowired
    private PgVipCodeService pgVipCodeService;

//    @Test
    void presentOne() {

        String phone = "15338132621";

        PgUsers users = pgUsersService.getOne(new LambdaQueryWrapper<PgUsers>()
                .eq(PgUsers::getPhone, phone)
                .last("LIMIT 1")
        );

        if (ObjectUtil.isNull(users)) {
            log.info("手机号：{}，用户不存在，跳过", phone);
        }
        // 查询，必须是开通过学期卡和年卡的才行
        boolean exists = pgVipService.exists(new LambdaQueryWrapper<PgVip>()
                .eq(PgVip::getUserId, users.getId())
                .and(i -> i
                        .eq(PgVip::getVipType, VipTypeEnum.TERM)
                        .or()
                        .eq(PgVip::getVipType, VipTypeEnum.YEAR)
                )

        );

        if (!exists) {
            log.info("手机号：{}，不符合年卡/学期卡标准：{}", phone, exists);
        }

        // 开通两张周卡
        PgVipCode code1 = pgVipCodeService.createVipCode(
                ChannelTypeEnum.ACTIVITY_PRESENT,
                VipTypeEnum.WEEK,
                null,
                null
        );
        PgVipCode code2 = pgVipCodeService.createVipCode(
                ChannelTypeEnum.ACTIVITY_PRESENT,
                VipTypeEnum.WEEK,
                null,
                null
        );

        code1.setUserId(users.getId());
        code2.setUserId(users.getId());
        code1.setRemark("2025-38妇女节，学期卡、年卡免费赠送2张周卡活动");
        code2.setRemark("2025-38妇女节，学期卡、年卡免费2张周卡活动");
        pgVipCodeService.updateById(code1);
        pgVipCodeService.updateById(code2);

        log.info("手机号：{}，成功生成2张周卡：{} 及 {}", phone, code1.getCode(), code2.getCode());
    }

    // @Test
    void present() {
        ExcelReader reader = ExcelUtil.getReader("C:\\Users\\<USER>\\Desktop\\新建 XLSX 工作表.xlsx");
        List<List<Object>> readAll = reader.read();

        for (List<Object> objects : readAll) {
            for (Object object : objects) {
                String phone = object.toString();
                log.info("手机号：{}", objects);

                PgUsers users = pgUsersService.getOne(new LambdaQueryWrapper<PgUsers>()
                        .eq(PgUsers::getPhone, phone)
                        .last("LIMIT 1")
                );

                if (ObjectUtil.isNull(users)) {
                    log.info("手机号：{}，用户不存在，跳过", phone);
                    continue;
                }

                // 查询，必须是开通过学期卡和年卡的才行
                boolean exists = pgVipService.exists(new LambdaQueryWrapper<PgVip>()
                        .eq(PgVip::getUserId, users.getId())
                        .and(i -> i
                                .eq(PgVip::getVipType, VipTypeEnum.TERM)
                                .or()
                                .eq(PgVip::getVipType, VipTypeEnum.YEAR)
                        )

                );

                if (!exists) {
                    log.info("手机号：{}，不符合年卡/学期卡标准：{}", phone, exists);
                    continue;
                }

                // 开通两张周卡
                PgVipCode code1 = pgVipCodeService.createVipCode(
                        ChannelTypeEnum.ACTIVITY_PRESENT,
                        VipTypeEnum.WEEK,
                        null,
                        null
                );
                PgVipCode code2 = pgVipCodeService.createVipCode(
                        ChannelTypeEnum.ACTIVITY_PRESENT,
                        VipTypeEnum.WEEK,
                        null,
                        null
                );

                code1.setUserId(users.getId());
                code2.setUserId(users.getId());
                code1.setRemark("2025-38妇女节，学期卡、年卡免费赠送2张周卡活动");
                code2.setRemark("2025-38妇女节，学期卡、年卡免费2张周卡活动");
                pgVipCodeService.updateById(code1);
                pgVipCodeService.updateById(code2);

                log.info("手机号：{}，成功生成2张周卡：{} 及 {}", phone, code1.getCode(), code2.getCode());
            }
        }

        System.out.println("OK");
    }
}
