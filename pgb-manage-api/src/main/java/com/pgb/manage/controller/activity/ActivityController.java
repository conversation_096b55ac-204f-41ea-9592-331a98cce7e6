package com.pgb.manage.controller.activity;

import cn.dev33.satoken.annotation.SaCheckRole;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.pgb.common.core.global.BaseResult;
import com.pgb.common.core.global.GlobalCode;
import com.pgb.common.core.global.RoleConstants;
import com.pgb.manage.domain.query.ActivityQuery;
import com.pgb.manage.domain.vo.PgActivityUserVO;
import com.pgb.service.db.PgActivityService;
import com.pgb.service.db.PgActivityUserService;
import com.pgb.service.db.PgUsersService;
import com.pgb.service.db.PgVipCodeService;
import com.pgb.service.domain.activity.*;
import com.pgb.service.domain.query.ActivityBuyQuery;
import com.pgb.service.domain.user.PgUsers;
import com.pgb.service.domain.vip.PgVipCode;
import com.pgb.service.enums.ChannelTypeEnum;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.List;

@Tag(name = "管理端/活动管理")
@RestController("ManageActivityController")
@Transactional(rollbackFor = Exception.class)
@RequestMapping("/manage/activity")
@RequiredArgsConstructor
@Slf4j
@SaCheckRole(RoleConstants.Manager)
public class ActivityController {

    private final PgActivityService pgActivityService;

    private final PgActivityUserService pgActivityUserService;

    private final PgUsersService pgUsersService;

    private final PgVipCodeService pgVipCodeService;

    @Operation(summary = "查询活动购买情况")
    @PostMapping("bought/page")
    public BaseResult<IPage<PgActivityUserVO>> page(@RequestBody ActivityBuyQuery query) {

        LambdaQueryWrapper<PgActivityUser> queryWrapper = new LambdaQueryWrapper<PgActivityUser>()
                // 对应活动
                .eq(ObjectUtil.isNotNull(query.getActivityId()), PgActivityUser::getActivityId, query.getActivityId())
                // 购买时间
                .between(CollUtil.isNotEmpty(query.getBuyTime()) && query.getBuyTime().size() == 2,
                        PgActivityUser::getCreateTime,
                        CollUtil.get(query.getBuyTime(), 0),
                        CollUtil.get(query.getBuyTime(), 1)
                );
        // 查询手机号
        if (StrUtil.isNotBlank(query.getPhone())) {
            List<Long> userIds = pgUsersService.list(new LambdaQueryWrapper<PgUsers>()
                    .like(PgUsers::getPhone, query.getPhone())
            ).stream().map(PgUsers::getId).toList();

            // 空的
            if (userIds.isEmpty()) {
                return BaseResult.success(new Page<>());
            }

            queryWrapper.in(PgActivityUser::getUserId, userIds);
        }

        // 查询是否已兑换
        if (ObjectUtil.isNotNull(query.getIsUseCode())) {
            List<Long> codeIds = pgVipCodeService.list(new LambdaQueryWrapper<PgVipCode>()
                    .eq(PgVipCode::getChannelType, ChannelTypeEnum.ACTIVITY)
                    .eq(PgVipCode::getStatus, query.getIsUseCode() ? 1 : 0)
            ).stream().map(PgVipCode::getId).toList();

            queryWrapper.in(PgActivityUser::getCodeId, codeIds);
        }

        // 查询
        IPage<PgActivityUserVO> page = pgActivityUserService.page(query.toMpPageSortByCreateTime(), queryWrapper).convert(pgActivityUser -> {
            PgActivityUserVO activityUserVO = BeanUtil.toBean(pgActivityUser, PgActivityUserVO.class);

            // 兑换码
            PgVipCode code = pgVipCodeService.getById(pgActivityUser.getCodeId());
            if (ObjectUtil.isNotNull(code)) {
                activityUserVO.setCode(code.getCode());
                activityUserVO.setIsUseCode(code.getStatus() == 1);
            } else {
                activityUserVO.setIsUseCode(true);
            }

            // 手机号
            PgUsers user = pgUsersService.getById(pgActivityUser.getUserId());
            if (ObjectUtil.isNotNull(user)) {
                activityUserVO.setPhone(user.getPhone());
            }

            // 活动名称
            PgActivity activity = pgActivityService.getById(pgActivityUser.getActivityId());
            if (ObjectUtil.isNotNull(activity)) {
                activityUserVO.setActivityName(activity.getName());
            }

            return activityUserVO;
        });

        return BaseResult.success(page);
    }

    @Operation(summary = "活动列表下拉选", description = "返回id和活动名称")
    @PostMapping("activities")
    public BaseResult<List<PgActivity>> list() {

        List<PgActivity> list = pgActivityService.list(new LambdaQueryWrapper<PgActivity>()
                .select(PgActivity::getId, PgActivity::getName));

        return BaseResult.success(list);

    }

    @Operation(summary = "活动列表")
    @PostMapping("list")
    public BaseResult<IPage<PgActivityVO>> list(@RequestBody ActivityQuery query) {

        IPage<PgActivityVO> page = pgActivityService.page(query.toMpPageSortByCreateTime(), new LambdaQueryWrapper<PgActivity>()
                .like(StrUtil.isNotBlank(query.getName()), PgActivity::getName, query.getName())
        ).convert(
                activity -> {

                    PgActivityVO activityVO = BeanUtil.copyProperties(activity, PgActivityVO.class, "config");

                    // 活动内容配置
                    ActivityDiscountConfig config = JSONUtil.toBean(activity.getConfig().toString(), ActivityDiscountConfig.class);

                    activityVO.setConfig(config);

                    // 活动参与人数
                    long buyNum = pgActivityUserService.count(new LambdaQueryWrapper<PgActivityUser>()
                            .eq(PgActivityUser::getActivityId, activity.getId())
                            .eq(PgActivityUser::getIsBuy, true));
                    activityVO.setBuyNum((int) buyNum);

                    return activityVO;
                }
        );

        return BaseResult.success(page);
    }

    @Operation(summary = "新增活动")
    @PostMapping("create")
    public BaseResult<Boolean> create(@RequestBody PgActivityDTO activity) {

        // 保存基本信息
        PgActivity pgActivity = BeanUtil.copyProperties(activity, PgActivity.class);

        pgActivity.setCreateTime(new Date());

        pgActivityService.save(pgActivity);

        return BaseResult.success(true);
    }

    @Operation(summary = "查看活动内容")
    @GetMapping("{id}")
    public BaseResult<PgActivityVO> get(@PathVariable Long id) {

        PgActivity activity = pgActivityService.getById(id);

        if (ObjectUtil.isNull(activity)) {
            return BaseResult.code(GlobalCode.Item_Null);
        }

        PgActivityVO activityVO = BeanUtil.copyProperties(activity, PgActivityVO.class, "config");

        if (ObjectUtil.isNotNull(activity.getConfig())) {
            activityVO.setConfig(JSONUtil.toBean(activity.getConfig().toString(), ActivityDiscountConfig.class));
        }

        return BaseResult.success(activityVO);
    }

    @Operation(summary = "修改活动内容")
    @PostMapping("update/{id}")
    public BaseResult<Boolean> update(@PathVariable Long id, @RequestBody PgActivityDTO activityDTO) {

        PgActivity activity = pgActivityService.getById(id);

        if (ObjectUtil.isNull(activity)) {
            return BaseResult.code(GlobalCode.Item_Null);
        }

        BeanUtil.copyProperties(activityDTO, activity);

        pgActivityService.updateById(activity);

        return BaseResult.success(true);
    }

    @Operation(summary = "删除活动内容")
    @DeleteMapping("{id}")
    public BaseResult<Boolean> delete(@PathVariable Long id) {

        PgActivity activity = pgActivityService.getById(id);

        if (ObjectUtil.isNull(activity)) {
            return BaseResult.code(GlobalCode.Item_Null);
        }

        pgActivityService.removeById(id);

        return BaseResult.success(true);
    }
}
