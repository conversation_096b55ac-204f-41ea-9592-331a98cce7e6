package com.pgb.service.db;

import com.baomidou.mybatisplus.extension.service.IService;
import com.pgb.service.domain.systemConfig.SystemConfig;
import com.pgb.service.enums.SystemConfigEnum;

/**
* <AUTHOR>
* @description 针对表【system_config(系统配置)】的数据库操作Service
* @createDate 2024-12-02 14:27:25
*/
public interface SystemConfigService extends IService<SystemConfig> {

    /**
     * 获取配置信息，自动缓存数据
     * @param key
     * @return
     */
    SystemConfig getByKeyByCache(SystemConfigEnum key);

    /**
     * 获取配置信息
     * @param key
     * @return
     */
    SystemConfig getByKey(SystemConfigEnum key);

    /**
     * 删除缓存
     * @param key
     */
    void removeCache(SystemConfigEnum key);
}
