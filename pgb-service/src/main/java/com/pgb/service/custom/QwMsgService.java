package com.pgb.service.custom;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.json.JSONUtil;
import cn.hutool.system.SystemUtil;
import com.pgb.common.core.utils.EnvUtils;
import com.pgb.service.domain.order.PgOrder;
import lombok.extern.slf4j.Slf4j;

import java.util.Date;

@Slf4j
public class QwMsgService {
    /**
     * 企微  发异常报警通知
     */
    public static void sendErrorMessage(String action) {

        String msg = EnvUtils.isDev() ? "小程序测试" : "小程序线上";
        // 发送企微机器人通知 技术反馈群
        try {
            String result = HttpRequest.post(
                            "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=295eae4d-ef26-4382-930e-0090c30ca9c7"
                    ).body(JSONUtil.createObj()
                            .putOnce("msgtype", "markdown")
                            .putOnce("markdown", JSONUtil.createObj()
                                    .putOnce("content",
                                            StrUtil.format(
                                                    """
                                                            小程序批改异常报警\n
                                                            环境：{}\n
                                                            时间：{}\n
                                                            操作：{}\n
                                                            机器：{}
                                                            """, msg, DateUtil.format(new Date(), "yyyy-MM-dd HH:mm:ss"), action, SystemUtil.get("correctName", true)
                                            )
                                    )
                            ).toString()
                    ).timeout(3 * 60 * 1000)
                    .execute()
                    .body();

            log.info("企微发送通知成功 - {}", result);
        } catch (Exception e) {
            log.error("企微发送通知失败 - {}", e.getMessage());
        }
    }

    /**
     * 企微  发PDF渲染异常报警通知
     */
    public static void sendPdfErrorMessage(String action) {

        String msg = EnvUtils.isDev() ? "小程序测试" : "小程序线上";
        // 发送企微机器人通知 技术反馈群
        try {
            String result = HttpRequest.post(
                            "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=295eae4d-ef26-4382-930e-0090c30ca9c7"
                    ).body(JSONUtil.createObj()
                            .putOnce("msgtype", "markdown")
                            .putOnce("markdown", JSONUtil.createObj()
                                    .putOnce("content",
                                            StrUtil.format(
                                                    """
                                                            小程序PDF渲染异常报警\n
                                                            环境：{}\n
                                                            时间：{}\n
                                                            操作：{}\n
                                                            机器：{}
                                                            """, msg, DateUtil.format(new Date(), "yyyy-MM-dd HH:mm:ss"), action, SystemUtil.get("correctName", true)
                                            )
                                    )
                            ).toString()
                    ).timeout(3 * 60 * 1000)
                    .execute()
                    .body();

            log.info("企微发送通知成功 - {}", result);
        } catch (Exception e) {
            log.error("企微发送通知失败 - {}", e.getMessage());
        }
    }

    /**
     * 企微  发支付成功通知
     *
     * @param order
     * @param phone
     */
    public static void sendPayMessage(PgOrder order, String phone, Boolean isNewBuy) {

        double amount = NumberUtil.div(order.getPayAmount().intValue(), 100);

        try {
            // 发送企微机器人通知
            String result = HttpRequest.post(
                            "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=056e4c94-32e8-449d-84dc-25294b28164a"
                    ).body(JSONUtil.createObj()
                            .putOnce("msgtype", "markdown")
                            .putOnce("markdown", JSONUtil.createObj()
                                    .putOnce("content", "爆单啦！爆单啦！\n"
                                            + "支付金额：" + amount + "\n"
                                            + "会员类型：" + order.getTitle() + "\n"
                                            + "手机号：" + phone + "\n"
                                            + "续购类型：" + (isNewBuy ? "新购" : "续购") + "\n"
                                            + "支付渠道：" + order.getOrderType().desc + "\n"
                                            + "支付方式：" + order.getPayType().desc +"\n"
                                            + "支付时间：" + DateUtil.format(new Date(), "yyyy-MM-dd HH:mm:ss")
                                    )
                            ).toString()
                    ).timeout(3 * 60 * 1000)
                    .execute()
                    .body();

            log.info("企微发送支付通知成功 - {}", result);
        } catch (Exception e) {
            log.error("企微发送支付通知失败 - {}", e.getMessage());
        }
    }

    public static void sendActivityMessage(PgOrder order, String phone, Boolean isNewBuy, String activityName) {
        //
        double amount = NumberUtil.div(order.getPayAmount().intValue(), 100);

        try {
            // 发送企微机器人通知
            String result = HttpRequest.post(
                            "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=77354f5e-21c0-4681-96b8-383388d89de3"
                    ).body(JSONUtil.createObj()
                            .putOnce("msgtype", "markdown")
                            .putOnce("markdown", JSONUtil.createObj()
                                    .putOnce("content", activityName + "爆单啦！\n"
                                            + "支付金额：" + amount + "\n"
                                            + "会员类型：" + order.getTitle() + "\n"
                                            + "手机号：" + phone + "\n"
                                            + "续购类型：" + (isNewBuy ? "新购" : "续购") + "\n"
                                            + "支付渠道：" + order.getOrderType().desc + "\n"
                                            + "支付时间：" + DateUtil.format(new Date(), "yyyy-MM-dd HH:mm:ss")
                                    )
                            ).toString()
                    ).timeout(3 * 60 * 1000)
                    .execute()
                    .body();

            log.info("企微发送活动支付通知成功 - {}", result);
        } catch (Exception e) {
            log.error("企微发送活动支付通知失败 - {}", e.getMessage());
        }
    }
}
