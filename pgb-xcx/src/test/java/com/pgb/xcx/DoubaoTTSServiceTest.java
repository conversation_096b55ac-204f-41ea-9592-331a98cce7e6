package com.pgb.xcx;

import com.pgb.ai.domain.doubao.DoubaoTTSProperty;
import com.pgb.ai.domain.doubao.DoubaoTTSResult;
import com.pgb.ai.models.DoubaoTTSService;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.io.File;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 豆包TTS服务单元测试类
 *
 * <p>该测试类用于验证豆包TTS服务的功能，包括：</p>
 * <ul>
 *   <li>基本的文本转语音功能</li>
 *   <li>指定音色的语音合成</li>
 *   <li>音频文件保存功能</li>
 *   <li>异常情况处理</li>
 * </ul>
 *
 * <AUTHOR>
 */
@Slf4j
public class DoubaoTTSServiceTest {

    private DoubaoTTSService ttsService;
    private DoubaoTTSProperty testConfig;
    private static final String TEST_OUTPUT_DIR = "test-audio-output";

    @BeforeEach
    void setUp() {
        ttsService = new DoubaoTTSService();

        // 创建测试输出目录
        try {
            Path outputDir = Paths.get(TEST_OUTPUT_DIR);
            if (!Files.exists(outputDir)) {
                Files.createDirectories(outputDir);
                log.info("创建测试输出目录: {}", outputDir.toAbsolutePath());
            }
        } catch (Exception e) {
            log.error("创建测试输出目录失败", e);
        }

        // 配置测试参数 - 使用提供的API密钥，参数与Python示例保持一致
        testConfig = DoubaoTTSProperty.builder()
                .appId("4381946153")
                .accessKey("kAx-boB_DVgoLXzQHB9GKOYUD9cwd808")
                .resourceId("volc.service_type.10029")
                .speaker("zh_male_jieshuonansheng_mars_bigtts")  // 使用与Python示例相同的音色
                .audioFormat("mp3")
                .sampleRate(24000)
                .timeout(30000)  // 30秒超时
                .build();
    }

    /**
     * 保存音频文件的辅助方法
     *
     * @param result TTS结果
     * @param fileName 文件名（不包含路径）
     * @return 保存的文件路径
     */
    private String saveAudioFile(DoubaoTTSResult result, String fileName) {
        if (result == null || !result.getSuccess() || result.getAudioData() == null) {
            return null;
        }

        try {
            String filePath = TEST_OUTPUT_DIR + File.separator + fileName;
            boolean saved = ttsService.saveAudioToFile(result.getAudioData(), filePath);

            if (saved) {
                File savedFile = new File(filePath);
                log.info("音频文件保存成功: {}", savedFile.getAbsolutePath());
                log.info("文件大小: {} KB", savedFile.length() / 1024.0);
                return filePath;
            } else {
                log.error("音频文件保存失败: {}", filePath);
                return null;
            }
        } catch (Exception e) {
            log.error("保存音频文件时发生异常: {}", fileName, e);
            return null;
        }
    }

    /**
     * 测试基本的文本转语音功能
     */
    @Test
    void testBasicTextToSpeech() {
        log.info("=== 测试基本文本转语音功能 ===");

        String testText = "这是一段测试文本，用于测试字节大模型语音合成http单向流式接口效果。";

        DoubaoTTSResult result = ttsService.textToSpeech(testText, testConfig);

        // 验证结果
        assertNotNull(result, "结果不应为null");

        if (result.getSuccess()) {
            assertTrue(true, "语音合成应该成功");
            assertNotNull(result.getAudioData(), "音频数据不应为null");
            assertTrue(result.getAudioData().length > 0, "音频数据不应为空");
            assertTrue(result.getAudioSize() > 0, "音频大小应大于0");
            assertNotNull(result.getProcessingTime(), "处理时长不应为null");

            log.info("TTS合成成功！");
            log.info("音频大小: {} KB", result.getAudioSizeInKB());
            log.info("处理时长: {} ms", result.getProcessingTime());
            log.info("音频格式: {}", result.getAudioFormat());

            // 保存音频文件
            String savedPath = saveAudioFile(result, "basic_test.mp3");
            if (savedPath != null) {
                log.info("基本测试音频已保存到: {}", savedPath);
            }
        } else {
            log.warn("TTS合成失败，可能是网络问题或API密钥问题: {}", result.getErrorMessage());
            // 在测试环境中，如果API调用失败，我们记录警告但不让测试失败
            assertNotNull(result.getErrorMessage(), "失败时应该有错误消息");
        }
    }

    /**
     * 测试指定音色的语音合成
     */
    @Test
    void testSpecificVoice() {
        log.info("=== 测试指定音色语音合成 ===");

        String testText = "测试指定音色zh_male_jieshuonansheng_mars_bigtts的语音合成效果。";

        // 确保使用指定的音色
        DoubaoTTSProperty voiceConfig = testConfig.toBuilder()
                .speaker("zh_male_jieshuonansheng_mars_bigtts")
                .build();

        DoubaoTTSResult result = ttsService.textToSpeech(testText, voiceConfig);

        assertNotNull(result, "结果不应为null");

        if (result.getSuccess()) {
            assertTrue(result.getSuccess(), "指定音色的语音合成应该成功");
            assertNotNull(result.getAudioData(), "音频数据不应为null");
            log.info("指定音色合成成功，音频大小: {:.2f} KB", result.getAudioSizeInKB());

            // 保存音频文件
            String savedPath = saveAudioFile(result, "specific_voice_test.mp3");
            if (savedPath != null) {
                log.info("指定音色测试音频已保存到: {}", savedPath);
            }
        } else {
            log.warn("指定音色合成失败: {}", result.getErrorMessage());
            assertNotNull(result.getErrorMessage(), "失败时应该有错误消息");
        }
    }

    /**
     * 测试音频文件保存功能
     */
    @Test
    void testAudioFileSave() {
        log.info("=== 测试音频文件保存功能 ===");

        String testText = "测试音频文件保存功能。";
        DoubaoTTSResult result = ttsService.textToSpeech(testText, testConfig);

        assertNotNull(result, "结果不应为null");

        if (result.getSuccess()) {
            // 保存音频文件到测试目录
            String savedPath = saveAudioFile(result, "file_save_test.mp3");
            assertNotNull(savedPath, "音频文件应该保存成功");

            // 验证文件是否存在
            File savedFile = new File(savedPath);
            assertTrue(savedFile.exists(), "保存的文件应该存在");
            assertTrue(savedFile.length() > 0, "保存的文件大小应大于0");

            log.info("音频文件保存测试完成，文件: {}", savedFile.getAbsolutePath());
        } else {
            log.warn("无法测试文件保存功能，TTS合成失败: {}", result.getErrorMessage());
        }
    }

    /**
     * 测试异常处理机制
     */
    @Test
    void testErrorHandling() {
        log.info("=== 测试异常处理机制 ===");

        // 测试空文本
        DoubaoTTSResult result1 = ttsService.textToSpeech("", testConfig);
        assertNotNull(result1, "结果不应为null");
        assertFalse(result1.getSuccess(), "空文本应该失败");
        assertEquals("文本内容不能为空", result1.getErrorMessage(), "错误消息应该正确");

        // 测试null文本
        DoubaoTTSResult result2 = ttsService.textToSpeech(null, testConfig);
        assertNotNull(result2, "结果不应为null");
        assertFalse(result2.getSuccess(), "null文本应该失败");
        assertEquals("文本内容不能为空", result2.getErrorMessage(), "错误消息应该正确");

        // 测试null配置
        DoubaoTTSResult result3 = ttsService.textToSpeech("测试文本", null);
        assertNotNull(result3, "结果不应为null");
        assertFalse(result3.getSuccess(), "null配置应该失败");
        assertEquals("TTS配置参数不能为空", result3.getErrorMessage(), "错误消息应该正确");

        // 测试无效配置
        DoubaoTTSProperty invalidConfig = DoubaoTTSProperty.builder()
                .appId("")
                .accessKey("")
                .resourceId("")
                .build();

        DoubaoTTSResult result4 = ttsService.textToSpeech("测试文本", invalidConfig);
        assertNotNull(result4, "结果不应为null");
        assertFalse(result4.getSuccess(), "无效配置应该失败");
        assertTrue(result4.getErrorMessage().contains("TTS配置参数不完整"), "错误消息应该包含配置不完整信息");

        log.info("异常处理测试完成");
    }

    /**
     * 测试不同音频格式
     */
    @Test
    void testDifferentAudioFormats() {
        log.info("=== 测试不同音频格式 ===");

        String testText = "测试不同音频格式。";

        String[] formats = {"mp3", "wav"};

        for (String format : formats) {
            log.info("测试音频格式: {}", format);

            DoubaoTTSProperty formatConfig = testConfig.toBuilder()
                    .audioFormat(format)
                    .build();

            DoubaoTTSResult result = ttsService.textToSpeech(testText, formatConfig);

            assertNotNull(result, "结果不应为null");

            if (result.getSuccess()) {
                assertEquals(format, result.getAudioFormat(), "音频格式应该匹配");
                log.info("格式 {} 测试成功，音频大小: {:.2f} KB", format, result.getAudioSizeInKB());

                // 保存音频文件
                String fileName = "format_test_" + format + "." + format;
                String savedPath = saveAudioFile(result, fileName);
                if (savedPath != null) {
                    log.info("格式 {} 测试音频已保存到: {}", format, savedPath);
                }
            } else {
                log.warn("格式 {} 测试失败: {}", format, result.getErrorMessage());
            }
        }
    }

    /**
     * 测试长文本处理
     */
    @Test
    void testLongText() {
        log.info("=== 测试长文本处理 ===");

        StringBuilder longText = new StringBuilder();
        for (int i = 0; i < 10; i++) {
            longText.append("这是第").append(i + 1).append("段文本内容，用于测试长文本的语音合成效果。");
        }

        DoubaoTTSResult result = ttsService.textToSpeech(longText.toString(), testConfig);

        assertNotNull(result, "结果不应为null");

        if (result.getSuccess()) {
            assertTrue(result.getAudioSize() > 0, "长文本应该生成音频数据");
            log.info("长文本合成成功，文本长度: {}, 音频大小: {:.2f} KB",
                    longText.length(), result.getAudioSizeInKB());

            // 保存音频文件
            String savedPath = saveAudioFile(result, "long_text_test.mp3");
            if (savedPath != null) {
                log.info("长文本测试音频已保存到: {}", savedPath);
            }
        } else {
            log.warn("长文本合成失败: {}", result.getErrorMessage());
        }
    }

    /**
     * 测试JSON请求格式验证
     */
    @Test
    void testJsonRequestFormat() {
        log.info("=== 测试JSON请求格式验证 ===");

        String testText = "测试JSON格式";

        // 使用反射来调用私有方法 buildRequest
        try {
            java.lang.reflect.Method buildRequestMethod = DoubaoTTSService.class.getDeclaredMethod("buildRequest", String.class, DoubaoTTSProperty.class);
            buildRequestMethod.setAccessible(true);

            Object jsonRequest = buildRequestMethod.invoke(ttsService, testText, testConfig);

            log.info("构建的JSON请求: {}", jsonRequest);
            log.info("JSON请求类型: {}", jsonRequest.getClass().getName());

            // 验证JSON结构
            if (jsonRequest instanceof cn.hutool.json.JSONObject) {
                cn.hutool.json.JSONObject json = (cn.hutool.json.JSONObject) jsonRequest;
                assertTrue(json.containsKey("user"), "应该包含user字段");
                assertTrue(json.containsKey("req_params"), "应该包含req_params字段");

                cn.hutool.json.JSONObject reqParams = json.getJSONObject("req_params");
                assertTrue(reqParams.containsKey("audio_params"), "应该包含audio_params字段");
                assertTrue(reqParams.containsKey("sample_rate") == false, "req_params不应该直接包含sample_rate字段");

                cn.hutool.json.JSONObject audioParams = reqParams.getJSONObject("audio_params");
                assertTrue(audioParams.containsKey("sample_rate"), "audio_params应该包含sample_rate字段");

                log.info("JSON格式验证通过！");
            } else {
                fail("返回的不是JSONObject类型: " + jsonRequest.getClass().getName());
            }

        } catch (Exception e) {
            log.error("测试JSON请求格式失败", e);
            fail("测试JSON请求格式失败: " + e.getMessage());
        }
    }
}
