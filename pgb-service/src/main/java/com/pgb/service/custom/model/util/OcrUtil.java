package com.pgb.service.custom.model.util;

import cn.hutool.core.collection.CollUtil;
import com.pgb.common.ocr.domain.OCRChars;
import com.pgb.service.domain.zc.common.ZcLocation;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;

public class OcrUtil {

    /**
     * 计算整体的坐标
     *
     * @param list
     * @return
     */
    public static ZcLocation areaLocation(List<OCRChars> list, Integer n) {

        if (CollUtil.isEmpty(list)) {
            return null;
        }

        // 当 n > size 时，取实际长度
        int length = Math.min(n, list.size());
        if (length <= 0) {  // 额外保护
            return null;
        }

        // 取最后 length 个字符
        List<OCRChars> subList = list.subList(list.size() - length, list.size());

        // 按行分组
        List<List<OCRChars>> lines = groupByLine(subList, 10);

        ZcLocation location = new ZcLocation();
        // 最小的左边
        location.setLeft(
                (float) subList.stream().mapToDouble(item -> item.getLocation().getLeft()).min().getAsDouble()
        );
        // 最小的top
        location.setTop(
                (float) subList.stream().mapToDouble(item -> item.getLocation().getTop()).min().getAsDouble()
        );
        // 计算每行宽度并取最大值
        location.setWidth(
                (float) lines.stream()
                        .mapToDouble(line -> {
                            // 最左边的
                            double minLeft = line.stream()
                                    .mapToDouble(item -> item.getLocation().getLeft())
                                    .min().orElse(0);
                            // 最右边的
                            double maxRight = line.stream()
                                    .mapToDouble(item -> item.getLocation().getLeft() + item.getLocation().getWidth())
                                    .max().orElse(0);
                            return maxRight - minLeft;
                        })
                        .max().orElse(0)
        );

        // 计算总高度（最高行的bottom - 最低行的top）
        location.setHeight(
                (float) (lines.stream()
                        .mapToDouble(line -> line.stream()
                                .mapToDouble(item -> item.getLocation().getTop() + item.getLocation().getHeight())
                                .max().orElse(0))
                        .max().orElse(0)
                        - lines.stream()
                        .mapToDouble(line -> line.stream()
                                .mapToDouble(item -> item.getLocation().getTop())
                                .min().orElse(0))
                        .min().orElse(0))
        );
        return location;
    }

    /**
     * 按行分组 阈值为10px
     *
     * @param chars
     * @param threshold
     * @return
     */
    private static List<List<OCRChars>> groupByLine(List<OCRChars> chars, int threshold) {
        if (CollUtil.isEmpty(chars)) {
            return new ArrayList<>();
        }

        List<List<OCRChars>> lines = new ArrayList<>();
        List<OCRChars> currentLine = new ArrayList<>();

        // 先按top排序
        chars.sort(Comparator.comparingDouble(c -> c.getLocation().getTop()));

        double baseTop = chars.get(0).getLocation().getTop();
        currentLine.add(chars.get(0));

        for (int i = 1; i < chars.size(); i++) {
            OCRChars current = chars.get(i);
            // 同属一行
            if (Math.abs(current.getLocation().getTop() - baseTop) <= threshold) {
                currentLine.add(current);
            }
            // 分行
            else {
                lines.add(currentLine);
                currentLine = new ArrayList<>();
                currentLine.add(current);
                baseTop = current.getLocation().getTop();
            }
        }

        if (!currentLine.isEmpty()) {
            lines.add(currentLine);
        }

        return lines;
    }

}
