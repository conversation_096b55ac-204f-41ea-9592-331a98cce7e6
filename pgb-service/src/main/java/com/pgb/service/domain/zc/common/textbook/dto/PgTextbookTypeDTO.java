package com.pgb.service.domain.zc.common.textbook.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 教材类型DTO
 *
 * <AUTHOR>
 * Created by 2025/7/25 20:30
 */
@Data
@Schema(description = "教材类型DTO")
public class PgTextbookTypeDTO {

    @Schema(description = "教材类型名称")
    private String name;

    @Schema(description = "排序值")
    private Integer sort;
}
