package com.pgb.service.domain.popup;

import com.pgb.service.enums.PopupTypeEnum;
import com.pgb.service.enums.ShowOnTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Size;
import lombok.*;
import org.springframework.validation.annotation.Validated;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
* 弹窗表
* @TableName pg_popup
*/
@Schema(description = "弹窗表 DTO")
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode
@ToString
@Validated
@Builder
public class PgPopupDTO implements Serializable {

    @Schema(title = "弹窗id", type = "string")
    private Long id;

    @Schema(title = "开始时间")
    private Date startTime;

    @Schema(title = "结束时间")
    private Date endTime;

    @Schema(title = "显示时机(如：首次注册、每次进入，指定用户标签)")
    private ShowOnTypeEnum showOn;

    @Schema(title = "用户标签列表，根据英文逗号分隔")
    private List<String> tagIds;

    @Schema(title = "排除特定的标签")
    private List<String> notTagIds;

    @Schema(title = "权重排序")
    private Integer sort;

    @Schema(title = "是否有效")
    private Boolean isValid;

    @Schema(title = "创建时间")
    private Date createTime;

    @Schema(title = "弹窗图片url")
    @Size(max= 255)
    private String imgUrl;

    @Schema(title = "备注")
    @Size(max= 255)
    private String remark;

    @Schema(title = "弹窗类型，0：弹窗，1：贴片广告")
    private PopupTypeEnum type;

    @Schema(title = "活动id")
    private Long activityId;

    @Schema(title = "页面url")
    @Size(max= 255)
    private String targetUrl;

}
