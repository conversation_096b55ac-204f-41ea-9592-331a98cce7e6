package com.pgb.xcx;

import lombok.extern.slf4j.Slf4j;

import java.net.URI;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;

/**
 * <AUTHOR>
 * Created by 2025/7/25 12:02
 */
@Slf4j
public class BaiduLLMTest {

    public static void main(String[] args) throws Exception {
        String jsonBody = """
                {
                    "model": "ernie-3.5-8k",
                    "stream_options": {
                        "include_usage": true
                    },
                    "messages": [{
                        "role": "user",
                        "content": "对这个网站进行快速摘要，需要包含核心要点、段落大意"
                    }],
                    "plugins": ["ChatFilePlus"],
                    "stream": true,
                    "plugin_options": {
                        "plugin_args": {
                            "ChatFilePlus": {
                                "body": {
                                    "files": [
                                        [{
                                            "type": "link",
                                            "url": "www.baidu.com",
                                            "name": "www.baidu.com"
                                        }]
                                    ]
                                }
                            }
                        }
                    }
                }
                """;

        HttpClient client = HttpClient.newHttpClient();

        HttpRequest request = HttpRequest.newBuilder()
                .uri(URI.create("https://qianfan.baidubce.com/v2/chat/completions"))
                .header("Content-Type", "application/json")
                .header("Authorization", "Bearer bce-v3/ALTAK-iJ6t4JgE9kjQN4TI1bdGA/904f225682637506380c046585f82b4a248f3829")
                .POST(HttpRequest.BodyPublishers.ofString(jsonBody))
                .build();

        HttpResponse<String> response = client.send(request, HttpResponse.BodyHandlers.ofString());

        System.out.println("ok");
        System.out.println(response.statusCode());
        System.out.println(response.body());
    }

}
