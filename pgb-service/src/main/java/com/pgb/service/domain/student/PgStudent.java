package com.pgb.service.domain.student;

import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;

import com.pgb.service.enums.IdentityTypeEnum;
import lombok.Data;

/**
 * 学生表
 * @TableName pg_student
 */
@TableName(value ="pg_student")
@Data
public class PgStudent implements Serializable {
    /**
     * 学生id
     */
    private Long id;

    /**
     * 学生名字
     */
    private String name;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 学号
     */
    private String studentNo;

    /**
     * 所属班级id
     */
    private Long classId;

    /**
     * 关联用户id
     */
    private Long studentUserId;
}
