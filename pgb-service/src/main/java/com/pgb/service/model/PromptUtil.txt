package com.pgb.service.model;

import cn.hutool.core.util.StrUtil;

/**
 * <AUTHOR>
 * Created by 2024/8/14 14:22
 */
public class PromptUtil {

    /**
     * 普通对话
     *
     * @param userContent
     * @return
     */
    public static String common(String userContent) {

        String prompt = """
                用户消息内容：{}
                """;
        return StrUtil.format(prompt, userContent);
    }


    /**
     * 教案设计
     *
     * @param form
     * @return
     */
    public static String lesson(ChatFrom form) {
        String prompt = """
                请根据以下内容生成一份教案设计表。

                当素材内容为空时，则可以忽略。

                教案设计内容包括：教材分析（包括学情分析,教材分析）、教学目标（包括文化自信、语言运用、思维能力、审美创造）、教学重难点、教学方法和准备、教学环节、教师活动、学生活动、评价任务。

                教案名称独占第一行并居中对齐。每个内容块都需要一个大标题，且大标题独占一行并左对齐。同时，使用数字编号对涉及子内容的部分进行标注。

                以下是可能用到的素材内容：
                学科：{}
                教材：{}
                阶段：{}
                年级：{}
                课题：{}
                教学框架：{}
                教案参考资料：{}
                其他要求：{}
                """;

        return StrUtil.format(prompt,
                form.getSubject().desc,
                form.getTextbook(),
                form.getStage().desc,
                form.getVolume().desc,
                form.getTopic(),
                form.getProcess(),
                form.getFileContent(),
                form.getOtherRequire()
        );
    }

    /**
     * 说课稿
     *
     * @param form
     * @return
     */
    public static String speech(ChatFrom form) {
        String prompt = """
                请根据以下内容设计一份说课稿，不要无序列表，要有序列表，所有序号必须使用数字。

                当素材内容为空时，则可以忽略。

                听课稿内容包括：教材分析（包括学情分析）、教学目标（包括文化自信、语言运用、思维能力、审美能力）、教学重难点、教学方法和准备、教学过程、教学环节、板书设计。

                说课稿名称独占第一行。每个内容块都需要一个大标题，且大标题独占一行。同时，使用数字编号对涉及子内容的部分进行标注。

                以下是可能用到的素材内容：
                学科：{}
                教材：{}
                学段：{}
                册别：{}
                课题：{}
                写作框架：{}
                其他要求：{}
                """;

        return StrUtil.format(prompt,
                form.getSubject().desc,
                form.getTextbook(),
                form.getStage().desc,
                form.getVolume().desc,
                form.getTopic(),
                form.getWritingFramework(),
                form.getOtherRequire()
        );
    }

    /**
     * 大单元设计
     *
     * @param form
     * @return
     */
    public static String unit(ChatFrom form) {
        String prompt = """
                请根据以下内容，协助老师对整个单元教学内容的规划和实施做教学设计，不要无序列表，要有序列表，所有序号必须使用数字。

                单元设计内容包括：大单元主题和实施计划。

                当素材内容为空时，则可以忽略。

                主题名称独占第一行。每个内容块都需要一个大标题，且大标题独占一行。同时，使用数字编号对涉及子内容的部分进行标注。

                以下是可能用到的素材内容：
                学科：{}
                教材：{}
                册别：{}
                单元主题：{}
                所需课时：{}
                单元目录：{}
                其他要求：{}
                """;

        return StrUtil.format(prompt,
                form.getSubject().desc,
                form.getTextbook(),
                form.getVolume().desc,
                form.getTopic(),
                form.getRequiredHour(),
                form.getUnitDirectory(),
                form.getOtherRequire()
        );
    }

    /**
     * 大单元教学设计
     *
     * @param form
     * @return
     */
    public static String unitTeach(ChatFrom form) {
        String prompt = """
                请根据以下内容，协助老师对整个单元教学内容的规划和实施做教学设计，不要无序列表，要有序列表，所有序号必须使用数字。

                单元设计内容包括：教学目标、教学内容、教学方法、教学评价等。

                当素材内容为空时，则可以忽略。

                主题名称独占第一行。每个内容块都需要一个大标题，且大标题独占一行。同时，使用数字编号对涉及子内容的部分进行标注。

                以下是可能用到的素材内容：
                学科：{}
                教材：{}
                册别：{}
                单元主题：{}
                所需课时：{}
                单元目录：{}
                其他要求：{}
                """;

        return StrUtil.format(prompt,
                form.getSubject().desc,
                form.getTextbook(),
                form.getVolume().desc,
                form.getTopic(),
                form.getRequiredHour(),
                form.getUnitDirectory(),
                form.getOtherRequire()
        );
    }

    /**
     * 单元作业设计
     *
     * @param form
     * @return
     */
    public static String unitHomework(ChatFrom form) {
        String prompt = """
                请根据以下内容，协助老师做单元作业设计。

                单元作业设计内容可包括：一、单元教学内容与要求 二、知识点梳理 三、单元作业内容 四、作业提交要求 五、评价标准与反馈等。

                根据课时来布置相应的作业，设计内容也可做相应的补充和优化。

                主题名称独占第一行。每个内容块都需要一个大标题，且大标题独占一行。同时，使用数字编号对涉及子内容的部分进行标注。

                以下是可能用到的素材内容：
                学科：{}
                教材：{}
                册别：{}
                单元主题：{}
                所需课时：{}
                单元作业学习目标：{}
                课文文档：{}
                其他要求：{}
                """;

        return StrUtil.format(prompt,
                form.getSubject().desc,
                form.getTextbook(),
                form.getVolume().desc,
                form.getTopic(),
                form.getRequiredHour(),
                form.getLearningTarget(),
                form.getOtherRequire()
        );
    }

    /**
     * 跨学科设计
     *
     * @param form
     * @return
     */
    public static String crossSubject(ChatFrom form) {
        String prompt = """
                请根据以下内容，协助老师完成跨学科项目设计，不要无序列表，要有序列表，所有序号必须使用数字。

                当素材内容为空时，则可以忽略。

                内容包括：项目概述、项目背景、项目目标、实施步骤、预期成果、评估标准、结论等

                以下是可能用到的素材内容：
                相关学科：{}
                年级：{}
                课题/知识点/某个问题/目的/现象：{}
                课本原文：{}
                其他要求：{}
                """;

        return StrUtil.format(prompt,
                form.getRelatedSubject(),
                form.getGrade(),
                form.getTopic(),
                form.getTextbookOriginal(),
                form.getOtherRequire()
        );
    }

    /**
     * 跨学科设计PBL
     *
     * @param form
     * @return
     */
    public static String crossSubjectPBL(ChatFrom form) {
        String prompt = """
                请根据以下内容，协助老师完成跨学科PBL项目设计，不要无序列表，要有序列表，所有序号必须使用数字。

                当素材内容为空时，则可以忽略。

                内容包括：项目主题、学习目标、项目过程、学习资源、评估方式、项目框架、角色和责任（明确参与项目的学生、教师和辅导员的角色和责任）、策略和技巧、反思和收获等。

                以下是可能用到的素材内容：
                主学科：{}
                阶段：{}
                册别：{}
                教材：{}
                课题/知识点：{}
                相关领域学科：{}
                活动主题：{}
                课本原文：{}
                其他要求：{}
                """;

        return StrUtil.format(prompt,
                form.getSubject().desc,
                form.getStage().desc,
                form.getVolume().desc,
                form.getTextbook(),
                form.getTopic(),
                form.getRelatedSubject(),
                form.getActivityTheme(),
                form.getTextbookOriginal(),
                form.getOtherRequire()
        );
    }

    /**
     * 教育案例设计
     *
     * @param form
     * @return
     */
    public static String educationCase(ChatFrom form) {
        String prompt = """
                请根据以下内容，帮老师生成生成提供案例分析，不要无序列表，要有序列表，所有序号必须使用数字。

                案例分析内容可以包括：背景、分析、建议、结论等。

                主题名称独占第一行。每个内容块都需要一个大标题，且大标题独占一行。同时，使用数字编号对涉及子内容的部分进行标注。

                当素材内容为空时，则可以忽略。

                 以下是可能用到的素材内容：
                 案例概述：{}
                 写作手法：{}
                 其他要求：{}
                """;

        return StrUtil.format(prompt,
                form.getCaseDescription(),
                form.getWritingMethod(),
                form.getOtherRequire()
        );
    }

    /**
     * 听评课
     *
     * @param form
     * @return
     */
    public static String listenClass(ChatFrom form) {
        String prompt = """
                请根据以下内容，帮老师生成一个听评课记录表，并返回一个表格。

                内容包括：教学内容、教学过程、分析评议、听课心得（优点、建议）等，具体内容请帮忙补充完善。

                当素材内容为空时，则可以忽略。

                以下是可能用到的素材内容：
                年级：{}
                科目：{}
                听课主题：{}
                其他要求：{}
                """;

        return StrUtil.format(prompt,
                form.getGrade(),
                form.getSubject().desc,
                form.getTopic(),
                form.getOtherRequire()
        );
    }

    /**
     * 教师慧语
     *
     * @param form
     * @return
     */
    public static String wisdom(ChatFrom form) {
        String prompt = """
                请根据以下内容，帮老师生成高情商的话术回复学生、家长还有领导的不同问题。

                当素材内容为空时，则可以忽略。

                以下是可能用到的素材内容：
                回复谁：{}
                回复问题：{}
                我的态度：{}
                回复风格：{}
                其他：{}

                """;

        return StrUtil.format(prompt,
                form.getReplyTo(),
                form.getAttitude(),
                form.getReplyStyle(),
                form.getOtherRequire()
        );
    }

    /**
     * 学生评语
     *
     * @param form
     * @return
     */
    public static String studentComment(ChatFrom form) {
        String prompt = """
                请根据以下对学生评价的核心内容，若素材内容为空，则可以忽略，帮老师生成完整的评语，若学生姓名有多个，则给每个学生都生成不同的评语。

                当素材内容为空时，则可以忽略。

                以下是可能用到的素材内容：
                学生姓名：{}
                所在年级：{}
                性别：{}
                学生在某方面的优秀表现：{}，
                学生需要提升改进的部分：{}
                """;

        return StrUtil.format(prompt,
                form.getStudentName(),
                form.getGrade(),
                form.getStudentGender(),
                form.getGoodPerformance(),
                form.getBadPerformance()
        );
    }

    /**
     * 家访记录表
     *
     * @param form
     * @return
     */
    public static String homeVisit(ChatFrom form) {
        String prompt = """
                请根据以下内容，帮老师生成家访记录表，并返回一个表格。

                注：合理排版家访记录表格的内容！！当素材内容为空时，则可以忽略。

                以下是可能用到的素材内容： 具体内容还请补充完善
                被访学生姓名：{}
                班级：{}
                家访教师姓名：{}
                学生家庭住址：{}
                学生联系电话：{}，
                家访时间：{},
                学生概况：{},
                家访过程：{}

                """;

        return StrUtil.format(prompt,
                form.getStudentName(),
                form.getGrade(),
                form.getTeacherName(),
                form.getAddress(),
                form.getPhone(),
                form.getVisitTime(),
                form.getStudentProfile(),
                form.getVisitProcess()
        );
    }

    /**
     * 家长会发言稿
     *
     * @param form
     * @return
     */
    public static String parentMeeting(ChatFrom form) {
        String prompt = """
                请根据以下内容，若素材内容为空，则可以忽略，帮助老师生成家长会发言稿，不要无序列表，要有序列表，所有序号必须使用数字。

                注：当素材内容为空时，则可以忽略。

                以下是可能用到的素材内容：
                主题：{}
                班级：{}
                目的：{}
                班级介绍：{}
                学生表现：{}，
                课程与教学情况：{},
                学生个案分享：{},
                问题与挑战：{}
                其他：{}

                """;

        return StrUtil.format(prompt,
                form.getTopic(),
                form.getGrade(),
                form.getPurpose(),
                form.getClassInfo(),
                form.getPerformance(),
                form.getCourseInfo(),
                form.getStudentCase(),
                form.getProblem(),
                form.getOtherRequire()
        );
    }

    /**
     * 教学计划
     *
     * @param form
     * @return
     */
    public static String teachingPlan(ChatFrom form) {
        String prompt = """
                请根据以下内容，若素材内容为空，则可以忽略，帮助老师生成教学计划，所有序号必须使用数字。

                注：当素材内容为空时，则可以忽略。

                生成内容可参考：教学目标、课程设置、教学形式、教学环节

                以下是可能用到的素材内容：
                教学计划类型：{}
                课程相关信息：{}
                """;

        return StrUtil.format(prompt,
                form.getPlanType(),
                form.getCourseRelatedInfo()
        );
    }

    /**
     * 扩写内容
     *
     * @param content
     * @return
     */
    public static String expand(String content) {

        String prompt = """
                请根据以下内容，对其进行扩写，使其更丰富、生动，并以纯文本形式返回。

                以下是可能用到的素材内容：
                内容：{}
                """;

        return StrUtil.format(prompt, content);
    }

    /**
     * 全文生成
     *
     * @param form
     * @return
     */
    public static String generate(ChatFrom form) {
        String prompt = """
                根据素材内容，返回相应的内容。

                注：当素材内容为空时，则可以忽略。

                以下是可能用到的素材内容：
                题目或主题：{}
                全文类型：{}
                文体类别：{}
                全文长度：{}

                """;

        return StrUtil.format(prompt,
                form.getTopic(),
                form.getType(),
                form.getStyle(),
                form.getWordNum()
        );
    }
}
