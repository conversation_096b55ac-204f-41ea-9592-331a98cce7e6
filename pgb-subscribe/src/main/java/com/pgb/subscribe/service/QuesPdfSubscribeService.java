package com.pgb.subscribe.service;

import cn.binarywang.wx.miniapp.api.WxMaService;
import cn.binarywang.wx.miniapp.bean.WxMaSubscribeMessage;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.TimeInterval;
import cn.hutool.core.lang.TypeReference;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.json.JSONUtil;
import com.pgb.common.core.global.BaseResult;
import com.pgb.common.core.global.exception.BaseException;
import com.pgb.common.core.utils.EnvUtils;
import com.pgb.common.redis.QueueUtils;
import com.pgb.common.redis.RedisUtils;
import com.pgb.service.custom.CorrectService;
import com.pgb.service.db.PgUsersService;
import com.pgb.service.domain.GlobQueueConstants;
import com.pgb.service.domain.user.PgUsers;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.error.WxErrorException;
import org.redisson.api.RBlockingQueue;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.util.Date;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.concurrent.Executors;
import java.util.concurrent.atomic.AtomicReference;

import static com.pgb.service.custom.QwMsgService.sendPdfErrorMessage;

/**
 * <AUTHOR>
 * Created by 2025/7/7 10:45
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class QuesPdfSubscribeService {

    private final PgUsersService pgUsersService;

    private final CorrectService correctService;

    private final WxMaService wxMaService;

    @Async
    public void cloudScribe(String queueName) {
        // 获取下载队列
        RBlockingQueue<Long> queue = QueueUtils.getClient().getBlockingQueue(queueName);
        AtomicReference<Boolean> isContinue = new AtomicReference<>(true);
//        Executor executor = Executors.newFixedThreadPool(10);
        while (isContinue.get()) {
            try {
                AtomicReference<Long> zcQuesId = new AtomicReference<>();

                // 阻塞获取一个题目
                zcQuesId.set(queue.take());

                CompletableFuture<Void> f = CompletableFuture.runAsync(() -> {

                    try {
                        log.info("【{}-接收字词题目PDF渲染监听信息】：{}", queueName, zcQuesId.get());

                        Boolean result = cloudExport(zcQuesId.get());

//                        if (result){
//                            // 获取题目
//                            PgZcQuestion zcQuestion = pgZcQuestionService.getById(zcQuesId.get());
//
//                            if (!EnvUtils.isDev()){
//                                sendWxMsg(zcQuestion);
//                            }
//                        }

                    } catch (Exception e) {
                        log.error("【{}-字词题目PDF渲染队列异常】：{}，进入重试机制", queueName, zcQuesId.get(), e);
                        // 进入重试机制
                        retry(zcQuesId.get());
                    }
                }).exceptionally(e -> {
                    log.error("【{}-字词题目PDF渲染队列异常】：{}，异步异常", queueName, zcQuesId.get(), e);
                    return null;
                }).whenComplete((v, e1) -> {
                    // 判断是否继续
                    if (!correctService.getCorrecting()) {
                        log.info("当前pdf渲染线程已手动停止");
                        isContinue.set(false);
                    }
                });
            } catch (InterruptedException e) {
                if (ObjectUtil.isNotNull(e.getMessage()) && e.getMessage().contains("Redisson is shutdown")) {
                    log.error("QuesPdfSubscribeService --> Redisson 已中断连接");
                } else {
                    e.printStackTrace();
                }
            }
        }
    }


    private Boolean cloudExport(Long zcQuestId) {

        TimeInterval timer = DateUtil.timer();

        String url = "http://zw-correct-cloud.pigaibang.com/api/export/zc/" + zcQuestId;

        String resultStr = HttpRequest.get(url)
                .timeout(20 * 60 * 1000)
                .execute()
                .body();

        BaseResult<Boolean> result = Convert.convert(new TypeReference<BaseResult<Boolean>>() {
        }, JSONUtil.parseObj(resultStr));

        if (result.isSuccess()) {
            log.info("【云函数字词题目PDF-渲染结束】{}用时：{}", zcQuestId, timer.intervalPretty());
            return true;
        } else {
            log.error("【云函数字词题目PDF渲染异常-ERROR】{}请检查：{}", zcQuestId, result.getMsg());
            throw new BaseException("云函数字词题目PDF渲染异常, 请检查");
        }
    }

    private void sendWxMsg(Long userId) {

        // 发送通知
        PgUsers users = pgUsersService.getById(userId);

        WxMaSubscribeMessage message = new WxMaSubscribeMessage();
        message.setToUser(users.getWxOpenId());
        message.setTemplateId("UsNIR66iF_rjs593zh4uPwFGspXNqtU1Lws97gefIRw");
        message.setPage("/pages/my/export/index");
        message.setData(ListUtil.toList(
                new WxMaSubscribeMessage.MsgData("phrase1", "已完成"),
                new WxMaSubscribeMessage.MsgData("thing2", DateUtil.format(new Date(), "HH:mm"))
        ));
        if (EnvUtils.isDev()) {
            message.setMiniprogramState("developer");
        }

        try {
            wxMaService.getMsgService().sendSubscribeMsg(message);
        } catch (WxErrorException e) {
            log.error("小程序模板发送失败：{}", e.getError());
        }
    }


    /**
     * 队列重试
     *
     * @param zcQuestionId
     */
    public void retry(Long zcQuestionId) {

        if (ObjectUtil.isNotNull(zcQuestionId)) {

            // 重试三次
            Integer retry = RedisUtils.getCacheObject(GlobQueueConstants.PGB_XCX_ZC_QUES_PDF_RETRY.name() + zcQuestionId);

            if (ObjectUtil.defaultIfNull(retry, 0) < 3) {
                // 加入渲染队列
                QueueUtils.addQueueObject(GlobQueueConstants.PGB_XCX_ZC_QUES_PDF_QUEUE.name(), zcQuestionId);

                // 发送异常报警通知
                if (!EnvUtils.isDev()) {
                    sendPdfErrorMessage("已进入重试队列，重试次数：" + ObjectUtil.defaultIfNull(retry, 0));
                }
                // 重试次数
                RedisUtils.setCacheObject(GlobQueueConstants.PGB_XCX_ZC_QUES_PDF_RETRY.name() + zcQuestionId, ObjectUtil.defaultIfNull(retry, 0) + 1, Duration.ofHours(6));
            } else {
                // 发送异常报警通知
                if (!EnvUtils.isDev()) {
                    sendPdfErrorMessage("已重试3次！必须线上排查！id：" + zcQuestionId);
                }
            }
        } else {
            log.error("无效zcQuestionId，需排查");
        }
    }
}
