package com.pgb.service.domain.vip;

import java.io.Serializable;

import java.util.Date;

import cn.hutool.core.util.ObjectUtil;
import com.fasterxml.jackson.annotation.JsonGetter;
import com.pgb.service.enums.ChannelTypeEnum;
import com.pgb.service.enums.VipTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.ToString;

/**
 * @TableName pg_vip_code
 */
@Schema(description = " VO")
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode
@ToString
public class PgVipCodeVO implements Serializable {

    @Schema(title = "用户兑换码")
    private Long id;

    @Schema(title = "兑换码")
    private String code;

    @Schema(title = "使用状态，0：未使用，1:已使用，2：已作废")
    private Integer status;

    @Schema(title = "创建时间")
    private Date createTime;

    @Schema(title = "兑换时间")
    private Date exchangeTime;

    @Schema(title = "兑换用户id")
    private Long userId;

    @Schema(title = "生成渠道，0：系统生成，1：小红书")
    private ChannelTypeEnum channelType;

    @Schema(title = "会员类型")
    private VipTypeEnum vipType;

    @Schema(title = "如果有渠道id")
    private Long channelId;

    @Schema(title = "所属活动")
    private String activityName;

    @Schema(title = "生成渠道")
    @JsonGetter("channelTypeStr")
    public String getChannelTypeStr() {
        return ObjectUtil.isNotNull(this.channelType) ? this.channelType.desc : null;
    }

    @Schema(title = "会员类型")
    @JsonGetter("vipTypeStr")
    public String getVipTypeStr() {
        return ObjectUtil.isNotNull(this.vipType) ? this.vipType.desc : null;
    }
}
