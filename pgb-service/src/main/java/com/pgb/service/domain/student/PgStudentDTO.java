package com.pgb.service.domain.student;

import java.io.Serializable;

import java.util.Date;

import com.pgb.service.enums.IdentityTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.ToString;
import lombok.Builder;
import org.springframework.validation.annotation.Validated;
import jakarta.validation.constraints.Size;

/**
* 学生表
* @TableName pg_student
*/
@Schema(description = "学生表 DTO")
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode
@ToString
@Validated
@Builder
public class PgStudentDTO implements Serializable {

    @Schema(title = "学生id")
    private Long id;

    @Schema(title = "学生名字")
    @Size(max= 255)
    private String name;

    @Schema(title = "创建时间")
    private Date createTime;

    @Schema(title = "学号")
    @Size(max= 255)
    private String studentNo;

    @Schema(title = "所属班级id")
    private Long classId;

    @Schema(title = "关联用户id")
    private Long studentUserId;

}
