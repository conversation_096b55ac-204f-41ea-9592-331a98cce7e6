package com.pgb.subscribe.controller;

import cn.hutool.core.util.ObjectUtil;
import com.pgb.common.core.global.BaseResult;
import com.pgb.service.custom.CorrectService;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


@RequestMapping("/user/zw/info")
@RestController
@RequiredArgsConstructor
@Slf4j
public class CorrectController {

    private final CorrectService correctService;

    // http://127.0.0.1:8083/api/user/zw/info/setCorrectStatus/setCorrectStatus/false
    @Operation(summary = "停止当前批改")
    @GetMapping("setCorrectStatus/{key}/{status}")
    public BaseResult<Boolean> setCorrectStatus(@PathVariable String key, @PathVariable Boolean status) {
        if (ObjectUtil.isNull(key)) {
            return BaseResult.error("参数错误");
        }

        if (!"setCorrectStatus".equals(key)) {
            return BaseResult.error("参数错误");
        }

        // 停止批改
        correctService.setCorrecting(status);

        log.info("手动停止当前批改服务");

        return BaseResult.success(correctService.getCorrecting());
    }
}
