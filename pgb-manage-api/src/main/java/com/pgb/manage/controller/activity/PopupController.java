package com.pgb.manage.controller.activity;

import cn.dev33.satoken.annotation.SaCheckRole;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.pgb.common.core.global.BaseResult;
import com.pgb.common.core.global.GlobalCode;
import com.pgb.common.core.global.RoleConstants;
import com.pgb.service.db.PgActivityService;
import com.pgb.service.db.PgPopupService;
import com.pgb.service.db.PgPopupUserService;
import com.pgb.service.db.PgTagService;
import com.pgb.service.domain.activity.ActivityDiscountConfig;
import com.pgb.service.domain.activity.PgActivity;
import com.pgb.service.domain.activity.PgActivityVO;
import com.pgb.service.domain.popup.PgPopup;
import com.pgb.service.domain.popup.PgPopupDTO;
import com.pgb.service.domain.popup.PgPopupUser;
import com.pgb.service.domain.popup.PgPopupVO;
import com.pgb.service.domain.query.PopupQuery;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.util.Date;

@Tag(name = "管理端/弹窗管理")
@RestController("ManagePopupController")
@Transactional(rollbackFor = Exception.class)
@RequestMapping("/manage/popup")
@RequiredArgsConstructor
@Slf4j
@SaCheckRole(RoleConstants.Manager)
public class PopupController {

    private final PgPopupService pgPopupService;

    private final PgActivityService pgActivityService;

    private final PgTagService pgTagService;

    private final PgPopupUserService pgPopupUserService;

    @Operation(summary = "弹窗列表")
    @PostMapping("list")
    public BaseResult<IPage<PgPopupVO>> list(@RequestBody PopupQuery query) {

        IPage<PgPopupVO> page = pgPopupService.page(query.toMpPage(), new LambdaQueryWrapper<PgPopup>()
                        .like(StrUtil.isNotBlank(query.getRemark()), PgPopup::getRemark, query.getRemark())
                        .orderByAsc(PgPopup::getSort))
                .convert(
                        pgPopup -> {
                            PgPopupVO popupVO = BeanUtil.copyProperties(pgPopup, PgPopupVO.class, "tagIds");

                            // 关联需弹窗用户标签
                            if (StrUtil.isNotBlank(pgPopup.getTagIds())) {
                                popupVO.setTagIds(
                                        StrUtil.split(pgPopup.getTagIds(), StrUtil.COMMA)
                                                .stream()
                                                .map(Long::parseLong)
                                                .toList()
                                );
                                popupVO.setTagList(
                                        pgTagService.listByIds(popupVO.getTagIds())
                                );
                            }

                            // 排除的标签
                            if (StrUtil.isNotBlank(pgPopup.getNotTagIds())) {
                                popupVO.setNotTagIds(
                                        StrUtil.split(pgPopup.getNotTagIds(), StrUtil.COMMA)
                                                .stream()
                                                .map(Long::parseLong)
                                                .toList()
                                );
                                popupVO.setNotTagList(
                                        pgTagService.listByIds(popupVO.getNotTagIds())
                                );
                            }

                            // 活动内容
                            if (ObjectUtil.isNotNull(pgPopup.getActivityId())) {
                                PgActivity activity = pgActivityService.getById(pgPopup.getActivityId());

                                PgActivityVO activityVO = BeanUtil.copyProperties(activity, PgActivityVO.class, "config");

                                if (ObjectUtil.isNotNull(activity.getConfig())) {
                                    ActivityDiscountConfig discountConfig = JSONUtil.toBean(activity.getConfig().toString(), ActivityDiscountConfig.class);
                                    activityVO.setConfig(discountConfig);
                                }
                                popupVO.setActivity(activityVO);
                            }

                            // 有效弹窗人数
                            long receiveNum = pgPopupUserService.count(new LambdaQueryWrapper<PgPopupUser>()
                                    .eq(PgPopupUser::getPopupId, pgPopup.getId()));
                            popupVO.setReceiveNum((int) receiveNum);

                            return popupVO;
                        }
                );

        return BaseResult.success(page);
    }

    @Operation(summary = "新增弹窗")
    @PostMapping("create")
    public BaseResult<Boolean> create(@RequestBody PgPopupDTO popupsDTO) {

        // 保存弹窗基本信息
        PgPopup popups = BeanUtil.copyProperties(popupsDTO, PgPopup.class, "tagIds");

        // 用户标签列表，根据英文逗号分隔
        if (ObjectUtil.isNotNull(popupsDTO.getTagIds()) && !popupsDTO.getTagIds().isEmpty()) {
            popups.setTagIds(CollUtil.join(popupsDTO.getTagIds(), StrUtil.COMMA));
        }

        // 排除标签列表
        if (ObjectUtil.isNotNull(popupsDTO.getNotTagIds()) && !popupsDTO.getNotTagIds().isEmpty()) {
            popups.setNotTagIds(CollUtil.join(popupsDTO.getNotTagIds(), StrUtil.COMMA));
        }

        popups.setCreateTime(new Date());
        pgPopupService.save(popups);

        return BaseResult.success(true);
    }

    @Operation(summary = "修改弹窗配置")
    @PostMapping("update/{id}")
    public BaseResult<Boolean> update(@PathVariable Long id, @RequestBody PgPopupDTO popupsDTO) {

        PgPopup popup = pgPopupService.getById(id);

        if (ObjectUtil.isNull(popup)) {
            return BaseResult.code(GlobalCode.Item_Null);
        }

        BeanUtil.copyProperties(popupsDTO, popup);

        // 用户标签列表，根据英文逗号分隔
        if (ObjectUtil.isNull(popupsDTO.getTagIds()) || popupsDTO.getTagIds().isEmpty()) {
            pgPopupService.update(Wrappers.<PgPopup>lambdaUpdate()
                    .eq(PgPopup::getId, id)
                    .set(PgPopup::getTagIds, null));
        } else {
            popup.setTagIds(CollUtil.join(popupsDTO.getTagIds(), StrUtil.COMMA));

        }

        // 排除标签列表
        if (ObjectUtil.isNull(popupsDTO.getNotTagIds()) || popupsDTO.getNotTagIds().isEmpty()) {
            pgPopupService.update(Wrappers.<PgPopup>lambdaUpdate()
                    .eq(PgPopup::getId, id)
                    .set(PgPopup::getNotTagIds, null));
        } else {
            popup.setNotTagIds(CollUtil.join(popupsDTO.getNotTagIds(), StrUtil.COMMA));
        }

        pgPopupService.updateById(popup);
        return BaseResult.success(true);
    }

    @Operation(summary = "查看弹窗配置")
    @GetMapping("{id}")
    public BaseResult<PgPopupVO> get(@PathVariable Long id) {

        PgPopup popup = pgPopupService.getById(id);

        if (ObjectUtil.isNull(popup)) {
            return BaseResult.code(GlobalCode.Item_Null);
        }

        PgPopupVO popupVO = BeanUtil.copyProperties(popup, PgPopupVO.class);

        return BaseResult.success(popupVO);
    }

    @Operation(summary = "删除弹窗配置")
    @DeleteMapping("{id}")
    public BaseResult<Boolean> delete(@PathVariable Long id) {

        PgPopup popup = pgPopupService.getById(id);

        if (ObjectUtil.isNull(popup)) {
            return BaseResult.code(GlobalCode.Item_Null);
        }

        pgPopupService.removeById(id);

        return BaseResult.success(true);
    }
}
