package com.pgb.student.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;

import com.pgb.service.enums.IdentityTypeEnum;
import lombok.Data;

/**
 * 用户家庭表
 * @TableName pg_family
 */
@TableName(value ="pg_family")
@Data
public class PgFamily implements Serializable {
    /**
     * 用户家庭id
     */
    private Long id;

    /**
     * 邀请人（主号）用户id
     */
    private Long userId;

    /**
     * 被邀请人（子号）用户id
     */
    private Long inviteUserId;

    /**
     * 身份关系
     */
    private IdentityTypeEnum identity;

    /**
     * 学生id
     */
    private Long studentId;

    /**
     * 创建时间
     */
    private Date createTime;

}
