package com.pgb.service.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;

/**
 * <AUTHOR>
 * Created by 2025/1/8 14:58
 */
@Schema(title = "统计内容类型")
@AllArgsConstructor
public enum StatisticContentTypeEnum {


    Register_Num(0,"注册用户数量"),

    Register_User_Submit(1,"注册用户体验数量"),

    Use_User_Num(2,"使用用户总数量"),

    Submit_Num(3,"提交作文总数量");

    /**
     * 枚举值对应的整数值
     */
    @EnumValue
    public final int value;

    /**
     * 文字说明
     */
    public final String desc;
}
