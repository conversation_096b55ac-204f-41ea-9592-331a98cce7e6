package com.pgb.service.domain.userConfig;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.util.Date;

import com.pgb.common.mybatis.handler.JsonbTypeHandler;
import lombok.Data;

/**
 * 用户配置表
 *
 * @TableName pg_user_config
 */
@TableName(value = "pg_user_config", autoResultMap = true)
@Data
public class PgUserConfig implements Serializable {
    /**
     * 用户配置
     */
    private Long id;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * key值
     */
    private String key;

    /**
     * 配置value值
     */
    @TableField(typeHandler = JsonbTypeHandler.class)
    private Object value;

    /**
     * 备注说明
     */
    private String remark;

    /**
     * 是否启用
     */
    private Boolean isValid;

    /**
     * 创建时间
     */
    private Date createTime;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
