package com.pgb.ai.domain.doubao;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 豆包TTS响应结果
 *
 * <AUTHOR>
 */
@Schema(title = "豆包TTS响应结果")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DoubaoTTSResponse {

    @Schema(title = "响应码", description = "0表示成功，20000000表示结束，其他表示错误")
    private Integer code;

    @Schema(title = "响应消息")
    private String message;

    @Schema(title = "音频数据", description = "Base64编码的音频数据")
    private String data;

    @Schema(title = "请求ID")
    private String requestId;

    @Schema(title = "时间戳")
    private Long timestamp;

    /**
     * 判断是否成功
     *
     * @return true表示成功
     */
    public boolean isSuccess() {
        return code != null && code == 0;
    }

    /**
     * 判断是否结束
     *
     * @return true表示结束
     */
    public boolean isEnd() {
        return code != null && code == 20000000;
    }

    /**
     * 判断是否有错误
     *
     * @return true表示有错误
     */
    public boolean hasError() {
        return code != null && code > 0 && code != 20000000;
    }

    /**
     * 判断是否有音频数据
     *
     * @return true表示有音频数据
     */
    public boolean hasAudioData() {
        return data != null && !data.trim().isEmpty();
    }
}
