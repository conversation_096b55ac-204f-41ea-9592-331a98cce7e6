package com.pgb.xcx.controller.application;

import cn.hutool.core.date.DateUtil;
import cn.hutool.json.JSONUtil;
import com.pgb.ai.domain.ChatRes;
import jakarta.websocket.*;
import jakarta.websocket.server.PathParam;
import jakarta.websocket.server.ServerEndpoint;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.io.EOFException;
import java.io.IOException;
import java.util.concurrent.ConcurrentHashMap;

import static com.pgb.ai.domain.ChatRes.ChatResType.*;

@Slf4j
@Component
@ServerEndpoint("/model/{chatId}")
public class ModelSocket {
    // 线程安全
    private static final ConcurrentHashMap<Long, Session> chatMaps = new ConcurrentHashMap<>();

    /**
     * 开启
     *
     * @param session
     * @param chatId
     */
    @OnOpen
    public void onOpen(Session session, @PathParam("chatId") Long chatId) {

        // 将session按照对话id来存储
        if (!chatMaps.containsKey(chatId)) {
            // 创建对话不存在时，创建对话
            chatMaps.put(chatId, session);
        } else {
            // 房间已存在，直接添加用户到相应的房间
            chatMaps.get(chatId);
        }

        log.info("{}用户处于生成状态：{}", session.getId(), chatId);

        // 发送开始状态
        this.sendMessage(ChatRes.builder()
                .type(START)
                .build(), session);
    }

    /**
     * 连接关闭调用的方法
     */
    @OnClose
    public void onClose(Session session, @PathParam("chatId") Long chatId) {
        // 关闭session
        try {
            if (session.isOpen()) {
                session.close();
                chatMaps.remove(chatId);
            }
        } catch (IOException e) {
            log.error("关闭session失败", e);
        }

        log.info("用户退出生成状态：{}", chatId);
    }

    /**
     * 收到客户端消息后调用的方法
     *
     * @param message 客户端发送过来的消息
     */
    @OnMessage
    public void onMessage(String message, Session session, @PathParam("chatId") Long chatId) {
        ChatRes body;
        // 转化消息体
        try {
            body = JSONUtil.toBean(message, ChatRes.class);
        } catch (Exception e) {
            this.sendMessage(
                    ChatRes.builder()
                            .type(ERROR)
                            .code(ChatRes.CharResCode.ERROR)
                            .result("实体转化异常")
                            .build(),
                    session);
            return;
        }

        // 【心跳验证机制】
        if (body.getType().equals(HEART)) {
            this.sendMessage(
                    ChatRes.builder()
                            .type(HEART)
                            .code(ChatRes.CharResCode.SUCCESS)
                            .result("心跳连接")
                            .build(),
                    session);
            return;
        }

        log.info("收到客户端[{}]的消息:{}", session.getId(), message);
    }

    /**
     * 报错
     */
    @OnError
    public void onError(Session session, Throwable throwable) {
        if ((throwable instanceof EOFException) && throwable.getCause() == null) {
            log.warn("客户端异常退出：{}", session.getId());
        } else {
            log.error("系统发生错误：", throwable);
        }

        try {
            session.close();
        } catch (IOException e) {
            log.error("关闭socket发生异常", e);
        }

    }

    /**
     * 服务端发送消息给客户端
     */
    private void sendMessage(ChatRes message, Session toSession) {
        try {
            // 设置时间戳
            message.setTimestamp(DateUtil.current());
            // 发送消息
            if (toSession.isOpen()) {
                toSession.getBasicRemote().sendText(message.toJSON());
            } else {
                chatMaps.remove(message.getChatId());
            }
        } catch (Exception e) {
            log.error("服务端发送消息给客户端失败：{}", e);
        }
    }

    /**
     * 发送消息
     *
     * @param chatId
     * @param message
     */
    public void sendMessage(Long chatId, ChatRes message) {
        if (chatMaps.containsKey(chatId)) {
            Session toSession = chatMaps.get(chatId);
            message.setChatId(chatId);
            sendMessage(message, toSession);
        }
    }

    public boolean isExist(Long chatId) {
        return chatMaps.containsKey(chatId);
    }
}
