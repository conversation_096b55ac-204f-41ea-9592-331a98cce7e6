package com.pgb.service.domain.zc.common.textbook.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * 教材单元DTO
 *
 * <AUTHOR>
 * Created by 2025/7/25 20:30
 */
@Data
@Schema(description = "教材单元DTO")
public class PgTextbookUnitDTO {

    @Schema(description = "主键ID")
    private Long id;

    @Schema(description = "单元名称")
    private String name;

    @Schema(description = "所属册别ID")
    private Long volumeId;

    @Schema(description = "排序值")
    private Integer sort;

    @Schema(description = "创建时间")
    private Date createTime;

    @Schema(description = "更新时间")
    private Date updateTime;
}
