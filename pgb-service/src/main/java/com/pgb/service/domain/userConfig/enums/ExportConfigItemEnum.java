package com.pgb.service.domain.userConfig.enums;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;

@Schema(description = "单个导出配置项")
@AllArgsConstructor
public enum ExportConfigItemEnum {

    questionInfo("作文题目及要求"),
    overallComment("老师总评"),
    comment("详细点评"),
    answerImg("旁批图片"),
    polish("作文润色"),
    compare("润色原文对比"),
    writingGuide("写作指导");

    public final String desc;
}
