package com.pgb.xcx.controller.zw;

import cn.binarywang.wx.miniapp.api.WxMaService;
import cn.dev33.satoken.annotation.SaCheckLogin;
import cn.hutool.core.img.ImgUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.pgb.common.core.global.BaseResult;
import com.pgb.common.core.global.GlobalCode;
import com.pgb.common.core.utils.EnvUtils;
import com.pgb.service.db.PgAnswerService;
import com.pgb.service.domain.answer.PgAnswer;
import com.pgb.service.domain.question.zwEssay.ZwEssayQuestion;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.error.WxErrorException;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.io.File;

/**
 * <AUTHOR>
 * Created by 2024/7/2 13:49
 */

@Tag(name = "用户端/批改/批改详情")
@RestController("UserCorrectController")
@Transactional(rollbackFor = Exception.class)
@RequestMapping("/user/record/correct")
@RequiredArgsConstructor
@Slf4j
public class CorrectController {

    private final PgAnswerService pgAnswerService;

    private final WxMaService wxMaService;

    @Operation(summary = "查看作文润色")
    @PostMapping("polish/{id}")
    @SaCheckLogin
    public BaseResult<String> polish(@PathVariable Long id) {

        PgAnswer answer = pgAnswerService.getById(id);

        if (ObjectUtil.isNull(answer)) {
            return BaseResult.code(GlobalCode.Item_Null);
        }
        ZwEssayQuestion userAnswer = JSONUtil.toBean(answer.getCorrectResult().toString(), ZwEssayQuestion.class);

        return BaseResult.success(
                userAnswer.getPolish()
        );
    }

    @Operation(summary = "获取微信小程序分享二维码")
    @GetMapping("getQrCode/{answerId}")
    public BaseResult<String> getQrCode(@PathVariable Long answerId) {

        File qrCode = null;
        try {
            qrCode = wxMaService.getQrcodeService().createWxaCodeUnlimit(
                    answerId.toString(),
                    "pages/zw/index",
                    true,
                    EnvUtils.isDev() ? "develop" : "release",
                    430,
                    false,
                    null,
                    false);

        } catch (WxErrorException e) {
            return BaseResult.error(GlobalCode.Error, "小程序码生成异常");
        }

        String base64 = ImgUtil.toBase64DataUri(
                ImgUtil.read(qrCode), ImgUtil.IMAGE_TYPE_PNG
        );

        return BaseResult.success("OK", base64);

    }
}
