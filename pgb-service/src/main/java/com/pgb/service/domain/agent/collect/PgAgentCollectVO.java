package com.pgb.service.domain.agent.collect;

import java.io.Serializable;

import java.util.Date;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.ToString;
import lombok.Builder;

/**
*
* @TableName pg_agent_collect
*/
@Schema(description = " VO")
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode
@ToString
@Builder
public class PgAgentCollectVO implements Serializable {

    @Schema(title = "智能体收藏", type = "string")
    private Long id;

    @Schema(title = "用户id")
    private Long userId;

    @Schema(title = "智能体id")
    private Long agentId;

    @Schema(title = "收藏时间")
    private Date createTime;

    @Schema(title = "排序")
    private Integer sort;

}
