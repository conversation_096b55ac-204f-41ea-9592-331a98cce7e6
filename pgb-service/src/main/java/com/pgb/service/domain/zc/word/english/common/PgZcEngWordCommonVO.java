package com.pgb.service.domain.zc.word.english.common;

import java.io.Serializable;

import com.pgb.service.enums.GenerateStatusEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.ToString;
import lombok.Builder;

/**
 * @TableName pg_zc_eng_word_common
 */
@Schema(description = " VO")
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode
@ToString
@Builder
public class PgZcEngWordCommonVO implements Serializable {

    @Schema(title = "词汇表，AI生成缓存表", type = "string")
    private Long id;

    @Schema(title = "单词")
    private String word;

    @Schema(title = "词性")
    private String wordClass;

    @Schema(title = "中文释义")
    private String chinese;

    @Schema(title = "音标")
    private String phonetic;

    @Schema(title = "美式发音")
    private String usAudioUrl;

    @Schema(title = "英式发音")
    private String ukAudioUrl;

    @Schema(title = "状态，单词信息生成的状态")
    private GenerateStatusEnum status;

}
