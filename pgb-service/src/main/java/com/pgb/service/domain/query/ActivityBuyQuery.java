package com.pgb.service.domain.query;

import com.pgb.common.mybatis.global.PageQuery;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Schema(description = "活动购买情况")
@Data
public class ActivityBuyQuery extends PageQuery {
    @Schema(title = "对应活动")
    private Long activityId;

    @Schema(title = "购买时间")
    private List<Date> buyTime;

    @Schema(title = "是否兑换")
    private Boolean isUseCode;

    @Schema(title = "手机号")
    private String phone;
}
