package com.pgb.service.domain.zc.textbook;

import com.fasterxml.jackson.annotation.JsonGetter;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * Created by 2025/4/27 20:02
 */
@Data
public class UnitContent {

    @Schema(title = "单元")
    private Integer unit;

    // 映射单元名称 1：一单元 2：二单元
    @JsonGetter
    public String getUnitName() {

        return switch (unit) {
            case 1 -> "第一单元";
            case 2 -> "第二单元";
            case 3 -> "第三单元";
            case 4 -> "第四单元";
            case 5 -> "第五单元";
            case 6 -> "第六单元";
            case 7 -> "第七单元";
            case 8 -> "第八单元";
            default -> "其他";
        };
    }

}
