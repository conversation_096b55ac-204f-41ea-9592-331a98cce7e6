package com.pgb.xcx.service;

import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.URLUtil;
import com.pgb.ai.LLMService;
import com.pgb.ai.domain.GPTAnswer;
import com.pgb.ai.domain.agent.FileItem;
import com.pgb.ai.enums.AgentFormFieldType;
import com.pgb.ai.enums.FileTypeEnum;
import com.pgb.common.ocr.OCRService;
import com.pgb.common.ocr.domain.OCRResult;
import com.pgb.common.oss.service.OssService;
import com.pgb.service.custom.CorrectService;
import com.pgb.ai.domain.agent.AgentFormField;
import com.pgb.service.db.PgChatService;
import com.pgb.service.domain.common.image.FilePaperImg;
import com.pgb.service.factory.LLMServiceFactory;
import com.pgb.service.model.ConvertService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@RequiredArgsConstructor
@Slf4j
public class AgentLLMService {

    private final CorrectService correctService;

    private final OCRService ocr;

    private final OssService ossService;

    private final ConvertService convertService;

    // 注入工厂
    private final LLMServiceFactory llmServiceFactory;

    // 动态获取 LLMService
    private LLMService getLLMService() {
        return llmServiceFactory.getLLMService("ali");
    }

    public String generatePrompt(String agentPrompt, List<AgentFormField> form) {
        // 获取提示词
        StringBuilder prompt = new StringBuilder(agentPrompt);
        prompt.append("\n").append("以下是参考资料：");

        // 根据表单，来补全提示词全部信息
        for (AgentFormField field : form) {
            // 文件类型
            if (field.getType().equals(AgentFormFieldType.upload)) {
                for (FileItem item : field.getFileList()) {
                    // 如果是图片类型，启用OCR识别
                    if (item.getFileType().equals(FileTypeEnum.image)) {
                        // 先处理图片
                        FilePaperImg img = new FilePaperImg();
                        img.setImgUrl(item.getUrl());
                        img = correctService.processImg(img);
                        item.setUrl(img.getImgUrl());
                        // 识别ocr
                        OCRResult result = ocr.handWriting(item.getUrl());
                        // 获取全部文字
                        prompt.append(StrUtil.format(
                                """
                                        {}：{}
                                        """, field.getTitle(), result.getAllTxt())
                        );
                        // 将图片标识为删除
                        ossService.setTagList(
                                URLUtil.getPath(img.getImgUrl()),
                                "Deleted"
                        );
                    }
                    // 视频，跳过，特殊处理
                    else if (item.getFileType().equals(FileTypeEnum.video)) {

                    } else if (item.getFileType().equals(FileTypeEnum.audio)) {

                    }
                    // 文件类型
                    else if (item.getFileType().equals(FileTypeEnum.file)) {
                        String fileContent = "";

                        // pdf
                        if (item.getUrl().endsWith(".pdf")) {

                            fileContent = convertService.pdf2Text(item.getUrl());
                        }
                        // word
                        else if (item.getUrl().endsWith(".docx")) {
                            fileContent = convertService.word2Text(item.getUrl());
                        }

                        if (StrUtil.isNotBlank(fileContent)) {
                            prompt.append(StrUtil.format(
                                    """
                                            {}：{}
                                            """, field.getTitle(), fileContent)
                            );
                        }
                    }
                }
            }
            // 如果是普通字段，叠加
            else {
                // 数据不能为空
                if (StrUtil.isNotBlank(field.getContent())) {
                    prompt.append(StrUtil.format(
                            """
                                    {}：{}
                                    """, field.getTitle(), field.getContent())
                    );
                }
            }
        }

        log.info("智能体提示词：{}", prompt);

        return prompt.toString();
    }

    public String generatePromptV2(String agentPrompt, List<AgentFormField> form) {
        // 获取提示词
        StringBuilder prompt = new StringBuilder(agentPrompt);
        prompt.append("\n").append("以下是参考资料：");

        // 根据表单，来补全提示词全部信息
        for (AgentFormField field : form) {
            // 文件类型
            if (field.getType().equals(AgentFormFieldType.upload)) {
                for (FileItem item : field.getFileList()) {

                    // 只处理图片格式
                    if (item.getFileType().equals(FileTypeEnum.image)) {

                        // 先处理图片
                        FilePaperImg img = new FilePaperImg();
                        img.setImgUrl(item.getUrl());
                        img = correctService.processImg(img);
                        item.setUrl(img.getImgUrl());

                        // 识别图片内容并返回
                        String imgContent = vlImage(img.getImgUrl());

                        // 将返回的内容作为提示词
                        prompt.append(StrUtil.format(
                                """
                                        {}：{}
                                        """, field.getTitle(), imgContent)
                        );

                        // 将图片标识为删除
                        ossService.setTagList(
                                URLUtil.getPath(img.getImgUrl()),
                                "Deleted"
                        );
                    }
                }
            }
            // 如果是普通字段，叠加
            else {
                // 数据不能为空
                if (StrUtil.isNotBlank(field.getContent())) {
                    prompt.append(StrUtil.format(
                            """
                                    {}：{}
                                    """, field.getTitle(), field.getContent())
                    );
                }
            }
        }

        log.info("构建智能体提示词：{}", prompt);

        return prompt.toString();
    }


    // 使用大模型处理图片内容
    private String vlImage(String imgUrl) {

        LLMService llmService = getLLMService(); // 动态获取服务

        String vlPrompt = """
                请识别并描述图片中的内容，并以文本格式返回
                """;
        GPTAnswer vlAnswer = llmService.vl(imgUrl, vlPrompt, "");

        return vlAnswer.getAnswer();

    }


    public String generateUserMsg(String agentName, List<AgentFormField> form) {
        StringBuilder msg = new StringBuilder(agentName).append("\n");

        for (AgentFormField field : form) {
            if (!field.getType().equals(AgentFormFieldType.upload)) {
                msg.append(StrUtil.format(
                        """
                                {}：{}
                                """, field.getTitle(), field.getContent())
                );
            }
        }
        return msg.toString();
    }

}
