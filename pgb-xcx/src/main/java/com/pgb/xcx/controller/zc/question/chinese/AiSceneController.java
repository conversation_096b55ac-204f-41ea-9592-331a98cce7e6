package com.pgb.xcx.controller.zc.question.chinese;

import cn.dev33.satoken.annotation.SaCheckLogin;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.pgb.ai.LLMService;
import com.pgb.ai.domain.GPTAnswer;
import com.pgb.ai.utils.LLMUtils;
import com.pgb.common.core.global.BaseResult;
import com.pgb.common.core.utils.EnvUtils;
import com.pgb.service.domain.zc.question.chinese.textBlank.TextBlankItem;
import com.pgb.service.factory.LLMServiceFactory;
import com.pgb.xcx.common.UserBehaviorUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;

@Tag(name = "用户端/字词/题目/语文/AI字词情景出题")
@RestController("AiSceneController")
@Transactional(rollbackFor = Exception.class)
@RequestMapping("/user/zc/question/aiScene")
@RequiredArgsConstructor
@Slf4j
public class AiSceneController {

    private final LLMService llmService;

    @Autowired
    public AiSceneController(LLMServiceFactory llmServiceFactory) {
        this.llmService = llmServiceFactory.getLLMService("ali");
    }

    @Data
    public static class SceneGenerateForm {
        @Schema(title = "词语列表，以空格间隔")
        private String text;

        @Schema(title = "年级")
        private String grade;

        @Schema(title = "图片")
        private String imgUrl;
    }

    @Operation(summary = "单个生成字词情景题目")
    @PostMapping("generate/single")
    @SaCheckLogin
    public BaseResult<TextBlankItem> generateSingleScene(@RequestBody SceneGenerateForm form) {

        String prompt = StrUtil.format("""
                请根据{}年级学生的语文水平，基于提供的词语列表设计情景式看拼音写词语题目。
                                
                具体要求如下：
                1. 情景构建：
                创设真实生活场景或完整叙事片段，上下文需自然体现目标词语的语义
                每个目标词语必须要在情景中出现一次且仅一次，且上下文提供足够语义线索
                避免直接解释词义，通过情境逻辑暗示目标词语
                                
                2. 题目规范：
                语句符合该年级语言表达能力，避免复杂句式
                                
                输入格式说明：
                输入的每个词语之间以空格为分隔
                                
                请严格按照以下 json 格式进行返回：
                {
                "text": "题目内容，纯文字",
                "textMark": [
                {
                "word": "词语",
                "pinyin": "标准拼音（音节间空格分隔），如：pīn yīn"
                }
                ]
                }
                                
                示例：
                输入：观察
                输出：
                {
                "text": "科学课上，老师让我们用放大镜仔细观察树叶的脉络，并把发现的细节记录在笔记本上。",
                "textMark": [
                {
                "word": "观察",
                "pinyin": "guān chá"
                }
                ]
                }
                """, form.getGrade()
        );

        List<String> userList = new ArrayList<>();
        userList.add(form.getText());

        // 作答结果
        GPTAnswer answer = llmService.chatComplete(prompt, userList, true, null);

        if (EnvUtils.isDev()) {
            log.info(answer.toString());
        }

        // 格式化
        JSONObject json = JSONUtil.parseObj(answer.getAnswer());

        // 处理返回内容
        TextBlankItem item = parseTextBlankItem(json);

        return BaseResult.success(item);

    }

    @Operation(summary = "拍照生成多个字词情景题目")
    @PostMapping("generate/img")
    public BaseResult<List<TextBlankItem>> generateSceneImg(@RequestBody SceneGenerateForm form) {
        // 限制频率，1分钟最多2次
        // boolean isLimit = userBehaviorUtil.isLimit(
        //         GlobalConstants.TENANT_OCR_LIMIT_REDIS_KEY + StpUtil.getLoginIdAsLong(),
        //         2,
        //         Duration.ofMinutes(1)
        // );
        //
        // if (isLimit) {
        //     return BaseResult.error("您当前频率过高，请稍后等待30秒后重试");
        // }

        String vlPrompt = """
                请结构化提取图片中每个题目对应的词语内容
                """;
        GPTAnswer vlAnswer = llmService.vl(form.getImgUrl(), vlPrompt, "");

        if (EnvUtils.isDev()) {
            log.info(vlAnswer.toString());
        }

        // 识别内容
        String prompt = StrUtil.format("""
                请根据{}年级学生的语文水平，基于提供的词语列表设计情景式看拼音写词语题目。
                                
                具体要求如下：
                1. 情景构建：
                创设真实生活场景或完整叙事片段，上下文需自然体现目标词语的语义
                每个目标词语需在情景中出现一次，且上下文提供足够语义线索
                避免直接解释词义，通过情境逻辑暗示目标词语
                                
                2. 题目规范：
                语句符合该年级语言表达能力，避免复杂句式
                每个题目下的词语，尽量在同一个题目中出现，尽量不要分拆成多个题目。
                                
                输入说明：
                可能会输入多个题目，如果涉及多个题目，则在json数组中，生成多个题目
                请过滤掉与题目和词语无关的内容
                                
                请严格按照以下 json 格式进行返回：
                [
                {
                "text": "题目内容，完整文字内容，不需要包含拼音和扣空",
                "textMark": [
                {
                "word": "词语",
                "pinyin": "标准拼音（音节间空格分隔），如：pīn yīn"
                }
                ]
                }
                ]
                                
                示例：
                输入：第一题 观察
                输出：
                [{
                "text": "科学课上，老师让我们用放大镜仔细观察树叶的脉络，并把发现的细节记录在笔记本上。",
                "textMark": [
                {
                "word": "观察",
                "pinyin": "guān chá"
                }
                ]
                }]
                """, form.getGrade()
        );

        String userContent = "下面内容是题目及题目下的词语，请返回出题内容：\n\n" + vlAnswer.getAnswer();

        GPTAnswer answer = llmService.chatComplete(prompt, List.of(userContent), true, null, "qwen-plus-latest");

        if (EnvUtils.isDev()) {
            log.info(answer.toString());
        }

        // 格式化
        List<TextBlankItem> result = new ArrayList<>();
        JSONArray allArray = LLMUtils.clearArray(answer.getAnswer());
        for (int j = 0; j < allArray.size(); j++) {
            JSONObject json = allArray.getJSONObject(j);

            // 处理返回数据
            TextBlankItem item = parseTextBlankItem(json);

            result.add(item);
        }

        return BaseResult.success(result);
    }


    // 处理返回数据
    private TextBlankItem parseTextBlankItem(JSONObject json) {
        String text = json.getStr("text");
        TextBlankItem item = new TextBlankItem();
        item.setText(text);
        item.setTextMark(new ArrayList<>());

        JSONArray array = json.getJSONArray("textMark");
        for (int i = 0; i < array.size(); i++) {
            JSONObject jsonMark = array.getJSONObject(i);
            String word = jsonMark.getStr("word");
            String pinyin = jsonMark.getStr("pinyin");

            List<String> pinyinList = StrUtil.split(pinyin, " ");
            List<String> wordList = List.of(word.split(""));

            if (wordList.size() != pinyinList.size()) {
                continue;
            }

            // 查找对应词
            int index = StrUtil.indexOf(text, word, 0, true);

            if (index == -1) {
                continue;
            }

            for (int i1 = 0; i1 < wordList.size(); i1++) {
                TextBlankItem.TextBlankItemMark mark = new TextBlankItem.TextBlankItemMark();
                mark.setText(wordList.get(i1));
                mark.setPinyin(pinyinList.get(i1));
                mark.setStart(index + i1);
                mark.setEnd(index + i1 + 1);
                item.getTextMark().add(mark);
            }
        }
        return item;
    }

}
