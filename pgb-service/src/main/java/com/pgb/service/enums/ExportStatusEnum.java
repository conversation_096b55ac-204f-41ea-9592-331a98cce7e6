package com.pgb.service.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;

@Schema(description = "导出状态")
@AllArgsConstructor
public enum ExportStatusEnum {

    Queuing(0),

    Exporting(1),

    Uploading(2),

    Completed(3),

    Init(4);

    /**
     * 枚举值对应的整数值
     */
    @EnumValue
    public final int value;
}
