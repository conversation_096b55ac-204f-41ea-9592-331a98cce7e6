package com.pgb.service.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * Created by 2024/7/17 11:46
 */
@Schema(description = "年级")
@AllArgsConstructor
@Getter
public enum  GradeEnum {

    GRADE_1(1, "一年级","小学"),

    GRADE_2(2, "二年级","小学"),

    GRADE_3(3, "三年级","小学"),

    GRADE_4(4, "四年级","小学"),

    GRADE_5(5, "五年级","小学"),

    GRADE_6(6, "六年级","小学"),

    GRADE_7(7, "七年级","初中"),

    GRADE_8(8, "八年级","初中"),

    GRADE_9(9, "九年级","初中"),

    SENIOR(10, "高一","高中"),

    SENIOR_2(11, "高二","高中"),

    SENIOR_3(12, "高三","高中"),

    COLLEGE(13, "大学","大学"),

    GRADUATE(14, "考研","考研");


    /**
     * 枚举值对应的整数值
     */
    @EnumValue
    public final int value;

    /**
     * 文字说明
     */
    public final String desc;

    /**
     * 分类
     */
    @Getter
    public final String cateName;
}
