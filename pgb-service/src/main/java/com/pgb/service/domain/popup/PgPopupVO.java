package com.pgb.service.domain.popup;

import com.fasterxml.jackson.annotation.JsonGetter;
import com.pgb.service.domain.activity.PgActivityVO;
import com.pgb.service.domain.tag.PgTag;
import com.pgb.service.enums.PopupTypeEnum;
import com.pgb.service.enums.ShowOnTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 弹窗表
 *
 * @TableName pg_popup
 */
@Schema(description = "弹窗表 VO")
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode
@ToString
@Builder
public class PgPopupVO implements Serializable {

    @Schema(title = "弹窗id", type = "string")
    private Long id;

    @Schema(title = "开始时间")
    private Date startTime;

    @Schema(title = "结束时间")
    private Date endTime;

    @Schema(title = "显示时机(ru：首次注册、每次进入，指定用户标签)")
    private ShowOnTypeEnum showOn;

    @Schema(title = "用户标签列表，根据英文逗号分隔")
    private List<Long> tagIds;

    @Schema(title = "排除特定的标签")
    private List<Long> notTagIds;

    @Schema(title = "用户标签")
    private List<PgTag> tagList;

    @Schema(title = "排除特定的标签")
    private List<PgTag> notTagList;

    @Schema(title = "权重排序")
    private Integer sort;

    @Schema(title = "是否有效")
    private Boolean isValid;

    @Schema(title = "创建时间")
    private Date createTime;

    @Schema(title = "弹窗图片url")
    private String imgUrl;

    @Schema(title = "备注")
    private String remark;

    @Schema(title = "弹窗类型，0：弹窗，1：贴片广告")
    private PopupTypeEnum type;

    @Schema(title = "活动id")
    private Long activityId;

    @Schema(title = "页面url")
    private String targetUrl;

    @Schema(title = "活动信息")
    private PgActivityVO activity;

    @Schema(title = "有效弹窗人数")
    private Integer receiveNum;

    @Schema(title = "弹窗状态")
    @JsonGetter
    public String getStatus() {

        Date now = new Date();

        if (now.before(startTime)){
            return "未开始";
        } else if (now.after(endTime)) {
            return "已结束";
        }else {
            return "进行中";
        }
    }

}
