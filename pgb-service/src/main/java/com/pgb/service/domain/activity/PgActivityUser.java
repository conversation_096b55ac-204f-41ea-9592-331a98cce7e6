package com.pgb.service.domain.activity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.pgb.service.enums.ActivityTypeEnum;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 *
 * @TableName pg_activity_user
 */
@TableName(value ="pg_activity_user")
@Data
public class PgActivityUser implements Serializable {
    /**
     * 活动用户关系
     */
    @TableId
    private Long id;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 活动id
     */
    private Long activityId;

    /**
     * 订单id
     */
    private Long orderId;

    /**
     * 活动类型
     */
    private ActivityTypeEnum activityType;

    /**
     * 兑换码id
     */
    private Long codeId;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 是否已购买
     */
    private Boolean isBuy;

}
