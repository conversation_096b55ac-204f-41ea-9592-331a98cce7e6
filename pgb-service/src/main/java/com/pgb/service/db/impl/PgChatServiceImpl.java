package com.pgb.service.db.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.pgb.ai.domain.ChatRecord;
import com.pgb.common.core.utils.EnvUtils;
import com.pgb.service.db.PgChatMsgService;
import com.pgb.ai.domain.agent.AgentForm;
import com.pgb.service.domain.chat.PgChat;
import com.pgb.service.db.PgChatService;
import com.pgb.service.domain.chat.msg.PgChatMsg;
import com.pgb.service.enums.RoleTypeEnum;
import com.pgb.service.mapper.PgChatMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
* <AUTHOR>
* @description 针对表【pg_chat(AI会话表)】的数据库操作Service实现
* @createDate 2025-02-08 14:28:51
*/
@Service
@RequiredArgsConstructor
@Slf4j
public class PgChatServiceImpl extends ServiceImpl<PgChatMapper, PgChat>
    implements PgChatService{

    private final PgChatMsgService pgChatMsgService;

    @Override
    public List<ChatRecord> getHistory(PgChat chat) {
        // prompt + 最后生成的AI + user 历史近三条
        // 注意顺序，一定是先用户信息，再AI信息
        List<ChatRecord> recordList = new ArrayList<>();

        AgentForm agentFrom = JSONUtil.toBean(chat.getForm().toString(), AgentForm.class);
        String prompt = agentFrom.getPrompt();

        if (EnvUtils.isDev()){
            log.info("当前会话 prompt: {}", prompt);
        }

        // 最初的表单内容
        recordList.add(
                ChatRecord.builder()
                        .role(ChatRecord.Role.User)
                        .content(prompt)
                        .build()
        );

        // 最后4轮对话
        List<PgChatMsg> userMsgs = pgChatMsgService.list(new LambdaQueryWrapper<PgChatMsg>()
                .eq(PgChatMsg::getChatId, chat.getId())
                .orderByAsc(PgChatMsg::getSort)
                .last("LIMIT 8"));

        if (ObjectUtil.isNotEmpty(userMsgs)) {
            for (PgChatMsg msg : userMsgs) {
                recordList.add(ChatRecord.builder()
                        .role(msg.getRole().equals(RoleTypeEnum.USER) ? ChatRecord.Role.User : ChatRecord.Role.Assistant)
                        .content(msg.getContent())
                        .chatId(msg.getChatId())
                        .build());
            }
        }

        return recordList;
    }
}




