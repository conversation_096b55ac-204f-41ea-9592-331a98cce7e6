package com.pgb.service.domain.query;

import com.pgb.common.mybatis.global.PageQuery;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * Created by 2024/8/14 15:52
 */

@EqualsAndHashCode(callSuper = true)
@Schema(description = "会话文档记录 搜索实体")
@Data
public class ChatRecordQuery extends PageQuery {

    @Schema(title = "标题")
    private String title;
}
