package com.pgb.service.domain.user;

import com.pgb.common.satoken.LoginVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Schema(title = "pc 微信登录状态 实体")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PcWxLoginCode {

    @Schema(title = "所属令牌code")
    private String code;

    @Schema(title = "所属二维码url")
    private String url;

    @Schema(title = "扫码状态", description = "0：未扫码，1：已扫码，2：已授权登录")
    private PcWxLoginCodeEnum status;

    @Schema(title = "用户id")
    private Long userId;

    @Schema(title = "登录状态")
    private LoginVO<PgUsersVO> loginData;

    public enum PcWxLoginCodeEnum {
        UnScan,
        Scanned,
        Logged
    }
}
