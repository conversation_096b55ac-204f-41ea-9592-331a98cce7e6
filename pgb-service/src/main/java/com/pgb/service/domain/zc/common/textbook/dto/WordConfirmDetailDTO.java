package com.pgb.service.domain.zc.common.textbook.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

import java.io.Serializable;

/**
 * 单词确认详细信息DTO
 *
 * <AUTHOR>
 */
@Schema(description = "单词确认详细信息DTO")
@Data
public class WordConfirmDetailDTO implements Serializable {

    @Schema(description = "单词原文", requiredMode = Schema.RequiredMode.REQUIRED, example = "hello")
    @NotBlank(message = "单词不能为空")
    private String word;

    @Schema(description = "中文翻译", example = "你好")
    private String translation;

    @Schema(description = "音标", example = "/həˈloʊ/")
    private String phonetic;

    @Schema(description = "词性", example = "int.")
    private String partOfSpeech;
}
