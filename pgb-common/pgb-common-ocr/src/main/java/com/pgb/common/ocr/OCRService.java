package com.pgb.common.ocr;

import cn.hutool.cache.CacheUtil;
import cn.hutool.cache.impl.TimedCache;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.pgb.common.ocr.domain.OCRResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.Duration;

/**
 * ocr
 */
@Slf4j
public abstract class OCRService {

    private final TimedCache<String, OCRResult> timedCache;

    protected OCRService() {
        // 默认3天
        this.timedCache = CacheUtil.newTimedCache(Duration.ofDays(3).toMillis());

        //启动定时任务，每30分钟清理一次过期条目，注释此行首次启动仍会清理过期条目
        timedCache.schedulePrune(Duration.ofMinutes(30).toMillis());
    }

    /**
     * (缓存)手写文字识别
     * @param imgUrl
     * @return
     */
    public OCRResult handWritingCache(String imgUrl) {

        if (StrUtil.isBlank(imgUrl)) {
            throw new RuntimeException("图片地址不能为空");
        }

        // 缓存key
        String redisKey = "PGB_MODEL_OCR_IMG_URL_KEY:" + imgUrl;

        // 获取缓存
        OCRResult ocrResult = timedCache.get(redisKey);

        // 缓存存在
        if (ObjectUtil.isNotNull(ocrResult)) {
            // 缓存不需要
            ocrResult.setToken(0L);
            return ocrResult;
        }

        ocrResult = this.handWriting(imgUrl);

        // 识别成功
        if (ObjectUtil.isNotNull(ocrResult.getWords_result())) {
            // 缓存三天
            timedCache.put(redisKey, ocrResult, Duration.ofDays(3).toMillis());

            // 保存脑力值
            ocrResult.setToken(500L);
        }

        return ocrResult;
    }

    public OCRResult handWriting(String imgUrl) {
        return handWriting(imgUrl, 0);
    }

    /**
     * 手写文字识别
     * @param imgUrl
     * @return
     */
    public abstract OCRResult handWriting(String imgUrl, Integer retryNum);
}
