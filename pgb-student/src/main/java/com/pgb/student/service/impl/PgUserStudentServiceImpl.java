package com.pgb.student.service.impl;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.pgb.common.core.global.BaseResult;
import com.pgb.common.core.global.GlobalCode;
import com.pgb.common.core.global.exception.BaseException;
import com.pgb.service.domain.student.StudentInfoDTO;
import com.pgb.student.domain.PgFamily;
import com.pgb.student.domain.PgUserStudent;
import com.pgb.student.domain.vo.PgUserStudentVO;
import com.pgb.student.service.PgFamilyService;
import com.pgb.student.service.PgUserStudentService;
import com.pgb.student.mapper.PgUserStudentMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【pg_user_student(用户学生表（一对多）)】的数据库操作Service实现
 * @createDate 2025-03-10 16:49:43
 */
@Service
@DS("master")
@RequiredArgsConstructor
public class PgUserStudentServiceImpl extends ServiceImpl<PgUserStudentMapper, PgUserStudent>
        implements PgUserStudentService {

    private final PgFamilyService pgFamilyService;

    @Override
    public PgUserStudent getOrCreateUserStudent(Long userId, StudentInfoDTO dto) {

        // 获取身份
        PgUserStudent userStudent = getOne(new LambdaQueryWrapper<PgUserStudent>()
                .eq(PgUserStudent::getUserId, userId)
                .eq(PgUserStudent::getName, dto.getName())
                .last("LIMIT 1")
        );

        if (ObjectUtil.isNull(userStudent)) {

            // 限制只能创建4个
            if (count(new LambdaQueryWrapper<PgUserStudent>()
                    .eq(PgUserStudent::getUserId, userId)) > 3) {
                throw new BaseException("您添加孩子数量已达上限，无法继续创建");
            }

            // 新增关联
            userStudent = new PgUserStudent();
            userStudent.setUserId(userId);
            userStudent.setName(dto.getName());
            userStudent.setCreateTime(new Date());
            userStudent.setAvatar("https://cdn.pigaibang.com/common/parent/avatar.png");
            userStudent.setIdentity(dto.getIdentityType());
            save(userStudent);
        }

        return userStudent;
    }

    @Override
    public void switchStudent(Long userId) {

        PgUserStudent student = getOne(new LambdaQueryWrapper<PgUserStudent>()
                .eq(PgUserStudent::getUserId, userId)
                .orderByDesc(PgUserStudent::getCreateTime)
                .last("limit 1")
        );

        if (ObjectUtil.isNotNull(student)) {
            StpUtil.getSession().set("studentId", student.getId());
        }
    }

    @Override
    public void switchByUserStudentId(Long userStudentId) {
        PgUserStudent userStudent = getById(userStudentId);
        if (ObjectUtil.isNotNull(userStudent)) {
            StpUtil.getSession().set("studentId", userStudentId);
        }
    }

    @Override
    public List<PgUserStudent> getStudentList(Long userId) {
        // 获取绑定的亲情号
        List<Long> studentIds = pgFamilyService.list(new LambdaQueryWrapper<PgFamily>()
                        .eq(PgFamily::getInviteUserId, userId)
                )
                .stream()
                .map(PgFamily::getStudentId)
                .toList();

        // 绑定的学生
        List<PgUserStudent> studentList = list(new LambdaQueryWrapper<PgUserStudent>()
                        .and(i -> i.eq(PgUserStudent::getUserId, userId)
                                .or()
                                .in(CollUtil.isNotEmpty(studentIds),PgUserStudent::getId, studentIds)
                        )
                );

        return studentList;
    }

    @Override
    public void resetStudentSession() {
        List<PgUserStudent> studentList = getStudentList(StpUtil.getLoginIdAsLong());
        if (!studentList.isEmpty()) {
            StpUtil.getSession().set("studentId", studentList.get(0).getId());
        } else {
            StpUtil.getSession().delete("studentId");
        }
    }
}




