
package com.pgb.service.domain.agent.cost;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 智能体会话生成记录表
 * @TableName pg_agent_cost
 */
@TableName(value ="pg_agent_cost")
@Data
public class PgAgentCost implements Serializable {
    /**
     * 会话生成记录id
     */
    private Long id;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 关联会话id
     */
    private Long chatId;

    /**
     * 智能体id
     */
    private Long agentId;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
