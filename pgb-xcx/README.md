```sql
SELECT * FROM "pg_answer" where status = 3 and create_time > '2024-09-18 12:00:00' ORDER BY create_time DESC limit 10
SELECT count(*) FROM "pg_answer" where status = 3 and create_time > '2024-09-18 12:00:00'
SELECT * FROM "pg_answer" where status = 0
ORDER BY create_time desc
SELECT count(*) FROM "pg_answer" where status = 0

SELECT * FROM pg_users where phone = '***********'
SELECT * FROM pg_users where phone = '***********'

SELECT * FROM "pg_answer" where user_id = 1836347756776742914 ORDER BY create_time DESC

SELECT * FROM "pg_answer" where user_id = 1833346216440766466 and status = 0
SELECT * FROM "pg_answer" where id = 1836341172797399042


SELECT count(*) FROM pg_answer where status = 3 and create_time > '2024-09-18' and ((correct_result ->> 'polish') IS NULL or char_length(correct_result ->> 'polish') = 0)
SELECT * FROM pg_answer where status = 3 and create_time > '2024-09-18' and ((correct_result ->> 'polish') IS NULL or char_length(correct_result ->> 'polish') = 0) ORDER BY create_time desc limit 10
SELECT count(*) FROM pg_answer where status = 3 and create_time > '2024-09-18' and ((correct_result ->> 'comment') IS NULL or char_length(correct_result ->> 'comment') = 0)

--- 复购人数
SELECT count(user_id) as num, user_id FROM "pg_vip" GROUP BY user_id HAVING count(user_id) >= 2 ORDER BY num desc
SELECT count(user_id) as num, user_id FROM "pg_vip" where user_id in (SELECT user_id FROM "pg_vip" where create_time > '2024-10-01') GROUP BY user_id HAVING count(user_id) >= 2 ORDER BY num desc
--- 非年卡、10月前，购买人数，非有效期内
SELECT count(DISTINCT(user_id)) FROM "pg_vip" where create_time < '2024-10-01' and vip_type != 0 and (vip_type != 1 or create_time < '2024-09-21')
```

#### 查询批改数量最多的人
```sql
SELECT user_id, count(*) as num FROM "pg_answer" GROUP BY user_id ORDER BY num desc limit 10
```

```bash

mvn install:install-file -Dfile=E:\PGB\pgb-server\pgb-xhs\lib\xhsSdk.jar -DgroupId=com.xiaohongshu.fls -DartifactId=openSdk -Dversion=3.1.15-SNAPSHOT -Dpackaging=jar


```
