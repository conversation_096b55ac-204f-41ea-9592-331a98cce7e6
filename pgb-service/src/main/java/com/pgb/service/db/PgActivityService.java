package com.pgb.service.db;

import com.baomidou.mybatisplus.extension.service.IService;
import com.pgb.service.domain.activity.PgActivity;

/**
* <AUTHOR>
* @description 针对表【pg_activity】的数据库操作Service
* @createDate 2024-12-23 16:21:29
*/
public interface PgActivityService extends IService<PgActivity> {
    /**
     * 某用户是否参与过某活动
     * @param userId
     * @param activityId
     * @return
     */
    public boolean isParticipate(Long userId, Long activityId);
}
