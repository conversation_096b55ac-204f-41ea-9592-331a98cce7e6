package com.pgb.xcx.controller.auth;

import cn.dev33.satoken.annotation.SaCheckLogin;
import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.pgb.common.core.global.BaseResult;
import com.pgb.common.core.global.GlobalCode;
import com.pgb.service.db.*;
import com.pgb.service.domain.answer.PgAnswer;
import com.pgb.service.domain.answer.answerBatch.PgAnswerBatch;
import com.pgb.service.domain.systemConfig.SystemConfig;
import com.pgb.service.domain.systemConfig.SystemXcxConfig;
import com.pgb.service.domain.user.PgUsers;
import com.pgb.service.domain.user.PgUsersVO;
import com.pgb.service.domain.zc.answer.PgZcAnswer;
import com.pgb.service.domain.zc.answer.batch.PgZcAnswerBatch;
import com.pgb.service.enums.SystemConfigEnum;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;

@Tag(name = "用户端/权限/信息")
@RestController("UserAuthInfoController")
@Transactional(rollbackFor = Exception.class)
@RequestMapping("/user/auth/info")
@RequiredArgsConstructor
@Slf4j
public class InfoController {

    private final PgUsersService pgUsersService;

    private final SystemConfigService systemConfigService;

    private final PgAnswerService pgAnswerService;

    private final PgZcAnswerService pgZcAnswerService;

    private final PgAnswerBatchService pgAnswerBatchService;

    private final PgZcAnswerBatchService pgZcAnswerBatchService;

//    @Operation(summary = "获取当前线上版本")
//    @GetMapping("version")
//    public BaseResult<Integer> getVersion() {
//        String response = HttpUtil.get("https://snros-out-server-6f0hpin9ca40a87-1300104514.ap-shanghai.app.tcloudbase.com/api/mini/version");
//
//        return BaseResult.success(
//                Integer.parseInt(response)
//        );
//    }

    @Operation(summary = "获取当前线上版本")
    @GetMapping("version")
    public BaseResult<Integer> getVersion() {

        SystemConfig systemConfig = systemConfigService.getById(SystemConfigEnum.XCX_VERSION);

        if (ObjectUtil.isNull(systemConfig)) {
            return BaseResult.code(GlobalCode.Item_Null);
        }

        SystemXcxConfig xcxConfig = BeanUtil.toBean(systemConfig.getValue(), SystemXcxConfig.class);

        return BaseResult.success(xcxConfig.getVersion());
    }

    @Operation(summary = "获取用户信息")
    @GetMapping("userInfo")
    @SaCheckLogin
    public BaseResult<PgUsersVO> getUserInfo() {
        PgUsers user = pgUsersService.getById(StpUtil.getLoginIdAsLong());

        return BaseResult.success(
                BeanUtil.toBean(user, PgUsersVO.class)
        );
    }

    public record UpdateUserInfoForm(String nickName) {
    }

    @Operation(summary = "修改用户昵称")
    @PostMapping("updateNickname")
    @SaCheckLogin
    public BaseResult<PgUsersVO> updateNickname(@RequestBody UpdateUserInfoForm form) {

        PgUsers user = pgUsersService.getById(StpUtil.getLoginIdAsLong());

        if (ObjectUtil.isNull(user)) {
            return BaseResult.error(GlobalCode.Item_Null, "用户不存在");
        }

        user.setNickName(form.nickName());
        pgUsersService.updateById(user);

        return BaseResult.success(
                BeanUtil.copyProperties(user, PgUsersVO.class)
        );

    }


    @Operation(summary = "注销当前登录用户帐号")
    @DeleteMapping("deleteUser")
    public BaseResult<Boolean> deleteUser() {

        // 获取当前登录用户
        Long userId = StpUtil.getLoginIdAsLong();

        PgUsers pgUsers = pgUsersService.getById(userId);
        if (ObjectUtil.isNull(pgUsers)) {
            return BaseResult.code(GlobalCode.Item_Null);
        }

        // 删除用户
        pgUsersService.removeById(userId);

        // 删除用户上传作文数据
        pgAnswerService.update(new LambdaUpdateWrapper<PgAnswer>()
                .eq(PgAnswer::getUserId, userId)
                .eq(PgAnswer::getDeleted, false)
                .set(PgAnswer::getDeleted, true)
        );

        // 删除用户上传字词数据
        pgZcAnswerService.update(new LambdaUpdateWrapper<PgZcAnswer>()
                .eq(PgZcAnswer::getUserId, userId)
                .eq(PgZcAnswer::getDeleted, false)
                .set(PgZcAnswer::getDeleted, true)
        );

        // 删除作文提交批次记录
        pgAnswerBatchService.remove(new LambdaQueryWrapper<PgAnswerBatch>()
                .eq(PgAnswerBatch::getUserId, userId)
        );

        // 删除字词提交批次记录
        pgZcAnswerBatchService.remove(new LambdaQueryWrapper<PgZcAnswerBatch>()
                .eq(PgZcAnswerBatch::getUserId, userId));

        return BaseResult.success(true);

    }

}
