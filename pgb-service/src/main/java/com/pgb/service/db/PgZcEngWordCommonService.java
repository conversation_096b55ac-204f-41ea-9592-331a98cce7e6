package com.pgb.service.db;

import com.pgb.service.domain.zc.word.english.common.PgZcEngWordCommon;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * <AUTHOR>
 * @description 针对表【pg_zc_eng_word_common】的数据库操作Service
 * @createDate 2025-06-06 18:42:15
 */
public interface PgZcEngWordCommonService extends IService<PgZcEngWordCommon> {


    /**
     * 获取单词发音
     *
     * @param enWord
     * @return
     */
    void getEnWordAudio(PgZcEngWordCommon enWord);

    /**
     * 获取单词音标和释义
     *
     * @param word
     * @return
     */
    PgZcEngWordCommon getEnWordInfo(String word);


    /**
     * 异步执行获取单词信息
     * @param text
     */
    void processWordsAsync(String text);
}
