package com.pgb.correct.script;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.TimeInterval;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.URLUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.pgb.common.oss.service.OssService;
import com.pgb.service.custom.CorrectService;
import com.pgb.service.db.PgAnswerService;
import com.pgb.service.db.PgUsersService;
import com.pgb.service.domain.answer.PgAnswer;
import com.pgb.service.domain.common.image.FilePaperImg;
import com.pgb.service.domain.question.zwEssay.ZwEssayQuestion;
import com.pgb.service.domain.user.PgUsers;
import com.pgb.service.enums.CorrectStatusEnum;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Future;

@Component
@Slf4j
public class ImgClearBootTest {

    @Autowired
    private PgAnswerService pgAnswerService;

    @Autowired
    private CorrectService correctService;

    @Autowired
    private PgUsersService pgUsersService;

    @Autowired
    private OssService ossService;

    private final String originUrl = "https://cdn.pigaibang.com";

    private final String archiveBaseUrl = "https://cdn-gd.pigaibang.com";

    // 用于平滑中断
    @Setter
    private Boolean status = true;

    public void answerMove() {
        // 分页执行
        int pageSize = 50;

        int pageNo = 24020;
        int endPageNo = 9999999;

        LambdaQueryWrapper<PgAnswer> queryWrapper = new LambdaQueryWrapper<PgAnswer>()
                //.between(PgAnswer::getCreateTime, DateUtil.parse("2024-07-01"), DateUtil.parse("2025-01-01"))
                .between(PgAnswer::getCreateTime, DateUtil.parse("2024-12-31"), DateUtil.parse("2025-06-30"))
                .orderByAsc(PgAnswer::getCreateTime);

        // 获取总记录，时间小于 2025-1月1日
        IPage<PgAnswer> totalPage = pgAnswerService.page(new Page<>(pageNo, pageSize), queryWrapper);

        // 获取需要分页的数量
        log.info("总记录数：{}，总页数：{}", totalPage.getTotal(), totalPage.getPages());

        while (this.status && pageNo <= totalPage.getPages()) {
            TimeInterval interval = DateUtil.timer();
            log.info("------开始处理第{}页数据", pageNo);

            // 获取答案，按时间正序
            IPage<PgAnswer> page = pgAnswerService.page(new Page<>(pageNo, pageSize), queryWrapper);

            // 遍历分页数据
            List<Future<Void>> futureList = new ArrayList<>();
            int finalI = pageNo;
            for (PgAnswer answer : page.getRecords()) {
                Future<Void> future = ThreadUtil.execAsync(() -> {
                    // 获取数据库用户上传图片
                    ZwEssayQuestion userAnswer = JSONUtil.toBean(answer.getAnswer().toString(), ZwEssayQuestion.class);

                    // 获取批改后的图片
                    String correctResultStr = answer.getCorrectResult().toString();

                    List<String> removeUrlList = new ArrayList<>();

                    // 先把用户图片替换
                    for (FilePaperImg userImg : userAnswer.getUserImgAnswerList()) {
                        // 执行替换
                        boolean moveResult = ossMove(userImg.getImgUrl());
                        if (moveResult) {
                            removeUrlList.add(userImg.getImgUrl());
                            String archiveUrl = StrUtil.replace(userImg.getImgUrl(), originUrl, archiveBaseUrl);
                            correctResultStr = StrUtil.replace(correctResultStr, userImg.getImgUrl(), archiveUrl);
                            userImg.setImgUrl(archiveUrl);
                            log.info("用户图片移动成功，新文件：{}", archiveUrl);
                        }
                    }

                    ZwEssayQuestion correctAnswer = JSONUtil.toBean(correctResultStr, ZwEssayQuestion.class);

                    // 把已经渲染的图片迁移
                    for (FilePaperImg correctImg : correctAnswer.getUserImgAnswerList()) {
                        // 执行替换
                        boolean moveResult = ossMove(correctImg.getImgUrl());
                        if (moveResult) {
                            removeUrlList.add(correctImg.getImgUrl());
                            String archiveUrl = StrUtil.replace(correctImg.getImgUrl(), originUrl, archiveBaseUrl);
                            correctImg.setImgUrl(archiveUrl);
                            log.info("批改图片移动成功，新文件：{}", archiveUrl);
                        }
                    }

                    // 保存数据
                    answer.setAnswer(userAnswer);
                    answer.setCorrectResult(correctAnswer);
                    if (ObjectUtil.isNull(answer.getDeleted())) {
                        answer.setDeleted(false);
                    }
                    pgAnswerService.updateById(answer);

                    // 删除原有图片
                    for (String removeUrl : removeUrlList) {
                        ossService.delete(
                                URLUtil.getPath(removeUrl)
                        );
                        log.info("删除图片 {}", URLUtil.getPath(removeUrl));
                    }

                    log.info("当前页数：{}/{}，处理完成第{}个：{}", finalI, totalPage.getPages(), page.getRecords().indexOf(answer) + 1, answer.getId());

                    return null;
                });
                futureList.add(future);
            }
            futureList.forEach(future -> {
                try {
                    future.get();
                } catch (InterruptedException | ExecutionException e) {
                    log.error("处理异常，第{}页", finalI, e);
                }
            });

            if (!correctService.getCorrecting() || endPageNo == pageNo) {
                log.info("停止处理，当前已执行页数：{}", pageNo);
                break;
            }

            log.info("------处理完成第{}/{}页数据，用时：{}", pageNo, totalPage.getPages(), interval.intervalPretty());
            pageNo++;
        }

        log.info("总记录数：{}，总页数：{}，停止页数：{}", totalPage.getTotal(), totalPage.getPages(), (pageNo - 1));
    }

    private boolean ossMove(String imgUrl) {

        String key = URLUtil.getPath(imgUrl);

        //String baseUrl = "http://oss-move.fcv3.1693801497649473.cn-beijing.fc.devsapp.net/process?key={}";
        String baseUrl = "http://123.56.255.187:9000/process?key={}";

        HttpResponse response = HttpRequest.get(StrUtil.format(baseUrl, key)).execute();

        if (response.getStatus() != 200) {
            log.error("移动图片失败，错误信息：{},文件名称：{}", response.body(), key);
            return false;
        }
        return response.getStatus() == 200;
    }

    // 将图片中的 tmp 更换
    public void updateTmpAnswer() {
        int pageNo = 1;
        int pageSize = 50;

        DateTime dateTime = DateUtil.parse("2025-03-24 15:20:00");
        DateTime dateTime2 = DateUtil.parse("2025-03-27 18:00:00");
        LambdaQueryWrapper<PgAnswer> queryWrapper = new LambdaQueryWrapper<PgAnswer>()
                .ne(PgAnswer::getStatus, CorrectStatusEnum.Uploaded)
                .between(PgAnswer::getCreateTime, dateTime, dateTime2)
                .orderByAsc(PgAnswer::getCreateTime);

        // 获取总记录，时间小于 2025-1月1日
        IPage<PgAnswer> totalPage = pgAnswerService.page(new Page<>(pageNo, pageSize), queryWrapper);

        // 获取需要分页的数量
        log.info("总记录数：{}，总页数：{}", totalPage.getTotal(), totalPage.getPages());

        TimeInterval interval = DateUtil.timer();
        for (int i = pageNo; i <= totalPage.getPages(); i++) {
            log.info("------开始处理第{}页数据", i);

            // 获取答案，按时间正序
            IPage<PgAnswer> page = pgAnswerService.page(new Page<>(i, pageSize), queryWrapper);

            for (PgAnswer answer : page.getRecords()) {

                log.info("开始处理数据：{}", answer.getId());

                // 获取数据库用户上传图片
                ZwEssayQuestion userAnswer = JSONUtil.toBean(answer.getAnswer().toString(), ZwEssayQuestion.class);

                // 先把用户图片替换
                for (FilePaperImg userImg : userAnswer.getUserImgAnswerList()) {
                    if (userImg.getImgUrl().contains("/tmp/")) {
                        String sourceKey = URLUtil.getPath(userImg.getImgUrl());
                        String destinationKey = StrUtil.replace(sourceKey, "/tmp/", "/zw/");
                        ossService.renameFile(
                                sourceKey, destinationKey
                        );
                        // 执行替换
                        userImg.setImgUrl(
                                userImg.getImgUrl().replaceAll("/tmp/", "/zw/")
                        );
                        log.info("用户图片移动成功，新文件：{}", userImg.getImgUrl());
                    }
                }


                if (ObjectUtil.isNotNull(answer.getCorrectResult())) {
                    // 获取批改后的图片
                    String correctResultStr = answer.getCorrectResult().toString();

                    correctResultStr = correctResultStr.replaceAll("https://cdn.pigaibang.com/tmp/", "https://cdn.pigaibang.com/zw/");

                    ZwEssayQuestion correctAnswer = JSONUtil.toBean(correctResultStr, ZwEssayQuestion.class);

                    // 把已经渲染的图片迁移
                    for (FilePaperImg correctImg : correctAnswer.getUserImgAnswerList()) {
                        if (correctImg.getImgUrl().contains("/tmp/")) {
                            String sourceKey = URLUtil.getPath(correctImg.getImgUrl());
                            String destinationKey = StrUtil.replace(sourceKey, "/tmp/", "/zw/");
                            ossService.renameFile(
                                    sourceKey, destinationKey
                            );
                            // 执行替换
                            correctImg.setImgUrl(
                                    correctImg.getImgUrl().replaceAll("/tmp/", "/zw/")
                            );

                            log.info("用户图片移动成功，新文件：{}", correctImg.getImgUrl());
                        }
                    }
                    answer.setCorrectResult(correctAnswer);
                }

                // 保存数据
                answer.setAnswer(userAnswer);
                pgAnswerService.updateById(answer);

                log.info("当前页数：{}，处理完成第{}个：{}", i, page.getRecords().indexOf(answer) + 1, answer.getId());
            }
        }
    }
}
