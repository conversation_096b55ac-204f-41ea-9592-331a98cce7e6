package com.pgb.ai.domain.agent;

import com.pgb.ai.enums.FileTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * Created by 2025/7/18 10:33
 */
@Data
@Schema(title = "文件对象")
public class FileItem {

    @Schema(title = "文件类型")
    private FileTypeEnum fileType;

    @Schema(title = "文件名称")
    private String name;

    @Schema(title = "文件url")
    private String url;

}
