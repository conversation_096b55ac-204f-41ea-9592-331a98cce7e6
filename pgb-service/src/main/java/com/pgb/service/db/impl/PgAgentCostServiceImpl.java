package com.pgb.service.db.impl;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.pgb.service.domain.agent.cost.PgAgentCost;
import com.pgb.service.db.PgAgentCostService;
import com.pgb.service.domain.agent.cost.TodayNum;
import com.pgb.service.mapper.PgAgentCostMapper;
import org.springframework.stereotype.Service;

import java.util.Date;

/**
 * <AUTHOR>
 * @description 针对表【pg_agent_cost(智能体会话生成记录表)】的数据库操作Service实现
 * @createDate 2025-02-14 16:40:29
 */
@Service
public class PgAgentCostServiceImpl extends ServiceImpl<PgAgentCostMapper, PgAgentCost>
        implements PgAgentCostService {

    @Override
    public TodayNum getTodayChatNum(Long userId,Integer totalUseNum) {

        long todayUseNum = count(new LambdaQueryWrapper<PgAgentCost>()
                .eq(PgAgentCost::getUserId, userId)
                .between(PgAgentCost::getCreateTime, DateUtil.beginOfDay(new Date()), DateUtil.endOfDay(new Date())));

        return new TodayNum((int) todayUseNum, totalUseNum);
    }
}




