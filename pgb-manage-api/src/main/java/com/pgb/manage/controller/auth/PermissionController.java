package com.pgb.manage.controller.auth;

import cn.dev33.satoken.annotation.SaCheckRole;
import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.pgb.common.core.global.BaseResult;
import com.pgb.common.core.global.RoleConstants;
import com.pgb.manage.service.saas.domain.SystemAdmin;
import com.pgb.manage.service.saas.domain.SystemPermission;
import com.pgb.manage.service.saas.domain.SystemRole;
import com.pgb.manage.service.saas.service.SystemPermissionService;
import com.pgb.manage.service.saas.service.SystemRoleService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@Tag(name = "管理端/权限/权限")
@RestController("manageAuthMenuController")
@Transactional(rollbackFor = Exception.class)
@RequestMapping("/manage/auth/permission")
@RequiredArgsConstructor
@Slf4j
public class PermissionController {

    private final SystemPermissionService systemPermissionService;

    private final SystemRoleService systemRoleService;

    @Operation(summary = "获取当前用户权限", description = "内部管理系统")
    @GetMapping
    @SaCheckRole(RoleConstants.Manager)
    @DS("manage")
    public BaseResult<List<String>> getPermission() {

        // 获取用户信息
        SystemAdmin systemAdmin = StpUtil.getSession().getModel(RoleConstants.Manager, SystemAdmin.class);

        // 根据角色，获取权限内容
        SystemRole role = systemRoleService.getById(systemAdmin.getRoleId());

        List<SystemPermission> list = systemPermissionService.list();

        List<String> permissions = new ArrayList<>();

        if (role.getIsSuper()) {
            // 超级管理员，有全部权限
            for (SystemPermission systemPermission : list) {
                if (StrUtil.isNotBlank(systemPermission.getPerms())) {
                    permissions.addAll(
                            Arrays.stream(systemPermission.getPerms().split("\n")).filter(StrUtil::isNotBlank).toList()
                    );
                }
            }
        } else {
            // 普通角色，获取目录及以下内容
            List<String> ids = List.of(role.getPermissions().split(","));
            // 不为空
            if (!ids.isEmpty()) {
                for (SystemPermission systemPermission : list) {
                    // 如果包含父节点，或者自身等于，那么直接包括进去
                    if (ids.contains(systemPermission.getParentId().toString()) || ids.contains(systemPermission.getId().toString())) {
                        // 如果没增加过
                        if (StrUtil.isNotBlank(systemPermission.getPerms()) && !permissions.contains(systemPermission.getPerms())) {
                            permissions.addAll(
                                    Arrays.stream(systemPermission.getPerms().split("\n")).filter(StrUtil::isNotBlank).toList()
                            );
                        }
                    }
                }
            }
        }

        // 放入缓存中

        StpUtil.getSession().set("permissions",  CollUtil.join(permissions, ","));

        return BaseResult.success(permissions);
    }

}
