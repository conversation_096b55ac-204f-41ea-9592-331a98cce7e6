package com.pgb.service.db.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.pgb.service.db.PgActivityUserService;
import com.pgb.service.domain.activity.PgActivityUser;
import com.pgb.service.mapper.PgActivityUserMapper;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【pg_activity_user】的数据库操作Service实现
* @createDate 2024-12-23 16:21:42
*/
@Service
public class PgActivityUserServiceImpl extends ServiceImpl<PgActivityUserMapper, PgActivityUser>
    implements PgActivityUserService {

}




