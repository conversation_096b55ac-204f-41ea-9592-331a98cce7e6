package com.pgb.service.db;

import com.baomidou.mybatisplus.extension.service.IService;
import com.pgb.service.domain.popup.PgPopup;
import com.pgb.service.enums.PopupTypeEnum;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【pg_popups(弹窗表)】的数据库操作Service
* @createDate 2024-11-14 14:24:40
*/
public interface PgPopupService extends IService<PgPopup> {
    /**
     * 根据用户标签，过滤有效弹窗
     * @param userId
     * @param type
     * @return
     */
    List<PgPopup> filterPopup(Long userId, PopupTypeEnum type);
}
