package com.pgb.common.sms;

//import cn.hutool.core.lang.Console;
//import lombok.Data;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.amqp.core.Message;
//import org.springframework.amqp.core.MessageProperties;
//import org.springframework.amqp.rabbit.annotation.RabbitHandler;
//import org.springframework.amqp.rabbit.annotation.RabbitListener;
//import org.springframework.amqp.rabbit.core.RabbitTemplate;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Component;
//import com.rabbitmq.client.Channel;
//
//import java.io.IOException;

//@Component
//@RabbitListener(queues = {RabbitMQConfig.ORDER_QUEUE_NAME})
//@Slf4j
public class DelayReceiverDemo {

//    @Autowired
//    private RabbitTemplate rabbitTemplate;
//
//    @Data
//    private static class QueueDemoCls {
//        private String name;
//    }
//
//    public void test() {
//        log.info("开始模拟生成订单，10秒后过期");
//
//        QueueDemoCls data = new QueueDemoCls();
//        data.setName("这是测试");
//        rabbitTemplate.convertAndSend(
//                RabbitMQConfig.ORDER_DELAY_EXCHANGE,
//                RabbitMQConfig.ORDER_DELAY_ROUTING_KEY,
//                data,
//                message -> {
//                    message.getMessageProperties().setExpiration(1000 * 10 + "");
//                    return message;
//                });
//    }
//
//    @RabbitHandler
//    public void orderDelayQueue(QueueDemoCls account, Message message, Channel channel) {
//        Console.log("收到消息：{}", account);
//        //消息头属性
//        MessageProperties properties = message.getMessageProperties();
//        //获取消息的标签，在channel内是按顺序递增的
//        long deliveryTag = properties.getDeliveryTag();
//        try {
//            /**
//             * 成功消费之后进行手动应答，RabbitMQ就可以将消费过的消息丢弃了
//             * 参数1：消息的标记（tag）
//             * 参数2：是否批量应答；false表示消费一个才应答一个，true表示消费一个之后将channel中小于tag标记的消息都应答了
//             */
//            channel.basicAck(deliveryTag, false);
//        } catch (IOException e) {
//            throw new RuntimeException(e);
//        }
//    }
}
