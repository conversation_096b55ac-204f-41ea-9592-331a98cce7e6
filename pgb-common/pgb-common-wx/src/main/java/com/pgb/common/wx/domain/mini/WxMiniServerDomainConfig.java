package com.pgb.common.wx.domain.mini;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

// https://developers.weixin.qq.com/doc/oplatform/openApi/OpenApiDoc/miniprogram-management/domain-management/modifyServerDomain.html
@Data
public class WxMiniServerDomainConfig {
    @Schema(title = "操作类型", description = "默认覆盖")
    private String action = "set";

    @JsonProperty("requestdomain")
    private List<String> requestDomain = List.of("https://api.pigaibang.com", "https://test-api.pigaibang.com");

    @JsonProperty("wsrequestdomain")
    private List<String> wsRequestDomain = List.of("wss://api.pigaibang.com", "wss://test-api.pigaibang.com");;

    @JsonProperty("uploaddomain")
    private List<String> uploadDomain = List.of("https://api.pigaibang.com", "https://test-api.pigaibang.com");;

    @JsonProperty("downloaddomain")
    private List<String> downloadDomain = List.of("https://api.pigaibang.com", "https://test-api.pigaibang.com");;

    @JsonProperty("udpdomain")
    private List<String> udpDomain;

    @JsonProperty("tcpdomain")
    private List<String> tcpDomain;
}
