package com.pgb.subscribe.subcribe;


import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.pgb.common.core.utils.EnvUtils;
import com.pgb.common.redis.QueueUtils;
import com.pgb.service.custom.ZwHomeworkReportService;
import com.pgb.service.db.PgHomeworkReportService;
import com.pgb.service.domain.homework.PgHomeworkReport;
import com.pgb.service.enums.CorrectStatusEnum;
import com.pgb.service.domain.GlobQueueConstants;
import com.pgb.subscribe.service.CloudReportService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RBlockingQueue;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;
import java.util.concurrent.CompletableFuture;

@Component("ReportQueueSubscribe")
@Slf4j
@RequiredArgsConstructor
public class ReportQueueSubscribe implements CommandLineRunner {

    private final ZwHomeworkReportService zwHomeworkReportService;

    private final PgHomeworkReportService pgHomeworkReportService;

    private final CloudReportService cloudReportService;

    @Override
    public void run(String... args) throws Exception {

        // 负载均衡，使用线上的报告生成
        if (EnvUtils.isProd()) {
           cloudReportService.cloudScribe(GlobQueueConstants.PGB_XCX_HOMEWORK_REPORT_QUEUE.name());
            // 立即执行一次
            pgHomeworkReportService.queryToExport();
            log.info("监听生成班级分析报告队列 --> 启动");
        }

        // doReportByHomeworkId(1934507336974798850L, true);

        if (EnvUtils.isDev()) {
            // 导出队列
            reExport();

            // 导出队列
            reExport();

            cloudReportService.cloudScribe(GlobQueueConstants.PGB_XCX_HOMEWORK_REPORT_QUEUE.name());
            log.info("监听生成班级分析报告队列 --> 启动");
        }

        // doReportByHomeworkId(1897516351594250244L, true);

        //if (EnvUtils.isProd()) {
        //    // 导出队列
        //    reExport();
        //
        //    // 导出队列
        //    reExport();
        //
        //    // 立即执行一次
        //    queryToExport();
        //    log.info("监听生成班级分析报告队列 --> 启动");
        //}
    }

    private void reExport() {

        // 获取导出队列
        RBlockingQueue<Long> queue = QueueUtils.getClient().getBlockingQueue(GlobQueueConstants.PGB_XCX_HOMEWORK_REPORT_QUEUE.name());

        // 全局异步监听导出word
        CompletableFuture<Void> f = CompletableFuture.runAsync(() -> {
            try {
                // 阻塞获取md5任务
                Long reportId = queue.take();

                doReport(reportId, false);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
        });

        f.whenComplete((v, e) -> {
            if (e != null) {
                // 如果是 RedissonShutdownException 则直接跳过
                if (e.getCause().getMessage().contains("Redisson is shutdown")) {
                    log.error("导出word队列监听异常 --> redisson 已中断！");
                    return;
                }

                log.error("导出word队列监听异常 -->  异常", e);
            }

            this.reExport();
        });
    }

    private void doReportByHomeworkId(Long homeworkId, boolean isReCorrect) {
        // 拿数据
        PgHomeworkReport report = pgHomeworkReportService.getOne(new LambdaQueryWrapper<PgHomeworkReport>()
                .eq(PgHomeworkReport::getHomeworkId, homeworkId)
                .last("LIMIT 1")
        );
        doReport(report.getId(), isReCorrect);
    }

    private void doReport(Long reportId, boolean isReCorrect) {
        // 保存记录
        PgHomeworkReport report = pgHomeworkReportService.getById(reportId);

        // 执行
        zwHomeworkReportService.statistic(report, isReCorrect);

        // 发送导出结果通知
        cloudReportService.sendWxMsg(report);
    }

    private void queryToExport() {
        // 7分钟之前的
        List<PgHomeworkReport> list = pgHomeworkReportService.list(new LambdaQueryWrapper<PgHomeworkReport>()
                .ne(PgHomeworkReport::getStatus, CorrectStatusEnum.Corrected)
                .lt(PgHomeworkReport::getCreateTime, DateUtil.offsetMinute(new Date(), -7))
        );

        log.info("【待生成班级分析报告扫描】待生成班级分析报告数量:{}个", list.size());

        list.forEach(record -> {
            // 加入导出队列
            if (!QueueUtils.containsQueueObject(GlobQueueConstants.PGB_XCX_HOMEWORK_REPORT_QUEUE.name(), record.getId())) {
                QueueUtils.addQueueObject(GlobQueueConstants.PGB_XCX_HOMEWORK_REPORT_QUEUE.name(), record.getId());
            } else {
                log.info("当前作业报告已存在导出队列中：{}，跳过", record.getId());
            }
        });
    }
}
