package com.pgb.common.oss.domain;

import com.pgb.common.oss.enumd.AccessPolicyType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@ToString
@Builder
@Schema(title = "OSS对象存储 配置属性", description = "S3 协议通用")
@EqualsAndHashCode
@Data
public class OssProperties {

    @Schema(title = "访问站点")
    private String endpoint;

    @Schema(title = "自定义域名", description = "可自定义cdn 域名")
    private String domain;

    @Schema(title = "ACCESS_KEY")
    private String accessKey;

    @Schema(title = "SECRET_KEY")
    private String secretKey;

    @Schema(title = "存储空间名")
    private String bucketName;

    @Schema(title = "存储区域")
    private String region;

    @Schema(title = "桶权限类型", description = "0：private 1：public 2：custom")
    private AccessPolicyType accessPolicy;

    @Schema(title = "是否启用 https")
    private Boolean isHttps;

}
