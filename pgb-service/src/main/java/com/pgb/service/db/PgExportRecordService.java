package com.pgb.service.db;

import com.baomidou.mybatisplus.extension.service.IService;
import com.pgb.service.domain.answer.PgAnswer;
import com.pgb.service.domain.export.PgExportRecord;
import com.pgb.service.domain.userConfig.ExportConfigDTO;
import com.pgb.service.domain.zc.answer.PgZcAnswer;

import java.io.File;
import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【pg_export_record】的数据库操作Service
 * @createDate 2024-09-25 19:34:27
 */
public interface PgExportRecordService extends IService<PgExportRecord> {

    /**
     * 单篇导出word批改报告
     * @param answer
     * @param fileName
     * @return
     */
    File getExportWord(PgAnswer answer, String fileName);

    File getExportOneDoc(List<Long> answerIds, ExportConfigDTO exportConfig);

    /**
     * 导出字词批改报告pdf
     * @param zcAnswer
     * @param fileName
     * @return
     */
    File getZcExportPdf(PgZcAnswer zcAnswer,String fileName);

    /**
     * 导出字词批改报告图片
     * @param zcAnswer
     * @param fileName
     * @return
     */
    File getZcExportImage(PgZcAnswer zcAnswer, String fileName);
}
