package com.pgb.service.domain.systemConfig;

import java.io.Serializable;
import java.util.Date;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.ToString;
import lombok.Builder;

/**
* 系统配置
* @TableName system_config
*/
@Schema(description = "系统配置 VO")
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode
@ToString
@Builder
public class SystemConfigVO implements Serializable {

    @Schema(title = "配置key")
    private String key;

    @Schema(title = "配置内容")
    private Object value;

    @Schema(title = "是否启用")
    private Boolean isValid;

    @Schema(title = "管理系统是否可见")
    private Boolean isShow;

    @Schema(title = "备注信息")
    private String remark;

    @Schema(title = "创建时间")
    private Date createTime;

    @Schema(title = "更新时间")
    private Date updateTime;

}
