package com.pgb.service.domain.zc.answer.batch;

import java.io.Serializable;

import java.util.Date;

import com.pgb.common.core.validate.UpdateGroup;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.ToString;
import lombok.Builder;
import org.springframework.validation.annotation.Validated;
import jakarta.validation.constraints.NotNull;

/**
* 默写批量上传表
* @TableName pg_zc_answer_batch
*/
@Schema(description = "默写批量上传表 DTO")
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode
@ToString
@Validated
@Builder
public class PgZcAnswerBatchDTO implements Serializable {

    @Schema(title = "批次id", type = "string")
    @NotNull(message="[批次id]不能为空", groups = {UpdateGroup.class})
    private Long id;

    @Schema(title = "用户id")
    private Long userId;

    @Schema(title = "")
    private Long zcQuestionId;

    @Schema(title = "创建时间")
    private Date createTime;

}
