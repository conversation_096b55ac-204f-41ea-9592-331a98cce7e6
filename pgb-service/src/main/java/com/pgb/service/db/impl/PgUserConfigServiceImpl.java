package com.pgb.service.db.impl;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.EnumUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.pgb.service.db.PgUserConfigService;
import com.pgb.service.domain.userConfig.*;
import com.pgb.service.domain.userConfig.enums.ExportConfigItemEnum;
import com.pgb.service.enums.UserConfigEnum;
import com.pgb.service.mapper.PgUserConfigMapper;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【pg_user_config(用户配置表)】的数据库操作Service实现
 * @createDate 2024-10-08 17:53:17
 */
@Service
public class PgUserConfigServiceImpl extends ServiceImpl<PgUserConfigMapper, PgUserConfig>
        implements PgUserConfigService {

    @Override
    public PgUserConfig getByKey(UserConfigEnum userConfig, Long userId) {
        return getOne(new LambdaQueryWrapper<PgUserConfig>()
                .eq(PgUserConfig::getKey, userConfig.name())
                .eq(PgUserConfig::getUserId, userId)
                .last("LIMIT 1"));
    }

    @Override
    public ExportConfigDTO getExportConfig(Long userId) {
        PgUserConfig config = getByKey(UserConfigEnum.EXPORT, userId);

        // 若没设置过
        if (ObjectUtil.isNull(config)) {
            return new ExportConfigDTO();
        }

        return BeanUtil.toBean(config.getValue(), ExportConfigDTO.class);
    }

    @Override
    public StudentReportConfigDTO getStudentConfig(Long userId) {

        PgUserConfig config = getByKey(UserConfigEnum.STUDENT_REPORT, userId);

        // 若没设置过
        if (ObjectUtil.isNull(config)) {
            return new StudentReportConfigDTO();
        }

        return BeanUtil.toBean(config.getValue(), StudentReportConfigDTO.class);
    }

    @Override
    public CorrectConfigDTO getCorrectConfig(Long userId) {

        PgUserConfig config = getByKey(UserConfigEnum.CORRECT, userId);

        // 若没设置过
        if (ObjectUtil.isNull(config)) {
            return new CorrectConfigDTO();
        }

        return BeanUtil.toBean(config.getValue(), CorrectConfigDTO.class);
    }

    @Override
    public ExportConfigVOV3 getExportConfigV3(long userId) {
        /**
         * config表 ==》 ExportConfigDTO ==》ExportConfigVOV3
         */
        PgUserConfig userConfig = getByKey(UserConfigEnum.EXPORT, userId);

        // 2. 获取数据库导出配置
        ExportConfigDTO dbConfig;
        // 若没设置过
        if (ObjectUtil.isNull(userConfig)) {
            dbConfig = new ExportConfigDTO();
        } else {
            dbConfig = BeanUtil.toBean(userConfig.getValue(), ExportConfigDTO.class);
        }

        // 先把基础配置拿到
        ExportConfigVOV3 configVoV3 = BeanUtil.toBean(dbConfig, ExportConfigVOV3.class);

        // 3. 获取导出排序配置项
        List<ExportConfigItem> exportItemList = new ArrayList<>();

        // 获取全部导出配置项
        List<String> allItem = EnumUtil.getNames(ExportConfigItemEnum.class);

        // 获取数据库中保存的顺序配置项
        List<String> dbSortList = dbConfig.getSortList();

        // 如果为空
        if (CollUtil.isEmpty(dbSortList)) {
            dbSortList = new ArrayList<>();
        }

        // 先根据数据库中的内容获取
        for (String key : dbSortList) {
            if (EnumUtil.getNames(ExportConfigItemEnum.class).contains(key)) {
                // 获取枚举
                ExportConfigItemEnum field = ExportConfigItemEnum.valueOf(key);
                // 创建配置项
                ExportConfigItem item = new ExportConfigItem();
                item.setField(field);
                item.setEnable(
                        (Boolean) BeanUtil.getFieldValue(dbConfig, field.name())
                );
                exportItemList.add(item);
                // 移除
                allItem.remove(key);
            }
        }

        // 剩下的就是没有配置过的，默认为启用
        for (String key : allItem) {
            // 获取枚举
            ExportConfigItemEnum field = ExportConfigItemEnum.valueOf(key);

            ExportConfigItem configItem = new ExportConfigItem();
            configItem.setField(field);
            configItem.setEnable(true);
            exportItemList.add(configItem);
        }

        // 设置排序
        configVoV3.setExportItemList(exportItemList);

        // 获取排序内容
        return configVoV3;
    }

    @Override
    public boolean saveExportConfigV3(ExportConfigVOV3 voConfig, Long userId) {
        PgUserConfig userConfig = getByKey(UserConfigEnum.EXPORT, userId);

        // 保存基础配置
        ExportConfigDTO configDTO = BeanUtil.toBean(voConfig, ExportConfigDTO.class);

        // 保存排序配置
        List<String> sortList = new ArrayList<>();
        for (ExportConfigItem item : voConfig.getExportItemList()) {
            sortList.add(item.getField().name());
            BeanUtil.setFieldValue(configDTO, item.getField().name(), item.getEnable());
        }
        configDTO.setSortList(sortList);

        // 如果没设置过
        if (ObjectUtil.isNull(userConfig)) {
            userConfig = new PgUserConfig();
            userConfig.setUserId(StpUtil.getLoginIdAsLong());
            userConfig.setKey(UserConfigEnum.EXPORT.name());
            userConfig.setValue(configDTO);
            userConfig.setRemark("自定义导出配置");
            userConfig.setIsValid(true);
            userConfig.setCreateTime(new Date());
            save(userConfig);
        }

        // 如果设置过 则更新
        userConfig.setValue(configDTO);
        return updateById(userConfig);
    }

}




