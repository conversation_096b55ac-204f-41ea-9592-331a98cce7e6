package com.pgb.service.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * Created by 2024/8/12 17:21
 */
@Schema(description = "应用类型")
@AllArgsConstructor
public enum ChatTypeEnum {

    @Schema(title = "教案设计")
    LESSON(0, "教案设计", "协助老师分布生成教案设计内容","教学资源准备", "markdown",true,"https://cdn.pigaibang.com/common/bfz/icon/lesson.png"),

    @Schema(title = "说课稿")
    SPEECH(1, "说课稿", "协助老师写说课稿","教学资源准备", "markdown",true,"https://cdn.pigaibang.com/common/bfz/icon/speech.png"),

    @Schema(title = "大单元设计")
    UNIT(2, "大单元设计", "教学评大单元教学设计","教学资源准备", "markdown",true,"https://cdn.pigaibang.com/common/bfz/icon/unit.png"),

    @Schema(title = "大单元教学设计")
    UNIT_TEACH(3, "大单元教学设计", "大单元教学设计","教学资源准备", "markdown",true,"https://cdn.pigaibang.com/common/bfz/icon/unit_teach.png"),

    @Schema(title = "单元作业设计")
    UNIT_HOMEWORK(4, "单元作业设计", "助力老师单元作业设计","教学资源准备", "markdown",true,"https://cdn.pigaibang.com/common/bfz/icon/unit-homework.png"),

    @Schema(title = "跨学科设计")
    CROSS_SUBJECT(5, "跨学科设计", "助力老师跨学科设计","教学资源准备", "markdown",true,"https://cdn.pigaibang.com/common/bfz/icon/cross_subject.png"),

    @Schema(title = "跨学科PBL设计")
    CROSS_SUBJECT_PBL(6, "跨学科PBL设计", "协助老师完成跨学科PBL项目设计","教学资源准备","markdown",true,"https://cdn.pigaibang.com/common/bfz/icon/cross_subject_pbl.png"),

    @Schema(title = "教育案例分析")
    EDUCATION_CASE(7, "教育案例分析", "教育教学案例分析","教学案例与反馈", "markdown",true,"https://cdn.pigaibang.com/common/bfz/icon/education_case.png"),

    @Schema(title = "听评课")
    LISTEN_CLASS(8, "听评课", "协助老师生成听评课记录","教学案例与反馈", "markdown",true,"https://cdn.pigaibang.com/common/bfz/icon/listen_class.png"),

    @Schema(title = "教师慧语")
    WISDOM(9, "教师慧语", "高情商回答家长和领导问题","家校沟通", "markdown",true,"https://cdn.pigaibang.com/common/bfz/icon/wisdom.png"),

    @Schema(title = "学生评语")
    STUDENT_COMMENT(10, "学生评语", "填写对学生评价的核心内容，帮助生成完整评语","家校沟通", "HTML",true,"https://cdn.pigaibang.com/common/bfz/icon/student_comment.png"),

    @Schema(title = "家访记录表")
    HOME_VISIT(11, "家访记录表", "帮你快速生成家访记录表","家校沟通", "markdown",true,"https://cdn.pigaibang.com/common/bfz/icon/home_visit.png"),

    @Schema(title = "家长会发言稿")
    PARENT_MEETING(12,"家长会发言稿","帮助老师生成家长会发言稿","家校沟通","markdown",true,"https://cdn.pigaibang.com/common/bfz/icon/parent_meeting.png"),

    @Schema(title = "教学计划")
    TEACHING_PLAN(13,"教学计划","帮助老师生成教学计划","教学资源准备","markdown",true,"https://cdn.pigaibang.com/common/bfz/icon/tearch_plan.png"),

    @Schema(title = "全文生成")
    GENERATE(14,"全文生成","一键生成大纲作文","助手","markdown",false,null);

    /**
     * 枚举值对应的整数值
     */
    @EnumValue
    public final int value;

    /**
     * 文字说明
     */
    @Getter
    public final String desc;

    /**
     * 描述
     */
    @Getter
    public final String comment;

    /**
     * 分类
     */
    @Getter
    public final String cateName;

    /**
     * 文档格式
     */
    public final String format;

    /**
     * 是否显示
     */
    public final Boolean isShow;

    /**
     * iconUrl
     */
    @Getter
    public final String iconUrl;


}
