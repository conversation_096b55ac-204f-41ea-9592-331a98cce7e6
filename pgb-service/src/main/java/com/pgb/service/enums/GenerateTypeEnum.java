package com.pgb.service.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;

/**
 * <AUTHOR>
 * Created by 2024/8/24 19:47
 */
@Schema(description = "生成 类型")
@AllArgsConstructor
public enum GenerateTypeEnum {

    @Schema(title = "写全文")
    Text(0,"写全文"),

    @Schema(title = "写段落")
    Paragraph(1,"写段落"),

    @Schema(title = "写英文")
    English(2,"写英文");

    /**
     * 枚举值对应的整数值
     */
    @EnumValue
    public final int value;

    /**
     * 文字说明
     */
    public final String desc;

}
