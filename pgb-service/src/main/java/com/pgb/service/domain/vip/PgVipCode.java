package com.pgb.service.domain.vip;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;

import com.pgb.service.enums.ChannelTypeEnum;
import com.pgb.service.enums.VipTypeEnum;
import lombok.Data;

/**
 *
 * @TableName pg_vip_code
 */
@TableName(value ="pg_vip_code")
@Data
public class PgVipCode implements Serializable {
    /**
     * 用户兑换码
     */
    @TableId
    private Long id;

    /**
     * 兑换码
     */
    private String code;

    /**
     * 使用状态，0：未使用，1:已使用，2：已作废
     */
    private Integer status;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 兑换时间
     */
    private Date exchangeTime;

    /**
     * 兑换用户id
     */
    private Long userId;

    /**
     * 生成渠道，0：系统生成，1：小红书  活动开通
     */
    private ChannelTypeEnum channelType;

    /**
     * 会员类型
     */
    private VipTypeEnum vipType;

    /**
     * 如果有渠道id
     */
    private Long channelId;

    /**
     * 订单id
     */
    private Long orderId;

    /**
     * 备注说明
     */
    private String remark;
}
