package com.pgb.xcx.controller.dictionary;

import cn.hutool.core.util.EnumUtil;
import com.pgb.common.core.global.BaseResult;
import com.pgb.common.core.global.GlobalCode;
import com.pgb.service.enums.CategoryInfo;
import com.pgb.service.enums.EnumDetail;
import com.pgb.service.enums.*;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.Builder;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * Created by 2024/7/17 17:24
 */

@Tag(name = "用户端/通用/字典管理")
@RequestMapping("/user/common/dict")
@RestController("userCommonDictController")
@Transactional(rollbackFor = Exception.class)
@RequiredArgsConstructor
public class DictController {

    public record EnumMapItem(String value, String label, String comment) {
    }

    @Operation(summary = "获取指定枚举字典Key的Map列表")
    @GetMapping("getEnumMapByKey/{key}")
    public BaseResult<List<EnumMapItem>> getEnumMapByKey(@PathVariable String key) {

        List<EnumMapItem> mapList = new ArrayList<>();
        // 年级
        if (key.equals(GradeEnum.class.getSimpleName())) {
            EnumUtil.getNames(GradeEnum.class).forEach(item -> {
                mapList.add(
                        new EnumMapItem(
                                item,
                                GradeEnum.valueOf(item).desc,
                                null
                        )
                );
            });
        }
        // 写作文体
        else if (key.equals(WritingStyleEnum.class.getSimpleName())) {
            EnumUtil.getNames(WritingStyleEnum.class).forEach(item -> {
                mapList.add(
                        new EnumMapItem(
                                item,
                                WritingStyleEnum.valueOf(item).desc,
                                null
                        )
                );
            });
        }
        // 学生身份
        else if (key.equals(IdentityTypeEnum.class.getSimpleName())) {
            EnumUtil.getNames(IdentityTypeEnum.class).forEach(item -> {
                mapList.add(
                        new EnumMapItem(
                                item,
                                IdentityTypeEnum.valueOf(item).desc,
                                null
                        )
                );
            });
        }
        // 学科
        else if (key.equals(SubjectEnum.class.getSimpleName())) {
            EnumUtil.getNames(SubjectEnum.class).forEach(item -> {
                mapList.add(
                        new EnumMapItem(
                                item,
                                SubjectEnum.valueOf(item).desc,
                                null
                        )
                );
            });
        }
        // 年级阶段
        else if (key.equals(GradeStageEnum.class.getSimpleName())) {
            EnumUtil.getNames(GradeStageEnum.class).forEach(item -> {
                mapList.add(
                        new EnumMapItem(
                                item,
                                GradeStageEnum.valueOf(item).desc,
                                null
                        )
                );
            });
        }
        // 册别
        else if (key.equals(VolumnEnum.class.getSimpleName())) {
            EnumUtil.getNames(VolumnEnum.class).forEach(item -> {
                mapList.add(
                        new EnumMapItem(
                                item,
                                VolumnEnum.valueOf(item).desc,
                                null
                        )
                );
            });
        }
        // 应用类型
        else if (key.equals(ChatTypeEnum.class.getSimpleName())) {
            EnumUtil.getNames(ChatTypeEnum.class).forEach(item -> {
                mapList.add(
                        new EnumMapItem(
                                item,
                                ChatTypeEnum.valueOf(item).desc,
                                ChatTypeEnum.valueOf(item).comment
                        )
                );
            });
        }
        // 作文批改结果内容类型
        else if (key.equals(CorrectResultEnum.class.getSimpleName())) {
            EnumUtil.getNames(CorrectResultEnum.class).forEach(item -> {
                mapList.add(
                        new EnumMapItem(
                                item,
                                CorrectResultEnum.valueOf(item).desc,
                                null
                        )
                );
            });
        } else {
            return BaseResult.code(GlobalCode.Item_Null);
        }

        return BaseResult.success(mapList);
    }

    @Schema(description = "枚举 分组数据")
    @Data
    @Builder
    public static class EnumCategory {

        @Schema(title = "分组")
        private List<CategoryInfo> items;

    }

    @Operation(summary = "获取AI应用类型枚举列表")
    @GetMapping("getAiAppCategory")
    public BaseResult<List<CategoryInfo>> getCategory() {

        // 根据 cateName 进行分组
        Map<String, List<ChatTypeEnum>> listByGroup = Arrays.stream(ChatTypeEnum.values())
                .filter(chatType -> chatType.isShow)
                // 根据appName筛选
//                .filter(chatType -> StrUtil.isNotBlank(appName) || chatType.getDesc().contains(appName))
                .collect(Collectors.groupingBy(ChatTypeEnum::getCateName, LinkedHashMap::new, Collectors.toList()));

        // 创建 CategoryInfo 对象列表
        List<CategoryInfo> categoryInfos = listByGroup.entrySet().stream()
                .map(entry -> {
                    List<EnumDetail> enumDetails = entry.getValue().stream()
                            .map(chatType -> new EnumDetail(
                                    chatType.name(),
                                    chatType.getDesc(),
                                    chatType.getComment(),
                                    chatType.getIconUrl()
                            ))
                            .collect(Collectors.toList());
                    return new CategoryInfo(entry.getKey(), enumDetails);
                })
                .collect(Collectors.toList());

        // 返回结果
        return BaseResult.success(categoryInfos);
    }
}
