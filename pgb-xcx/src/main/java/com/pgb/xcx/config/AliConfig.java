package com.pgb.xcx.config;

import cn.hutool.json.JSONUtil;
import com.alipay.api.AlipayApiException;
import com.alipay.api.AlipayClient;
import com.alipay.api.AlipayConfig;
import com.alipay.api.DefaultAlipayClient;
import com.pgb.common.pay.config.AliAppPayProperty;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Slf4j
@Configuration
@EnableConfigurationProperties({AliAppPayProperty.class})
@RequiredArgsConstructor
public class AliConfig {
    @Bean
    public AlipayClient aliPayClient(AliAppPayProperty property) throws AlipayApiException {

        AlipayConfig config = new AlipayConfig();

        config.setServerUrl("https://openapi.alipay.com/gateway.do");
        config.setAppId(property.getAppId());
        config.setPrivateKey(property.getPrivateKey());
        config.setAlipayPublicKey(property.getAliPublicKey());
        config.setFormat("json");
        config.setCharset("UTF-8");
        config.setSignType("RSA2");

        // log.info("【支付宝】：初始化支付宝配置 {}", JSONUtil.toJsonStr(config));

        // 初始化客户端（普通公钥模式）
        return new DefaultAlipayClient(config);
    }
}
