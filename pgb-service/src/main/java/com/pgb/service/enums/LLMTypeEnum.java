package com.pgb.service.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;

@Schema(description = "大模型 类型")
@AllArgsConstructor
public enum LLMTypeEnum {
    @Schema(title = "文本模型")
    TEXT(0, "文本模型"),

    @Schema(title = "视觉模型")
    VISION(1, "视觉模型"),

    @Schema(title = "音频模型")
    AUDIO(2, "音频模型"),

    @Schema(title = "图片生成模型")
    IMAGE(3, "图片生成模型"),

    @Schema(title = "视频生成模型")
    VIDEO(4, "视频生成模型");


    /**
     * 枚举值对应的整数值
     */
    @EnumValue
    public final int value;

    /**
     * 文字说明
     */
    public final String desc;
}
