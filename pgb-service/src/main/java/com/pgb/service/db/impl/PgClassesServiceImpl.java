package com.pgb.service.db.impl;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.pgb.service.db.PgClassesService;
import com.pgb.service.domain.classes.PgClasses;
import com.pgb.service.enums.GradeEnum;
import com.pgb.service.mapper.PgClassesMapper;
import org.springframework.stereotype.Service;

import java.util.Calendar;
import java.util.Date;

/**
 * <AUTHOR>
 * @description 针对表【pg_classes(班级表)】的数据库操作Service实现
 * @createDate 2024-12-10 16:37:13
 */
@Service
public class PgClassesServiceImpl extends ServiceImpl<PgClassesMapper, PgClasses>
        implements PgClassesService {


    @Override
    public PgClasses createClass(Long createUserId, String name, String classNum, GradeEnum grade) {
        PgClasses classes = new PgClasses();
        classes.setClassNum(classNum);
        classes.setCreateTime(new Date());
        classes.setUserId(createUserId);
        classes.setGrade(grade);

        // 设置班级名称
        if (StrUtil.isBlank(name)) {
            // 几年级几班
            classes.setName(grade.getDesc() + classNum);
        } else {
            classes.setName(name);
        }

        // 班级口令
        Calendar calendar = Calendar.getInstance();
        int i = calendar.get(Calendar.YEAR) % 100;

        // 店铺下有多少班级
        long count = count(new LambdaQueryWrapper<PgClasses>());

        // 三个随机数
        int randomPart = (int) (Math.random() * 900) + 100;

        classes.setPassword(StrUtil.format("{}{}{}", i, count, randomPart));

        // 保存
        save(classes);

        return classes;
    }
}




