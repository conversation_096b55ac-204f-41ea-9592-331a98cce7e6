package com.pgb.common.pay.domain.apple;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * @Datetime: 2025年06月14日18:22
 * @Description:
 */
@Data
@Accessors(chain = true)
public class AppleReceiptResponse {

    private Boolean isSuccess;

    @JSONField(name = "status")
    private Integer status;

    @JSONField(name = "receipt")
    private Receipt receipt;

    @JSONField(name = "latest_receipt")
    private String latestReceipt;

    @JSONField(name = "latest_receipt_info")
    private List<InAppPurchase> latestReceiptInfo;

    @Data
    public static class Receipt {
        @JSONField(name = "bundle_id")
        private String bundleId;

        @JSONField(name = "application_version")
        private String applicationVersion;

        @JSONField(name = "in_app")
        private List<InAppPurchase> inApp;
    }

    @Data
    public static class InAppPurchase {
        @JSONField(name = "transaction_id")
        private String transactionId;

        @JSONField(name = "product_id")
        private String productId;

        @JSONField(name = "purchase_date_ms")
        private String purchaseDate;

        @JSONField(name = "expires_date_ms")
        private String expiresDate;
    }
}
