package com.pgb.service.domain.zc.answer.batch;

import java.io.Serializable;

import java.util.Date;

import cn.hutool.core.util.ObjectUtil;
import com.fasterxml.jackson.annotation.JsonGetter;
import com.pgb.service.enums.ZcQuestionTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.ToString;
import lombok.Builder;

/**
* 默写批量上传表
* @TableName pg_zc_answer_batch
*/
@Schema(description = "默写批量上传表 VO")
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode
@ToString
@Builder
public class PgZcAnswerBatchVO implements Serializable {

    @Schema(title = "批次id")
    private Long id;

    @Schema(title = "用户id")
    private Long userId;

    @Schema(title = "关联字词题目id")
    private Long zcQuestionId;

    @Schema(title = "创建时间")
    private Date createTime;

    @Schema(title = "总数")
    private Integer totalNum;

    @Schema(title = "已批改数量")
    private Integer correctedNum;

    @Schema(title = "题目名称")
    private String questionName;

    /**
     * 字词题目类型，0：通用，1：情景式填空
     */
    @Schema(title = "题目类型")
    private ZcQuestionTypeEnum quesType;

    @JsonGetter
    public String getTypeStr() {
        if(ObjectUtil.isNull(this.quesType)) {
            return "";
        }

        return quesType.desc;
    }

}
