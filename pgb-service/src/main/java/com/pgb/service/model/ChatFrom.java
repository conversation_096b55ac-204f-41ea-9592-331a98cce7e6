package com.pgb.service.model;

import com.pgb.service.enums.ChatTypeEnum;
import com.pgb.service.enums.GradeStageEnum;
import com.pgb.service.enums.SubjectEnum;
import com.pgb.service.enums.VolumnEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import org.springframework.validation.annotation.Validated;

import java.util.List;

/**
 * <AUTHOR>
 * Created by 2024/8/14 14:38
 */

@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode
@ToString
@Validated
@Builder
@Data
@Schema(title = "表单内容")
public class ChatFrom {

    @Schema(title = "用户消息内容")
    private String userContent;

    @Schema(title = "用户图片")
    private String imgUrl;

    @Schema(title = "文件")
    private String fileUrl;

    @Schema(title = "是否使用深度思考")
    private Boolean isReason;

    @Schema(title = "是否重新生成")
    private Boolean isFirst = false;
}
