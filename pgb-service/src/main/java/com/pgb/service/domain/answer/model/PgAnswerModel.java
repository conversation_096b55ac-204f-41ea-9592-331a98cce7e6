package com.pgb.service.domain.answer.model;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 范文表
 * @TableName pg_answer_model
 */
@TableName(value ="pg_answer_model")
@Data
public class PgAnswerModel implements Serializable {
    /**
     * id
     */
    private Long id;

    /**
     * 关联范文id
     */
    private Long answerId;

    /**
     * 评语（如：被选为范文的原因）
     */
    private String comment;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 所属作业id
     */
    private Long homeworkId;

    /**
     * 所属用户id
     */
    private Long userId;

    /**
     * 排序
     */
    private Integer sort;

}
