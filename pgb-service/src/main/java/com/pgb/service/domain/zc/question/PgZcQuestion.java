package com.pgb.service.domain.zc.question;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;

import com.pgb.common.mybatis.handler.JsonbTypeHandler;
import com.pgb.service.enums.ExportStatusEnum;
import com.pgb.service.enums.GradeEnum;
import com.pgb.service.enums.SubjectEnum;
import com.pgb.service.enums.ZcQuestionTypeEnum;
import lombok.Data;

/**
 *
 * @TableName pg_zc_question
 */
@TableName(value ="pg_zc_question")
@Data
public class PgZcQuestion implements Serializable {
    /**
     * 默写题库表
     */
    @TableId
    private Long id;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 科目，注意，不同的科目，默写类型是不同的
     */
    private SubjectEnum subject;

    /**
     * 字词类型，0：通用，1：情景式填空
     */
    private ZcQuestionTypeEnum type;

    /**
     * 题目分数
     */
    private Integer score;

    /**
     * 题目名称
     */
    private String name;

    /**
     * 年级，可选
     */
    private GradeEnum grade;

    /**
     * 是否官方题目
     */
    private Boolean isOfficial;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 题目内容
     */
    @TableField(typeHandler = JsonbTypeHandler.class)
    private Object contentJson;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date updateTime;

    /**
     * pdf生成状态
     */
    private ExportStatusEnum pdfStatus;

    /**
     * pdf链接 三份，英文逗号分隔
     */
    private String pdfUrls;

    /**
     * md5唯一值
     */
    private String md5;

}
