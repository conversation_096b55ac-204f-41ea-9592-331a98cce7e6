package com.pgb.student.controller.student;

import cn.dev33.satoken.annotation.SaCheckLogin;
import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.pgb.common.core.global.BaseResult;
import com.pgb.common.core.global.GlobalCode;
import com.pgb.common.mybatis.global.PageQuery;
import com.pgb.service.enums.IdentityTypeEnum;
import com.pgb.student.domain.PgFamily;
import com.pgb.student.domain.PgUserStudent;
import com.pgb.student.domain.PgUsers;
import com.pgb.student.domain.vo.PgFamilyVO;
import com.pgb.student.service.PgFamilyService;
import com.pgb.student.service.PgUserStudentService;
import com.pgb.student.service.PgUsersService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * Created by 2025/3/17 14:15
 */
@Tag(name = "学生端/学生/亲情号")
@RestController("StudentFamilyController")
//@Transactional(rollbackFor = Exception.class)
@RequestMapping("/student/family")
@RequiredArgsConstructor
@Slf4j
public class FamilyController {

    private final PgFamilyService pgFamilyService;

    private final PgUserStudentService pgUserStudentService;

    private final PgUsersService pgUsersService;

    @Operation(summary = "查看当前学生的亲情号列表")
    @PostMapping("list")
    public BaseResult<List<PgFamilyVO>> list() {

        // 当前学生id
        long userStudentId = StpUtil.getSession().getLong("studentId");

        List<PgFamilyVO> list = pgFamilyService.list(new LambdaQueryWrapper<PgFamily>()
                .eq(PgFamily::getStudentId, userStudentId)
        ).stream().map(pgFamily -> {

            PgFamilyVO familyVO = BeanUtil.copyProperties(pgFamily, PgFamilyVO.class);
            PgUsers users = pgUsersService.getById(pgFamily.getInviteUserId());
            familyVO.setInviteUserInfo(users);
            return familyVO;
        }).toList();

        return BaseResult.success(list);
    }


    @Data
    public static class FamilyBindForm {

        @Schema(title = "身份")
        private IdentityTypeEnum identity;
    }

    @Operation(summary = "绑定学生亲情号", description = "被邀请绑定")
    @PostMapping("bind/{userStudentId}")
    @SaCheckLogin
    public BaseResult<Boolean> bind(@PathVariable Long userStudentId, @RequestBody FamilyBindForm form) {

        PgUserStudent student = pgUserStudentService.getById(userStudentId);

        if (ObjectUtil.isNull(student)) {
            return BaseResult.error("当前学生不存在");
        }

        // 不能绑定本人
        if (student.getUserId().equals(StpUtil.getLoginIdAsLong())) {
            return BaseResult.error("不能绑定自己的账号");
        }

        // 不能超过四个
        List<PgUserStudent> studentList = pgUserStudentService.getStudentList(StpUtil.getLoginIdAsLong());
        if (studentList.size() >= 4) {
            return BaseResult.error("最多只能绑定四位学生");
        }

        // 判断是否已绑定过
        PgFamily family = pgFamilyService.getOne(new LambdaQueryWrapper<PgFamily>()
                .eq(PgFamily::getStudentId, userStudentId)
                .eq(PgFamily::getInviteUserId, StpUtil.getLoginIdAsLong()));

        if (ObjectUtil.isNotNull(family)) {
            return BaseResult.error("已存在绑定关系，无需重复绑定");
        }

        // 新增亲情号关联
        PgFamily pgFamily = new PgFamily();
        // 邀请人
        pgFamily.setUserId(student.getUserId());
        // 被邀请人 --> 当前登录用户
        pgFamily.setInviteUserId(StpUtil.getLoginIdAsLong());
        pgFamily.setStudentId(userStudentId);
        // 身份
        pgFamily.setIdentity(form.getIdentity());
        pgFamily.setCreateTime(new Date());

        pgFamilyService.save(pgFamily);

        return BaseResult.success(true);
    }


    @Operation(summary = "解除亲情号绑定")
    @PostMapping("unbind/{familyId}")
    public BaseResult<Boolean> unbind(@PathVariable Long familyId) {

        PgFamily family = pgFamilyService.getById(familyId);

        if (ObjectUtil.isNull(family)) {
            return BaseResult.code(GlobalCode.Item_Null);
        }

        pgFamilyService.removeById(family);

        // 解除session student
        pgUserStudentService.resetStudentSession();

        return BaseResult.success(true);

    }

}
