package com.pgb.common.wx.domain.mini;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * Created by 2024/5/14 17:13
 */
@Data
public class WxMiniDeleteCategory {

    @Schema(description = "一级类目 ID")
    @JsonProperty("first")
    Integer first;

    @Schema(description = "二级类目 ID")
    @JsonProperty("second")
    Integer second;
}
