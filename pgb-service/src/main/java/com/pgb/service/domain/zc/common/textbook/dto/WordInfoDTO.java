package com.pgb.service.domain.zc.common.textbook.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * Created by 2025/7/29 19:33
 */
@Data
@Schema(title = "单词信息DTO")
public class WordInfoDTO {

    @Schema(description = "中文翻译")
    private List<PgWordTranslationDTO> translations;

    @Schema(description = "音标")
    private List<PgWordPronunciationDTO> pronunciations;

    @Schema(description = "例句")
    private List<PgWordExampleDTO> examples;
}
