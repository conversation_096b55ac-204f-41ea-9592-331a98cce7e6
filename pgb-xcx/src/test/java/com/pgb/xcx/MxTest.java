package com.pgb.xcx;

import cn.hutool.core.codec.PunyCode;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.img.ImgUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.json.JSONUtil;
import com.github.difflib.DiffUtils;
import com.github.difflib.patch.Patch;
import com.github.difflib.text.DiffRow;
import com.github.difflib.text.DiffRowGenerator;
import com.pgb.XcxApplication;
import com.pgb.common.ocr.OCRService;
import com.pgb.common.ocr.domain.OCRResult;
import com.pgb.common.ocr.domain.OCRWordResult;
import com.pgb.common.oss.service.OssService;
import com.pgb.service.domain.common.fabric.FabricJson;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.builder.DiffResult;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.awt.image.BufferedImage;
import java.io.File;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * Created by 2025/4/14 11:41
 */

@SpringBootTest(classes = XcxApplication.class, webEnvironment = SpringBootTest.WebEnvironment.DEFINED_PORT)
@Slf4j
public class MxTest {

    @Autowired
    private OCRService ocrService;

    @Autowired
    private OssService ossService;

    @Test
    void setTag() {
        ossService.setTagList("tmp/user/1810610447493398529/IMG/26f1e9fd8fb843b2a37c2114b07c452b.jpg", "test");
    }


    @Test
    void mxTest() {

        String userImg = "https://cdn.pigaibang.com/user3.jpg";

        String answerImg = "https://cdn.pigaibang.com/answer3.jpg";

        // 用户
        OCRResult userResult = ocrService.handWriting(userImg);

        String userAnswer = CollUtil.join(userResult.getWords_result().stream().map(OCRWordResult::getWords).toList(), "");

        // 答案
        OCRResult answerResult = ocrService.handWriting(answerImg);
        String answerAnswer = CollUtil.join(answerResult.getWords_result().stream().map(OCRWordResult::getWords).toList(), "");

//        List<DiffRow> diffRows = DiffRowGenerator.create()
//                .showInlineDiffs(true)
//                .inlineDiffByWord(true)// 按单词比较
//                .build()
//                .generateDiffRows(
//                        Arrays.asList(userAnswer.split(" ")),
//                        Arrays.asList(answerAnswer.split(" "))
//                );
//
//
//        System.out.println(diffRows);

        // 1. 标准化文本
//        String normalizedUser = normalizeText(userText);
//        String normalizedCorrect = normalizeText(correctText);

        // 2. 执行差异分析
//        Patch<String> patch = DiffUtils.diff(
//                Arrays.asList(normalizedCorrect.split("")), // 字符级比较
//                Arrays.asList(normalizedUser.split(""))
//        );
//
//        // 3. 计算相似度
//        double similarity = 1 - (double) patch.getDeltas().size() /
//                Math.max(normalizedCorrect.length(), normalizedUser.length());
//
//        return new DiffResult(patch, similarity);

    }

    /**
     * 正则处理文本，去除空格、标点符号，并转换为小写
     *
     * @param text
     * @return
     */
    private String normalizeText(String text) {
        return text.replaceAll("\\s+", " ")
                .replaceAll("[\\p{P}&&[^']]", "") // 保留英文撇号
                .trim()
                .toLowerCase();
    }

    // 带相似度计算的增强比较
//    public DiffResult enhancedCompare(String userText, String correctText) {
//        // 1. 标准化文本
//        String normalizedUser = normalizeText(userText);
//        String normalizedCorrect = normalizeText(correctText);
//
//        // 2. 执行差异分析
//        Patch<String> patch = DiffUtils.diff(
//                Arrays.asList(normalizedCorrect.split("")), // 字符级比较
//                Arrays.asList(normalizedUser.split(""))
//        );
//
//        // 3. 计算相似度
//        double similarity = 1 - (double) patch.getDeltas().size() /
//                Math.max(normalizedCorrect.length(), normalizedUser.length());
//
//        return new DiffResult(patch, similarity);
//    }
}
