package com.pgb.service.db;


import com.baomidou.mybatisplus.extension.service.IService;
import com.pgb.service.domain.vip.PgVipCode;
import com.pgb.service.enums.ChannelTypeEnum;
import com.pgb.service.enums.VipTypeEnum;

/**
* <AUTHOR>
* @description 针对表【pg_vip_code】的数据库操作Service
* @createDate 2024-10-21 23:50:52
*/
public interface PgVipCodeService extends IService<PgVipCode> {
    /**
     * 创建会员兑换码
     * @param channelType
     * @param vipType
     * @param channelId
     * @param orderId 系统内部订单号
     * @return
     */
    public PgVipCode createVipCode(ChannelTypeEnum channelType, VipTypeEnum vipType, Long channelId, Long orderId);
}
