package com.pgb.service.domain.common.fabric;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Schema(title = "JSON 批改结果")
@NoArgsConstructor
public class FabricJson {

    @Schema(title = "默认背景颜色", description = "置空 为透明")
    private String background = "#ffffff";

    @Schema(title = "批改背景图片")
    private MarkObjectImage backgroundImage;

    @Schema(title = "标注对象 列表")
    private List<Object> objects;
}
