package com.pgb.student.domain.login;

import com.pgb.service.enums.DeviceTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.validation.annotation.Validated;

import java.io.Serializable;

/**
 * <AUTHOR>
 * Created by 2024/11/6 17:46
 */
@Data
@Schema(description = "登录表单实体")
@Validated
@ToString
@EqualsAndHashCode
public class LoginByPhoneFormDTO implements Serializable {

    @Schema(title = "手机号", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "手机号不能为空")
    @Size(min = 11, max = 11)
    String phone;

    @Schema(title = "微信openId")
    String openId;

    @Schema(title = "微信unionId")
    String unionId;

    @Schema(title = "短信验证码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "短信验证码不能为空")
    String smsCode;

    @Schema(title = "登录端口")
    private DeviceTypeEnum deviceType;
}
