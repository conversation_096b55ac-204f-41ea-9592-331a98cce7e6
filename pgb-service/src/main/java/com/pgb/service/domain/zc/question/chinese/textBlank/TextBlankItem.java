package com.pgb.service.domain.zc.question.chinese.textBlank;

import cn.hutool.core.collection.CollUtil;
import com.fasterxml.jackson.annotation.JsonGetter;
import com.pgb.service.domain.zc.common.ZcLocation;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.awt.geom.Rectangle2D;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Datetime: 2025年05月24日01:53
 * @Description:
 */
@Data
public class TextBlankItem {
    @Schema(title = "课文标题")
    private String title;

    @Schema(title = "课文内容")
    private String text;

    @Schema(title = "词语列表")
    private List<String> wordList;

    @Schema(title = "标题划线")
    private List<TextBlankItemMark> titleMark;

    @Schema(title = "正文划线")
    private List<TextBlankItemMark> textMark;

    @Data
    public static class TextBlankItemMark {

        private Integer start;

        private Integer end;

        @Schema(title = "划线单个文字")
        private String text;

        @Schema(title = "拼音")
        private String pinyin;

        @Schema(title = "用户填写内容")
        private String userContent;

        @Schema(title = "批改正误情况：0：未作答，1：正确，2：错误")
        private Integer rightType;

        @Schema(title = "坐标")
        private ZcLocation location;
    }

    @Data
    public static class TextBlankMarkArea {
        private Integer start;

        private Integer end;

        @Schema(title = "划线单个文字")
        private List<String> text;

        @Schema(title = "拼音")
        private List<String> pinyin;

        @Schema(title = "坐标列表")
        private List<ZcLocation> locationList;

        @Schema(title = "学生书写文字")
        private List<String> userText;

        @Schema(title = "学生书写拼音")
        private List<String> userPinyin;

        @Schema(title = "学生汉字坐标")
        private List<ZcLocation> userTextLocationList;

        public TextBlankMarkArea() {
            this.text = new ArrayList<>();
            this.pinyin = new ArrayList<>();
            this.locationList = new ArrayList<>();
            this.userPinyin = new ArrayList<>();
            this.userText = new ArrayList<>();
            this.userTextLocationList = new ArrayList<>();
        }

        @Schema(title = "当前区域坐标")
        @JsonGetter
        public ZcLocation getLocation() {
            if (CollUtil.isEmpty(this.locationList)) {
                return new ZcLocation();
            }

            ZcLocation location = new ZcLocation();
            // 最小的左边
            location.setLeft(
                    (float) locationList.stream().mapToDouble(ZcLocation::getLeft).min().getAsDouble()
            );
            // 最大的width
            location.setWidth(
                    (float) locationList.stream().mapToDouble(ZcLocation::getWidth).sum()
            );
            location.setTop(
                    (float) locationList.stream().mapToDouble(ZcLocation::getTop).min().getAsDouble()
            );
            location.setHeight(
                    (float) locationList.stream().mapToDouble(ZcLocation::getHeight).max().getAsDouble()
            );

            location.setPageNo(locationList.get(0).getPageNo());
            return location;
        }

        @Schema(title = "判断是否在该区域内")
        public Boolean isInArea(Float left, Float top, Float width, Float height) {

            if (CollUtil.isEmpty(this.locationList)) {
                return false;
            }
            // 按pageNo和top进行分组
            Map<String, List<ZcLocation>> groups = this.locationList.stream()
                    .collect(Collectors.groupingBy(loc ->
                            loc.getPageNo() + "-" + loc.getTop()
                    ));
            for (List<ZcLocation> group : groups.values()) {

                ZcLocation lineLocation = new ZcLocation();

                // 最小的左边
                lineLocation.setLeft(
                        (float) group.stream().mapToDouble(ZcLocation::getLeft).min().getAsDouble()
                );
                // 最大的width
                lineLocation.setWidth(
                        (float) group.stream().mapToDouble(ZcLocation::getWidth).sum()
                );
                lineLocation.setTop(
                        (float) group.stream().mapToDouble(ZcLocation::getTop).min().getAsDouble()
                );
                lineLocation.setHeight(
                        (float) group.stream().mapToDouble(ZcLocation::getHeight).max().getAsDouble()
                );

                lineLocation.setPageNo(group.get(0).getPageNo());

                Rectangle2D.Float lineRect = new Rectangle2D.Float(
                        lineLocation.getLeft(), lineLocation.getTop(),
                        lineLocation.getWidth(), lineLocation.getHeight()
                );

                Rectangle2D.Float userRect = new Rectangle2D.Float(left, top, width, height);

                if (lineRect.intersects(userRect)) {
                    return true;
                }

            }
            return false;
        }

    }
}
