package com.pgb.service.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;

/**
 * <AUTHOR>
 * Created by 2025/7/16 12:04
 */
@Schema(title = "生成状态类型")
@AllArgsConstructor
public enum GenerateStatusEnum {

    Init(0, "初始化"),

    Generating(1, "生成中"),

    Completed(2, "已完成"),

    Failed(3, "生成失败");

    /**
     * 枚举值对应的整数值
     */
    @EnumValue
    public final int value;

    /**
     * 文字说明
     */
    public final String desc;
}
