<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <!--  工程信息 -->
    <groupId>com.pgb</groupId>
    <artifactId>pgb-server</artifactId>
    <version>${revision}</version>

    <name>pgb-server</name>
    <url>https://pigaibang.com</url>
    <description>批改邦 后端</description>

    <!--  父工程必须指定为 pom  -->
    <packaging>pom</packaging>

    <!--  模块说明：这里声明多个子模块  -->
    <modules>
        <module>pgb-common</module>
        <module>pgb-xcx</module>
        <module>pgb-manage-api</module>
        <module>pgb-service</module>
        <module>pgb-correct</module>
        <module>pgb-student</module>
        <module>pgb-subscribe</module>
    </modules>

    <!-- 版本说明 -->
    <properties>
        <!-- 项目工程 -->
        <revision>1.0.2</revision>
        <!-- JDK -->
        <java.version>17</java.version>
        <!-- SpringBoot -->
        <spring-boot.version>3.1.5</spring-boot.version>
        <!-- 项目依赖 -->
        <!-- redisson依赖 -->
        <redisson.version>3.29.0</redisson.version>
        <!-- 注解 -->
        <lombok.version>1.18.30</lombok.version>
        <!-- pg 数据库 -->
        <postgresql.version>42.6.0</postgresql.version>
        <!-- Hutool 工具包 -->
        <hutool.version>5.8.36</hutool.version>
        <!-- mybatis-plus -->
        <mybatis-plus.version>3.5.9</mybatis-plus.version>
        <dynamic-datasource.version>4.3.1</dynamic-datasource.version>
        <!-- saToken -->
        <satoken.version>1.37.0</satoken.version>
        <easyexcel.version>3.3.2</easyexcel.version>
        <springdoc.version>2.1.0</springdoc.version>
        <rabbit-mq.version>3.1.2</rabbit-mq.version>
        <tencentcloud.version>3.1.830</tencentcloud.version>
        <tencent-cos.version>5.6.213</tencent-cos.version>
        <!-- SMS 配置 -->
        <sms4j.version>3.2.1</sms4j.version>
        <!-- 微信公众号 -->
        <wxjava.version>4.6.0</wxjava.version>
        <!-- OSS S3 SDK -->
        <aws-java-sdk-s3.version>1.12.540</aws-java-sdk-s3.version>
        <!-- pdfbox 工具 -->
        <pdfbox.version>3.0.1</pdfbox.version>

        <!-- 插件版本 -->
        <maven-jar-plugin.version>3.2.2</maven-jar-plugin.version>
        <maven-compiler-plugin.verison>3.11.0</maven-compiler-plugin.verison>
        <flatten-maven-plugin.version>1.3.0</flatten-maven-plugin.version>

        <!--   百度 OCR https://ai.baidu.com/ai-doc/OCR/Nkibizxlf#%E6%89%8B%E5%86%99%E6%96%87%E5%AD%97%E8%AF%86%E5%88%AB   -->
        <baidu.aip.version>4.16.18</baidu.aip.version>
        <!--AI 大模型-->
        <baidu.ai.version>0.0.9</baidu.ai.version>
        <!--百度云 OSS -->
        <baidu.oss.version>0.10.358</baidu.oss.version>

        <!--通义大模型-->
        <ali.ai.version>2.18.2</ali.ai.version>

        <!--支付宝支付-->
        <ali.pay.version>4.40.251.ALL</ali.pay.version>

        <!--  阿里云 工具  -->
        <ali.nls.version>2.2.1</ali.nls.version>
        <ali.sdk.version>4.6.3</ali.sdk.version>
        <ali.oss.version>3.17.4</ali.oss.version>

        <!--  word导出  -->
        <poi.version>5.2.3</poi.version>

    </properties>

    <!-- 版本说明：这里统一管理依赖的版本号 -->
    <dependencyManagement>
        <dependencies>

            <!-- 公共模块 -->
            <dependency>
                <groupId>com.pgb</groupId>
                <artifactId>pgb-common-bom</artifactId>
                <version>1.0.2</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!-- 标品-用户端接口  -->
            <dependency>
                <groupId>com.pgb</groupId>
                <artifactId>pgb-api-user</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- 标品-后台管理端接口  -->
            <dependency>
                <groupId>com.pgb</groupId>
                <artifactId>pgb-api-admin</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- 标品-系统通用  -->
            <dependency>
                <groupId>com.pgb</groupId>
                <artifactId>pgb-api-system</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- 标品-晚辅接口  -->
            <!--<dependency>-->
            <!--    <groupId>com.pgb</groupId>-->
            <!--    <artifactId>pgb-api-wf</artifactId>-->
            <!--    <version>${revision}</version>-->
            <!--</dependency>-->

            <!-- 人脸识别-插件  -->
            <!--<dependency>-->
            <!--    <groupId>com.pgb</groupId>-->
            <!--    <artifactId>pgb-face</artifactId>-->
            <!--    <version>${revision}</version>-->
            <!--</dependency>-->

            <!-- 管理端-接口  -->
            <dependency>
                <groupId>com.pgb</groupId>
                <artifactId>pgb-api-manage</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- 标品-通用实体类  -->
            <dependency>
                <groupId>com.pgb</groupId>
                <artifactId>pgb-pojo</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- 标品-通用实体类  -->
            <dependency>
                <groupId>com.pgb</groupId>
                <artifactId>pgb-service</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- 订单系统-插件  -->
            <dependency>
                <groupId>com.pgb</groupId>
                <artifactId>pgb-order</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- 批改模块-插件  -->
            <dependency>
                <groupId>com.pgb</groupId>
                <artifactId>pgb-correct</artifactId>
                <version>${revision}</version>
            </dependency>

            <!--订阅服务-->
            <dependency>
                <groupId>com.pgb</groupId>
                <artifactId>pgb-subscribe</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- SpringBoot3.x 框架 依赖配置 -->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring-boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!-- lombok -->
            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>${lombok.version}</version>
            </dependency>

            <!-- PostgreSql -->
            <dependency>
                <groupId>org.postgresql</groupId>
                <artifactId>postgresql</artifactId>
                <version>${postgresql.version}</version>
            </dependency>

            <!-- Mybatis-plus 官网：https://mp.baomidou.com/ -->
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-spring-boot3-starter</artifactId>
                <version>${mybatis-plus.version}</version>
            </dependency>
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-jsqlparser</artifactId>
                <version>${mybatis-plus.version}</version>
            </dependency>
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>dynamic-datasource-spring-boot3-starter</artifactId>
                <version>${dynamic-datasource.version}</version>
            </dependency>

            <!-- redis 工具 redisson-->
            <dependency>
                <groupId>org.redisson</groupId>
                <artifactId>redisson-spring-boot-starter</artifactId>
                <version>${redisson.version}</version>
            </dependency>

            <!-- sa-token 权限认证, 在线文档：http://sa-token.dev33.cn/ -->
            <dependency>
                <groupId>cn.dev33</groupId>
                <artifactId>sa-token-spring-boot3-starter</artifactId>
                <version>${satoken.version}</version>
            </dependency>

            <!-- Sa-Token 整合 Redis （使用 jackson 序列化方式） -->
            <dependency>
                <groupId>cn.dev33</groupId>
                <artifactId>sa-token-redisson-jackson</artifactId>
                <version>${satoken.version}</version>
            </dependency>

            <!-- EasyExcel   -->
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>easyexcel</artifactId>
                <version>${easyexcel.version}</version>
            </dependency>

            <!--  HuTool 工具包   -->
            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-all</artifactId>
                <version>${hutool.version}</version>
            </dependency>

            <!--  springdoc 文档工具 https://springdoc.org/   -->
            <dependency>
                <groupId>org.springdoc</groupId>
                <artifactId>springdoc-openapi-starter-webmvc-ui</artifactId>
                <version>${springdoc.version}</version>
            </dependency>

            <!-- RabbitMQ依赖-->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-amqp</artifactId>
                <version>${rabbit-mq.version}</version>
            </dependency>

            <!-- AOP 切面-->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-aop</artifactId>
                <version>3.1.2</version>
            </dependency>

            <!--  腾讯云接口SDK   -->
            <dependency>
                <groupId>com.tencentcloudapi</groupId>
                <artifactId>tencentcloud-sdk-java</artifactId>
                <version>${tencentcloud.version}</version>
            </dependency>

            <!--  腾讯云 COS SDK -->
            <dependency>
                <groupId>com.qcloud</groupId>
                <artifactId>cos_api</artifactId>
                <version>${tencent-cos.version}</version>
            </dependency>

            <!--  sms4j  https://sms4j.com/ -->
            <dependency>
                <groupId>org.dromara.sms4j</groupId>
                <artifactId>sms4j-spring-boot-starter</artifactId>
                <version>${sms4j.version}</version>
            </dependency>

            <!--  微信公众号 开发工具包 https://gitee.com/binary/weixin-java-tools   -->
            <dependency>
                <groupId>com.github.binarywang</groupId>
                <artifactId>wx-java-mp-spring-boot-starter</artifactId>
                <version>${wxjava.version}</version>
            </dependency>
            <dependency>
                <groupId>com.github.binarywang</groupId>
                <artifactId>weixin-java-open</artifactId>
                <version>${wxjava.version}</version>
            </dependency>
            <dependency>
                <groupId>com.github.binarywang</groupId>
                <artifactId>weixin-java-pay</artifactId>
                <version>${wxjava.version}</version>
            </dependency>
            <dependency>
                <groupId>com.github.binarywang</groupId>
                <artifactId>weixin-java-miniapp</artifactId>
                <version>${wxjava.version}</version>
            </dependency>


            <!--  Amazon OSS S3 通用协议 -->
            <dependency>
                <groupId>com.amazonaws</groupId>
                <artifactId>aws-java-sdk-s3</artifactId>
                <version>${aws-java-sdk-s3.version}</version>
            </dependency>

            <!-- PDFBox pdf转图片 -->
            <dependency>
                <groupId>org.apache.pdfbox</groupId>
                <artifactId>pdfbox</artifactId>
                <version>${pdfbox.version}</version>
            </dependency>

            <!--  百度云  -->
            <dependency>
                <groupId>com.baidu.aip</groupId>
                <artifactId>java-sdk</artifactId>
                <version>${baidu.aip.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.slf4j</groupId>
                        <artifactId>slf4j-simple</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <!-- 百度云 文心一言  -->
            <dependency>
                <groupId>com.baidubce</groupId>
                <artifactId>qianfan</artifactId>
                <version>${baidu.ai.version}</version>
            </dependency>

            <!--  Ali 通义千问   -->
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>dashscope-sdk-java</artifactId>
                <version>${ali.ai.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.slf4j</groupId>
                        <artifactId>slf4j-simple</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <!--   阿里云，语音识别     -->
            <dependency>
                <groupId>com.alibaba.nls</groupId>
                <artifactId>nls-sdk-common</artifactId>
                <version>${ali.nls.version}</version>
                <!--处理依赖冲突-->
                <exclusions>
                    <exclusion>
                        <groupId>com.squareup.okio</groupId>
                        <artifactId>okio</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.squareup.okhttp3</groupId>
                        <artifactId>okhttp</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.aliyun</groupId>
                <artifactId>aliyun-java-sdk-core</artifactId>
                <version>${ali.sdk.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba.nls</groupId>
                <artifactId>nls-sdk-tts</artifactId>
                <version>${ali.nls.version}</version>
            </dependency>

            <!-- word poi -->
            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi-ooxml</artifactId>
                <version>${poi.version}</version>
            </dependency>

            <!-- 阿里云 OSS -->
            <dependency>
                <groupId>com.aliyun.oss</groupId>
                <artifactId>aliyun-sdk-oss</artifactId>
                <version>${ali.oss.version}</version>
            </dependency>

            <!--  支付宝支付  -->
            <dependency>
                <groupId>com.alipay.sdk</groupId>
                <artifactId>alipay-sdk-java</artifactId>
                <version>${ali.pay.version}</version>
            </dependency>

            <!-- openai 规范 https://cloud.baidu.com/doc/qianfan-docs/s/nm9l6oc8e -->
            <!-- openai 规范 https://github.com/openai/openai-java -->
            <dependency>
                <groupId>com.openai</groupId>
                <artifactId>openai-java</artifactId>
                <version>2.8.1</version>
            </dependency>

            <dependency>
                <groupId>com.baidubce</groupId>
                <artifactId>bce-java-sdk</artifactId>
                <version>${baidu.oss.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.apache.hbase</groupId>
                        <artifactId>hbase-client</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

        </dependencies>
    </dependencyManagement>

    <!-- 环境切换 -->
    <profiles>
        <!-- 本地开发环境 -->
        <profile>
            <id>dev</id>
            <properties>
                <profileActive>dev</profileActive>
                <logging.level>debug</logging.level>
            </properties>
            <activation>
                <!--默认环境-->
                <activeByDefault>true</activeByDefault>
            </activation>
        </profile>
        <!-- 生产环境 -->
        <profile>
            <id>test</id>
            <properties>
                <profileActive>test</profileActive>
                <logging.level>warn</logging.level>
            </properties>
        </profile>
        <!-- 生产环境 -->
        <profile>
            <id>prod</id>
            <properties>
                <profileActive>prod</profileActive>
                <logging.level>warn</logging.level>
            </properties>
        </profile>
    </profiles>

    <!-- 编译配置 -->
    <build>
        <plugins>
            <!-- maven 编译插件 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>${maven-compiler-plugin.verison}</version>
                <configuration>
                    <!-- 指定源代码兼容的Java版本 -->
                    <source>${java.version}</source>
                    <!-- 指定生成的字节码兼容的Java版本 -->
                    <target>${java.version}</target>
                    <encoding>UTF-8</encoding>
                    <compilerArgs>
                        <compilerArg>
                            -parameters
                        </compilerArg>
                    </compilerArgs>
                </configuration>
            </plugin>
            <!-- 防止打包导致的文件异常 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-resources-plugin</artifactId>
                <configuration>
                    <encoding>UTF-8</encoding>
                    <!-- 过滤后缀为pem、pfx的证书文件 -->
                    <nonFilteredFileExtensions>
                        <nonFilteredFileExtension>p12</nonFilteredFileExtension>
                        <nonFilteredFileExtension>pem</nonFilteredFileExtension>
                        <nonFilteredFileExtension>pfx</nonFilteredFileExtension>
                        <nonFilteredFileExtension>dll</nonFilteredFileExtension>
                        <nonFilteredFileExtension>so</nonFilteredFileExtension>
                        <nonFilteredFileExtension>jar</nonFilteredFileExtension>
                    </nonFilteredFileExtensions>
                </configuration>
            </plugin>
            <!-- 统一版本号管理 -->
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>flatten-maven-plugin</artifactId>
                <version>${flatten-maven-plugin.version}</version>
                <configuration>
                    <updatePomFile>true</updatePomFile>
                    <flattenMode>resolveCiFriendliesOnly</flattenMode>
                </configuration>
                <executions>
                    <execution>
                        <id>flatten</id>
                        <phase>process-resources</phase>
                        <goals>
                            <goal>flatten</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>flatten.clean</id>
                        <phase>clean</phase>
                        <goals>
                            <goal>clean</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>

        <!--配置环境的文件-->
        <resources>
            <resource>
                <directory>src/main/resources</directory>
                <filtering>true</filtering>
                <!--默认打包时过先滤掉所有配置文件-->
                <excludes>
                    <exclude>application-dev.yml</exclude>
                    <exclude>application-prod.yml</exclude>
                    <exclude>application-test.yml</exclude>
                    <exclude>application.yml</exclude>
                </excludes>
            </resource>
            <resource>
                <directory>src/main/resources</directory>
                <filtering>true</filtering>
                <!--配置跟随打包指令环境而配置-prod生产环境 例如：mvn package -prod-->
                <includes>
                    <include>application-${profileActive}.yml</include>
                    <include>application.yml</include>
                </includes>
            </resource>
        </resources>
    </build>

    <!-- 加速镜像配置 -->
    <repositories>
        <repository>
            <id>aliyun-repos</id>
            <name>Alibaba Repository</name>
            <url>https://maven.aliyun.com/repository/public</url>
        </repository>
    </repositories>
</project>
