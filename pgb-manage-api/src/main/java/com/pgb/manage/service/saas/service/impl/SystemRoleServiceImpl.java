package com.pgb.manage.service.saas.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.pgb.manage.service.saas.domain.SystemRole;
import com.pgb.manage.service.saas.mapper.SystemRoleMapper;
import com.pgb.manage.service.saas.service.SystemRoleService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【system_role】的数据库操作Service实现
* @createDate 2023-11-13 14:34:29
*/
@Service
@RequiredArgsConstructor
public class SystemRoleServiceImpl extends ServiceImpl<SystemRoleMapper, SystemRole> implements SystemRoleService {

}




