package com.pgb.service.db.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.pgb.service.db.PgVipChannelService;
import com.pgb.service.domain.vip.PgVipChannel;
import com.pgb.service.mapper.PgVipChannelMapper;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【pg_vip_channel(订单详情表（含详细地址）)】的数据库操作Service实现
* @createDate 2024-10-21 23:49:47
*/
@Service
public class PgVipChannelServiceImpl extends ServiceImpl<PgVipChannelMapper, PgVipChannel>
    implements PgVipChannelService {

}




