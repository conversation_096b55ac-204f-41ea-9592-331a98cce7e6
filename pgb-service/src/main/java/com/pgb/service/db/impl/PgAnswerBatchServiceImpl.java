package com.pgb.service.db.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.pgb.service.domain.answer.answerBatch.PgAnswerBatch;
import com.pgb.service.db.PgAnswerBatchService;
import com.pgb.service.mapper.PgAnswerBatchMapper;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【pg_answer_batch(批次提交表)】的数据库操作Service实现
* @createDate 2025-01-07 15:32:03
*/
@Service
public class PgAnswerBatchServiceImpl extends ServiceImpl<PgAnswerBatchMapper, PgAnswerBatch>
    implements PgAnswerBatchService{

}




