package com.pgb.service.domain.zc.textbook;

import java.io.Serializable;

import java.util.Date;

import com.pgb.common.core.validate.UpdateGroup;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.ToString;
import lombok.Builder;
import org.springframework.validation.annotation.Validated;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;

/**
* 教材表
* @TableName pg_textbook
*/
@Schema(description = "教材表 DTO")
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode
@ToString
@Validated
@Builder
public class PgZcTextbookDTO implements Serializable {

    @Schema(title = "", type = "string")
    @NotNull(message="[]不能为空", groups = {UpdateGroup.class})
    private Long id;

    @Schema(title = "学制")
    @Size(max= 255)
    private String system;

    @Schema(title = "年级")
    @Size(max= 255)
    private String grade;

    @Schema(title = "册别")
    @Size(max= 255)
    private String volume;

    @Schema(title = "创建时间")
    private Date createTime;

    @Schema(title = "更新时间")
    private Date updateTime;

    @Schema(title = "所含单元")
    private Object units;

    @Schema(title = "排序")
    private Integer sort;

}
