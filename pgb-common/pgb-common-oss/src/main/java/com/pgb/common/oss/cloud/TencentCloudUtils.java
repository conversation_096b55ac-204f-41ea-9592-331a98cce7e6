package com.pgb.common.oss.cloud;

import cn.hutool.core.codec.Base64;
import com.tencentcloudapi.common.Credential;
import com.tencentcloudapi.common.exception.TencentCloudSDKException;
import com.tencentcloudapi.sts.v20180813.StsClient;
import com.tencentcloudapi.sts.v20180813.models.GetFederationTokenRequest;
import com.tencentcloudapi.sts.v20180813.models.GetFederationTokenResponse;
import com.tencentcloudapi.vod.v20180717.VodClient;
import com.tencentcloudapi.vod.v20180717.models.DescribeEventConfigRequest;
import com.tencentcloudapi.vod.v20180717.models.DescribeEventConfigResponse;
import com.tencentcloudapi.vod.v20180717.models.ModifyEventConfigRequest;
import com.tencentcloudapi.vod.v20180717.models.ModifyEventConfigResponse;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;

/**
 * 腾讯云 接口工具
 * 抹平厂商差异
 */
public class TencentCloudUtils {


    /**
     * 获取腾讯云账户sts凭证
     * 注意：这里是腾讯云账户的操作，和 OSS无关
     *
     * @param secretId
     * @param secretKey
     * @param region
     * @param sessionName
     * @return
     */
    public static StsDTO getSts(String secretId, String secretKey, String region, String sessionName) {

        final String policy = """
                {
                  "version":"2.0",
                  "statement":[
                    {
                      "effect":"allow",
                      "action":[
                          "name/cos:PutObject",
                          "name/cos:PostObject",
                          "name/cos:InitiateMultipartUpload",
                          "name/cos:ListMultipartUploads",
                          "name/cos:ListParts",
                          "name/cos:UploadPart",
                          "name/cos:CompleteMultipartUpload"
                        ],
                      "resource":[
                          "*"
                      ]
                    }
                  ]
                }""";

        try {

            long start = System.currentTimeMillis() / 1000L;

            // 实例化一个认证对象，入参需要传入腾讯云账户 SecretId 和 SecretKey，此处还需注意密钥对的保密
            // 代码泄露可能会导致 SecretId 和 SecretKey 泄露，并威胁账号下所有资源的安全性。以下代码示例仅供参考，建议采用更安全的方式来使用密钥，请参见：https://cloud.tencent.com/document/product/1278/85305
            // 密钥可前往官网控制台 https://console.cloud.tencent.com/cam/capi 进行获取
            Credential cred = new Credential(secretId, secretKey);

            // 实例化要请求产品的client对象,clientProfile是可选的
            StsClient client = new StsClient(cred, region);

            // 实例化一个请求对象,每个接口都会对应一个request对象
            GetFederationTokenRequest req = new GetFederationTokenRequest();
            req.setName(sessionName);
            req.setPolicy(policy);
            req.setDurationSeconds(7200L);

            // 返回的resp是一个GetFederationTokenResponse的实例，与请求对象对应
            GetFederationTokenResponse resp = client.GetFederationToken(req);

            // 输出json格式的字符串回包
            return StsDTO.builder()
                    .tmpSecretId(resp.getCredentials().getTmpSecretId())
                    .tmpSecretKey(resp.getCredentials().getTmpSecretKey())
                    .token(resp.getCredentials().getToken())
                    .expiredTime(resp.getExpiredTime())
                    .startTime(start)
                    .build();
        } catch (TencentCloudSDKException e) {
            System.out.println(e.toString());
        }

        return null;
    }

    /**
     * 获取腾讯云 云点播 上传签名
     * 文档：https://cloud.tencent.com/document/product/266/9221#p2
     *
     * @param secretId
     * @param secretKey
     * @param signValidDuration
     * @param random
     * @return
     */
    public static String getVodSign(String secretId, String secretKey, String appId, long signValidDuration, int random, String procedure) {
        String strSign = "";
        String contextStr = "";
        long currentTime = System.currentTimeMillis() / 1000;

        try {
            // 生成原始参数字符串
            long endTime = (currentTime + signValidDuration);
            contextStr += "secretId=" + java.net.URLEncoder.encode(secretId, StandardCharsets.UTF_8);
            contextStr += "&currentTimeStamp=" + currentTime;
            contextStr += "&expireTime=" + endTime;
            contextStr += "&random=" + random;
            if (procedure != null && !procedure.isEmpty()) {
                contextStr += "&procedure=" + procedure;
            }
            contextStr += "&vodSubAppId=" + appId;

            Mac mac = Mac.getInstance("HmacSHA1");
            SecretKeySpec secretKeySpec = new SecretKeySpec(secretKey.getBytes(StandardCharsets.UTF_8), mac.getAlgorithm());
            mac.init(secretKeySpec);

            byte[] hash = mac.doFinal(contextStr.getBytes(StandardCharsets.UTF_8));
            byte[] sigBuf = byteMerger(hash, contextStr.getBytes(StandardCharsets.UTF_8));
            strSign = Base64.encode(sigBuf);
            strSign = strSign.replace(" ", "").replace("\n", "").replace("\r", "");
        } catch (NoSuchAlgorithmException | InvalidKeyException ex) {
            throw new RuntimeException(ex);
        }
        return strSign;
    }

    private static byte[] byteMerger(byte[] byte1, byte[] byte2) {
        byte[] byte3 = new byte[byte1.length + byte2.length];
        System.arraycopy(byte1, 0, byte3, 0, byte1.length);
        System.arraycopy(byte2, 0, byte3, byte1.length, byte2.length);
        return byte3;
    }

    /**
     * 获取当前腾讯云VOD回调 URL
     * https://cloud.tencent.com/document/product/266/55296
     * @param secretId
     * @param secretKey
     * @param appId
     * @return
     */
    public static String getVodNotificationUrl(String secretId, String secretKey, String appId) {

        Credential cred = new Credential(secretId, secretKey);
        VodClient client = new VodClient(cred, "");

        // 实例化一个请求对象,每个接口都会对应一个request对象
        DescribeEventConfigRequest req = new DescribeEventConfigRequest();
        req.setSubAppId(Long.valueOf(appId));

        // 返回的resp是一个DescribeEventConfigResponse的实例，与请求对象对应
        DescribeEventConfigResponse resp;
        try {
            resp = client.DescribeEventConfig(req);
        } catch (TencentCloudSDKException e) {
            throw new RuntimeException(e);
        }

        // 输出json格式的字符串回包
        return resp.getNotificationUrl();
    }

    /**
     * 设置腾讯云VOD回调 URL
     * https://cloud.tencent.com/document/product/266/55244
     * @param secretId
     * @param secretKey
     * @param appId
     * @param url
     * @return
     */
    public static String setVodNotificationUrl(String secretId, String secretKey, String appId, String url) {

        Credential cred = new Credential(secretId, secretKey);
        VodClient client = new VodClient(cred, "");

        ModifyEventConfigRequest req = new ModifyEventConfigRequest();
        req.setMode("PUSH");
        req.setNotificationUrl(url);
        req.setUploadMediaCompleteEventSwitch("ON");
        req.setDeleteMediaCompleteEventSwitch("ON");
        req.setSubAppId(Long.valueOf(appId));

        try {
            ModifyEventConfigResponse resp = client.ModifyEventConfig(req);
            return resp.getRequestId();
        } catch (TencentCloudSDKException e) {
            throw new RuntimeException(e);
        }
    }

}
