package com.pgb.xcx.controller.homework;

import cn.dev33.satoken.annotation.SaCheckLogin;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.pgb.common.core.global.BaseResult;
import com.pgb.common.core.global.GlobalCode;
import com.pgb.service.db.PgAnswerService;
import com.pgb.service.db.PgClassesService;
import com.pgb.service.db.PgHomeworkService;
import com.pgb.service.db.PgStudentService;
import com.pgb.service.domain.answer.PgAnswer;
import com.pgb.service.domain.classes.PgClasses;
import com.pgb.service.domain.homework.PgHomework;
import com.pgb.service.domain.homework.statistic.LevelNumInfo;
import com.pgb.service.domain.homework.statistic.StudentInfo;
import com.pgb.service.domain.question.zwEssay.ZwEssayQuestion;
import com.pgb.service.domain.student.PgStudent;
import com.pgb.service.domain.student.PgStudentVO;
import com.pgb.service.enums.CorrectStatusEnum;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.util.*;

/**
 * <AUTHOR>
 * Created by 2025/3/12 14:54
 */
@Tag(name = "用户端/作业/统计")
@RestController("UserHomeworkStatisticController")
@Transactional(rollbackFor = Exception.class)
@RequestMapping("/user/homework/statistic")
@RequiredArgsConstructor
@Slf4j
public class StatisticController {

    private final PgHomeworkService pgHomeworkService;

    private final PgStudentService pgStudentService;

    private final PgAnswerService pgAnswerService;

    @Operation(summary = "作业成绩统计表", description = "返回数据占比饼状图")
    @GetMapping("scoreChart/{homeworkId}")
    @SaCheckLogin
    public BaseResult<List<LevelNumInfo>> scoreChart(@PathVariable Long homeworkId) {

        PgHomework homework = pgHomeworkService.getById(homeworkId);

        if (ObjectUtil.isNull(homework)) {
            return BaseResult.error(GlobalCode.Item_Null, "作业不存在");
        }

        List<Long> studentIds = pgStudentService.list(new LambdaQueryWrapper<PgStudent>()
                        .eq(PgStudent::getClassId, homework.getClassId()))
                .stream()
                .map(PgStudent::getId)
                .toList();

        if (studentIds.isEmpty()) {
            return BaseResult.success(new ArrayList<>());
        }

        List<LevelNumInfo> levelNumInfos = pgAnswerService.LevelPercentage(homeworkId, studentIds);

        return BaseResult.success(levelNumInfos);
    }

    @Data
    public static class LevelDetail {

        @Schema(title = "学生名单")
        private List<StudentInfo> students;
    }

    // 按等级筛选
    public record StudentInfoByLevel(Integer level) {
    }

    @Operation(summary = "作业成绩统计数据")
    @PostMapping("scoreData/{homeworkId}")
    @SaCheckLogin
    public BaseResult<LevelDetail> scoreData(@PathVariable Long homeworkId, @RequestBody StudentInfoByLevel form) {

        PgHomework homework = pgHomeworkService.getById(homeworkId);

        if (ObjectUtil.isNull(homework)) {
            return BaseResult.error(GlobalCode.Item_Null, "作业不存在");
        }

        List<Long> studentIds = pgStudentService.list(new LambdaQueryWrapper<PgStudent>()
                        .eq(PgStudent::getClassId, homework.getClassId())
                )
                .stream()
                .map(PgStudent::getId)
                .toList();

        if (studentIds.isEmpty()) {
            return BaseResult.success(new LevelDetail());
        }

        List<PgAnswer> answers = pgAnswerService.list(new LambdaQueryWrapper<PgAnswer>()
                .eq(PgAnswer::getHomeworkId, homeworkId)
                .in(PgAnswer::getStudentId, studentIds)
                .eq(PgAnswer::getDeleted, false)
                .ne(PgAnswer::getStatus, CorrectStatusEnum.Uploaded)
        );

        LevelDetail detail = new LevelDetail();

        List<StudentInfo> students = new ArrayList<>();

        for (PgAnswer answer : answers) {

            ZwEssayQuestion result = JSONUtil.toBean(JSONUtil.toJsonStr(answer.getCorrectResult()), ZwEssayQuestion.class);

            // 评分等级
            Integer scoreLevel = result.getScoreLevel();

            if (scoreLevel.equals(form.level())) {

                StudentInfo studentInfo = new StudentInfo();

                // 获取学生信息
                PgStudent student = pgStudentService.getById(answer.getStudentId());
                if (ObjectUtil.isNotNull(student)) {
                    studentInfo.setName(student.getName());
                    studentInfo.setStudentNo(student.getStudentNo());
                }
                studentInfo.setScore(result.getUserScore());
                studentInfo.setAnswerId(answer.getId());

                students.add(studentInfo);

            }
            // 按成绩降序排序
            students.sort(Comparator.comparing(StudentInfo::getScore).reversed());

            detail.setStudents(students);

        }

        return BaseResult.success(detail);
    }

    @Data
    public static class ScoreLevelNum {

        @Schema(title = "优秀")
        private Integer excellent = 0;

        @Schema(title = "良好")
        private Integer good = 0;

        @Schema(title = "中等")
        private Integer middle = 0;

        @Schema(title = "较差")
        private Integer poor = 0;
    }

    @Operation(summary = "作业成绩各等级数量")
    @GetMapping("scoreLevelNum/{homeworkId}")
    @SaCheckLogin
    public BaseResult<ScoreLevelNum> num(@PathVariable Long homeworkId) {

        PgHomework homework = pgHomeworkService.getById(homeworkId);

        if (ObjectUtil.isNull(homework)) {
            return BaseResult.error(GlobalCode.Item_Null, "作业不存在");
        }

        List<Long> studentIds = pgStudentService.list(new LambdaQueryWrapper<PgStudent>()
                        .eq(PgStudent::getClassId, homework.getClassId())
                )
                .stream()
                .map(PgStudent::getId)
                .toList();

        ScoreLevelNum scoreLevelNum = new ScoreLevelNum();

        if (studentIds.isEmpty()) {
            return BaseResult.success(scoreLevelNum);
        }

        List<PgAnswer> answers = pgAnswerService.list(new LambdaQueryWrapper<PgAnswer>()
                .eq(PgAnswer::getHomeworkId, homeworkId)
                .in(PgAnswer::getStudentId, studentIds)
                .eq(PgAnswer::getDeleted, false)
                .ne(PgAnswer::getStatus, CorrectStatusEnum.Uploaded)
        );


        for (PgAnswer answer : answers) {

            ZwEssayQuestion result = JSONUtil.toBean(JSONUtil.toJsonStr(answer.getCorrectResult()), ZwEssayQuestion.class);
            if (ObjectUtil.isNotNull(result.getScoreLevel())) {
                switch (result.getScoreLevel()) {
                    case 1:
                        scoreLevelNum.setExcellent(scoreLevelNum.getExcellent() + 1);
                        break;
                    case 2:
                        scoreLevelNum.setGood(scoreLevelNum.getGood() + 1);
                        break;
                    case 3:
                        scoreLevelNum.setMiddle(scoreLevelNum.getMiddle() + 1);
                        break;
                    case 4:
                        scoreLevelNum.setPoor(scoreLevelNum.getPoor() + 1);
                        break;
                }
            }
        }
        return BaseResult.success(scoreLevelNum);
    }

    @Data
    @Schema(title = "作业统计信息")
    public static class HomeworkStatistic {

        @Schema(title = "状态")
        private String name;

        @Schema(title = "数量")
        private Integer value = 0;
    }

    @Operation(summary = "作业提交数据统计")
    @GetMapping("submitStatistic/{homeworkId}")
    @SaCheckLogin
    public BaseResult<List<HomeworkStatistic>> submitStatistic(@PathVariable Long homeworkId) {

        PgHomework homework = pgHomeworkService.getById(homeworkId);

        if (ObjectUtil.isNull(homework)) {
            return BaseResult.error(GlobalCode.Item_Null, "作业不存在");
        }

        List<Long> studentIds = pgStudentService.list(new LambdaQueryWrapper<PgStudent>()
                        .eq(PgStudent::getClassId, homework.getClassId())
                )
                .stream()
                .map(PgStudent::getId)
                .toList();

        if (studentIds.isEmpty()) {
            return BaseResult.success(new ArrayList<>());
        }

        List<PgAnswer> answers = pgAnswerService.list(new LambdaQueryWrapper<PgAnswer>()
                .select(PgAnswer::getId, PgAnswer::getStatus, PgAnswer::getStudentId)
                .eq(PgAnswer::getDeleted, false)
                .eq(PgAnswer::getHomeworkId, homework.getId())
                .in(PgAnswer::getStudentId, studentIds)
        );

        answers = CollUtil.distinct(answers, PgAnswer::getStudentId, false);
        List<Long> submitStudentIds = answers.stream().map(PgAnswer::getStudentId).toList();

        List<HomeworkStatistic> list = new ArrayList<>();

        // 总人数
        HomeworkStatistic totalStatistic = new HomeworkStatistic();
        totalStatistic.setName("总人数");
        totalStatistic.setValue(studentIds.size());
        list.add(totalStatistic);

        // 未提交
        HomeworkStatistic unsubmittedStatistic = new HomeworkStatistic();
        unsubmittedStatistic.setName("未提交");
        unsubmittedStatistic.setValue(
                (int) studentIds.stream().filter(id -> !submitStudentIds.contains(id)).count()
        );
        list.add(unsubmittedStatistic);

        // 批改中
        HomeworkStatistic correctingStatistic = new HomeworkStatistic();
        correctingStatistic.setName("批改中");
        correctingStatistic.setValue(
                (int) answers.stream().filter(answer -> answer.getStatus().equals(CorrectStatusEnum.Uploaded)).count()
        );
        list.add(correctingStatistic);

        // 已批改
        HomeworkStatistic correctedStatistic = new HomeworkStatistic();
        correctedStatistic.setName("已批改");
        correctedStatistic.setValue(
                (int) answers.stream().filter(answer -> !answer.getStatus().equals(CorrectStatusEnum.Uploaded)).count()
        );
        list.add(correctedStatistic);

        // 已审核
        HomeworkStatistic checkedStatistic = new HomeworkStatistic();
        checkedStatistic.setName("已审核");
        checkedStatistic.setValue(
                (int) answers.stream().filter(answer -> answer.getStatus().equals(CorrectStatusEnum.Checked)).count()
        );
        list.add(checkedStatistic);

        return BaseResult.success(list);

    }


}
