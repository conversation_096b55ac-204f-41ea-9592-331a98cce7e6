package com.pgb.xcx.controller.system;

import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import com.pgb.common.core.global.BaseResult;
import com.pgb.common.core.global.GlobalCode;
import com.pgb.common.core.global.GlobalConstants;
import com.pgb.common.redis.RedisUtils;
import com.pgb.common.satoken.captcha.CaptchaImgUtils;
import com.pgb.common.satoken.captcha.domain.CaptchaSliderVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.constraints.NotBlank;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.Duration;
import java.util.Date;

@Tag(name = "系统管理/校验/滑动验证码")
@RestController("SystemCaptchaSliderController")
@Transactional(rollbackFor = Exception.class)
@RequestMapping("/system/captcha/slider")
@RequiredArgsConstructor
@Slf4j
public class SliderController {

    @Operation(summary = "获取验证码")
    @GetMapping("/captcha")
    public BaseResult<CaptchaSliderVO> captcha() {
        // 生成图片验证码
        CaptchaSliderVO captchaSlider = CaptchaImgUtils.getCaptchaImg();

        // 保存坐标信息
        String redisKey = GlobalConstants.CAPTCHA_CODE_KEY + captchaSlider.getVerifyKey();

        // 设置有效期，5分钟
        RedisUtils.setCacheObject(redisKey, captchaSlider, Duration.ofMinutes(5));

        return BaseResult.success(captchaSlider);
    }

    @Operation(summary = "校验验证码")
    @GetMapping("/verify")
    public BaseResult<Boolean> verify(
            @Parameter(name = "唯一凭证") @NotBlank String verifyKey,
            @Parameter(name = "x 坐标") @NotBlank String x,
            @Parameter(name = "y 坐标") @NotBlank String y) {

        // 校验参数
        if (!NumberUtil.isNumber(x) || !NumberUtil.isNumber(y)) {
            return BaseResult.code(GlobalCode.Param_Wrong);
        }

        String redisKey = GlobalConstants.CAPTCHA_CODE_KEY + verifyKey;

        CaptchaSliderVO captchaSliderVO = RedisUtils.getCacheObject(redisKey);

        if (ObjectUtil.isNull(captchaSliderVO)) {
            return BaseResult.success("验证码不存在", false);
        }

        log.info(captchaSliderVO.getPoint().toString());

        // 校验规则，误差在 15 像素以内
        if (captchaSliderVO.getPoint().getX() >= Float.parseFloat(x) - 15 && captchaSliderVO.getPoint().getX() <= Float.parseFloat(x) + 15) {
            // 校验成功
            captchaSliderVO.setIsVerify(true);
            captchaSliderVO.setVerifyTime(new Date());
            RedisUtils.setCacheObject(redisKey, captchaSliderVO, true);

            return BaseResult.success("校验成功", true);
        }

        // 删除验证码
        RedisUtils.deleteObject(redisKey);

        return BaseResult.success("校验失败", false);
    }
}
