package com.pgb.service.domain.zc.textbook;

import java.io.Serializable;

import java.util.Date;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.ToString;
import lombok.Builder;

/**
* 教材表
* @TableName pg_textbook
*/
@Schema(description = "教材表 VO")
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode
@ToString
@Builder
public class PgZcTextbookVO implements Serializable {

    @Schema(title = "", type = "string")
    private Long id;

    @Schema(title = "学制")
    private String system;

    @Schema(title = "年级")
    private String grade;

    @Schema(title = "册别")
    private String volume;

    @Schema(title = "创建时间")
    private Date createTime;

    @Schema(title = "更新时间")
    private Date updateTime;

    @Schema(title = "所含单元")
    private Object units;

    @Schema(title = "排序")
    private Integer sort;

}
