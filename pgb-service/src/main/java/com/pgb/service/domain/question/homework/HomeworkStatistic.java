package com.pgb.service.domain.question.homework;

import cn.hutool.core.util.ObjectUtil;
import com.pgb.service.domain.question.PgQuestion;
import com.pgb.service.domain.question.zwEssay.ZwEssayQuestion;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
@Schema(title = "作业信息")
public class HomeworkStatistic {
    @Schema(title = "对应作文学生名字")
    private List<String> nameList;

    @Schema(title = "全班作文")
    private List<ZwEssayQuestion> questionList;

    @Schema(title = "作业信息")
    private PgQuestion require;

    @Schema(title = "token数")
    private Long aiTokens;

    @Schema(title = "具体内容")
    private List<HomeworkStatisticSection> sectionList;

    /**
     * 增加 脑力值
     * @param tokens
     */
    public void addTokens(Long tokens){
        if (ObjectUtil.isNull(this.aiTokens)) {
            this.aiTokens = 0L;
        }

        this.aiTokens += tokens;
    }

    public void addTokens(int tokens){
        addTokens((long) tokens);
    }
}
