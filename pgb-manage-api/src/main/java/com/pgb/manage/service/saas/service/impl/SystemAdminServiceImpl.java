package com.pgb.manage.service.saas.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.pgb.manage.service.saas.domain.SystemAdmin;
import com.pgb.manage.service.saas.mapper.SystemAdminMapper;
import com.pgb.manage.service.saas.service.SystemAdminService;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【system_admin(系统用户)】的数据库操作Service实现
* @createDate 2023-11-12 17:40:32
*/
@Service
public class SystemAdminServiceImpl extends ServiceImpl<SystemAdminMapper, SystemAdmin>
    implements SystemAdminService {

}




