package com.pgb.service.db.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.pgb.service.domain.answer.model.PgAnswerModel;
import com.pgb.service.db.PgAnswerModelService;
import com.pgb.service.mapper.PgAnswerModelMapper;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【pg_answer_model(范文表)】的数据库操作Service实现
* @createDate 2025-03-17 17:23:38
*/
@Service
public class PgAnswerModelServiceImpl extends ServiceImpl<PgAnswerModelMapper, PgAnswerModel>
    implements PgAnswerModelService{

}




