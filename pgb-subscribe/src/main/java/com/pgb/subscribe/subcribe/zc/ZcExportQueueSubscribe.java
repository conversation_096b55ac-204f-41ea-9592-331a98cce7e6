package com.pgb.subscribe.subcribe.zc;

import cn.binarywang.wx.miniapp.api.WxMaService;
import cn.binarywang.wx.miniapp.bean.WxMaSubscribeMessage;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.compress.ZipWriter;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.TimeInterval;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.ReUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.pgb.common.core.global.exception.BaseException;
import com.pgb.common.core.utils.EnvUtils;
import com.pgb.common.oss.service.OssService;
import com.pgb.common.redis.QueueUtils;
import com.pgb.service.db.PgExportRecordService;
import com.pgb.service.db.PgUsersService;
import com.pgb.service.db.PgZcAnswerService;
import com.pgb.service.domain.GlobQueueConstants;
import com.pgb.service.domain.export.PgExportRecord;
import com.pgb.service.domain.user.PgUsers;
import com.pgb.service.domain.zc.answer.PgZcAnswer;
import com.pgb.service.domain.zc.question.ZcQuestion;
import com.pgb.service.enums.ExportStatusEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.error.WxErrorException;
import org.redisson.api.RBlockingQueue;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;

import java.io.File;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * <AUTHOR>
 * Created by 2025/4/24 17:31
 */
@Component("ZcExportQueueSubscribe")
@Slf4j
@RequiredArgsConstructor
public class ZcExportQueueSubscribe implements CommandLineRunner {

    private final PgExportRecordService pgExportRecordService;

    private final PgZcAnswerService pgZcAnswerService;

    private final PgUsersService pgUsersService;

    private final WxMaService wxMaService;

    private final OssService ossService;

    @Override
    public void run(String... args) throws Exception {

//        if (EnvUtils.isProd() || EnvUtils.isDev()) {
//
//            for (int i = 0; i < 5; i++) {
//                // 导出队列
//                reExport();
//            }
//
//            log.info("监听批量导出字词批改报告队列 --> 启动");
//
//            // 立即执行一次
//            queryToExport();
//        }
    }

    private void reExport() {

        //获取导出队列
        RBlockingQueue<Long> queue = QueueUtils.getClient().getBlockingQueue(GlobQueueConstants.PGB_XCX_ZC_BATCH_EXPORT_WORD_QUEUE.name());

        // 全部异步监听导出word
        CompletableFuture<Void> f = CompletableFuture.runAsync(() -> {

            try {
                // 阻塞获取导出任务
                Long recordId = queue.take();
                log.info("【接收批量导出字词批改报告队列-监听信息】：{}", recordId);

                // 执行导出
                doExport(recordId);

            } catch (InterruptedException e) {
                e.printStackTrace();
            }
        });

        f.whenComplete((v, e) -> {
            if (e != null) {
                // 如果是 RedissonShutdownException 则直接跳过
                if (e.getCause().getMessage().contains("Redisson is shutdown")) {
                    log.error("导出字词批改报告队列监听异常 --> redisson 已中断！");
                    return;
                }
                log.error("导出字词批改报告队列监听异常 -->  异常", e);
            }

            this.reExport();
        });

    }

    private void doExport(Long recordId) {

        TimeInterval timer = DateUtil.timer();

        // 拿任务
        PgExportRecord record = pgExportRecordService.getById(recordId);

        // 任务有效性判断
        if (ObjectUtil.isNull(record) || record.getStatus().equals(ExportStatusEnum.Completed)) {
            log.info("批量导出字词批改报告任务跳过：{}", recordId);
            return;
        }
        // 构建进度数据
        log.info("开始批量导出字词批改报告任务：{}", recordId);

        List<Long> answerIds = StrUtil.split(record.getAnswerIds(), ";").stream().map(Long::valueOf).toList();

        // 导出zip
        String url = zipUrl(answerIds, record);

        // 更新上传状态
        record.setStatus(ExportStatusEnum.Completed);
        record.setZipUrl(url);
        record.setExportTime(new Date());
        pgExportRecordService.updateById(record);

        // 发送导出结果通知
        sendWxMsg(record.getUserId());

        log.info("【导出字词批改报告队列】结束：{}，耗时：{}",
                record.getId(),
                timer.intervalPretty()
        );
    }

    private String zipUrl(List<Long> answerIds, PgExportRecord record) {

        // 获取压缩文件
        File zipFile = FileUtil.createTempFile(".zip", false);
        // 初始化文件列表
        List<File> tempFileList = new ArrayList<>();

        try {

            ZipWriter zipWriter = ZipWriter.of(zipFile, StandardCharsets.UTF_8);

            for (Long answerId : answerIds) {

                // 获取字词作业
                PgZcAnswer zcAnswer = pgZcAnswerService.getById(answerId);

                // 如果是删除状态，跳过
                if (ObjectUtil.isNull(zcAnswer) || ObjectUtil.defaultIfNull(zcAnswer.getDeleted(), false)) {
                    continue;
                }
                String fileName;

                if (StrUtil.isNotBlank(zcAnswer.getName())) {
                    String name = ReUtil.delAll("[^\\u4e00-\\u9fa5a-zA-Z\\d_《》]", zcAnswer.getName());
                    fileName = answerIds.indexOf(answerId) + 1 + "_" + StrUtil.subWithLength(name, 0, 10) + "_" + DateUtil.format(new Date(), "MM-dd") + "_字词作业批改报告";
                } else {
                    fileName = answerIds.indexOf(answerId) + 1 + "_" + DateUtil.format(new Date(), "MM-dd") + "_字词作业批改报告";
                }

                // 构建pdf
                try {
                    File zcExportWord = pgExportRecordService.getZcExportPdf(zcAnswer, fileName);

                    // 添加到临时文件中
                    fileName = fileName + "." + FileUtil.getSuffix(zcExportWord);
                    zipWriter.add(fileName, FileUtil.getInputStream(zcExportWord));

                    tempFileList.add(zcExportWord);
                } catch (Exception e) {
                    log.error("导出字词批改报告异常：{}，异常原因：{}", answerId, e.getMessage());
                }

            }
            zipWriter.close();

            // 上传压缩文件
            String path = StrUtil.format("zc/zip/{}/{}.zip", record.getUserId(), record.getMd5());

            // 上传oss
            return ossService.putFile(path, zipFile);

        } catch (Exception e) {
            log.error("导出字词批改报告压缩包异常:{}", e.getMessage());
            throw new BaseException(e.getMessage());
        } finally {
            // 删除临时压缩文件
            FileUtil.del(zipFile);
            // 删除临时文件
            tempFileList.forEach(FileUtil::del);
        }

    }

    private void sendWxMsg(Long userId) {

        // 发送通知
        PgUsers users = pgUsersService.getById(userId);

        WxMaSubscribeMessage message = new WxMaSubscribeMessage();
        message.setToUser(users.getWxOpenId());
        message.setTemplateId("UsNIR66iF_rjs593zh4uPwFGspXNqtU1Lws97gefIRw");
        // TODO
        message.setPage("/pages/my/export/index");
        message.setData(ListUtil.toList(
                new WxMaSubscribeMessage.MsgData("phrase1", "已完成"),
                new WxMaSubscribeMessage.MsgData("thing2", DateUtil.format(new Date(), "HH:mm"))
        ));
        if (EnvUtils.isDev()) {
            message.setMiniprogramState("developer");
        }

        try {
            wxMaService.getMsgService().sendSubscribeMsg(message);
        } catch (WxErrorException e) {
            log.error("小程序模板发送失败：{}", e.getError());
        }
    }

    private void queryToExport() {

        // 7分钟之前的
        List<PgExportRecord> list = pgExportRecordService.list(new LambdaQueryWrapper<PgExportRecord>()
                .eq(PgExportRecord::getType, 1)
                .eq(PgExportRecord::getStatus, ExportStatusEnum.Queuing)
        );

        log.info("【待导出扫描】待导出字词批改报告压缩包数量:{}个", list.size());

        list.forEach(record -> {
            // 加入导出记录
            if (!QueueUtils.containsQueueObject(GlobQueueConstants.PGB_XCX_ZC_BATCH_EXPORT_WORD_QUEUE.name(), record.getId())) {
                QueueUtils.addQueueObject(GlobQueueConstants.PGB_XCX_ZC_BATCH_EXPORT_WORD_QUEUE.name(), record.getId());
            } else {
                log.info("当前任务已存在导出队列中：{}，跳过", record.getId());
            }
        });

    }
}
