package com.pgb.service.db.impl;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.digest.DigestUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.pgb.common.core.global.BaseResult;
import com.pgb.common.core.global.GlobalCode;
import com.pgb.common.core.global.exception.BaseException;
import com.pgb.common.redis.QueueUtils;
import com.pgb.common.redis.RedisUtils;
import com.pgb.service.db.*;
import com.pgb.service.domain.GlobalXcxConstants;
import com.pgb.service.domain.answer.PgAnswer;
import com.pgb.service.domain.answer.model.PgAnswerModel;
import com.pgb.service.domain.common.image.FilePaperImg;
import com.pgb.service.domain.homework.PgHomework;
import com.pgb.service.domain.homework.statistic.LevelNumInfo;
import com.pgb.service.domain.question.PgQuestion;
import com.pgb.service.domain.question.zwEssay.ZwEssayQuestion;
import com.pgb.service.domain.question.zwEssay.ZwEssayTypeEnum;
import com.pgb.service.domain.question.zwEssay.ZwRequire;
import com.pgb.service.domain.student.PgStudent;
import com.pgb.service.domain.userConfig.CorrectConfigDTO;
import com.pgb.service.domain.userConfig.ExportConfigDTO;
import com.pgb.service.domain.user.PgUsers;
import com.pgb.service.domain.common.TodayNum;
import com.pgb.service.enums.CorrectStatusEnum;
import com.pgb.service.domain.GlobQueueConstants;
import com.pgb.service.enums.SubjectEnum;
import com.pgb.service.mapper.PgAnswerMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【pg_answer】的数据库操作Service实现
 * @createDate 2024-06-19 13:40:14
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class PgAnswerServiceImpl extends ServiceImpl<PgAnswerMapper, PgAnswer>
        implements PgAnswerService {

    private final PgUsersService pgUsersService;

    private final PgUserConfigService pgUserConfigService;

    private final PgStudentService pgStudentService;

    private final PgHomeworkService pgHomeworkService;

    private final PgAnswerBatchService pgAnswerBatchService;

    private final PgAnswerModelService pgAnswerModelService;

    private final PgQuestionService pgQuestionService;

    private final PgAnswerCostService pgAnswerCostService;

    @Override
    public void reCorrectByHomeworkId(Long homeworkId, boolean isCost) {
        List<PgAnswer> list = list(new LambdaQueryWrapper<PgAnswer>()
                .eq(PgAnswer::getHomeworkId, homeworkId)
                .eq(PgAnswer::getDeleted, false)
        );

        log.info("开始重批：{}，作文份数：{}", homeworkId, list.size());

        for (PgAnswer answer : list) {
            reCorrect(answer, isCost);
        }
    }

    @Override
    public PgAnswer reCorrect(PgAnswer answer, boolean isCost) {

        Long userId = answer.getUserId();

        // 获取原批改标准
        ZwEssayQuestion oldEssay = JSONUtil.toBean(answer.getAnswer().toString(), ZwEssayQuestion.class);

        // 初始化批改所需参数
        ZwEssayQuestion essay = new ZwEssayQuestion();

        // 初始化批改设置
        ZwRequire require = new ZwRequire();

        // 拿最新的配置
        CorrectConfigDTO correctConfig = pgUserConfigService.getCorrectConfig(userId);
        require.setCorrectConfig(correctConfig);

        PgQuestion question;

        // 关联作业
        if (ObjectUtil.isNotNull(answer.getHomeworkId())) {
            PgHomework pgHomework = pgHomeworkService.getById(answer.getHomeworkId());
            question = JSONUtil.toBean(JSONUtil.toJsonStr(pgHomework.getQuestionInfo()), PgQuestion.class);
            // 复制批改标准
            BeanUtil.copyProperties(question, require);
            // 分数
            essay.setScore(
                    Double.valueOf(
                            ObjectUtil.defaultIfNull(question.getScore(), 30)
                    )
            );
        }
        // 关联题目
        else if (ObjectUtil.isNotNull(answer.getQuestionId())) {
            question = pgQuestionService.getById(answer.getQuestionId());
            if (ObjectUtil.isNull(question)) {
                throw new BaseException("当前关联的作文题目已被删除，无法重批");
            }
            // 复制批改标准
            BeanUtil.copyProperties(question, require);
            // 设置分数
            essay.setScore(
                    Double.valueOf(
                            ObjectUtil.defaultIfNull(question.getScore(), 30)
                    )
            );
        }
        // 默认批改 则按分数的配置来
        else {
            // 评分标准
            essay.setScore(correctConfig.getScoreStandard());
        }

        // 批改标准内容
        essay.setRequire(require);
        // 上传的图片
        essay.setUserImgAnswerList(oldEssay.getUserImgAnswerList());
        // 科目类型
        essay.setZwType(oldEssay.getZwType());
        // 姓名
        essay.setName(oldEssay.getName());

        // 更新数据
        answer.setAnswer(essay);
        answer.setStatus(CorrectStatusEnum.Uploaded);

        updateById(answer);

        // 新增消耗情况
        if (isCost) {
            pgAnswerCostService.addAnswerCost(userId, answer.getId(),0);
        }

        // 加入批改队列
        if (!QueueUtils.containsQueueObject(GlobQueueConstants.PGB_XCX_CORRECT_QUEUE_VIP.name(), answer.getId()) && !QueueUtils.containsQueueObject(GlobQueueConstants.PGB_XCX_CORRECT_QUEUE.name(), answer.getId())) {
            QueueUtils.addQueueObject(GlobQueueConstants.PGB_XCX_CORRECT_QUEUE_VIP.name(), answer.getId());
            log.info("题目已加入批改队列：{}", answer.getId());
        } else {
            log.info("当前题目已存在批改队列中：{}，跳过", answer.getId());
        }

        return answer;
    }

    @Override
    public void deleteAnswer(PgAnswer answer) {

        if (ObjectUtil.isNull(answer)) {
            log.warn("删除作文提交记录失败，记录不存在");
            return;
        }

        // 标记为删除
        answer.setDeleted(true);
        updateById(answer);

        // 判断是否有批次
        if (ObjectUtil.isNotNull(answer.getBatchId())) {

            // 查看该批次下是否还有提交记录
            long count = count(new LambdaQueryWrapper<PgAnswer>()
                    .eq(PgAnswer::getBatchId, answer.getBatchId())
                    .ne(PgAnswer::getDeleted, true)
            );

            // 如果没有 则删除批次
            if (count == 0) {
                pgAnswerBatchService.removeById(answer.getBatchId());
            }
        }

        // 删除范文
        pgAnswerModelService.remove(new LambdaQueryWrapper<PgAnswerModel>()
                .eq(PgAnswerModel::getUserId, answer.getUserId())
                .eq(PgAnswerModel::getAnswerId, answer.getId())
        );
    }

    @Override
    public PgAnswer submitHomeworkZwAnswer(ZwEssayQuestion essayForm, Long userId) {
        // 获取用户批改配置
        CorrectConfigDTO configDTO = pgUserConfigService.getCorrectConfig(userId);

        // 加入到提交列表中
        PgAnswer answer = new PgAnswer();
        answer.setUserId(userId);
        answer.setStatus(CorrectStatusEnum.Uploaded);
        answer.setAiTokens(0L);
        answer.setCreateTime(new Date());
        answer.setDeleted(false);
        answer.setIsArchive(false);

        // 备注名称
        if (StrUtil.isNotBlank(essayForm.getName())) {
            answer.setName(essayForm.getName());
        }

        // 所属关联学生
        if (ObjectUtil.isNotNull(essayForm.getStudentId())) {

            PgStudent student = pgStudentService.getById(essayForm.getStudentId());

            if (ObjectUtil.isNull(student)) {
                throw new BaseException("当前学生不存在");
            }

            answer.setName(student.getName());
            answer.setStudentId(student.getId());
            // 内层名称
            essayForm.setName(student.getName());
        }

        // 所属题目
        PgQuestion question = null;

        // 【关联作业提交】
        if (ObjectUtil.isNotNull(essayForm.getHomeworkId())) {

            PgHomework homework = pgHomeworkService.getById(essayForm.getHomeworkId());

            if (ObjectUtil.isNull(homework)) {
                throw new BaseException("作业不存在");
            }

            answer.setHomeworkId(homework.getId());

            // 所属题目
            if (ObjectUtil.isNotNull(homework.getQuestionInfo())) {
                question = JSONUtil.toBean(JSONUtil.toJsonStr(homework.getQuestionInfo()), PgQuestion.class);

                // 设置作文类型
                if (question.getSubject().equals(SubjectEnum.Chinese)) {
                    essayForm.setZwType(ZwEssayTypeEnum.Chinese);
                } else if (question.getSubject().equals(SubjectEnum.English)) {
                    essayForm.setZwType(ZwEssayTypeEnum.English);
                }
            }
        }

        // 初始化批改设置
        ZwRequire require = new ZwRequire();
        // 批改配置
        require.setCorrectConfig(configDTO);
        // 所属题目
        if (ObjectUtil.isNotNull(question)) {
            // 复制批改标准
            BeanUtil.copyProperties(question, require);
            // 设置分数
            essayForm.setScore(
                    Double.valueOf(
                            ObjectUtil.defaultIfNull(question.getScore(), 30)
                    )
            );
            // 设置题目id
//            answer.setQuestionId(question.getId());
        }
        // 若没有关联题目 则按配置来
        else {
            // 分数标准
            essayForm.setScore(configDTO.getScoreStandard());
        }

        // 赋值批改要求
        essayForm.setRequire(require);

        answer.setAnswer(essayForm);

        // 保存
        save(answer);

        return answer;
    }

    @Override
    public String getImgMd5(List<Long> ids, Long userId) {

        // 获取用户自定义配置
        // 获取导出config
        ExportConfigDTO config = pgUserConfigService.getExportConfig(userId);
        String configStr = StrUtil.str(config, StandardCharsets.UTF_8);

        // 获取批改结果图片
        StringBuilder result = new StringBuilder();
        if (ObjectUtil.isNotEmpty(ids)) {
            List<PgAnswer> pgAnswers = listByIds(ids);
            for (PgAnswer answer : pgAnswers) {
                ZwEssayQuestion correctAnswer = JSONUtil.toBean(answer.getCorrectResult().toString(), ZwEssayQuestion.class);

                result.append(correctAnswer);
            }
        }
        // md5 唯一值
        return DigestUtil.md5Hex(result + configStr);
    }

    @Override
    public Integer queryToCorrect(Boolean isReCorrect, Integer overMinute) {
        // 扫描当前需要批改的题目列表
        // 7分钟之前的
        List<PgAnswer> allList = list(new LambdaQueryWrapper<PgAnswer>()
                .eq(PgAnswer::getStatus, CorrectStatusEnum.Uploaded)
                .lt(PgAnswer::getCreateTime, DateUtil.offsetMinute(new Date(), -overMinute))
        );

        List<PgAnswer> list = new ArrayList<>();

        // 排除掉正在批改的
        for (PgAnswer answer : allList) {
            // 是否批改中
            if (RedisUtils.isExistsObject(GlobalXcxConstants.XCX_ZW_CORRECTING + answer.getId())) {
                log.info("当前题目正在批改中：{}，跳过", answer.getId());
                continue;
            }

            // 是否已存在队列
            if (QueueUtils.containsQueueObject(GlobQueueConstants.PGB_XCX_CORRECT_QUEUE_VIP.name(), answer.getId())
                    || QueueUtils.containsQueueObject(GlobQueueConstants.PGB_XCX_CORRECT_QUEUE.name(), answer.getId())
            ) {
                log.info("当前题目已存在批改队列中：{}，跳过", answer.getId());
                continue;
            }

            list.add(answer);
        }

        log.info("【待批改题目扫描】超过7分钟未批改题目数量:{}个", list.size());

        if (isReCorrect) {
            list.forEach(quesAnswer -> {
                // 加入批改队列
                QueueUtils.addQueueObject(GlobQueueConstants.PGB_XCX_CORRECT_QUEUE_VIP.name(), quesAnswer.getId());
            });
        }

        return list.size();
    }

    @Override
    public Integer queryToCorrect() {
        return queryToCorrect(true, 7);
    }

    @Override
    public List<Integer> levelNum(Long homeworkId, List<Long> studentIds, List<Integer> levelNum) {

        // 获取班级学生的提交记录
        List<PgAnswer> answers = list(new LambdaQueryWrapper<PgAnswer>()
                .eq(PgAnswer::getHomeworkId, homeworkId)
                .ne(PgAnswer::getStatus, CorrectStatusEnum.Uploaded)
                .in(PgAnswer::getStudentId, studentIds)
        );

        answers.forEach(answer -> {

            ZwEssayQuestion result = JSONUtil.toBean(JSONUtil.toJsonStr(answer.getCorrectResult()), ZwEssayQuestion.class);

            // 评级等级
            if (ObjectUtil.isNotNull(result.getScoreLevel())) {

                switch (result.getScoreLevel()) {
                    case 1:
                        levelNum.set(
                                0, levelNum.get(0) + 1
                        );
                        break;
                    case 2:
                        levelNum.set(
                                1, levelNum.get(1) + 1
                        );
                        break;
                    case 3:
                        levelNum.set(
                                2, levelNum.get(2) + 1
                        );
                        break;
                    case 4:
                        levelNum.set(
                                3, levelNum.get(3) + 1
                        );
                        break;
                }
            }
        });

        return levelNum;
    }


    @Override
    public List<LevelNumInfo> LevelPercentage(Long homeworkId, List<Long> studentIds) {

        // 存储评级等级
        List<Integer> levelNum = new ArrayList<>();

        levelNum.add(0);
        levelNum.add(0);
        levelNum.add(0);
        levelNum.add(0);

        // 获取班级学生的提交记录
        List<PgAnswer> answers = list(new LambdaQueryWrapper<PgAnswer>()
                .eq(PgAnswer::getHomeworkId, homeworkId)
                .ne(PgAnswer::getStatus, CorrectStatusEnum.Uploaded)
                .eq(PgAnswer::getDeleted, false)
                .in(PgAnswer::getStudentId, studentIds)
        );

        answers.forEach(answer -> {

            ZwEssayQuestion result = JSONUtil.toBean(JSONUtil.toJsonStr(answer.getCorrectResult()), ZwEssayQuestion.class);

            // 评级等级
            if (ObjectUtil.isNotNull(result.getScoreLevel())) {

                switch (result.getScoreLevel()) {
                    case 1:
                        levelNum.set(
                                0, levelNum.get(0) + 1
                        );
                        break;
                    case 2:
                        levelNum.set(
                                1, levelNum.get(1) + 1
                        );
                        break;
                    case 3:
                        levelNum.set(
                                2, levelNum.get(2) + 1
                        );
                        break;
                    case 4:
                        levelNum.set(
                                3, levelNum.get(3) + 1
                        );
                        break;
                }
            }
        });

        // 计算总数量
        int total = answers.size();

        // 计算每个等级的百分比并创建 LevelNumInfo 对象
        List<LevelNumInfo> levelNumInfos = new ArrayList<>();
        // 计算每个等级的百分比
        for (int i = 0; i < levelNum.size(); i++) {
            double percentage = total == 0 ? 0 : ((levelNum.get(i) * 100.0) / total);
            // 四舍五入
            percentage = NumberUtil.round(percentage, 2).doubleValue();

            LevelNumInfo levelNumInfo = new LevelNumInfo();

            levelNumInfo.setNum(levelNum.get(i));
            levelNumInfo.setPercentage(percentage);

            levelNumInfos.add(levelNumInfo);
        }

        return levelNumInfos;
    }
}





