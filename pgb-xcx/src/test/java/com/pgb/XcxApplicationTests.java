package com.pgb;

import cn.binarywang.wx.miniapp.api.WxMaService;
import cn.binarywang.wx.miniapp.bean.WxMaSubscribeMessage;
import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.TimeInterval;
import cn.hutool.core.img.ImgUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.lang.TypeReference;
import cn.hutool.core.lang.UUID;
import cn.hutool.core.util.*;
import cn.hutool.crypto.digest.DigestUtil;
import cn.hutool.extra.servlet.JakartaServletUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.json.JSONUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.read.listener.PageReadListener;
import com.alipay.api.AlipayApiException;
import com.alipay.api.AlipayClient;
import com.alipay.api.domain.AlipayTradeAppPayModel;
import com.alipay.api.domain.AlipayTradeQueryModel;
import com.alipay.api.request.AlipayTradeAppPayRequest;
import com.alipay.api.request.AlipayTradeQueryRequest;
import com.alipay.api.response.AlipayTradeAppPayResponse;
import com.alipay.api.response.AlipayTradeQueryResponse;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.binarywang.wxpay.bean.profitsharing.request.ProfitSharingUnfreezeV3Request;
import com.github.binarywang.wxpay.bean.profitsharing.result.ProfitSharingUnfreezeV3Result;
import com.github.binarywang.wxpay.bean.result.WxPayOrderQueryV3Result;
import com.github.binarywang.wxpay.exception.WxPayException;
import com.github.binarywang.wxpay.service.WxPayService;
import com.github.houbb.pinyin.util.PinyinHelper;
import com.pgb.common.core.global.BaseResult;
import com.pgb.common.core.utils.EnvUtils;
import com.pgb.common.ocr.OCRService;
import com.pgb.common.oss.service.OssService;
import com.pgb.common.pay.enums.BuyTypeEnum;
import com.pgb.common.pay.enums.OrderStatusEnum;
import com.pgb.common.pay.enums.OrderTypeEnum;
import com.pgb.common.pay.enums.PayTypeEnum;
import com.pgb.common.redis.QueueUtils;
import com.pgb.common.sms.SmsResDTO;
import com.pgb.service.db.*;
import com.pgb.service.domain.GlobQueueConstants;
import com.pgb.service.domain.answer.answerBatch.PgAnswerBatch;
import com.pgb.service.domain.answer.answerCost.PgAnswerCost;
import com.pgb.service.domain.common.image.FilePaperImg;
import com.pgb.service.domain.order.OrderPayParam;
import com.pgb.service.domain.userConfig.CorrectConfigDTO;
import com.pgb.service.domain.zc.text.PgZcText;
import com.pgb.service.domain.zc.textbook.PgZcTextbook;
import com.pgb.service.domain.zc.word.chinese.PgZcChineseWord;
import com.pgb.service.domain.zc.word.english.PgZcEnglishWord;
import com.pgb.service.domain.zc.word.english.common.PgZcEngWordCommon;
import com.pgb.service.enums.*;
import com.pgb.xcx.common.SmsService;
import com.pgb.service.domain.tag.PgTagUser;
import com.pgb.service.domain.userConfig.ExportConfigDTO;
import com.pgb.service.domain.userConfig.PgUserConfig;
import com.pgb.service.custom.CorrectService;
import com.pgb.service.domain.GlobalXcxConstants;
import com.pgb.service.domain.answer.PgAnswer;
import com.pgb.service.domain.export.PgExportRecord;
import com.pgb.service.domain.order.PgOrder;
import com.pgb.service.domain.question.PgQuestion;
import com.pgb.service.domain.question.zwEssay.ZwEssayQuestion;
import com.pgb.service.domain.user.PgUsers;
import com.pgb.service.domain.vip.PgVip;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.error.WxErrorException;
import org.apache.pdfbox.Loader;
import org.apache.pdfbox.multipdf.Splitter;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.poi.openxml4j.exceptions.InvalidFormatException;
import org.apache.poi.xwpf.usermodel.ParagraphAlignment;
import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.apache.poi.xwpf.usermodel.XWPFParagraph;
import org.apache.poi.xwpf.usermodel.XWPFRun;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.geom.Ellipse2D;
import java.awt.image.BufferedImage;
import java.io.*;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.StandardCopyOption;
import java.util.*;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

@SpringBootTest(classes = XcxApplication.class, webEnvironment = SpringBootTest.WebEnvironment.DEFINED_PORT)
@Slf4j
class XcxApplicationTests {

    @Autowired
    private OssService ossService;

    @Autowired
    private PgAnswerService pgAnswerService;

    @Autowired
    private CorrectService correctService;

    @Autowired
    private PgOrderService pgOrderService;

    @Autowired
    private PgVipService pgVipService;

    @Autowired
    private PgUsersService pgUsersService;

    @Autowired
    private PgAnswerCostService pgAnswerCostService;

    @Autowired
    private PgQuestionService pgQuestionService;

    @Autowired
    private PgExportRecordService pgExportRecordService;

    @Autowired
    private WxMaService wxMaService;

    @Autowired
    private PgVipCodeService pgVipCodeService;

    @Autowired
    private PgUserConfigService pgUserConfigService;

    @Autowired
    private PgTagUserService pgTagUserService;

    @Autowired
    private PgAnswerBatchService pgAnswerBatchService;

    @Autowired
    private WxPayService wxPayService;

    @Autowired
    private PgZcEnglishWordService pgEnglishWordService;

    @Autowired
    private PgZcChineseWordService pgZcChineseWordService;

    @Autowired
    private PgZcTextService pgZcTextService;

    @Autowired
    private PgZcTextbookService pgZcTextbookService;

    @Autowired
    private OCRService ocrService;

    @Autowired
    private PgZcEngWordCommonService pgZcEngWordCommonService;

    @Autowired
    private AlipayClient alipayClient;

    @Test
    public void aliPayTest() throws AlipayApiException {
        VipTypeEnum vipType = VipTypeEnum.MONTH;

        // 生成订单
        PgOrder order = pgOrderService.generateOrder(
                vipType,
                PayTypeEnum.ALI_APP,
                BuyTypeEnum.PAY,
                null,
                0L
        );

        // 构造请求参数以调用接口
        AlipayTradeAppPayRequest payRequest = new AlipayTradeAppPayRequest();
        AlipayTradeAppPayModel model = new AlipayTradeAppPayModel();

        // 设置商户订单号
        model.setOutTradeNo(order.getOrderNo());
        // 设置订单总金额
        model.setTotalAmount(
                EnvUtils.isDev() ? "0.01" : NumberUtil.decimalFormatMoney(order.getPayAmount() / 100.0)
        );
        // 设置订单标题
        model.setSubject(order.getDescription());
        // 设置订单绝对超时时间
        model.setTimeExpire(
                DateUtil.format(DateUtil.offsetMinute(order.getCreateTime(), 30), "yyyy-MM-dd'T'HH:mm:ssXXX")
        );
        // 设置公用回传参数
        model.setPassbackParams(order.getId().toString());
        // 设置商户的原始订单号
        model.setMerchantOrderNo(order.getOrderNo());
        // 设置请求参数
        payRequest.setBizModel(model);
        // 回调地址
        payRequest.setNotifyUrl(
                "https://api.pigaibang.com/user/order/aliPay/aliPayNotify"
        );

        try {
            AlipayTradeAppPayResponse response = alipayClient.sdkExecute(payRequest);
            log.info(
                    JSONUtil.toJsonStr(response)
            );
        } catch (AlipayApiException e) {
            throw new RuntimeException(e);
        }
    }


    @Test
    public void getChWordAudio() {


        List<PgZcChineseWord> list = pgZcChineseWordService.list(new LambdaQueryWrapper<PgZcChineseWord>()
                .isNull(PgZcChineseWord::getAudioUrl)
        );

        list.forEach(word -> {

            pgZcChineseWordService.getOrCreateWordInfo(word.getWord());

        });
        System.out.println("处理完成");
    }


    //    @Test
    public void getEnWordAudio() {


        List<PgZcEngWordCommon> list = pgZcEngWordCommonService.list(new LambdaQueryWrapper<PgZcEngWordCommon>()
//                .eq(PgZcEngWordCommon::getId,1943222756610187266L)
        );

        list.forEach(word -> {

            // 获取英式发音的接口
            String ukUrl = StrUtil.format("http://dict.youdao.com/dictvoice?audio={}&type=1", word.getWord());

            // 调用接口
            byte[] result = HttpRequest.get(ukUrl)
                    .timeout(20 * 60 * 1000)
                    .execute()
                    .bodyBytes();
            String key = StrUtil.format(
                    "resource/eng/audio/{}/{}_1.mp3",
                    word.getWord(),
                    word.getWord()

            );
            // 上传云存储
            File tempFile = FileUtil.createTempFile(".mp3", true);

            FileUtil.writeBytes(result, tempFile);

            String ukAudioUrl = ossService.putFile(key, tempFile);
            word.setUkAudioUrl(ukAudioUrl);


            // 获取美式发音
            String usUrl = StrUtil.format("http://dict.youdao.com/dictvoice?audio={}&type=2", word.getWord());
            byte[] result2 = HttpRequest.get(usUrl)
                    .timeout(20 * 60 * 1000)
                    .execute()
                    .bodyBytes();

            String key2 = StrUtil.format(
                    "resource/eng/audio/{}/{}_2.mp3",
                    word.getWord(),
                    word.getWord()

            );
            File tempFile2 = FileUtil.createTempFile(".mp3", true);
            FileUtil.writeBytes(result2, tempFile2);
            String usAudioUrl = ossService.putFile(key2, tempFile2);
            word.setUsAudioUrl(usAudioUrl);

            log.info("单词: {}", word.getWord());
            log.info("英式发音: {}", word.getUkAudioUrl());
            log.info("美式发音: {}", word.getUsAudioUrl());
            pgZcEngWordCommonService.updateById(word);
        });


    }

    @Test
    public void exportEngWord() throws IOException {
        // 读取TXT文件
        File file = new File("C:\\Users\\<USER>\\Desktop\\中考英语词汇表.txt"); // 替换为你的文件路径
        List<String> lines = FileUtil.readLines(file, StandardCharsets.UTF_8);

        // 处理每一行数据
        lines.forEach(line -> {
            if (StrUtil.isNotBlank(line)) {

//                log.info("原始行: {}", line);

                String word = "";
                String phonetic = "";
                String wordClass = "";
                String chinese = "";

                // 1.处理单词和音标部分
                int phoneticStart = line.indexOf('[');
                int phoneticEnd = line.indexOf(']');

                // 如果没有 就跳过
                if (phoneticStart == -1 || phoneticEnd == -1) {
                    return;
                }

                // 提取单词
                word = line.substring(0, phoneticStart).trim();
                // 提取音标
                phonetic = "/" + line.substring(phoneticStart + 1, phoneticEnd).trim() + "/";
                // 剩余部分为词性和释义
                String remaining = line.substring(phoneticEnd + 1).trim();

                // 分割词性和释义
                String[] parts = remaining.split("\\.");

                // 处理词性部分
                List<String> wordClasses = new ArrayList<>();
                for (int i = 0; i < parts.length - 1; i++) {
                    String part = parts[i].trim();
                    // 提取词性(只保留字母部分)
                    String cleaned = part.replaceAll("[^a-zA-Z]", "").trim();
                    if (StrUtil.isNotBlank(cleaned)) {
                        wordClasses.add(cleaned + ".");
                    }
                }
                wordClass = String.join("/", wordClasses);

                // 处理中文释义部分
                StringBuilder chineseStr = new StringBuilder();
                for (int i = 0; i < parts.length; i++) {
                    String part = parts[i].trim();
                    // 提取非字母部分（中文释义）
                    String ch = part.replaceAll("[a-zA-Z]", "").trim();
                    if (StrUtil.isNotBlank(ch)) {
                        chineseStr.append(ch);
                        // 如果不是最后一个部分，添加分隔符
                        if (i != parts.length - 1) {
                            chineseStr.append("/");
                        }
                    }
                }
                chinese = chineseStr.toString();

//                log.info("单词：{}", word);
//                log.info("音标：{}", phonetic);
//                log.info("词性：{}", wordClass);
//                log.info("中文释义：{}", chinese);

                PgZcEngWordCommon wordCommon = pgZcEngWordCommonService.getOne(new LambdaQueryWrapper<PgZcEngWordCommon>()
                        .eq(PgZcEngWordCommon::getWord, word)
                        .last("limit 1")
                );

                if (ObjectUtil.isNull(wordCommon)) {

                    // 创建实体并保存
                    wordCommon = new PgZcEngWordCommon();
                    wordCommon.setWord(word);
                    wordCommon.setPhonetic(phonetic);
                    wordCommon.setWordClass(wordClass);
                    wordCommon.setChinese(chinese);
                    // 保存到数据库
                    pgZcEngWordCommonService.save(wordCommon);
                } else {
                    // 更新
                    wordCommon.setPhonetic(phonetic);
                    wordCommon.setWordClass(wordClass);
                    wordCommon.setChinese(chinese);
                    pgZcEngWordCommonService.updateById(wordCommon);
                }
                log.info("导入单词: {} - 音标：{} - 词性：{} - 释义：{}", word, phonetic, wordClass, chinese);
            }
        });

        log.info("英文单词导入完成，共导入 {} 条记录", lines.size());
    }

    @Test
    public void correct() {

        List<PgAnswer> answers = pgAnswerService.list(new LambdaQueryWrapper<PgAnswer>()
//                        .eq(PgAnswer::getBatchId, 1940019864047841281L)
                        .eq(PgAnswer::getId, 1940324811486392321L)
//                .eq(PgAnswer::getStatus, 0)
        );

        answers.forEach(answer -> {

            log.info("answerId: {}", answer.getId());

            ZwEssayQuestion userAnswer = JSONUtil.toBean(answer.getAnswer().toString(), ZwEssayQuestion.class);

            userAnswer.getUserImgAnswerList().forEach(img -> {
                // 源
                String sourceKey = URLUtil.getPath(img.getImgUrl());

                log.info("sourceKey: {}", sourceKey);

                if (sourceKey.startsWith("/tmp/")) {
                    // 将 temp 替换为 zw
                    String destinationKey = StrUtil.replace(sourceKey, "/tmp/", "/zw/");

                    ossService.renameFile(sourceKey, destinationKey);

                    img.setImgUrl(
                            img.getImgUrl().replaceAll("/tmp/", "/zw/")
                    );
                }
                answer.setAnswer(userAnswer);

            });
        });
        log.info("更新：{}", answers.size());

        pgAnswerService.updateBatchById(answers);

    }


    /**
     * 测试PDF分割
     *
     * @throws IOException
     */
    @Test
    public void testSplitPdf() throws IOException {
        // 获取桌面路径
        Path desktopPath = Path.of(System.getProperty("user.home"), "Desktop");

        // 创建一个临时PDF文件（模拟输入文件）
//        File sourceFile = File.createTempFile("testFullPdf", ".pdf");
        // 这里可以替换为实际存在的PDF文件路径，用于测试
        File sourceFile = new File("C:\\Users\\<USER>\\Desktop\\测试分割1.pdf");

        PDDocument document = Loader.loadPDF(sourceFile);

        int pages = document.getNumberOfPages();
        // 调用splitPdf方法进行分割
        Splitter pdfSplitter = new Splitter();
        pdfSplitter.setSplitAtPage(pages / 2);
        List<PDDocument> splitPDFFiles = pdfSplitter.split(document);

        File part1 = File.createTempFile("part1", ".pdf");
        File part2 = File.createTempFile("part2", ".pdf");

        splitPDFFiles.get(0).save(part1);
        splitPDFFiles.get(1).save(part2);

        List<File> splitFiles = new ArrayList<>();
        splitFiles.add(part1);
        splitFiles.add(part2);
        splitFiles.add(sourceFile);

        // 将生成的文件复制到桌面
        for (int i = 0; i < splitFiles.size(); i++) {
            File srcFile = splitFiles.get(i);
            String destFileName = "part_" + (i + 1) + ".pdf";
            Path destPath = desktopPath.resolve(destFileName);
            Files.copy(srcFile.toPath(), destPath, StandardCopyOption.REPLACE_EXISTING);
        }

        System.out.println("PDF文件已成功生成");
    }


    public static void main(String[] args) {

        String pinyin = PinyinHelper.toPinyin("嗯");
        log.info("pinyin: {}", pinyin);
    }


    @Test
    void removeTrailingSemicolon() {
        // 获取所有课文记录
        List<PgZcText> texts = pgZcTextService.list();

        for (PgZcText text : texts) {
            // 去除 knowWordIds 末尾的分号
            if (StrUtil.isNotBlank(text.getKnowWordIds()) && text.getKnowWordIds().endsWith(";")) {
                text.setKnowWordIds(text.getKnowWordIds().substring(0, text.getKnowWordIds().length() - 1));
            }

            // 去除 newWordIds 末尾的分号
            if (StrUtil.isNotBlank(text.getNewWordIds()) && text.getNewWordIds().endsWith(";")) {
                text.setNewWordIds(text.getNewWordIds().substring(0, text.getNewWordIds().length() - 1));
            }

            // 去除 ciyuIds 末尾的分号
            if (StrUtil.isNotBlank(text.getCiyuIds()) && text.getCiyuIds().endsWith(";")) {
                text.setCiyuIds(text.getCiyuIds().substring(0, text.getCiyuIds().length() - 1));
            }

            // 去除 readAndWriteIds 末尾的分号
            if (StrUtil.isNotBlank(text.getReadAndWriteIds()) && text.getReadAndWriteIds().endsWith(";")) {
                text.setReadAndWriteIds(text.getReadAndWriteIds().substring(0, text.getReadAndWriteIds().length() - 1));
            }

            // 更新数据库中的记录
            pgZcTextService.updateById(text);
        }

        System.out.println("已移除所有字段末尾的分号");
    }


    @Schema(title = "实体映射")
    @Data
    public static class ChineseWordExcelDTO {

        @ExcelProperty("年级")
        private String grade;

        @ExcelProperty("册别")
        private String volume;

        @ExcelProperty("单元")
        private Integer unit;

        @ExcelProperty("排序")
        private Integer sort;

        @ExcelProperty("第几课")
        private String lesson;

        @ExcelProperty("课文名")
        private String name;

        @ExcelProperty("识字")
        private String knowWord;

        @ExcelProperty("写字")
        private String newWord;

        @ExcelProperty("词语")
        private String ciyu;

        @ExcelProperty("读读写写")
        private String readAndWrite;

        @ExcelProperty("课文内容")
        private String content;

        @ExcelProperty("类型")
        private Integer type;

    }

    /**
     * 导入字词内容
     */
    @Test
    void exportChineseWord() {

        File file = new File("C:\\Users\\<USER>\\Desktop\\语文字词素材.xlsx");

        if (ObjectUtil.isNull(file)) {
            System.out.println("文件不存在");
        }

        EasyExcel.read(file, ChineseWordExcelDTO.class, new PageReadListener<ChineseWordExcelDTO>(dataList -> {

            for (ChineseWordExcelDTO data : dataList) {

                // 识字
                if (StrUtil.isNotBlank(data.getKnowWord())) {

                    String[] knowWords = data.getKnowWord().split("、");

                    for (String knowWord : knowWords) {
                        log.info("识字: {}", knowWord);
                        String knowWordPy = PinyinHelper.toPinyin(knowWord);
                        log.info("识字拼音: {}", knowWordPy);

                        if (!pgZcChineseWordService.exists(new LambdaQueryWrapper<PgZcChineseWord>()
                                .eq(PgZcChineseWord::getWord, knowWord)
                                .eq(PgZcChineseWord::getPinyin, knowWordPy))
                        ) {
                            PgZcChineseWord chineseWord = new PgZcChineseWord();
                            chineseWord.setWord(knowWord);
                            chineseWord.setPinyin(knowWordPy);
                            chineseWord.setCreateTime(new Date());
//                            chineseWord.setUpdateTime(new Date());
                            pgZcChineseWordService.save(chineseWord);
                        }
                    }
                }

                // 生字
                if (StrUtil.isNotBlank(data.getNewWord())) {
                    String[] newWords = data.getNewWord().split("、");
                    for (String newWord : newWords) {
                        log.info("写字: {}", newWord);

                        String newWordPy = PinyinHelper.toPinyin(newWord);
                        log.info("写字拼音: {}", newWordPy);

                        if (!pgZcChineseWordService.exists(new LambdaQueryWrapper<PgZcChineseWord>()
                                .eq(PgZcChineseWord::getWord, newWord)
                                .eq(PgZcChineseWord::getPinyin, newWordPy)
                        )) {
                            PgZcChineseWord chineseWord = new PgZcChineseWord();

                            chineseWord.setWord(newWord);
                            chineseWord.setPinyin(newWordPy);
                            chineseWord.setCreateTime(new Date());
//                            chineseWord.setUpdateTime(new Date());
                            pgZcChineseWordService.save(chineseWord);
                        }
                    }
                }

                // 词语
                if (StrUtil.isNotBlank(data.getCiyu())) {

                    String[] ciyus = data.getCiyu().split("、");

                    for (String ciyu : ciyus) {
                        log.info("词语: {}", ciyu);
                        String ciyuPy = PinyinHelper.toPinyin(ciyu);
                        log.info("词语拼音: {}", ciyuPy);

                        if (!pgZcChineseWordService.exists(new LambdaQueryWrapper<PgZcChineseWord>()
                                .eq(PgZcChineseWord::getWord, ciyu)
                                .eq(PgZcChineseWord::getPinyin, ciyuPy)
                        )
                        ) {
                            PgZcChineseWord chineseWord = new PgZcChineseWord();
                            chineseWord.setWord(ciyu);
                            chineseWord.setPinyin(ciyuPy);
                            chineseWord.setCreateTime(new Date());
//                            chineseWord.setUpdateTime(new Date());
                            pgZcChineseWordService.save(chineseWord);
                        }
                    }
                }

                // 读读写写
                if (StrUtil.isNotBlank(data.getReadAndWrite())) {

                    String[] readAndWrites = data.getReadAndWrite().split("、");

                    for (String readAndWrite : readAndWrites) {

                        log.info("读读写写: {}", readAndWrite);
                        String rwPinyin = PinyinHelper.toPinyin(readAndWrite);
                        log.info("读读写写拼音: {}", rwPinyin);
                        if (!pgZcChineseWordService.exists(new LambdaQueryWrapper<PgZcChineseWord>()
                                .eq(PgZcChineseWord::getWord, readAndWrite)
                                .eq(PgZcChineseWord::getPinyin, rwPinyin)
                        )) {

                            PgZcChineseWord chineseWord = new PgZcChineseWord();
                            chineseWord.setWord(readAndWrite);
                            chineseWord.setPinyin(rwPinyin);
                            chineseWord.setCreateTime(new Date());
//                            chineseWord.setUpdateTime(new Date());
                            pgZcChineseWordService.save(chineseWord);

                        }

                    }

                }
            }
        })).sheet().headRowNumber(1).doRead();

        log.info("完成");
    }


    // 导入课文
    @Test
    void exportText() {

        File file = new File("C:\\Users\\<USER>\\Desktop\\语文字词素材.xlsx");

        if (ObjectUtil.isNull(file)) {
            System.out.println("文件不存在");
        }

        EasyExcel.read(file, ChineseWordExcelDTO.class, new PageReadListener<ChineseWordExcelDTO>(dataList -> {

            for (ChineseWordExcelDTO data : dataList) {

                if (ObjectUtil.isNotNull(data)) {

                    PgZcText text = new PgZcText();
                    text.setName(data.getName());

                    // 关联的识字
                    if (StrUtil.isNotBlank(data.getKnowWord())) {

                        // 关联的字词id
                        StringBuilder knowWordIds = new StringBuilder();
                        String[] words = data.getKnowWord().split("、");

                        for (int i = 0; i < words.length; i++) {
                            String word = words[i];

                            PgZcChineseWord chineseWord = pgZcChineseWordService.getOne(new LambdaQueryWrapper<PgZcChineseWord>()
                                    .eq(PgZcChineseWord::getWord, word)
                                    .last("limit 1")
                            );

                            if (ObjectUtil.isNotNull(chineseWord)) {
                                Long wordId = chineseWord.getId();
                                knowWordIds.append(wordId);
                                if (i != words.length - 1) { // 避免最后一个元素后加 ;
                                    knowWordIds.append(";");
                                }
                            }
                        }
                        text.setKnowWordIds(knowWordIds.toString());
                    }

                    // 关联的生字
                    if (StrUtil.isNotBlank(data.getNewWord())) {

                        // 关联的字词id
                        StringBuilder newWordIds = new StringBuilder();
                        String[] words = data.getNewWord().split("、");
                        for (int i = 0; i < words.length; i++) {
                            String word = words[i];

                            PgZcChineseWord chineseWord = pgZcChineseWordService.getOne(new LambdaQueryWrapper<PgZcChineseWord>()
                                    .eq(PgZcChineseWord::getWord, word)
                                    .last("limit 1")
                            );

                            if (ObjectUtil.isNotNull(chineseWord)) {
                                Long wordId = chineseWord.getId();
                                newWordIds.append(wordId);
                                if (i != words.length - 1) { // 避免最后一个元素后加 ;
                                    newWordIds.append(";");
                                }
                            }
                        }
                        text.setNewWordIds(newWordIds.toString());
                    }

                    // 关联的词语
                    if (StrUtil.isNotBlank(data.getCiyu())) {

                        // 关联的字词id
                        StringBuilder ciyuWordIds = new StringBuilder();
                        String[] ciyus = data.getCiyu().split("、");
                        for (int i = 0; i < ciyus.length; i++) {
                            String word = ciyus[i];

                            PgZcChineseWord chineseWord = pgZcChineseWordService.getOne(new LambdaQueryWrapper<PgZcChineseWord>()
                                    .eq(PgZcChineseWord::getWord, word)
                                    .last("limit 1")
                            );

                            if (ObjectUtil.isNotNull(chineseWord)) {
                                Long wordId = chineseWord.getId();
                                ciyuWordIds.append(wordId);
                                if (i != ciyus.length - 1) { // 避免最后一个元素后加 ;
                                    ciyuWordIds.append(";");
                                }
                            }
                        }
                        text.setCiyuIds(ciyuWordIds.toString());
                    }

                    // 关联的读读写写
                    if (StrUtil.isNotBlank(data.getReadAndWrite())) {

                        StringBuilder rwWordIds = new StringBuilder();
                        String[] rwWords = data.getReadAndWrite().split("、");
                        for (int i = 0; i < rwWords.length; i++) {
                            String word = rwWords[i];

                            PgZcChineseWord chineseWord = pgZcChineseWordService.getOne(new LambdaQueryWrapper<PgZcChineseWord>()
                                    .eq(PgZcChineseWord::getWord, word)
                                    .last("limit 1")
                            );

                            if (ObjectUtil.isNotNull(chineseWord)) {
                                Long wordId = chineseWord.getId();
                                rwWordIds.append(wordId);
                                if (i != rwWords.length - 1) { // 避免最后一个元素后加 ;
                                    rwWordIds.append(";");
                                }
                            }
                        }
                        text.setReadAndWriteIds(rwWordIds.toString());

                    }

                    // 关联的课文内容
                    if (StrUtil.isNotBlank(data.getContent())) {
                        text.setContent(data.getContent());
                    }
                    log.info("课文名：{}", data.getName());

                    // 教材
                    PgZcTextbook textbook = pgZcTextbookService.getOne(new LambdaQueryWrapper<PgZcTextbook>()
                            .eq(PgZcTextbook::getGrade, data.getGrade())
                            .eq(PgZcTextbook::getVolume, data.getVolume())
                    );
                    text.setTextbookId(textbook.getId());
                    text.setCreateTime(new Date());
                    text.setUnit(data.getUnit());
                    text.setLesson(data.getLesson());
                    text.setSort(data.getSort());
                    text.setType(data.getType());

                    pgZcTextService.save(text);
                }
            }

        })).sheet().headRowNumber(1).doRead();

    }


    // 导入高中课文
    @Test
    void exportSeniorText() {

        File file = new File("C:\\Users\\<USER>\\Desktop\\高中语文素材.xlsx");

        if (ObjectUtil.isNull(file)) {
            System.out.println("文件不存在");
        }

        EasyExcel.read(file, ChineseWordExcelDTO.class, new PageReadListener<ChineseWordExcelDTO>(dataList -> {

            for (ChineseWordExcelDTO data : dataList) {

                if (ObjectUtil.isNotNull(data)) {

                    PgZcText text = new PgZcText();
                    text.setName(data.getName());

                    // 关联的课文内容
                    if (StrUtil.isNotBlank(data.getContent())) {
                        text.setContent(data.getContent());
                    }
                    log.info("课文名：{}", data.getName());

                    // 教材
                    PgZcTextbook textbook = pgZcTextbookService.getOne(new LambdaQueryWrapper<PgZcTextbook>()
                            .eq(PgZcTextbook::getGrade, data.getGrade())
                            .eq(PgZcTextbook::getVolume, data.getVolume())
                    );
                    text.setTextbookId(textbook.getId());
                    text.setCreateTime(new Date());
                    text.setUnit(data.getUnit());
                    text.setLesson(data.getLesson());
                    text.setSort(data.getSort());
                    text.setType(data.getType());

                    pgZcTextService.save(text);
                }
            }

        })).sheet().headRowNumber(1).doRead();

    }


    // 导入教材
    @Test
    void exportTextbook() {

        File file = new File("C:\\Users\\<USER>\\Desktop\\语文字词1-9.xlsx");

        if (ObjectUtil.isNull(file)) {
            System.out.println("文件不存在");
        }

        EasyExcel.read(file, ChineseWordExcelDTO.class, new PageReadListener<ChineseWordExcelDTO>(dataList -> {

            for (ChineseWordExcelDTO data : dataList) {
                if (ObjectUtil.isNotNull(data)) {

                    if (!pgZcTextbookService.exists(new LambdaQueryWrapper<PgZcTextbook>()
                            .eq(PgZcTextbook::getSystem, "六三制")
                            .eq(PgZcTextbook::getGrade, data.getGrade())
                            .eq(PgZcTextbook::getVolume, data.getVolume())
                    )) {

                        PgZcTextbook textbook = new PgZcTextbook();

                        textbook.setSystem("六三制");
                        textbook.setGrade(data.getGrade());
                        textbook.setVolume(data.getVolume());
                        textbook.setCreateTime(new Date());
                        pgZcTextbookService.save(textbook);
                    }
                }
            }

        })).sheet().headRowNumber(1).doRead();

    }


    @Data
    public static class Form {

        @Schema(title = "音标")
        private String ukphone;
    }

    /**
     * 音标
     */
    @Test
    void usphone() {

        List<PgZcEnglishWord> list = pgEnglishWordService.list(new LambdaQueryWrapper<PgZcEnglishWord>()
                .orderByAsc(PgZcEnglishWord::getSort)
                .isNull(PgZcEnglishWord::getPhonetic));

        list.forEach(word -> {

            String url = "https://v2.xxapi.cn/api/englishwords?word={}";

            String resultStr = HttpRequest.get(StrUtil.format(url, word.getWord()))
                    .timeout(20 * 60 * 1000)
                    .execute()
                    .body();

            BaseResult<Form> result = Convert.convert(new TypeReference<BaseResult<Form>>() {
            }, JSONUtil.parseObj(resultStr));

            System.out.println(result.getData().getUkphone());

            if (StrUtil.isNotBlank(result.getData().getUkphone())) {
                word.setPhonetic(result.getData().getUkphone());
            }
            pgEnglishWordService.updateById(word);

        });
        System.out.println("over");
    }


    @Schema(title = "实体映射")
    @Data
    public static class WordExcelDTO {

        @ExcelProperty("单词")
        private String word;

        @ExcelProperty("释义")
        private String definition;

        @ExcelProperty("年级")
        private String grade;

        @ExcelProperty("单元")
        private String unit;

        @ExcelProperty("排序")
        private Integer sort;

    }

    @Test
    void exportEnglishWordExcel() {

        File file = new File("C:\\Users\\<USER>\\Desktop\\PEP.xlsx");

        if (ObjectUtil.isNull(file)) {
            System.out.println("文件不存在");
        }

        EasyExcel.read(file, WordExcelDTO.class, new PageReadListener<WordExcelDTO>(dataList -> {

            for (WordExcelDTO data : dataList) {

                PgZcEnglishWord words = new PgZcEnglishWord();
                words.setIsOfficial(true);
                words.setVersion("小学人教PEP版");
                words.setGrade(data.getGrade());
                words.setUnit(data.getUnit());
                words.setWord(data.getWord());
                words.setDefinition(data.getDefinition());
                words.setCreateTime(new Date());
                words.setSort(data.getSort());
                pgEnglishWordService.save(words);

            }
        })).sheet().headRowNumber(1).doRead();

        System.out.println("导入成功");

    }


    // 重批作业下的作文
    @Test
    void reCorrectByHomework() {

        List<PgAnswer> answers = pgAnswerService.list(new LambdaQueryWrapper<PgAnswer>()
//                        .eq(PgAnswer::getId, 1913166185684561922L)
//                        .eq(PgAnswer::getUserId, 1904782092248838145L)
                        .eq(PgAnswer::getHomeworkId, 1938414946786516993L)
        );

        answers.forEach(answer -> {
            answer.setStatus(CorrectStatusEnum.Uploaded);
        });

        pgAnswerService.updateBatchById(answers);
        System.out.println("OK：" + answers.size());
    }


    /**
     * 更新用户配置
     */
    @Test
    void updateUserConfig() {
        List<PgUserConfig> configs = pgUserConfigService.list(new LambdaQueryWrapper<PgUserConfig>()
                .eq(PgUserConfig::getKey, UserConfigEnum.EXPORT.name()));

        configs.forEach(config -> {

            ExportConfigDTO exportConfig = BeanUtil.toBean(config.getValue(), ExportConfigDTO.class);

            // 赋值 批改标准：分数、润色幅度
            CorrectConfigDTO configDTO = new CorrectConfigDTO();
            configDTO.setScoreStandard(exportConfig.getScoreStandard());
            configDTO.setPolishRange(exportConfig.getPolishRange());

            // 保存批改配置
            PgUserConfig pgConfig = new PgUserConfig();
            pgConfig.setUserId(config.getUserId());
            pgConfig.setKey(UserConfigEnum.CORRECT.name());

            pgConfig.setValue(configDTO);
            pgConfig.setRemark("批改配置");
            pgConfig.setIsValid(true);
            pgConfig.setCreateTime(new Date());
            pgUserConfigService.save(pgConfig);

        });

        System.out.println("ok");
    }


    @Test
    void updateAnswerGrade() {

        List<PgAnswer> answerList = pgAnswerService.list(new LambdaQueryWrapper<PgAnswer>()
                        .eq(PgAnswer::getHomeworkId, 1899702866629971969L)
//                        .eq(PgAnswer::getId,1894959905858904065L)
//                        .between(PgAnswer::getCreateTime, DateUtil.beginOfDay(new Date()), DateUtil.endOfDay(new Date()))
        );

        answerList.forEach(pgAnswer -> {

//            ZwEssayQuestion zw = JSONUtil.toBean(JSONUtil.toJsonStr(pgAnswer.getAnswer()), ZwEssayQuestion.class);
//
//            PgQuestion question = zw.getRequire();
//
//            question.setWordNum(500);
//
//            zw.setRequire(question);
//
//            pgAnswer.setAnswer(zw);

            pgAnswer.setStatus(CorrectStatusEnum.Uploaded);

        });

        pgAnswerService.updateBatchById(answerList);

        System.out.println("ok");
    }

    // 给近一个月的所有记录加批次id
    @Test
    void setBatchId() {

        List<PgAnswer> answers = pgAnswerService.list(new LambdaQueryWrapper<PgAnswer>()
                .select(PgAnswer::getId, PgAnswer::getUserId, PgAnswer::getQuestionId, PgAnswer::getCreateTime)
//                .isNull(PgAnswer::getBatchId)
                .isNull(PgAnswer::getHomeworkId)
                .eq(PgAnswer::getDeleted, false)
                // 近一个月
                .ge(PgAnswer::getCreateTime, DateUtil.offsetDay(new Date(), -30)));

        answers.forEach(pgAnswer -> {

            PgAnswerBatch batch = new PgAnswerBatch();
            batch.setCreateTime(pgAnswer.getCreateTime());
            batch.setUserId(pgAnswer.getUserId());
            if (ObjectUtil.isNotNull(pgAnswer.getQuestionId())) {
                batch.setQuestionId(pgAnswer.getQuestionId());
            }
            pgAnswerBatchService.save(batch);

            pgAnswer.setBatchId(batch.getId());

        });
        // 更新
        pgAnswerService.updateBatchById(answers);

        System.out.println("共更新" + answers.size() + "条数据");

    }


    /**
     * 给符合条件的用户打标签
     */
    @Test
    void addTag() {

//        // 创建标签
//        // TODO
//        PgTag tag = new PgTag();
//        tag.setName("2025年新年活动用户");
//        tag.setCreateTime(new Date());
//        pgTagService.save(tag);

        // 购买过周卡或者月卡的
        List<Long> userIds = pgVipService.list(new LambdaQueryWrapper<PgVip>()
                        .in(PgVip::getVipType, VipTypeEnum.WEEK, VipTypeEnum.MONTH))
                .stream()
                .map(PgVip::getUserId)
                .distinct()
                .toList();

        // 获取购买过年卡的
        List<Long> userIds2 = pgVipService.list(new LambdaQueryWrapper<PgVip>()
                        .eq(PgVip::getVipType, VipTypeEnum.YEAR))
                .stream()
                .map(PgVip::getUserId)
                .distinct()
                .toList();

        // 目标用户
        List<Long> resultUserIds = userIds.stream()
                .filter(userId -> !userIds2.contains(userId))
                .toList();

        // 筛选符合条件的用户
        List<PgUsers> users = pgUsersService.list(new LambdaQueryWrapper<PgUsers>()
                .in(PgUsers::getId, resultUserIds));

        log.info("用户数：{}", users.size());

        users.forEach(user -> {

            if (!pgTagUserService.exists(new LambdaQueryWrapper<PgTagUser>()
                    .eq(PgTagUser::getUserId, user.getId())
                    .eq(PgTagUser::getTagId, 1873712800328937474L))) {

                // 保存关联关系
                PgTagUser tagUser = new PgTagUser();
                tagUser.setUserId(user.getId());
                tagUser.setTagId(1873712800328937474L);
                tagUser.setCreateTime(new Date());

                pgTagUserService.save(tagUser);
            }
        });
    }

    @Test
    void renderImg() {
        // 获取答案对象
        long answerId = 1846022761230901250L;
        PgAnswer answer = pgAnswerService.getById(answerId);

        // 解析答案内容为作文题目对象
        ZwEssayQuestion essay = JSONUtil.toBean(JSONUtil.toJsonStr(answer.getCorrectResult()), ZwEssayQuestion.class);

        // 创建固定大小的线程池
        int threadCount = 100;
        ExecutorService executorService = Executors.newFixedThreadPool(threadCount);

        try {
            // 提交任务到线程池
            for (int i = 0; i < threadCount; i++) {
                executorService.submit(() -> {
                    try {
                        correctService.getImg(800, 800, essay.getMarkJsonList().get(0), 0, false);
                    } catch (Exception e) {
                        // 记录异常日志
                        System.err.println("Error in thread: " + e.getMessage());
                        e.printStackTrace();
                    }
                });
            }

            // 等待所有任务完成
            executorService.shutdown();
            executorService.awaitTermination(1, TimeUnit.HOURS); // 设置合理的超时时间
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            System.err.println("Thread pool was interrupted: " + e.getMessage());
            e.printStackTrace();
        } finally {
            if (!executorService.isTerminated()) {
                executorService.shutdownNow();
            }
        }
    }

    /**
     * 重新 批量导出
     */
    @Test
    void reExport() {

        // 查看未导出的内容
        List<PgExportRecord> list = pgExportRecordService.list(new LambdaQueryWrapper<PgExportRecord>()
//                .eq(PgExportRecord::getUserId, 1865742750847307777L)
                .eq(PgExportRecord::getStatus, 0));
        list.forEach(exRecord -> {

            // 构建进度数据
            log.info("开始批量导出word任务：{}", exRecord.getId());

            // 拿任务
            PgExportRecord record = pgExportRecordService.getById(exRecord.getId());

            // 任务有效性判断
            if (ObjectUtil.isNull(record) || record.getStatus().equals(ExportStatusEnum.Completed)) {
                log.info("批量导出word任务跳过：{}", record.getId());
                return;
            }

            List<Long> exportIds = StrUtil.split(record.getAnswerIds(), ";").stream().map(Long::valueOf).toList();

            // 初始化文件列表
            List<File> tempFileList = new ArrayList<>();

            for (Long answerId : exportIds) {

                // 获取已上传作业
                PgAnswer answer = pgAnswerService.getById(answerId);

                String fileName;

                ZwEssayQuestion userAnswer = JSONUtil.toBean(answer.getCorrectResult().toString(), ZwEssayQuestion.class);

                // 若备注不为空
                if (StrUtil.isNotBlank(userAnswer.getName())) {
                    fileName = exportIds.indexOf(answerId) + 1 + "_" + userAnswer.getName() + "_" + DateUtil.format(new Date(), "MM-dd") + "_作文批改报告";
                } else {
                    // 导出的文件名
                    fileName = exportIds.indexOf(answerId) + 1 + "_" + DateUtil.format(new Date(), "MM-dd") + "_作文批改报告";
                }

                // 构建word
                File tempWord = pgExportRecordService.getExportWord(answer, fileName);

                // 添加到临时文件中
                tempFileList.add(tempWord);
            }

            // 创建计时器
            TimeInterval timer = DateUtil.timer();

            // 获取压缩文件
            File zipFile = FileUtil.createTempFile();

            zipFile.deleteOnExit();

            ZipUtil.zip(zipFile, StandardCharsets.UTF_8, false, ArrayUtil.toArray(tempFileList, File.class));

            // 删除临时文件
            tempFileList.forEach(FileUtil::del);

            // 上传压缩文件
            String path = StrUtil.format("zw/zip/{}/{}.zip", record.getUserId(), record.getMd5());

            // 上传oss
            String zipUrl = ossService.putFile(path, zipFile);

//            ossService.setTagList(URLUtil.getPath(zipUrl), "已删除");

            log.info("导出word压缩包上传oss耗时：{}", timer.intervalSecond());

            // 更新上传状态
            record.setStatus(ExportStatusEnum.Completed);
            record.setZipUrl(zipUrl);
            record.setExportTime(new Date());

            pgExportRecordService.updateById(record);

            // 发送导出结果通知
            sendWxMsg(record.getUserId());

        });


//        // 查看未导出的内容
//        List<PgExportRecord> list = pgExportRecordService.list(new LambdaQueryWrapper<PgExportRecord>()
//                .eq(PgExportRecord::getUserId, 1834809588633989122L)
//                .eq(PgExportRecord::getStatus, 0));
//
//        list.forEach(record -> {
//
//            // 发送队列请求
//            QueueUtils.addQueueObjectInTransaction(GlobQueueConstants.PGB_XCX_BATCH_EXPORT_WORD_QUEUE.name(), record.getId());
//
//            log.info("【监听批量导出word队列-接收批改监听信息】：{}", record.getId());
//
//        });

    }

    private void sendWxMsg(Long userId) {
        // 发送批改结束通知
        PgUsers users = pgUsersService.getById(userId);

        WxMaSubscribeMessage message = new WxMaSubscribeMessage();
        message.setToUser(users.getWxOpenId());
        message.setTemplateId("UsNIR66iF_rjs593zh4uPwFGspXNqtU1Lws97gefIRw");
        message.setPage("/pages/my/export/index");
        message.setData(ListUtil.toList(
                new WxMaSubscribeMessage.MsgData("phrase1", "已完成"),
                new WxMaSubscribeMessage.MsgData("thing2", DateUtil.format(new Date(), "HH:mm"))
        ));
        if (EnvUtils.isDev()) {
            message.setMiniprogramState("developer");
        }

        try {
            wxMaService.getMsgService().sendSubscribeMsg(message);
        } catch (WxErrorException e) {
            log.error("小程序模板发送失败：{}", e.getError());
        }
    }


    @Test
    void updateAnswerScore() {
        // TODO 更新题目总分

        // 获取题目
        List<PgAnswer> answers = pgAnswerService.list(new LambdaQueryWrapper<PgAnswer>()
                .select(PgAnswer::getId, PgAnswer::getQuestionId, PgAnswer::getCorrectResult));

        answers.forEach(answer -> {

                    ZwEssayQuestion userAnswer = JSONUtil.toBean(answer.getCorrectResult().toString(), ZwEssayQuestion.class);

                    // 备注名称
                    String name = userAnswer.getName();
                    if (StrUtil.isNotBlank(name)) {

                        answer.setName(name);
                    }

                    // 获取题目分数--总分
                    if (ObjectUtil.isNull(answer.getQuestionId())) {
                        // 没关联题目则默认为总分 30分
                        userAnswer.setScore(30.);
                    } else {
                        PgQuestion question = pgQuestionService.getById(answer.getQuestionId());

                        // 若题目不存在 or 题目没有分数 --- 默认为30分
                        if (ObjectUtil.isNull(question) || ObjectUtil.isNull(question.getScore())) {
                            userAnswer.setScore(30.);
                        } else {
                            userAnswer.setScore(Double.valueOf(question.getScore()));
                        }
                    }

                    // 分数样式 --- 默认为评级
                    if (ObjectUtil.isNull(userAnswer.getScoreStyle())) {
                        userAnswer.setScoreStyle(0);
                    }

                    // 返回用户分数
                    if (ObjectUtil.isNull(userAnswer.getUserScore())) {
                        // 默认为 30 分
                        userAnswer.setUserScore(30);
                    }

                    answer.setCorrectResult(userAnswer);

                    // 打印进度
                    log.info("更新数据id ：{}", answer.getId());

                }
        );

        pgAnswerService.updateBatchById(answers);

        System.out.println("ok");

    }

    @Test
    void num() {
        int i = (int) NumberUtil.mul(30.0, 0.9);

        System.out.println("结果：：：" + i);
    }

    @Test
    void updateSort() {
        List<PgQuestion> questions = pgQuestionService.list(new LambdaQueryWrapper<PgQuestion>()
                .eq(PgQuestion::getIsOfficial, true));

        for (PgQuestion question : questions) {
            question.setSort(question.getSort() + 1);
        }
        pgQuestionService.updateBatchById(questions);
    }

    @Test
    void md5() {
        String url = "https://cdn.pigaibang.com/zw/user/1810610771813761026/IMG/8c3f1171deec48a982b6817ec645457a.jpghttps://cdn.pigaibang.com/zw/user/1810610771813761026/IMG/280b023ceb394d1bba1f1e2cb1215d3f.jpg";
        String md5 = DigestUtil.md5Hex(url);

        System.out.println(md5);

    }

    void zip() {

        // 所有的批改结果图片累加，做  md5操作   1.png + 2.png + 3.png   ==== md5
        // 去redis查，有没有缓存记录，如果有，直接返回链接

        // 如果没有，直接发送队列请求，有一个队列，专门做导出的，执行下面打包操作，并返回给前端一个 taskId
        // md5(str = 1.png + 2.png + 3.png)

        // 091059-作文批改结果.zip
        File zipFile = FileUtil.createTempFile();

        File word1 = FileUtil.createTempFile();

        // 执行完一次导出单个文件后，redis 更新 taskId 序号 +1
        // 前端  拿taskId去轮询查导出进度，{status: 'exporting'/'OK', num: 0.85, zipLink: ''} 如果进度100，则返回  链接
        File word2 = FileUtil.createTempFile();

        List<File> wordList = CollUtil.newArrayList(word1, word2);

        ZipUtil.zip(zipFile, false, ArrayUtil.toArray(wordList, File.class));

        // 上传oss
        // 如果在三天内容，重复打包，
        // redis 缓存三天，加一个延时任务，三天删除该压缩包
        // /zw/zip/userId/xxx.zip

    }

    @Test
    void reCo() {
        List<PgAnswer> pgAnswerList = pgAnswerService.list(new LambdaQueryWrapper<PgAnswer>()
                .lt(PgAnswer::getCreateTime, DateUtil.parse("2024-07-10"))
        );

        pgAnswerList.forEach(answer -> {
            ZwEssayQuestion userAnswer = JSONUtil.toBean(answer.getAnswer().toString(), ZwEssayQuestion.class);
            ZwEssayQuestion question = JSONUtil.toBean(answer.getCorrectResult().toString(), ZwEssayQuestion.class);

            userAnswer.getUserImgAnswerList().forEach(imgAnswer -> {
                if (imgAnswer.getImgUrl().contains("pgb-user-1300104514")) {
                    String path = StrUtil.format("zw/user/{}/{}", answer.getUserId(), IdUtil.fastSimpleUUID() + "." + FileUtil.getSuffix(imgAnswer.getImgUrl()));
                    String imgUrl = ossService.putCdnImg(path, ImgUtil.read(URLUtil.url(imgAnswer.getImgUrl())));
                    imgAnswer.setImgUrl(imgUrl);
                    log.info(path);
                }
            });

            answer.setAnswer(userAnswer);

            question.getUserImgAnswerList().forEach(imgAnswer -> {
                if (imgAnswer.getImgUrl().contains("pgb-user-1300104514")) {
                    log.info(imgAnswer.getImgUrl());
                    String path = StrUtil.format("zw/correct/{}/{}", answer.getUserId(), IdUtil.fastSimpleUUID() + "." + FileUtil.getSuffix(imgAnswer.getImgUrl()));
                    String imgUrl = ossService.putCdnImg(path, ImgUtil.read(URLUtil.url(imgAnswer.getImgUrl())));
                    imgAnswer.setImgUrl(imgUrl);
                    log.info(path);
                }
            });

            answer.setCorrectResult(question);

            pgAnswerService.updateById(answer);
        });
    }

    // 重新批改指定手机号用户及时间的题目
    @Test
    void reCorrect() {
//        PgUsers pgUsers = pgUsersService.getOne(new LambdaQueryWrapper<PgUsers>()
//                .eq(PgUsers::getPhone, "13529675310")
//        );

//        List<PgAnswer> list = pgAnswerService.list(new LambdaQueryWrapper<PgAnswer>()
//                .eq(PgAnswer::getUserId, 1818851951417393154L)
//                .eq(PgAnswer::getStatus, 0)
//                .gt(PgAnswer::getCreateTime, DateUtil.parse("2024-10-30 06:00:00"))
//        );
//
//        System.out.println(list.size());

        PgAnswer answer = pgAnswerService.getById(1858765785699373057L);

//        list.forEach(answer -> {

        // 执行AI批改
        doCorrect(answer);

        pgAnswerService.updateById(answer);

        System.out.println("批改完成");

        // pgAnswer.setStatus(CorrectStatusEnum.Uploaded);

        // pgAnswerService.updateById(pgAnswer);

        // 加入批改队列
//            QueueUtils.addQueueObject(GlobQueueConstants.PGB_XCX_CORRECT_QUEUE.name(), pgAnswer.getId());
//        });
    }

    // 开始批改题目
    private void doCorrect(PgAnswer answer) {
        // 开始统计批改时长
        TimeInterval timer = DateUtil.timer();
        log.info("开始批改作文题：{}", answer.getId());

        ZwEssayQuestion essay = JSONUtil.toBean(answer.getAnswer().toString(), ZwEssayQuestion.class);

        // 无效答案
        if (CollUtil.isEmpty(essay.getUserImgAnswerList())) {
            answer.setAiTokens(0L);
        } else {
            ZwEssayQuestion correctResult = correctService.zwCorrect(essay);

            // 渲染图片
            correctResult = correctService.renderImg(essay.getUserImgAnswerList(), correctResult, answer.getUserId(), true, false);

//            log.info("【doCorrect】：{}", correctResult.toString());

            // 将批改结果赋值
            essay.setComment(correctResult.getComment());
            essay.setPolish(correctResult.getPolish());
            essay.setMarkJsonList(correctResult.getMarkJsonList());
            essay.setUserImgAnswerList(correctResult.getUserImgAnswerList());
            essay.setFullText(correctResult.getFullText());
            essay.setPolishDiffList(correctResult.getPolishDiffList());

            // 用户分数 -- 默认为30
            essay.setUserScore(ObjectUtil.defaultIfNull(correctResult.getUserScore(), 30));

            // 脑力值
            answer.setAiTokens(correctResult.getAiTokens());

            // 获取配置信息 保存分数样式
            PgUserConfig config = pgUserConfigService.getByKey(UserConfigEnum.EXPORT, answer.getUserId());

            if (ObjectUtil.isNull(config)) {
                // 默认是评级
                essay.setScoreStyle(0);
            } else {
                ExportConfigDTO configDTO = BeanUtil.toBean(config.getValue(), ExportConfigDTO.class);
                essay.setScoreStyle(configDTO.getScoreStyle());
            }

            // 保存批改结果
            answer.setCorrectResult(essay);
        }

        // 更新批改状态
        answer.setStatus(CorrectStatusEnum.Corrected);
        // 批改时长
        answer.setCorrectTime(new Date());
        answer.setCorrectDuration((int) timer.intervalSecond());

        // 发送批改结束通知
        PgUsers users = pgUsersService.getById(answer.getUserId());

        WxMaSubscribeMessage message = new WxMaSubscribeMessage();
        message.setToUser(users.getWxOpenId());
        message.setTemplateId("Qa5iizS13gl7a0z11F3aFihf-6nSyeivLR1DU7BcoUc");
        message.setPage("/pages/zw/index?id=" + answer.getId());
        message.setData(ListUtil.toList(
                // new WxMaSubscribeMessage.MsgData("thing2", DateUtil.format(answer.getCreateTime(), "yyyy-MM-dd HH:mm:ss") + " 上传作文"),
                new WxMaSubscribeMessage.MsgData("thing2", "作文已批改"),
                new WxMaSubscribeMessage.MsgData("thing4", "点击立即阅览")
        ));
        if (EnvUtils.isDev()) {
            message.setMiniprogramState("developer");
        }
//        try {
//            wxMaService.getMsgService().sendSubscribeMsg(message);
//        } catch (WxErrorException e) {
//            log.error("小程序模板发送失败：{}", e.getError());
//        }

        log.info("批改完成：{}，总用时：{}秒", answer.getId(), timer.intervalSecond());
    }

    /**
     * 重批单个题目
     */
    @Test
    void reCorrectOne() {

        QueueUtils.addQueueObject(GlobQueueConstants.PGB_XCX_CORRECT_QUEUE.name(), 1839934419939606529L);

        System.out.println("ok");
    }

    // -------------------  开通会员 --------------------------
    @Test
    void contextLoads() {

        String phone = "18825933911";

        VipTypeEnum type = VipTypeEnum.TERM;

        // 查看用户是否已经存在
        PgUsers users = pgUsersService.getOne(new LambdaQueryWrapper<PgUsers>()
                .eq(PgUsers::getPhone, phone));

        if (ObjectUtil.isNull(users)) {
            // 生成用户
            PgUsers user = new PgUsers();
            user.setPhone(phone);
            user.setNickName("邦友" + user.getPhone().substring(user.getPhone().length() - 4));
            // 用户来源
            user.setUserFrom(0);
            user.setCreateTime(new Date());
            user.setAvatarImgUrl("https://cdn.pigaibang.com/common/xcx-zw/avatar.png");
            pgUsersService.save(user);
        }
        // 生成订单
        PgOrder order = new PgOrder();
        // 订单号
        order.setOrderNo(IdUtil.getSnowflakeNextIdStr());
        // 订单类型
        order.setOrderType(OrderTypeEnum.Pay);
        // 订单状态
        order.setStatus(OrderStatusEnum.Generate);
        // 下单时间
        order.setCreateTime(new Date());
        // 订单过期时间
        order.setExpireTime(DateUtil.offsetMinute(new Date(), 60));
        // 支付方式
        order.setPayType(PayTypeEnum.WX_MINI);
        // TODO 保存用户信息
        order.setUserId(users.getId());
        // 是否已退款
        order.setIsRefund(false);

        // 月卡
        if (type.equals(VipTypeEnum.MONTH)) {
            order.setSku("{\"buyType\": \"PAY\", \"vipType\": \"MONTH\", \"duration\": 30}");
            order.setTotalAmount(2990);
            order.setPayAmount(2990);
            order.setTitle("批改邦批改月卡");
            order.setDescription("批改邦批改月卡");
        }
        // 年卡
        else if (type.equals(VipTypeEnum.YEAR)) {
            order.setSku("{\"buyType\": \"PAY\", \"vipType\": \"YEAR\", \"duration\": 365}");
            order.setTotalAmount(29800);
            order.setPayAmount(29800);
            order.setTitle("批改邦批改年卡");
            order.setDescription("批改邦批改年卡");
        }
        // 周卡
        else if (type.equals(VipTypeEnum.WEEK)) {
            order.setSku("{\"buyType\": \"PAY\", \"vipType\": \"WEEK\", \"duration\": 7}");
            order.setTotalAmount(990);
            order.setPayAmount(990);
            order.setTitle("批改邦批改周卡");
            order.setDescription("批改邦批改周卡");
        }
        // 学期卡
        else if (type.equals(VipTypeEnum.TERM)) {
            order.setSku("{\"buyType\": \"PAY\", \"vipType\": \"TERM\", \"duration\": 124}");
            order.setTotalAmount(8800);
            order.setPayAmount(8800);
            order.setTitle("批改邦批改学期卡（活动）");
            order.setDescription("批改邦批改学期卡（活动）");
        }

        order.setIsPay(false);

        // 数据库，保存订单
        pgOrderService.save(order);

        // ---- 走支付成功逻辑
        if (!order.getIsPay()) {
            order.setStatus(OrderStatusEnum.PaySuccess);
            order.setIsPay(true);
            order.setPayTime(new Date());
            pgOrderService.updateById(order);
        }

        // 走充值会员逻辑
        if (!pgVipService.exists(new LambdaQueryWrapper<PgVip>()
                .eq(PgVip::getOrderId, order.getId())
                .eq(PgVip::getBuyType, BuyTypeEnum.PAY)
                .eq(PgVip::getUserId, order.getUserId())
        )) {
            PgVip vip = JSONUtil.toBean(order.getSku().toString(), PgVip.class);
            vip.setUserId(order.getUserId());
            vip.setCreateTime(new Date());
            vip.setOrderId(order.getId());
            vip.setBuyType(BuyTypeEnum.PAY);

            pgVipService.save(vip);

            // 增加会员时间
            pgUsersService.addVipDay(order.getUserId(), vip.getDuration());
        }
        log.info("【微信支付回调】：{}", 11);
    }


    @Test
    void saveZwAnswer() {

        List<Long> ids = pgAnswerService.list(new LambdaQueryWrapper<PgAnswer>()
                .eq(PgAnswer::getStatus, CorrectStatusEnum.Corrected)
        ).stream().map(PgAnswer::getId).toList();

        ids.forEach(
                id -> {
                    PgAnswer answer = pgAnswerService.getById(1806218368332742658L);

                    ZwEssayQuestion userAnswer = JSONUtil.toBean(answer.getAnswer().toString(), ZwEssayQuestion.class);
                    ZwEssayQuestion question = JSONUtil.toBean(answer.getCorrectResult().toString(), ZwEssayQuestion.class);

                    ZwEssayQuestion rendered = correctService.renderImg(userAnswer.getUserImgAnswerList(), question, answer.getUserId(), true, false);

                    // 保存题目批改信息
                    answer.setCorrectResult(rendered);

                    // 更新保存
                    pgAnswerService.updateById(answer);
                }
        );

//        PgAnswer answer = pgAnswerService.getById(1808356908797681665L);
//
//        ZwEssayQuestion userAnswer = JSONUtil.toBean(answer.getAnswer().toString(), ZwEssayQuestion.class);
//        ZwEssayQuestion question = JSONUtil.toBean(answer.getCorrectResult().toString(), ZwEssayQuestion.class);
//
//        ZwEssayQuestion rendered = correctService.renderImg(userAnswer.getUserImgAnswerList(), question, answer.getUserId(), true);
//
//        // 保存题目批改信息
//        answer.setCorrectResult(rendered);
//
//        // 更新保存
//        pgAnswerService.updateById(answer);
        System.out.println("ok");
    }

    @Test
    void paySuccess() {

        // 处理其他支付操作
        QueueUtils.addQueueObjectInTransaction(GlobalXcxConstants.XCX_PAY_SUCCESS, 1811286607286992898L);

        System.out.println("支付成功！");

    }
// 报警通知
//    @Test
//    void alarm() {
//
//        QueueUtils.addQueueObjectInTransaction(GlobQueueConstants.PGB_XCX_CORRECT_QUEUE.name(), 1808111862342537217L);
//
//        System.out.println("报警通知");
//    }


    /**
     * 生成海报
     */
    @Test
    void poster() {
        // 开始计时
        long startTime = System.currentTimeMillis();

//        String name = "张";

//        String certificateUrl = "https://cdn.pigaibang.com/common/xcx-zw/poster/fx_share_2.jpg";
        String certificateUrl = "https://cdn.pigaibang.com/common/xcx-zw/poster/fx_share.jpg";

        // 获取背景图片
        BufferedImage bgImg = null;
        try {
            bgImg = ImageIO.read(new URL(certificateUrl));

            // 获取二维码
            BufferedImage avatarImage = ImageIO.read(new URL("https://list-1300315497.cos.ap-beijing.myqcloud.com/mini/activity/drawLot/zhufu/code.jpg"));

//            BufferedImage avatarImage = cutHeadImages("https://list-1300315497.cos.ap-beijing.myqcloud.com/mini/activity/drawLot/zhufu/code.jpg");

            int width = 1242;
            int height = 2134;

            int avatarWidth = 250;
            int avatarHeight = 250;

            // 创建画布
            BufferedImage canvas = new BufferedImage(width, height, BufferedImage.TYPE_INT_RGB);
            Graphics2D g = (Graphics2D) canvas.getGraphics();
            g.setBackground(Color.WHITE);   //  设置背景色
            g.clearRect(0, 0, width, height);

            // 设置文字抗锯齿
            g.setRenderingHint(RenderingHints.KEY_TEXT_ANTIALIASING, RenderingHints.VALUE_TEXT_ANTIALIAS_ON);

            // 画背景
            g.drawImage(bgImg.getScaledInstance(width, height, Image.SCALE_SMOOTH), 0, 0, null);
            g.drawImage(avatarImage.getScaledInstance(avatarWidth, avatarHeight, Image.SCALE_SMOOTH), 520, 1755, null);

            // 画名字
//            g.setColor(new Color(0, 0, 0));
//            g.setFont(new Font("宋体", Font.BOLD, 45));
//            g.drawString(name + " 同学", 667, 630);

            // 生成图片
            String uuid = UUID.randomUUID().toString().replaceAll("-", "");
            File file = null;
            file = File.createTempFile(uuid, ".png");
            ImageIO.write(canvas, "png", file);

            file.deleteOnExit();

            // 结束计时
            log.info("【活动海报】【分享专属福利】海报生成成功！" + "【耗时:" + (System.currentTimeMillis() - startTime) / 1000.0 + "s】");

            String fileName = file.getName();

//             测试环节
            String newPath = System.getProperty("user.home") + File.separator + "Desktop";
            File file1 = new File(newPath + "test.png");
            ImageIO.write(canvas, "png", file1);


            System.out.println(file1.getPath());

//            return imageService.submitImg(fileName, file);

        } catch (IOException e) {
            throw new RuntimeException(e);
        } finally {

        }
    }

    public static BufferedImage cutHeadImages(String headUrl) {

        BufferedImage avatarImage = null;
        try {
            // 获取二维码
            avatarImage = ImageIO.read(new URL("https://list-1300315497.cos.ap-beijing.myqcloud.com/mini/activity/drawLot/zhufu/code.jpg"));

//            avatarImage = ImageIO.read(new URL(headUrl));
            avatarImage = scaleByPercentage(avatarImage, avatarImage.getWidth(), avatarImage.getWidth());
            int width = avatarImage.getWidth();
            // 透明底的图片
            BufferedImage formatAvatarImage = new BufferedImage(width, width, BufferedImage.TYPE_4BYTE_ABGR);
            Graphics2D graphics = formatAvatarImage.createGraphics();
            // 把图片切成一个园
            graphics.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
            // 留一个像素的空白区域，这个很重要，画圆的时候把这个覆盖
            int border = 1;
            // 图片是一个圆型
            Ellipse2D.Double shape = new Ellipse2D.Double(border, border, width - border * 2, width - border * 2);
            // 需要保留的区域
            graphics.setClip(shape);
            graphics.drawImage(avatarImage, border, border, width - border * 2, width - border * 2, null);
            graphics.dispose();
            // 在圆图外面再画一个圆
            // 新创建一个graphics，这样画的圆不会有锯齿
            graphics = formatAvatarImage.createGraphics();
            graphics.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
            int border1 = 3;
            // 画笔是4.5个像素，BasicStroke的使用可以查看下面的参考文档
            // 使画笔时基本会像外延伸一定像素，具体可以自己使用的时候测试
            Stroke s = new BasicStroke(5F, BasicStroke.CAP_ROUND, BasicStroke.JOIN_ROUND);
            graphics.setStroke(s);
            graphics.setColor(Color.WHITE);
            graphics.drawOval(border1, border1, width - border1 * 2, width - border1 * 2);
            graphics.dispose();
//            OutputStream os = new FileOutputStream("D:\\WorkStation\\JSJ\\JSJ Member Center\\src\\main\\resources\\static\\李步云.png");
//            ImageIO.write(formatAvatarImage, "PNG", os);
            return formatAvatarImage;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    public static BufferedImage scaleByPercentage(BufferedImage inputImage, int newWidth, int newHeight) {
        // 获取原始图像透明度类型
        try {
            int type = inputImage.getColorModel().getTransparency();
            int width = inputImage.getWidth();
            int height = inputImage.getHeight();
            // 开启抗锯齿
            RenderingHints renderingHints = new RenderingHints(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
            // 使用高质量压缩
            renderingHints.put(RenderingHints.KEY_RENDERING, RenderingHints.VALUE_RENDER_QUALITY);
            BufferedImage img = new BufferedImage(newWidth, newHeight, type);
            Graphics2D graphics2d = img.createGraphics();
            graphics2d.setRenderingHints(renderingHints);
            graphics2d.drawImage(inputImage, 0, 0, newWidth, newHeight, 0, 0, width, height, null);
            graphics2d.dispose();
            return img;

        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    @Test
    void logout() {

        // 查用户 openId为null 的用户id

        pgUsersService.list().forEach(user -> {

            if (ObjectUtil.isNull(user.getWxOpenId())) {

                // 登出
                StpUtil.logout(user.getId());

                System.out.println(user.getId());
            }
        });
    }

    @Test
    void word() {
        XWPFDocument document = new XWPFDocument();
        XWPFParagraph paragraph = document.createParagraph();

        // 设置段落居中
        paragraph.setAlignment(ParagraphAlignment.CENTER);

        // 插入图片
        XWPFRun runImage = paragraph.createRun();
        String path = "C:\\Users\\<USER>\\Desktop\\评级\\title.png";
        File file = new File(path);
        InputStream stream = FileUtil.getInputStream(file);
        try {
            runImage.addPicture(stream, XWPFDocument.PICTURE_TYPE_PNG, ".png", 30, 30);

            // 插入文本
            XWPFRun runText = paragraph.createRun();
            runText.setFontFamily("宋体");
            runText.setBold(true);
            runText.setFontSize(23);
            runText.setText("作文批改报告");

            // 将文档保存到文件
            String outputPath = System.getProperty("user.home") + File.separator + "Desktop" + File.separator + "批改报告.docx";
            try (FileOutputStream out = new FileOutputStream(outputPath)) {
                document.write(out);
            }
            document.close();

        } catch (InvalidFormatException | IOException e) {
            throw new RuntimeException(e);
        }
    }

    @Schema(title = "实体映射")
    @Data
    public static class ExportExcelDTO {

        @ExcelProperty("年级")
        private String grade;

        @ExcelProperty("单元")
        private String unit;

        @ExcelProperty("name")
        private String name;

        @ExcelProperty("写作要求")
        private String writingRequest;

        @ExcelProperty("批改标准")
        private String correctRequest;

        @ExcelProperty("写作文体")
        private String style;

        @ExcelProperty("参考字数")
        private Integer wordNum;

        @ExcelProperty("好标题/选材")
        private String headline;

        @ExcelProperty("好词语")
        private String word;

        @ExcelProperty("好开头")
        private String opening;

        @ExcelProperty("好结尾")
        private String ending;

        @ExcelProperty("排序")
        private Integer sort;

        @ExcelProperty("参考分数")
        private Integer score;

        @ExcelProperty("evaluation")
        private String evaluation;

        @ExcelProperty("cover")
        private String cover;

    }

    //
//    @Test
//    void exportCover() {
//        File file = new File("C:\\Users\\<USER>\\Desktop\\作文库.xlsx");
////        File file = new File("C:\\Users\\<USER>\\Desktop\\1.xlsx");
//
//        if (ObjectUtil.isNull(file)) {
//            System.out.println("文件不存在");
//        }
//
//        EasyExcel.read(file, ExportExcelDTO.class, new PageReadListener<ExportExcelDTO>(dataList -> {
//
//            for (ExportExcelDTO data : dataList) {
//
//                if (ObjectUtil.isNotNull(data)) {
//
//                    PgQuestion question = pgQuestionService.getOne(new LambdaQueryWrapper<PgQuestion>()
//                            .eq(PgQuestion::getIsOfficial, true)
//                            .eq(PgQuestion::getName, data.getName()));
//
//                    if (ObjectUtil.isNull(question)) {
//                        log.info("{}：不存在", data.getName());
//                    }
//

    /// /                    question.setEvaluation(data.getEvaluation());
//                    question.setCover(data.getCover());
//
//                    pgQuestionService.updateById(question);
//                }
//            }
//        }
//        )).sheet().headRowNumber(1).doRead();
//
//        System.out.println("导入成功");
//    }
//
    @Test
    void exportExcel() {
        File file = new File("C:\\Users\\<USER>\\Desktop\\作文库素材.xlsx");
//        File file = new File("C:\\Users\\<USER>\\Desktop\\1.xlsx");

        if (ObjectUtil.isNull(file)) {
            System.out.println("文件不存在");
        }

        EasyExcel.read(file, ExportExcelDTO.class, new PageReadListener<ExportExcelDTO>(dataList -> {

            for (ExportExcelDTO data : dataList) {

                if (ObjectUtil.isNotNull(data) && ObjectUtil.isNotNull(data.getSort())) {

                    PgQuestion question = pgQuestionService.getOne(new LambdaQueryWrapper<PgQuestion>()
                            .eq(PgQuestion::getIsOfficial, true)
                            .eq(PgQuestion::getSort, data.getSort()));

                    if (ObjectUtil.isNull(question)) {
                        question = new PgQuestion();
                    }

                    // 官方
                    question.setIsOfficial(true);
                    question.setCreateTime(new Date());
                    question.setSubject(SubjectEnum.Chinese);

                    // 年级
                    if (StrUtil.isNotBlank(data.getGrade())) {
                        // 获取所有年级
                        List<GradeEnum> grades = Arrays.stream(GradeEnum.values()).toList();

                        PgQuestion finalQuestion = question;
                        grades.forEach(
                                grade -> {
                                    if (data.getGrade().equals(grade.desc)) {
                                        // 设置年级
                                        finalQuestion.setGrade(grade);
                                    }
                                }
                        );
                    }

                    // 单元
                    if (StrUtil.isNotBlank(data.getUnit())) {
                        // 设置单元
                        question.setUnit(data.getUnit());
                    }

                    // 题目
                    if (StrUtil.isNotBlank(data.getName())) {
                        // 设置题目
                        question.setName(data.getName());
                    }

                    // 写作要求
                    if (StrUtil.isNotBlank(data.getWritingRequest())) {
                        // 设置写作要求
                        question.setWritingRequest(data.getWritingRequest());
                    }

                    // 批改标准
                    if (StrUtil.isNotBlank(data.getCorrectRequest())) {
                        // 设置批改标准
                        question.setCorrectRequest(data.getCorrectRequest());
                    }

                    // 写作文体
                    if (StrUtil.isNotBlank(data.getStyle())) {
                        // 获取所有年级
                        List<WritingStyleEnum> styles = Arrays.stream(WritingStyleEnum.values()).toList();

                        PgQuestion finalQuestion1 = question;
                        styles.forEach(
                                style -> {
                                    if (data.getStyle().equals(style.desc)) {
                                        // 设置年级
                                        finalQuestion1.setStyle(style);
                                    }
                                }
                        );
                    }

                    // 参考字数
                    if (ObjectUtil.isNotNull(data.getWordNum())) {
                        // 设置参考字数
                        question.setWordNum(data.getWordNum());
                    }

                    // 好标题/选材
                    if (StrUtil.isNotBlank(data.getHeadline())) {
                        // 设置好标题/选材
                        question.setHeadline(data.getHeadline());
                    }

                    // 好词语
                    if (StrUtil.isNotBlank(data.getWord())) {
                        // 设置好词语
                        question.setWord(data.getWord());
                    }

                    // 好开头
                    if (StrUtil.isNotBlank(data.getOpening())) {
                        // 设置好开头
                        question.setOpening(data.getOpening());
                    }

                    // 好结尾
                    if (StrUtil.isNotBlank(data.getEnding())) {
                        // 设置好结尾
                        question.setEnding(data.getEnding());
                    }

                    // 排序
                    if (ObjectUtil.isNotNull(data.getSort())) {
                        // 设置排序
                        question.setSort(data.getSort());
                    }

                    pgQuestionService.saveOrUpdate(question);
                }
            }
        }
        )).sheet().headRowNumber(1).doRead();

        System.out.println("导入成功");
    }

//    /**
//     * 设置分数
//     */
//    @Test
//    void setScore() {
//        File file = new File("C:\\Users\\<USER>\\Desktop\\作文库素材.xlsx");
//
//        EasyExcel.read(file, ExportExcelDTO.class, new PageReadListener<ExportExcelDTO>(dataList -> {
//            for (ExportExcelDTO data : dataList) {
//
//                if (ObjectUtil.isNotNull(data)) {
//
//                    PgQuestion question = pgQuestionService.getOne(new LambdaQueryWrapper<PgQuestion>()
//                            .eq(PgQuestion::getIsOfficial, true)
//                            .eq(PgQuestion::getSort, data.getSort()));
//
//                    if (ObjectUtil.isNotNull(question)) {
//                        if (ObjectUtil.isNotNull(data.getScore())) {
//                            question.setScore(data.getScore());
//                        }
//
//                        pgQuestionService.updateById(question);
//                    }
//                }
//            }
//
//
//        })).sheet().headRowNumber(1).doRead();
//        System.out.println("导入成功");
//    }

    @Test
    void id() {

        String idStr = IdUtil.getSnowflakeNextIdStr();

        System.out.println(idStr);
    }

    @Autowired
    private SmsService smsService;

    @Test
    void record() {

        // 查今天提交的作文批改
        List<PgAnswer> answers = pgAnswerService.list(new LambdaQueryWrapper<PgAnswer>()
                .between(PgAnswer::getCreateTime, DateUtil.beginOfDay(new Date()), DateUtil.endOfDay(new Date())));

        System.out.println("answers = " + answers);

        for (PgAnswer answer : answers) {
            // 关联明细表
            if (!pgAnswerCostService.exists(new LambdaQueryWrapper<PgAnswerCost>()
                    .eq(PgAnswerCost::getAnswerId, answer.getId()))) {
                PgAnswerCost answerCost = new PgAnswerCost();

                answerCost.setAnswerId(answer.getId());
                answerCost.setUserId(answer.getUserId());
                answerCost.setCreateTime(answer.getCreateTime());
                pgAnswerCostService.save(answerCost);
            }
        }
        System.out.println("OK");
    }

    @Test
    void statistic() {
        // 今天的新用户 id List
        List<Long> newUserIds = pgUsersService.list(new LambdaQueryWrapper<PgUsers>()
                        .between(PgUsers::getCreateTime, DateUtil.beginOfDay(new Date()), DateUtil.endOfDay(new Date())))
                .stream()
                .map(PgUsers::getId)
                .toList();

        // 查看今天有多少新用户
        log.info("今天有 {} 位新用户", newUserIds.size());

        // 今天提交作文的用户 包含重复的
        List<Long> submitUserIds = pgAnswerCostService.list(new LambdaQueryWrapper<PgAnswerCost>()
                        .between(PgAnswerCost::getCreateTime, DateUtil.beginOfDay(new Date()), DateUtil.endOfDay(new Date())))
                .stream()
                .map(PgAnswerCost::getUserId)
                .toList();

        // 有多少新用户提交作文 交集
        long newUserSubmit = newUserIds.stream().filter(submitUserIds::contains).count();

        log.info("有 {} 位新用户提交作文", newUserSubmit);

        // 统计今天有多少用户提交作文，去重
        int userSubmit = CollUtil.distinct(submitUserIds).size();
        log.info("统计有 {} 位用户提交作文", userSubmit);

        // 今天总共提交多少份作文
        long count = pgAnswerCostService.count(new LambdaQueryWrapper<PgAnswerCost>()
                .between(PgAnswerCost::getCreateTime, DateUtil.beginOfDay(new Date()), DateUtil.endOfDay(new Date()))
        );
        log.info("今天总共提交 {} 份作文", count);

    }
}

