package com.pgb.xcx.controller.zw;

import cn.dev33.satoken.annotation.SaCheckLogin;
import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.codec.Base64;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.img.ImgUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.resource.ResourceUtil;
import cn.hutool.core.util.*;
import cn.hutool.json.JSONUtil;
import cn.hutool.poi.word.PicType;
import cn.hutool.poi.word.Word07Writer;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.alibaba.excel.enums.poi.HorizontalAlignmentEnum;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.pgb.common.core.global.BaseResult;
import com.pgb.common.core.global.GlobalCode;
import com.pgb.common.mybatis.global.PageQuery;
import com.pgb.common.redis.QueueUtils;
import com.pgb.service.db.*;
import com.pgb.service.domain.GlobQueueConstants;
import com.pgb.service.domain.answer.PgAnswer;
import com.pgb.service.domain.answer.answerBatch.PgAnswerBatch;
import com.pgb.service.domain.common.fabric.FabricJson;
import com.pgb.service.domain.export.PgExportRecord;
import com.pgb.service.domain.homework.PgHomework;
import com.pgb.service.domain.question.PgQuestion;
import com.pgb.service.domain.question.zwEssay.ZwEssayQuestion;
import com.pgb.service.domain.question.zwEssay.ZwEssayTypeEnum;
import com.pgb.service.domain.question.zwEssay.ZwTextDiff;
import com.pgb.service.domain.student.PgStudent;
import com.pgb.service.enums.*;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.common.usermodel.PictureType;
import org.apache.poi.openxml4j.exceptions.InvalidFormatException;
import org.apache.poi.util.Units;
import org.apache.poi.xwpf.usermodel.*;
import org.apache.xmlbeans.XmlCursor;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.*;
import java.net.URL;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * Created by 2024/9/13 11:48
 */
@Tag(name = "用户端/批改/导出")
@RestController("UserRecordExportController")
@Transactional(rollbackFor = Exception.class)
@RequestMapping("/user/record/export")
@RequiredArgsConstructor
@Slf4j
public class ExportController {

    private final PgAnswerService pgAnswerService;

    private final PgExportRecordService pgExportRecordService;

    private final PgHomeworkService pgHomeworkService;

    private final PgStudentService pgStudentService;

    private final PgAnswerBatchService pgAnswerBatchService;

    @Operation(summary = "导出word")
    @GetMapping("word/{id}")
    public ResponseEntity<byte[]> exportWord(@PathVariable Long id) {

        // 获取批改详情
        PgAnswer answer = pgAnswerService.getById(id);

        String fileName;
        // 若备注不为空
        if (StrUtil.isNotBlank(answer.getName())) {
            String name = ReUtil.delAll("[^\\u4e00-\\u9fa5a-zA-Z\\d_《》]", answer.getName());
            fileName = URLEncoder.encode(name + "_" + DateUtil.format(new Date(), "MM-dd") + "_作文批改报告", StandardCharsets.UTF_8);
        } else {
            // 导出的文件名
            fileName = URLEncoder.encode(DateUtil.format(new Date(), "MM-dd") + "_作文批改报告", StandardCharsets.UTF_8);
        }

        File exportWord = pgExportRecordService.getExportWord(answer, fileName);

        // 构建导出所需参数
        HttpHeaders headers = new HttpHeaders();
        headers.set("Access-Control-Expose-Headers", "Content-Disposition"); // 必须设置，否则无法获取文件名
        headers.set("Content-Disposition", "attachment;filename=" + exportWord.getName());
        headers.set("Content-Type", "application/vnd.openxmlformats-officedocument.wordprocessingml.document");

        // 读取文件
        byte[] wordBytes = FileUtil.readBytes(exportWord);

        // 删除临时文件
        FileUtil.del(exportWord);

        return ResponseEntity.ok()
                .headers(headers)
                .contentType(MediaType.valueOf("application/vnd.openxmlformats-officedocument.wordprocessingml.document"))
                .body(wordBytes);
    }

//    @Operation(summary = "导出全文润色")
//    @GetMapping("polish/{id}")
//    public ResponseEntity<byte[]> exportPolish(@PathVariable Long id) {
//
//        // 获取批改详情
//        PgAnswer answer = pgAnswerService.getById(id);
//
//        // 初始化
//        Word07Writer writer = new Word07Writer();
//
//        writer.addText(new Font("宋体", Font.BOLD, 15), "【全文润色】");
//        writer.addText(new Font("宋体", Font.PLAIN, 10), " ");
//
//        String polish = JSONUtil.parseObj(answer.getCorrectResult()).getStr("polish");
//        List<String> polishList = ListUtil.toList(polish.split("\n"));
//
//        if (!polishList.isEmpty()) {
//
//            // 第一段内容
//            String first = polishList.get(0);
//
//            if (first.length() > 20) {
//                // 正文
//                writer.addText(new Font("宋体", Font.PLAIN, 11), "        " + first);
//            } else {
//                // 作文标题
//                writer.addText(ParagraphAlignment.CENTER, new Font("宋体", Font.PLAIN, 13), first);
//            }
//
//            for (int i = 1; i < polishList.size(); i++) {
//                // 构造润色内容
//                writer.addText(new Font("宋体", Font.PLAIN, 11), "        " + polishList.get(i));
//            }
//        }
//
//        // 创建临时文件
//        File tempFile = FileUtil.createTempFile();
//        writer.flush(tempFile);
//        writer.close();
//
//        // 导出的文件名
//        String fileName = URLEncoder.encode(DateUtil.format(new Date(), "yyyy-MM-dd") + "全文润色", StandardCharsets.UTF_8);
//
//        // 构建导出所需参数
//        HttpHeaders headers = new HttpHeaders();
//        headers.set("Access-Control-Expose-Headers", "Content-Disposition"); // 必须设置，否则无法获取文件名
//        headers.set("Content-Disposition", "attachment;filename=" + fileName + ".docx");
//        headers.set("Content-Type", "application/vnd.openxmlformats-officedocument.wordprocessingml.document");
//
//        // 读取文件
//        byte[] wordBytes = FileUtil.readBytes(tempFile);
//
//        // 删除临时文件
//        FileUtil.del(tempFile);
//
//        return ResponseEntity.ok()
//                .headers(headers)
//                .contentType(MediaType.valueOf("application/vnd.openxmlformats-officedocument.wordprocessingml.document"))
//                .body(wordBytes);
//
//    }

    @Operation(summary = "导出全文润色")
    @GetMapping("polish/{id}")
    public ResponseEntity<byte[]> exportPolish(@PathVariable Long id) {

        // 获取批改详情
        PgAnswer answer = pgAnswerService.getById(id);

        ZwEssayQuestion userAnswer = JSONUtil.toBean(JSONUtil.toJsonStr(answer.getAnswer()), ZwEssayQuestion.class);

        // 获取润色内容
        String polish = JSONUtil.parseObj(answer.getCorrectResult()).getStr("polish");

        // 初始化最终导出的 word 文件
        File tempFile = null;

        if (ObjectUtil.isNotNull(userAnswer.getZwType())) {

            if (StrUtil.isNotBlank(polish)) {
                polish = polish.replaceAll("\n\n", "\n");
                List<String> polishList = ListUtil.toList(polish.split("\n"));

                if (!polishList.isEmpty()) {

                    // 语文作文
                    if (userAnswer.getZwType().equals(ZwEssayTypeEnum.Chinese)) {

                        // 【作文润色】 使用模板
                        String templatePath = "https://cdn.pigaibang.com/common/xcx-zw/template/polish_export_template.docx";

                        FileInputStream fis = null;
                        FileOutputStream fos = null;

                        try {
                            InputStream in = new URL(templatePath).openStream();
                            File tempFile1 = FileUtil.createTempFile();
                            FileUtil.writeFromStream(in, tempFile1);
                            tempFile1.deleteOnExit();
                            fis = new FileInputStream(tempFile1);

                            XWPFDocument doc = new XWPFDocument(fis);

                            // 替换模板中的占位符
                            // 占位符
                            String placeholder = "polish";
                            String replacement = polishList.get(0);

                            // 替换占位符并记录第一个匹配的段落
                            XWPFParagraph firstMatchedParagraph = null;
                            for (XWPFParagraph paragraph : doc.getParagraphs()) {
                                for (XWPFRun run : paragraph.getRuns()) {
                                    // 获取文档中的占位符 从头开始获取
                                    String text = run.getText(0);
                                    if (ObjectUtil.isNotNull(text) && text.contains(placeholder)) {
                                        text = text.replace(placeholder, replacement);

                                        // 作文格长度
                                        int totalLength = 20;
                                        // 文本长度
                                        int textLength = text.length();

                                        // 有作文题目
                                        if (textLength < 15) {

                                            // 需要的总空格数
                                            int totalSpace = totalLength - textLength;
                                            // 前导空格
                                            int leadingSpaces = (int) Math.ceil(totalSpace / 2.0);
                                            StringBuilder builder = new StringBuilder();
                                            for (int i = -1; i < leadingSpaces; i++) {
                                                builder.append("  ");
                                            }
                                            run.setText(builder + text, 0);
                                        }
                                        // 没有作文题目时
                                        else {
                                            run.setText("    " + text, 0);
                                        }
                                        if (ObjectUtil.isNull(firstMatchedParagraph)) {
                                            firstMatchedParagraph = paragraph;
                                        }
                                    }
                                }
                            }

                            // 将光标移动到下一个段落  因为光标生成在段落最开始位置
                            XmlCursor xmlCursor = firstMatchedParagraph.getCTP().newCursor();
                            xmlCursor.toNextSibling();

                            // 在第一个匹配的段落后添加新段落
                            if (ObjectUtil.isNotNull(firstMatchedParagraph)) {

                                for (int i = 1; i < polishList.size(); i++) {

                                    // 从光标位置开始插入段落
                                    XWPFParagraph newParagraph = doc.insertNewParagraph(xmlCursor);

                                    XWPFRun newRun = newParagraph.createRun();
//                        log.info("内容:{}", polishList.get(i));
                                    newRun.setText(polishList.get(i));
                                    // 设置首行缩进两字符
                                    newParagraph.setIndentationFirstLine(720);

                                    xmlCursor = newParagraph.getCTP().newCursor();
                                    xmlCursor.toNextSibling();
                                }
                            }

                            // 创建临时文件
                            tempFile = FileUtil.createTempFile();
                            fos = new FileOutputStream(tempFile);
                            doc.write(fos);
                            doc.close();

                        } catch (IOException e) {
                            throw new RuntimeException(e);
                        } finally {
                            if (fis != null) {
                                try {
                                    fis.close();
                                } catch (IOException e) {
                                    e.printStackTrace();
                                }
                            }
                            if (fos != null) {
                                try {
                                    fos.close();
                                } catch (IOException e) {
                                    e.printStackTrace();
                                }
                            }
                        }
                    }
                    // 英语作文
                    else {
                        // 初始化
                        Word07Writer writer = new Word07Writer();

                        writer.addText(new Font("宋体", Font.BOLD, 15), "【全文润色】");
                        writer.addText(new Font("宋体", Font.PLAIN, 10), " ");

                        // 第一段内容
                        String first = polishList.get(0);

                        if (first.length() > 20) {
                            // 正文
                            writer.addText(new Font("宋体", Font.PLAIN, 11), "        " + first);
                        } else {
                            // 作文标题
                            writer.addText(ParagraphAlignment.CENTER, new Font("宋体", Font.PLAIN, 13), first);
                        }

                        for (int i = 1; i < polishList.size(); i++) {
                            // 构造润色内容
                            writer.addText(new Font("宋体", Font.PLAIN, 11), "        " + polishList.get(i));
                        }

                        // 创建临时文件
                        tempFile = FileUtil.createTempFile();
                        writer.flush(tempFile);
                        writer.close();
                    }
                }
            }
        }

        // 导出的文件名
        String fileName = URLEncoder.encode(DateUtil.format(new Date(), "yyyy-MM-dd") + "全文润色", StandardCharsets.UTF_8);

        // 构建导出所需参数
        HttpHeaders headers = new HttpHeaders();
        headers.set("Access-Control-Expose-Headers", "Content-Disposition"); // 必须设置，否则无法获取文件名
        headers.set("Content-Disposition", "attachment;filename=" + fileName + ".docx");
        headers.set("Content-Type", "application/vnd.openxmlformats-officedocument.wordprocessingml.document");

        // 读取文件
        byte[] wordBytes = FileUtil.readBytes(tempFile);

        return ResponseEntity.ok()
                .headers(headers)
                .contentType(MediaType.valueOf("application/vnd.openxmlformats-officedocument.wordprocessingml.document"))
                .body(wordBytes);

    }


    @Operation(summary = "导出润色对比")
    @GetMapping("compare/{id}")
    public ResponseEntity<byte[]> exportCompare(@PathVariable Long id) {

        // 获取批改详情
        PgAnswer answer = pgAnswerService.getById(id);

        // 初始化
        Word07Writer writer = new Word07Writer();

        writer.addText(new Font("宋体", Font.BOLD, 15), "【原文对比】");
        writer.addText(new Font("宋体", Font.PLAIN, 10), " ");

        ZwEssayQuestion userAnswer = JSONUtil.toBean(answer.getCorrectResult().toString(), ZwEssayQuestion.class);

        if (CollUtil.isNotEmpty(userAnswer.getPolishDiffList())) {
            XWPFParagraph paragraph = writer.getDoc().createParagraph();
            XWPFRun run = paragraph.createRun();
            run.setFontFamily("宋体");
            run.setFontSize(11);

            // 获取全文润色和作文的差异
            List<ZwTextDiff> polishDiffList = userAnswer.getPolishDiffList();

            for (ZwTextDiff diff : polishDiffList) {
                // 根据差异类型设置样式
                String color = "42A5F5"; // 默认绿色
                boolean strikeThrough = false;
                String sentence = "";

                switch (diff.getType()) {
                    case DELETE:
                        color = "FF0000";
                        strikeThrough = true;
                        sentence = diff.getSource();
                        break;
                    case EQUAL:
                        color = "000000";
                        sentence = diff.getTarget();
                        break;
                    case INSERT, CHANGE:
                        color = "42A5F5";
                        sentence = diff.getTarget();
                        break;
                    default:
                        break;
                }
                // 处理文本中的匹配项
                // 创建一个新的 XWPFRun 来处理匹配的部分
                XWPFRun styledRun = paragraph.createRun();
                styledRun.setText(sentence);
                styledRun.setFontFamily("宋体");
                styledRun.setColor(color);
                styledRun.setStrikeThrough(strikeThrough);

                if (StrUtil.endWith(sentence, "\n")) {
                    // 换行
                    styledRun.addBreak();
                }
            }
        }

        // 创建临时文件
        File tempFile = FileUtil.createTempFile();
        writer.flush(tempFile);
        writer.close();

        // 导出的文件名
        String fileName = URLEncoder.encode(DateUtil.format(new Date(), "yyyy-MM-dd") + "全文润色", StandardCharsets.UTF_8);
        // 构建导出所需参数
        HttpHeaders headers = new HttpHeaders();
        headers.set("Access-Control-Expose-Headers", "Content-Disposition"); // 必须设置，否则无法获取文件名
        headers.set("Content-Disposition", "attachment;filename=" + fileName + ".docx");
        headers.set("Content-Type", "application/vnd.openxmlformats-officedocument.wordprocessingml.document");

        // 读取文件
        byte[] wordBytes = FileUtil.readBytes(tempFile);

        // 删除临时文件
        FileUtil.del(tempFile);

        return ResponseEntity.ok()
                .headers(headers)
                .contentType(MediaType.valueOf("application/vnd.openxmlformats-officedocument.wordprocessingml.document"))
                .body(wordBytes);

    }

    public record BatchExportForm(List<Long> ids, DurationEnum duration) {
    }

    @Operation(summary = "批量导出word")
    @PostMapping("batch/exec")
    @SaCheckLogin
    public BaseResult<PgExportRecord> batchExport(@RequestBody BatchExportForm form) {

        if (ObjectUtil.isNull(form.duration)) {
            return BaseResult.error(GlobalCode.Error, "请选择时间区间");
        }

        // 使用的ids
        List<Long> ids;

        // 实际的answerIds
        List<Long> answerIds;

        // 自定义
        // 判断时间区间
        if (form.duration.equals(DurationEnum.Custom)) {
            ids = form.ids;
        }
        // 固定区间
        else {
            answerIds = pgAnswerService.list(new LambdaQueryWrapper<PgAnswer>()
                            .select(PgAnswer::getId)
                            .eq(PgAnswer::getUserId, StpUtil.getLoginIdAsLong())
                            // 批改完成
                            .eq(PgAnswer::getStatus, CorrectStatusEnum.Corrected)
                            // 今天
                            .between(form.duration.equals(DurationEnum.Today), PgAnswer::getCreateTime,
                                    DateUtil.beginOfDay(new Date()), DateUtil.endOfDay(new Date())
                            )
                            // 昨天
                            .between(form.duration.equals(DurationEnum.Yesterday), PgAnswer::getCreateTime,
                                    DateUtil.beginOfDay(DateUtil.offsetDay(new Date(), -1)),
                                    DateUtil.endOfDay(DateUtil.offsetDay(new Date(), -1)))
                            // 根据时间降序排序
                            .orderByDesc(PgAnswer::getCreateTime)
                            // 不能删除
                            .and(i -> i.ne(PgAnswer::getDeleted, true)
                                    .or()
                                    .isNull(PgAnswer::getDeleted)
                            )
                    )
                    .stream()
                    .map(PgAnswer::getId)
                    .toList();

            ids = answerIds;
        }

        // 先查缓存有没有
        // md5 唯一值
        if (ObjectUtil.isEmpty(ids)) {
            return BaseResult.code(GlobalCode.Item_Null);
        }

        String md5 = pgAnswerService.getImgMd5(ids, StpUtil.getLoginIdAsLong());

        PgExportRecord record = pgExportRecordService.getOne(new LambdaQueryWrapper<PgExportRecord>()
                .eq(PgExportRecord::getUserId, StpUtil.getLoginIdAsLong())
                .eq(PgExportRecord::getMd5, md5)
                .and(i -> i.eq(PgExportRecord::getType, 0)
                        .or()
                        .isNull(PgExportRecord::getType))
                .last("LIMIT 1")
        );

        // 有记录
        if (ObjectUtil.isNotNull(record)) {
            // 如果处于导出中，直接提示
            // if (!record.getStatus().equals(ExportStatusEnum.Completed)) {
            //     return BaseResult.error(GlobalCode.Limit_Over, "正在导出中");
            // }

            log.info("存在导出记录: {}", record.getZipUrl());

            record.setCreateTime(new Date());
            pgExportRecordService.updateById(record);

            return BaseResult.success(record);
        }

        // 没记录，插入记录
        record = new PgExportRecord();
        record.setUserId(StpUtil.getLoginIdAsLong());
        record.setTotalNum(ids.size());
        record.setCreateTime(new Date());
        record.setMd5(md5);
        record.setStatus(ExportStatusEnum.Queuing);
        record.setAnswerIds(CollUtil.join(ids, ";"));
        record.setType(0);
        // 名称默认时间信息
        record.setName(DateUtil.format(new Date(), "yyyy-MM-dd") + "—批改报告导出");
        record.setType(0);
        pgExportRecordService.save(record);

        // 发送队列请求
        QueueUtils.addQueueObjectInTransaction(GlobQueueConstants.PGB_XCX_BATCH_EXPORT_WORD_QUEUE.name(), record.getId());

        // 队列请求成功后 再返回
        return BaseResult.success(record);
    }

    @Operation(summary = "分页查询导出记录")
    @PostMapping("batch/record")
    @SaCheckLogin
    public BaseResult<IPage<PgExportRecord>> getExportRecord(@RequestBody PageQuery query) {
        IPage<PgExportRecord> page = pgExportRecordService.page(query.toMpPageSortByCreateTime(), new LambdaQueryWrapper<PgExportRecord>()
                .eq(PgExportRecord::getUserId, StpUtil.getLoginIdAsLong())
        );

        return BaseResult.success(page);
    }

    public record updateRecordForm(String name) {
    }

    @Operation(summary = "修改导出记录备注名称")
    @PostMapping("updateName/{recordId}")
    @SaCheckLogin
    public BaseResult<Boolean> updateName(@PathVariable Long recordId, @RequestBody updateRecordForm form) {

        PgExportRecord record = pgExportRecordService.getById(recordId);

        if (ObjectUtil.isNull(record)) {
            return BaseResult.code(GlobalCode.Item_Null);
        }

        record.setName(form.name());

        return BaseResult.success(
                pgExportRecordService.updateById(record)
        );
    }


    @Operation(summary = "查看导出状态")
    @GetMapping("status/{id}")
    @SaCheckLogin
    public BaseResult<PgExportRecord> getStatus(@PathVariable Long id) {

        PgExportRecord record = pgExportRecordService.getById(id);

        if (ObjectUtil.isNull(record)) {
            return BaseResult.code(GlobalCode.Item_Null);
        }

        return BaseResult.success(record);
    }

    public record ExportSelectData(List<ExportSelectEnum> select) {
    }

    @Deprecated(since = "暂时不用")
    @Operation(summary = "自定义导出word")
    @PostMapping("customExport/{id}")
    public ResponseEntity<byte[]> customExport(@PathVariable Long id, @RequestBody ExportSelectData data) {

        // 获取批改详情
        PgAnswer answer = pgAnswerService.getById(id);

        ZwEssayQuestion userAnswer = JSONUtil.toBean(answer.getCorrectResult().toString(), ZwEssayQuestion.class);

        String fileName;

        // 若备注不为空
        if (StrUtil.isNotBlank(userAnswer.getName())) {
            fileName = URLEncoder.encode(userAnswer.getName() + "_" + DateUtil.format(new Date(), "MM-dd") + "_作文批改报告", StandardCharsets.UTF_8);
        } else {
            // 导出的文件名
            fileName = URLEncoder.encode(DateUtil.format(new Date(), "MM-dd") + "_作文批改报告", StandardCharsets.UTF_8);
        }

        // 初始化
        Word07Writer writer = new Word07Writer();

        // 定义字体
        Font spaceFont = new Font("宋体", Font.PLAIN, 10);
        Font standardFont = new Font("宋体", Font.PLAIN, 11);
        Font quesFont = new Font("宋体", Font.PLAIN, 12);
        Font boldFont = new Font("宋体", Font.BOLD, 15);

        BufferedImage markImg = ImgUtil.read(
                ResourceUtil.getResourceObj("correct/title.png")
        );

        // 【作文批改报告】
        XWPFParagraph paragraph = writer.getDoc().createParagraph();
        XWPFRun run = paragraph.createRun();
        try {
            run.addPicture(ImgUtil.toStream(markImg, ImgUtil.IMAGE_TYPE_PNG), PictureType.PNG, "title.png", Units.toEMU(30), Units.toEMU(30));
        } catch (InvalidFormatException | IOException e) {
            throw new RuntimeException(e);
        }
        run.setText("作文批改报告");
        run.setFontSize(23);
        run.setBold(true);
        run.setFontFamily("宋体");
        paragraph.setAlignment(ParagraphAlignment.CENTER);

        // 作文分数
        Integer userScore = userAnswer.getUserScore();
//        Integer score = 90;
        if (ObjectUtil.isNotNull(userScore)) {
            // 中等
            String scoreUrl = "https://cdn.pigaibang.com/common/xcx-zw/grade/3.png";
            if (userScore >= 90) {
                // 优
                scoreUrl = "https://cdn.pigaibang.com/common/xcx-zw/grade/1.png";
            } else if (userScore >= 80) {
                // 良
                scoreUrl = "https://cdn.pigaibang.com/common/xcx-zw/grade/2.png";
            }

            writer.addPicture(
                    ImgUtil.toStream(ImgUtil.read(URLUtil.url(scoreUrl)), ImgUtil.IMAGE_TYPE_PNG),
                    PicType.PNG, fileName, 30, 30, ParagraphAlignment.RIGHT);
        }
        writer.addText(spaceFont, " ");

        PgQuestion question = userAnswer.getRequire();

        // 获取作文题目相关信息
        if (ObjectUtil.isNotNull(question)) {

            // 题目
            if (ObjectUtil.isNotNull(question.getName())) {
                writer.addText(quesFont, "题目：" + question.getName());
                writer.addText(spaceFont, " ");
            }

            // 写作要求
            if (ObjectUtil.isNotNull(question.getWritingRequest())) {
                writer.addText(quesFont, "写作要求：");
                List<String> split = StrUtil.split(question.getWritingRequest(), "\n");
                split.forEach(s -> {
                    writer.addText(quesFont, "        " + s);
                });
                writer.addText(spaceFont, " ");
            }

            // 写作文体
            if (ObjectUtil.isNotNull(question.getStyle())) {
                writer.addText(quesFont, "写作文体：" + question.getStyle().desc);
            }
        }

        if (data.select().contains(ExportSelectEnum.Comment)) {
            // 【老师评语内容】
            writer.addText(spaceFont, " ");
            writer.addText(boldFont, "【老师点评】");

            String comment = JSONUtil.parseObj(answer.getCorrectResult()).getStr("comment");
            List<String> commentList = ListUtil.toList(comment.split("\n"));

            // 首段 首行缩进
            for (String s : commentList) {
                // 构造评语内容
                writer.addText(standardFont, "        " + s);
            }

        }

        // 【旁批】
        if (data.select().contains(ExportSelectEnum.Image)) {
            // 插入分页符
//            writer.getDoc().createParagraph().createRun().addBreak(BreakType.PAGE);

            // ----------------图片渲染-----------------
            // markJson
            List<FabricJson> markJsonList = userAnswer.getMarkJsonList();

            // 【旁批】
            writer.addText(boldFont, "【旁批】");

            // 传 高 宽 markJson
            for (int i = 0; i < markJsonList.size(); i++) {
                String imgUrl = userAnswer.getUserImgAnswerList().get(i).getImgUrl();

                BufferedImage img = ImgUtil.read(URLUtil.url(imgUrl));
                writer.addPicture(ImgUtil.toStream(img, ImgUtil.IMAGE_TYPE_PNG), PicType.PNG, fileName, 500, (int) ((500.0 / img.getWidth()) * img.getHeight()));
            }

        }

        // 【作文润色】
        if (data.select().contains(ExportSelectEnum.Polish)) {
            // 插入分页符
            writer.getDoc().createParagraph().createRun().addBreak(BreakType.PAGE);
            writer.addText(boldFont, "【作文润色】");
            writer.addText(spaceFont, " ");

            String polish = JSONUtil.parseObj(answer.getCorrectResult()).getStr("polish");
            List<String> polishList = ListUtil.toList(polish.split("\n"));

            if (!polishList.isEmpty()) {

                // 第一段内容
                String first = polishList.get(0);

                if (first.length() > 20) {
                    // 正文
                    writer.addText(standardFont, "        " + polishList.get(0));
                } else {
                    // 作文标题
                    writer.addText(ParagraphAlignment.CENTER, new Font("宋体", Font.PLAIN, 13), "《" + first + "》");
                }

                for (int i = 1; i < polishList.size(); i++) {
                    // 构造润色内容
                    writer.addText(standardFont, "        " + polishList.get(i));
                }
            }
        }


        //   -----------------------------写作指导 -----------------------------------
        // 当题目不为空且好标题，好词 好句 等其中任一项不为空时才显示”写作指导“

        if (data.select().contains(ExportSelectEnum.Guide)) {

            if (ObjectUtil.isNotNull(question) &&
                    (ObjectUtil.isNotNull(question.getHeadline()) ||
                            ObjectUtil.isNotNull(question.getWord()) ||
                            ObjectUtil.isNotNull(question.getOpening()) ||
                            ObjectUtil.isNotNull(question.getEnding()))) {

                // 插入分页符
                writer.getDoc().createParagraph().createRun().addBreak(BreakType.PAGE);

                writer.addText(ParagraphAlignment.CENTER,
                        new Font("宋体", Font.BOLD, 20),
                        ObjectUtil.isNotNull(question.getName()) ? "《" + question.getName() + "》写作指导" : "写作指导"
                );


                // 【好标题/选材】
                if (ObjectUtil.isNotNull(question.getHeadline())) {
                    writer.addText(spaceFont, " ");
                    writer.addText(boldFont, "【好标题/选材】");
                    writer.addText(spaceFont, " ");
                    List<String> headlineList = ListUtil.toList(question.getHeadline().split("\n"));

                    for (String headline : headlineList) {
                        // 构造好标题/选材内容
                        writer.addText(standardFont, headline);
                    }
                }

                // 【好词语】
                if (ObjectUtil.isNotNull(question.getWord())) {
                    writer.addText(spaceFont, " ");
                    writer.addText(boldFont, "【好词语】");
                    writer.addText(spaceFont, " ");
                    List<String> wordList = ListUtil.toList(question.getWord().split("\n"));

                    for (String word : wordList) {
                        // 构造好词语内容
                        writer.addText(standardFont, word);
                    }
                }

                // 【好开头】
                if (ObjectUtil.isNotNull(question.getOpening())) {
                    writer.addText(spaceFont, " ");
                    writer.addText(boldFont, "【好开头】");
                    writer.addText(spaceFont, " ");
                    List<String> beginningList = ListUtil.toList(question.getOpening().split("\n"));

                    for (String beginning : beginningList) {

                        if (!beginning.trim().isEmpty()) {
                            // 构造好开头内容
                            writer.addText(standardFont, "⭐ " + beginning);
                        }
                    }
                }

                // 【好结尾】
                if (ObjectUtil.isNotNull(question.getEnding())) {
                    writer.addText(spaceFont, " ");
                    writer.addText(boldFont, "【好结尾】");
                    writer.addText(spaceFont, " ");
                    List<String> endingList = ListUtil.toList(question.getEnding().split("\n"));

                    for (String ending : endingList) {

                        if (!ending.trim().isEmpty()) {
                            // 构造好结尾内容
                            writer.addText(standardFont, "⭐ " + ending);
                        }
                    }
                }
            }
        }

        // 创建临时文件
        File tempFile = FileUtil.createTempFile();
        writer.flush(tempFile);
        writer.close();

        // 构建导出所需参数
        HttpHeaders headers = new HttpHeaders();
        headers.set("Access-Control-Expose-Headers", "Content-Disposition"); // 必须设置，否则无法获取文件名
        headers.set("Content-Disposition", "attachment;filename=" + fileName + ".docx");
        headers.set("Content-Type", "application/vnd.openxmlformats-officedocument.wordprocessingml.document");

        // 读取文件
        byte[] wordBytes = FileUtil.readBytes(tempFile);

        // 删除临时文件
        FileUtil.del(tempFile);

        return ResponseEntity.ok()
                .headers(headers)
                .contentType(MediaType.valueOf("application/vnd.openxmlformats-officedocument.wordprocessingml.document"))
                .body(wordBytes);
    }

    @Operation(summary = "导出班级作业的学生批改报告")
    @GetMapping("export/{homeworkId}")
    public BaseResult<PgExportRecord> export(@PathVariable Long homeworkId) {

        // 获取作业
        PgHomework homework = pgHomeworkService.getById(homeworkId);

        if (ObjectUtil.isNull(homework)) {
            return BaseResult.error("作业不存在");
        }

        // 获取班级学生
        List<Long> studentIds = pgStudentService.list(new LambdaQueryWrapper<PgStudent>()
                        .eq(PgStudent::getClassId, homework.getClassId()))
                .stream()
                .map(PgStudent::getId)
                .toList();

        if (studentIds.isEmpty()) {
            return BaseResult.error("班级没有关联学生！");
        }

        List<Long> answerIds = pgAnswerService.list(new LambdaQueryWrapper<PgAnswer>()
                        .select(PgAnswer::getId)
                        .eq(PgAnswer::getDeleted, false)
                        .eq(PgAnswer::getHomeworkId, homeworkId)
                        .in(PgAnswer::getStudentId, studentIds)
                        // 批改完成
                        .ne(PgAnswer::getStatus, CorrectStatusEnum.Uploaded))
                .stream()
                .map(PgAnswer::getId)
                .toList();

        if (answerIds.isEmpty()) {
            return BaseResult.error("作业还未批改完成，请稍等");
        }

        String md5 = pgAnswerService.getImgMd5(answerIds, StpUtil.getLoginIdAsLong());

        PgExportRecord record = pgExportRecordService.getOne(new LambdaQueryWrapper<PgExportRecord>()
                .eq(PgExportRecord::getUserId, StpUtil.getLoginIdAsLong())
                .eq(PgExportRecord::getMd5, md5)
                .and(i -> i.eq(PgExportRecord::getType, 0)
                        .or()
                        .isNull(PgExportRecord::getType))
                .last("LIMIT 1"));

        // 有记录
        if (ObjectUtil.isNotNull(record)) {

            log.info("存在导出记录: {}", record.getZipUrl());

            record.setCreateTime(new Date());
            pgExportRecordService.updateById(record);

            return BaseResult.success(record);
        }
        // 没记录，插入记录
        record = new PgExportRecord();
        record.setHomeworkId(homeworkId);
        record.setUserId(StpUtil.getLoginIdAsLong());
        record.setTotalNum(answerIds.size());
        record.setCreateTime(new Date());
        record.setMd5(md5);
        record.setStatus(ExportStatusEnum.Queuing);
        record.setAnswerIds(CollUtil.join(answerIds, ";"));
        record.setType(0);
        // 名称默认时间信息
        record.setName(DateUtil.format(new Date(), "yyyy-MM-dd") + "—批改报告导出");
        record.setType(0);
        pgExportRecordService.save(record);

        // 发送队列请求
        QueueUtils.addQueueObjectInTransaction(GlobQueueConstants.PGB_XCX_BATCH_EXPORT_WORD_QUEUE.name(), record.getId());

        // 队列请求成功后 再返回
        return BaseResult.success(record);

    }

    @Operation(summary = "根据批次导出批改报告")
    @GetMapping("batch/{batchId}")
    @SaCheckLogin
    public BaseResult<PgExportRecord> exportBatch(@PathVariable Long batchId) {

        PgAnswerBatch batch = pgAnswerBatchService.getById(batchId);

        if (ObjectUtil.isNull(batch)) {
            return BaseResult.code(GlobalCode.Item_Null);
        }

        List<Long> answerIds = pgAnswerService.list(new LambdaQueryWrapper<PgAnswer>()
                        .select(PgAnswer::getId)
                        .eq(PgAnswer::getDeleted, false)
                        .eq(PgAnswer::getBatchId, batchId)
                        // 批改完成
                        .eq(PgAnswer::getStatus, CorrectStatusEnum.Corrected)
                        .orderByDesc(PgAnswer::getCreateTime))
                .stream()
                .map(PgAnswer::getId)
                .toList();

        if (answerIds.isEmpty()) {
            return BaseResult.error("作业还未批改完成，请稍等");
        }

        String md5 = pgAnswerService.getImgMd5(answerIds, StpUtil.getLoginIdAsLong());

        PgExportRecord record = pgExportRecordService.getOne(new LambdaQueryWrapper<PgExportRecord>()
                .eq(PgExportRecord::getUserId, batch.getUserId())
                .eq(PgExportRecord::getMd5, md5)
                .and(i -> i.eq(PgExportRecord::getType, 0)
                        .or()
                        .isNull(PgExportRecord::getType))
                .last("LIMIT 1"));

        // 有记录
        if (ObjectUtil.isNotNull(record)) {

            log.info("存在导出记录: {} : {}", record.getId(), record.getZipUrl());

            record.setCreateTime(new Date());
            pgExportRecordService.updateById(record);

            return BaseResult.success(record);
        }
        // 没记录，插入记录
        record = new PgExportRecord();
        record.setUserId(batch.getUserId());
        record.setTotalNum(answerIds.size());
        record.setCreateTime(new Date());
        record.setMd5(md5);
        record.setStatus(ExportStatusEnum.Queuing);
        record.setAnswerIds(CollUtil.join(answerIds, ";"));
        record.setType(0);
        // 名称默认时间信息
        record.setName(DateUtil.format(new Date(), "yyyy-MM-dd") + "—批改报告导出");
        record.setType(0);
        pgExportRecordService.save(record);

        // 发送队列请求
        QueueUtils.addQueueObjectInTransaction(GlobQueueConstants.PGB_XCX_BATCH_EXPORT_WORD_QUEUE.name(), record.getId());

        // 队列请求成功后 再返回
        return BaseResult.success(record);

    }


    @Deprecated
    @Operation(summary = "根据批次导出全部批改报告", description = "导出到一个word中")
    @GetMapping("exportAll/{batchId}")
    public ResponseEntity<byte[]> exportAll(@PathVariable Long batchId) {

        List<Long> answerIds = pgAnswerService.list(new LambdaQueryWrapper<PgAnswer>()
                        .select(PgAnswer::getId)
                        .eq(PgAnswer::getDeleted, false)
                        .eq(PgAnswer::getBatchId, batchId)
                        // 批改完成
                        .eq(PgAnswer::getStatus, CorrectStatusEnum.Corrected))
                .stream()
                .map(PgAnswer::getId)
                .toList();

        File doc = pgExportRecordService.getExportOneDoc(answerIds, null);

        String fileName = URLEncoder.encode(DateUtil.format(new Date(), "yyyy-MM-dd") + "总批改报告", StandardCharsets.UTF_8);

        // 构建导出所需参数
        HttpHeaders headers = new HttpHeaders();
        headers.set("Access-Control-Expose-Headers", "Content-Disposition"); // 必须设置，否则无法获取文件名
        headers.set("Content-Disposition", "attachment;filename=" + fileName + ".docx");
        headers.set("Content-Type", "application/vnd.openxmlformats-officedocument.wordprocessingml.document");

        // 读取文件
        byte[] wordBytes = FileUtil.readBytes(doc);

        // 删除临时文件
        FileUtil.del(doc);

        return ResponseEntity.ok()
                .headers(headers)
                .contentType(MediaType.valueOf("application/vnd.openxmlformats-officedocument.wordprocessingml.document"))
                .body(wordBytes);
    }


    @Schema(title = "导出excel表头")
    @Data
    @ColumnWidth(30)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER)
    public static class ReportExcel {

        @ExcelProperty("学生学号")
        private String studentNo;

        @ExcelProperty("学生姓名")
        private String name;

        @ExcelProperty("分数")
        private Integer score;

//        @ExcelProperty("旁批")
//        private String answerImg;

        @ExcelProperty("老师总评")
        private String overallComment;

        @ExcelProperty("详细点评")
        private String comment;

        @ExcelProperty("作文润色")
        private String polish;

    }


    @Data
    public static class ExportColumns {

        @Schema(title = "用户选择导出的列")
        private List<CorrectResultEnum> selectedColumns;
    }

    @Operation(summary = "导出批改报告excel", description = "指定列导出")
    @PostMapping("excel/{homeworkId}")
    public void exportExcel(HttpServletResponse response, @PathVariable Long homeworkId, @RequestBody ExportColumns columns) throws IOException {

        // 获取作业
        PgHomework homework = pgHomeworkService.getById(homeworkId);

        if (ObjectUtil.isNull(homework)) {
            return;
        }

        // 导出
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");
        response.setHeader("Access-Control-Expose-Headers", "Content-Disposition"); // 必须设置，否则无法获取文件名
        response.setHeader("Access-Control-Allow-Origin", "*");

        String fileName = URLEncoder.encode(homework.getName() + "—批改报告表", StandardCharsets.UTF_8);
        response.setHeader("Content-Disposition", "attachment;filename=" + fileName + ".xlsx");

        // 选择导出的列
        List<String> columnNames = new ArrayList<>(columns.getSelectedColumns().stream()
                .map(item -> item.value)
                .toList());

        // 默认导出学生学号，姓名
//        columnNames.add(0, CorrectResultEnum.Student_No.value);
//        columnNames.add(1, CorrectResultEnum.Name.value);

        // 执行导出操作
        EasyExcel.write(response.getOutputStream(), ReportExcel.class)
                .head(head(columns))
                .sheet("表格一")
                // 只导出选择的头
                .includeColumnFieldNames(columnNames)
//                .registerWriteHandler()
                .doWrite(data(homeworkId, columns));
    }

    @Operation(summary = "导出批改报告excel base64", description = "指定列导出")
    @PostMapping("excel/base64/{homeworkId}")
    public BaseResult<String> exportExcel(@PathVariable Long homeworkId, @RequestBody ExportColumns columns) {

        // 获取作业
        PgHomework homework = pgHomeworkService.getById(homeworkId);

        if (ObjectUtil.isNull(homework)) {
            return BaseResult.error("作业不存在");
        }
        String fileName = URLEncoder.encode(homework.getName() + "—批改报告表", StandardCharsets.UTF_8);

        try (ByteArrayOutputStream out = new ByteArrayOutputStream()) {
            // 选择导出的列
            List<String> columnNames;
            // 默认给全部
            if (columns.getSelectedColumns().isEmpty()) {
                columnNames = Arrays.stream(CorrectResultEnum.values())
                        .map(item -> item.value)
                        .toList();
                columns.setSelectedColumns(Arrays.asList(CorrectResultEnum.values()));
            } else {
                columnNames = new ArrayList<>(columns.getSelectedColumns().stream()
                        .map(item -> item.value)
                        .toList());
            }

            EasyExcel.write(out, ReportExcel.class)
                    .head(head(columns))
                    .sheet("表格一")
                    // 只导出选择的头
                    .includeColumnFieldNames(columnNames)
                    .doWrite(data(homeworkId, columns));

            return BaseResult.success(
                    fileName,
                    Base64.encode(out.toByteArray())
            );
        } catch (IOException e) {
            throw new RuntimeException("生成Excel失败", e);
        }
    }

    /**
     * 自定义头
     *
     * @param columns
     * @return
     */
    private List<List<String>> head(ExportColumns columns) {

        List<List<String>> list = new ArrayList<>();

//        // 添加默认列
//        List<String> studentNoHead = new ArrayList<>();
//        studentNoHead.add(CorrectResultEnum.Student_No.desc);
//        list.add(studentNoHead);
//
//        List<String> nameHead = new ArrayList<>();
//        nameHead.add(CorrectResultEnum.Name.desc);
//        list.add(nameHead);

        // 根据用户选择的列的顺序
        columns.getSelectedColumns().forEach(item -> {
            List<String> head = new ArrayList<>();

            // 自定义头
            head.add(item.desc);
            list.add(head);
        });

        return list;
    }


    /**
     * 数据处理
     *
     * @param homeworkId
     * @param
     * @return
     */
    private List<List<Object>> data(Long homeworkId, ExportColumns columns) {

        // 每一行
        List<List<Object>> rowList = new ArrayList<>();

        // 根据homeworkId查answers
        List<PgAnswer> answers = pgAnswerService.list(new LambdaQueryWrapper<PgAnswer>()
                .eq(PgAnswer::getHomeworkId, homeworkId)
                .eq(PgAnswer::getDeleted, false));

        for (PgAnswer answer : answers) {

            ZwEssayQuestion userAnswer = JSONUtil.toBean(answer.getCorrectResult().toString(), ZwEssayQuestion.class);

            // 初始化列
            List<Object> column = new ArrayList<>();

            PgStudent student = pgStudentService.getById(answer.getStudentId());

            for (CorrectResultEnum columnName : columns.getSelectedColumns()) {

                switch (columnName) {

                    case Student_No:
                        if (ObjectUtil.isNotNull(student)) {
                            column.add(StrUtil.isNotBlank(student.getStudentNo()) ? student.getStudentNo() : "");
                        } else {
                            column.add("");
                        }
                        break;
                    case Name:
                        column.add(StrUtil.isNotBlank(answer.getName()) ? answer.getName() : "");
                        break;
                    case Score:
                        column.add(ObjectUtil.isNotNull(userAnswer.getUserScore()) ? userAnswer.getUserScore() : "");
                        break;
//                    case Answer_Img:
//
//                        if (ObjectUtil.isNotNull(userAnswer.getMarkJsonList())) {
//
//                            for (int i = 0; i < userAnswer.getMarkJsonList().size(); i++) {
//
//                                String imgUrl = userAnswer.getUserImgAnswerList().get(i).getImgUrl();
//
//                                BufferedImage img = ImgUtil.read(URLUtil.url(imgUrl));
//                                column.add(ImgUtil.toStream(img, ImgUtil.IMAGE_TYPE_PNG));
//                            }
//                        } else {
//                            column.add("");
//                        }
//                        break;
//                    case Answer_Img:
//                        if (ObjectUtil.isNotNull(userAnswer.getMarkJsonList())) {
//
//                            // 创建 WriteCellData 对象
//                            WriteCellData<Void> writeCellData = new WriteCellData<>();
//                            // 设置为 EMPTY 表示不需要其他数据
//                            writeCellData.setType(CellDataTypeEnum.EMPTY);
//
//                            // 将 ImageData 添加到 WriteCellData
//                            List<ImageData> imageDataList = new ArrayList<>();
//
//                            for (int i = 0; i < userAnswer.getMarkJsonList().size(); i++) {
//                                String imgUrl = userAnswer.getUserImgAnswerList().get(i).getImgUrl();
//                                try (InputStream inputStream = new URL(imgUrl).openStream()) {
//                                    byte[] imageBytes = IOUtils.toByteArray(inputStream);
//
//                                    // 创建 ImageData 对象
//                                    ImageData imageData = new ImageData();
//                                    imageData.setImage(imageBytes);
//                                    imageData.setImageType(ImageData.ImageType.PICTURE_TYPE_PNG);
//                                    imageData.setTop(5);
//                                    imageData.setRight(40);
//                                    imageData.setBottom(5);
//                                    imageData.setLeft(5);
//
//                                    // 设置图片的位置
//                                    imageData.setRelativeFirstRowIndex(0);
//                                    imageData.setRelativeFirstColumnIndex(0);
//                                    imageData.setRelativeLastRowIndex(0);
//                                    imageData.setRelativeLastColumnIndex(i + 1); // 根据需要调整
//
//                                    // 图片嵌入单元格 大小跟着单元格动
//                                    imageData.setAnchorType(ClientAnchorData.AnchorType.MOVE_AND_RESIZE);
//
//                                    imageDataList.add(imageData);
//                                } catch (IOException e) {
//                                    log.error("导出旁批图片失败: {}", imgUrl, e);
//                                    column.add("");
//                                }
//                            }
//                            writeCellData.setImageDataList(imageDataList);
//
//                            // 将 WriteCellData 添加到列中
//                            column.add(writeCellData);
//                        } else {
//                            column.add("");
//                        }
//                        break;
                    case Overall_Comment:
                        String overallComment = userAnswer.getOverallComment();
                        column.add(StrUtil.isNotBlank(overallComment) ? overallComment : "");
                        break;
                    case Comment:
                        String comment = userAnswer.getComment();
                        column.add(StrUtil.isNotBlank(comment) ? comment : "");
                        break;
                    case Polish:
                        String polish = userAnswer.getPolish();
                        column.add(StrUtil.isNotBlank(polish) ? polish : "");
                        break;
                    default:
                        break;
                }
            }
            // 添加到每一行
            rowList.add(column);
        }
        return rowList;
    }


    @Schema(title = "导出excel表头")
    @Data
    @ColumnWidth(30)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER)
    @AllArgsConstructor
    public static class LevelPercentage {

        @ExcelProperty("评级")
        private String level;

        @ExcelProperty("学生名单")
        private String students;

        @ExcelProperty("人数")
        private Integer num;

        @ExcelProperty("占比（%）")
        private double percentage;
    }


    @Operation(summary = "导出成绩数据统计")
    @PostMapping("levelPercentV2/{homeworkId}")
    public void LevelPercentage(HttpServletResponse response, @PathVariable Long homeworkId) throws IOException {

        // 获取作业
        PgHomework homework = pgHomeworkService.getById(homeworkId);

        if (ObjectUtil.isNull(homework)) {
            return;
        }
        // 导出
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");
        response.setHeader("Access-Control-Expose-Headers", "Content-Disposition"); // 必须设置，否则无法获取文件名
        response.setHeader("Access-Control-Allow-Origin", "*");
        String fileName = URLEncoder.encode(homework.getName() + "—成绩数据统计表", StandardCharsets.UTF_8);

        response.setHeader("Content-Disposition", "attachment;filename=" + fileName + ".xlsx");

        EasyExcel.write(response.getOutputStream(), LevelPercentage.class)
                .sheet("表格一")
                .doWrite(getDataForExport(homeworkId));

    }


    @Operation(summary = "导出成绩数据统计 base64")
    @PostMapping("levelPercent/{homeworkId}")
    public BaseResult<String> LevelPercentage(@PathVariable Long homeworkId) {

        // 获取作业
        PgHomework homework = pgHomeworkService.getById(homeworkId);

        if (ObjectUtil.isNull(homework)) {
            return BaseResult.error("作业不存在");
        }
        String fileName = URLEncoder.encode(homework.getName() + "—成绩数据统计表", StandardCharsets.UTF_8);

        try (ByteArrayOutputStream out = new ByteArrayOutputStream()) {

            EasyExcel.write(out, LevelPercentage.class)
                    .sheet("表格一")
                    .doWrite(getDataForExport(homeworkId));


            return BaseResult.success(
                    fileName,
                    Base64.encode(out.toByteArray())
            );

        } catch (IOException e) {
            throw new RuntimeException("生成Excel失败", e);
        }
    }

    private List<LevelPercentage> getDataForExport(Long homeworkId) {

        // 初始化包含固定评级的LevelPercentage对象列表
        List<LevelPercentage> data = new ArrayList<>();
        data.add(new LevelPercentage("优秀", "", 0, 0.0));
        data.add(new LevelPercentage("良好", "", 0, 0.0));
        data.add(new LevelPercentage("中等", "", 0, 0.0));
        data.add(new LevelPercentage("较差", "", 0, 0.0));

        List<PgAnswer> answers = pgAnswerService.list(new LambdaQueryWrapper<PgAnswer>()
                .ne(PgAnswer::getStatus, CorrectStatusEnum.Uploaded)
                .eq(PgAnswer::getHomeworkId, homeworkId)
                .eq(PgAnswer::getDeleted, false)
        );

        // 初始化等级个数
        List<Integer> levelNum = new ArrayList<>();
        levelNum.add(0);
        levelNum.add(0);
        levelNum.add(0);
        levelNum.add(0);

        // 初始化学生姓名列表
        List<List<String>> studentNameList = new ArrayList<>();
        studentNameList.add(0, new ArrayList<>());
        studentNameList.add(1, new ArrayList<>());
        studentNameList.add(2, new ArrayList<>());
        studentNameList.add(3, new ArrayList<>());

        for (PgAnswer answer : answers) {

            ZwEssayQuestion userAnswer = JSONUtil.toBean(answer.getCorrectResult().toString(), ZwEssayQuestion.class);

            // 评级等级
            if (ObjectUtil.isNotNull(userAnswer.getScoreLevel())) {

                switch (userAnswer.getScoreLevel()) {
                    case 1:
                        levelNum.set(
                                0, levelNum.get(0) + 1
                        );
                        studentNameList.get(0).add(answer.getName());
                        break;
                    case 2:
                        levelNum.set(
                                1, levelNum.get(1) + 1
                        );
                        studentNameList.get(1).add(answer.getName());
                        break;
                    case 3:
                        levelNum.set(
                                2, levelNum.get(2) + 1
                        );
                        studentNameList.get(2).add(answer.getName());
                        break;
                    case 4:
                        levelNum.set(
                                3, levelNum.get(3) + 1
                        );
                        studentNameList.get(3).add(answer.getName());
                        break;
                }
            }

            // 计算总人数
            int totalNum = answers.size();

            // 填充数据
            for (int i = 0; i < data.size(); i++) {
                LevelPercentage levelPercentage = data.get(i);
                levelPercentage.setStudents(StrUtil.join(",", studentNameList.get(i)));
                levelPercentage.setNum(levelNum.get(i));

                double percentage = totalNum == 0 ? 0 : ((levelNum.get(i) * 100.0) / totalNum);
                // 四舍五入
                percentage = NumberUtil.round(percentage, 2).doubleValue();

                levelPercentage.setPercentage(percentage);
            }
        }

        return data;
    }


    @Data
    @Schema(title = "导出学生作文集请求参数")
    public static class ExportStudentRecordForm {

        @Schema(title = "学生id列表")
        private List<Long> studentIds;

        @Schema(title = "作业id列表")
        private List<Long> homeworkIds;

    }

    @Operation(summary = "导出学生作文集")
    @PostMapping("exportStudentReport")
    public BaseResult<PgExportRecord> exportStudentReport(@RequestBody ExportStudentRecordForm form) {

        if (form.getStudentIds().isEmpty() || form.getHomeworkIds().isEmpty()) {
            return BaseResult.error(GlobalCode.Item_Null, "请选择学生和作业");
        }

        List<Long> answerIds = pgAnswerService.list(new LambdaQueryWrapper<PgAnswer>()
                        .select(PgAnswer::getId)
                        .in(PgAnswer::getStudentId, form.getStudentIds())
                        .in(PgAnswer::getHomeworkId, form.getHomeworkIds())
                        .ne(PgAnswer::getStatus, CorrectStatusEnum.Uploaded)
                        .and(i -> i.ne(PgAnswer::getDeleted, true)
                                .or()
                                .isNull(PgAnswer::getDeleted)
                        )
                )
                .stream()
                .map(PgAnswer::getId)
                .toList();

        if (CollUtil.isEmpty(answerIds)) {
            return BaseResult.error(GlobalCode.Item_Null, "没有可导出的作文");
        }

        String md5 = pgAnswerService.getImgMd5(answerIds, StpUtil.getLoginIdAsLong());

        PgExportRecord record = pgExportRecordService.getOne(new LambdaQueryWrapper<PgExportRecord>()
                .eq(PgExportRecord::getType, 2)
                .eq(PgExportRecord::getMd5, md5)
                .eq(PgExportRecord::getUserId, StpUtil.getLoginIdAsLong())
                .last("limit 1")
        );
        // 有记录
        if (ObjectUtil.isNotNull(record)) {
            log.info("存在导出记录: {}", record.getZipUrl());
            record.setCreateTime(new Date());
            pgExportRecordService.updateById(record);

            return BaseResult.success(record);
        }

        // 没记录 插入记录
        record = new PgExportRecord();
        record.setUserId(StpUtil.getLoginIdAsLong());
        record.setTotalNum(answerIds.size());
        record.setCreateTime(new Date());
        record.setMd5(md5);
        record.setStatus(ExportStatusEnum.Queuing);
        record.setAnswerIds(CollUtil.join(answerIds, ";"));
        record.setName(DateUtil.format(new Date(), "yyyy-MM-dd") + "—作文集导出");
        // 选择的学生作业
        record.setStudentIds(CollUtil.join(form.getStudentIds(), ";"));
        record.setHomeworkIds(CollUtil.join(form.getHomeworkIds(), ";"));
        // 作文集
        record.setType(2);

        pgExportRecordService.save(record);

        // 发送队列请求
        QueueUtils.addQueueObjectInTransaction(GlobQueueConstants.PGB_XCX_EXPORT_STUDENT_WORD_QUEUE.name(), record.getId());

        // 队列请求成功后 再返回
        return BaseResult.success(record);
    }

}

