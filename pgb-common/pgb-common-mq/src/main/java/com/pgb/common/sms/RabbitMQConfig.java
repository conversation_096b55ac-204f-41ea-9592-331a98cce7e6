package com.pgb.common.sms;

//import org.springframework.amqp.core.*;
//import org.springframework.context.annotation.Bean;
//import org.springframework.context.annotation.Configuration;
//
//import java.util.HashMap;
//import java.util.Map;

//@Configuration
public class RabbitMQConfig {

//    /**
//     * 延迟队列 TTL 名称
//     */
//    private static final String ORDER_DELAY_QUEUE = "user.pay.delay.queue";
//
//    /**
//     * DLX，dead letter发送到的 exchange
//     * 延时消息就是发送到该交换机的
//     */
//    public static final String ORDER_DELAY_EXCHANGE = "user.order.delay.exchange";
//
//    /**
//     * routing key 名称 路由键
//     * 具体消息发送在该 routingKey 的
//     */
//    public static final String ORDER_DELAY_ROUTING_KEY = "order_delay";
//
//
//    // 死信交换机、队列
//    public static final String ORDER_QUEUE_NAME = "user.order.queue";
//    public static final String ORDER_EXCHANGE_NAME = "user.order.exchange";
//    public static final String ORDER_ROUTING_KEY = "order";
//
//    /**
//     * 死信接收队列
//     */
//    @Bean
//    public Queue orderQueue() {
//        // 默认是一个持久队列
//        return new Queue(ORDER_QUEUE_NAME);
//    }
//
//    /**
//     * 死信交换机
//     * 将路由键和某模式进行匹配。此时队列需要绑定要一个模式上。
//     * 符号“#”匹配一个或多个词，符号“*”匹配不多不少一个词。因此“audit.#”能够匹配到“audit.irs.corporate”，但是“audit.*” 只会匹配到“audit.irs”。
//     **/
//    @Bean
//    public TopicExchange orderTopicExchange() {
//        return new TopicExchange(ORDER_EXCHANGE_NAME);
//    }
//
//    /**
//     * 死信接收队列绑定交换机
//     */
//    @Bean
//    public Binding orderBinding() {
//        // TODO 如果要让延迟队列之间有关联,这里的 routingKey 和 绑定的交换机很关键
//        return BindingBuilder.bind(orderQueue()).to(orderTopicExchange()).with(ORDER_ROUTING_KEY);
//    }
//
//    /**
//     * 延迟队列配置
//     * @return
//     */
//    @Bean
//    public Queue delayOrderQueue() {
//        Map<String, Object> arguments = new HashMap<>();
//        // 可以由生产者动态设置
//        //arguments.put("x-message-ttl", 10000);//消息过期时间，单位ms，可以不设置，可以由生产者设置消息过期时间
//        // 设置消息过期后，成为死信后，转发到哪个交换机
//        arguments.put("x-dead-letter-exchange", ORDER_EXCHANGE_NAME);
//        // 设置消息过期后由交换机路由到哪个队列的路由键
//        arguments.put("x-dead-letter-routing-key", ORDER_ROUTING_KEY);
//        // arguments.put("x-max-length", 6);//设置队列的长度，能存储消息的个数
//        /**
//         * 声明一个持久的队列
//         * durable：持久的
//         * withArguments：添加队列的属性（批量方式）
//         * withArgument：添加队列的属性（单个方式）
//         */
//        return QueueBuilder.durable(ORDER_DELAY_QUEUE).withArguments(arguments).build();
//    }
//
//    @Bean
//    public DirectExchange orderDelayExchange() {
//        return new DirectExchange(ORDER_DELAY_EXCHANGE);
//    }
//
//    /**
//     * 延迟队列绑定交换机
//     */
//    @Bean
//    public Binding dlxBinding() {
//        return BindingBuilder.bind(delayOrderQueue()).to(orderDelayExchange()).with(ORDER_DELAY_ROUTING_KEY);
//    }

}
