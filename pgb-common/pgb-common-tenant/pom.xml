<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.pgb</groupId>
        <artifactId>pgb-common</artifactId>
        <version>${revision}</version>
    </parent>

    <artifactId>pgb-common-tenant</artifactId>
    <description>多租户模式</description>

    <dependencies>
        <dependency>
            <groupId>com.pgb</groupId>
            <artifactId>pgb-common-core</artifactId>
        </dependency>

        <dependency>
            <groupId>com.pgb</groupId>
            <artifactId>pgb-common-redis</artifactId>
        </dependency>

        <dependency>
            <groupId>com.pgb</groupId>
            <artifactId>pgb-common-satoken</artifactId>
        </dependency>

        <dependency>
            <groupId>com.pgb</groupId>
            <artifactId>pgb-common-mybatis</artifactId>
        </dependency>
    </dependencies>

</project>
