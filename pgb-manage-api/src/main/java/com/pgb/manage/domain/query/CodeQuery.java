package com.pgb.manage.domain.query;

import com.pgb.common.mybatis.global.PageQuery;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * Created by 2025/2/6 10:36
 */
@EqualsAndHashCode(callSuper = true)
@Schema(description = "活动 搜索实体")
@Data
public class CodeQuery extends PageQuery {

    @Schema(title = "兑换码")
    private String code;

    @Schema(title = "使用状态，0：未使用，1:已使用，2：已作废")
    private Integer status;

}
