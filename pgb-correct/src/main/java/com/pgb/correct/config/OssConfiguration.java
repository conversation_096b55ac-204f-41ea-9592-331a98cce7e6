package com.pgb.correct.config;

import cn.hutool.core.bean.BeanUtil;
import com.pgb.common.oss.property.OssAliProperty;
import com.pgb.common.oss.property.OssBdProperty;
import com.pgb.common.oss.service.OssAliService;
import com.pgb.common.oss.service.OssBdService;
import com.pgb.common.oss.service.OssService;
import io.swagger.v3.oas.annotations.media.Schema;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
@EnableConfigurationProperties({OssAliProperty.class, OssBdProperty.class, OssAliGdProperty.class})
public class OssConfiguration {

    //@Schema(title = "注入 阿里云 OSS")
    //@Bean
    //public OssService ossService(OssAliProperty property) {
    //    return new OssAliService(property);
    //}

     @Schema(title = "注入 阿里云 归档 OSS")
     @Bean
     public OssService ossService(OssAliGdProperty property) {
         OssAliProperty propertyNew = BeanUtil.copyProperties(property, OssAliProperty.class);
         return new OssAliService(propertyNew);
     }


    //@Schema(title = "注入 百度云 BOS")
    //@Bean
    //public OssService bosService(OssBdProperty property) {
    //    return new OssBdService(property);
    //}

}
