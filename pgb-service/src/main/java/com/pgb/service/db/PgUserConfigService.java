package com.pgb.service.db;

import com.baomidou.mybatisplus.extension.service.IService;
import com.pgb.service.domain.userConfig.*;
import com.pgb.service.enums.UserConfigEnum;

/**
 * <AUTHOR>
 * @description 针对表【pg_user_config(用户配置表)】的数据库操作Service
 * @createDate 2024-10-08 17:53:17
 */
public interface PgUserConfigService extends IService<PgUserConfig> {

    /**
     * 根据配置key获取配置信息
     *
     * @param userConfig
     * @return
     */
    PgUserConfig getByKey(UserConfigEnum userConfig, Long userId);

    /**
     * 获取导出配置
     *
     * @param userId
     * @return
     */
    ExportConfigDTO getExportConfig(Long userId);

    /**
     * 获取家长端配置
     *
     * @param userId
     * @return
     */
    StudentReportConfigDTO getStudentConfig(Long userId);

    /**
     * 获取批改配置
     * @param userId
     * @return
     */
    CorrectConfigDTO getCorrectConfig(Long userId);

    /**
     * 获取导出配置排序
     * @param userId
     * @return
     */
    ExportConfigVOV3 getExportConfigV3(long userId);

    /**
     * 保存导出配置顺序
     * @param voConfig
     * @param userId
     * @return
     */
    boolean saveExportConfigV3(ExportConfigVOV3 voConfig, Long userId);
}
