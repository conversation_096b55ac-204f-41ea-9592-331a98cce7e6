package com.pgb.service.domain.zc.question.chinese.pinyinAndWord;

import com.pgb.service.domain.zc.common.ZcLocation;
import com.pgb.service.domain.zc.word.TextWordInfo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * Created by 2025/4/23 18:52
 */
@Data
@Schema(title = "看拼音写词语/看词语写拼音")
public class PinyinAndWord {

    @Schema(title = "显示配置")
    private Config config;

    @Schema(title = "课文列表")
    private List<TextWordInfo> content;

    @Schema(title = "位置信息")
    private ZcLocation location;

    @Data
    public static class Config {

        @Schema(title = "格子大小（默认12毫米）,可拖拽大小")
        private Integer size;

        @Schema(title = "填空类型，0：看拼音写词语，1：看词语写拼音")
        private Integer type;

        @Schema(title = "大标题")
        private String title;

        @Schema(title = "格子类型 0: 方格，1：田字格，2：米字格，3：括号")
        private GridTypeEnum gridType;

        @Schema(title = "是否显示姓名")
        private Boolean showName;

        @Schema(title = "格子颜色，0：green，1：red，2：black")
        private String gridColor;

        @Schema(title = "是否显示成绩")
        private Boolean showScore;

        @Schema(title = "文字颜色 '#63B169', '#B22D3D', '#0C0D07'")
        private String wordColor;

        @Schema(title = "是否显示学号")
        private Boolean showStudentNo;

        @Schema(title = "是否分课显示")
        private Boolean isShowTextTitle;

        @Schema(title = "分数")
        private Integer score;

        public enum GridTypeEnum {
            fang_ge,
            tian_zi_ge,
            mi_zi_ge,
            kuo_hao
        }
    }
}
