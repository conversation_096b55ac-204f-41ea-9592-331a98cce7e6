package com.pgb.common.oss.property;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

@Schema(title = "百度云OSS配置")
@ConfigurationProperties(prefix = "oss.baidu")
@Data
public class OssBdProperty {
    @Schema(title = "密钥 id")
    private String secretId;

    @Schema(title = "密钥 key")
    private String secretKey;

    @Schema(title = "存储区域")
    private String region;

    @Schema(title = "自定义域名")
    private String cdnDomain;

    @Schema(title = "桶名称")
    private String bucketName;

    @Schema(title = "endpoint")
    private String endpoint;

    @Schema(title = "sts-endpoint", description = "注意，该接入点和oss的不一样")
    private String stsEndpoint;

}
