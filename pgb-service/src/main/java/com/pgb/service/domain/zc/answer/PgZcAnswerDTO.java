package com.pgb.service.domain.zc.answer;

import java.io.Serializable;

import java.util.Date;

import com.pgb.common.core.validate.UpdateGroup;
import com.pgb.service.enums.CorrectStatusEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.ToString;
import lombok.Builder;
import org.springframework.validation.annotation.Validated;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;

/**
*
* @TableName pg_zc_answer
*/
@Schema(description = " DTO")
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode
@ToString
@Validated
@Builder
public class PgZcAnswerDTO implements Serializable {

    @Schema(title = "默写批改记录", type = "string")
    @NotNull(message="[默写批改记录]不能为空", groups = {UpdateGroup.class})
    private Long id;

    @Schema(title = "用户id")
    private Long userId;

    @Schema(title = "用户本题作答")
    private Object answer;

    @Schema(title = "批改状态")
    private CorrectStatusEnum status;

    @Schema(title = "消耗token量")
    private Long aiTokens;

    @Schema(title = "批改所需时长，单位：秒")
    private Integer correctDuration;

    @Schema(title = "创建时间")
    private Date createTime;

    @Schema(title = "AI批改完成时间")
    private Date correctTime;

    @Schema(title = "批改结果")
    private Object correctResult;

    @Schema(title = "所属题目id")
    private Long zcQuestionId;

    @Schema(title = "备注名称")
    @Size(max= 255)
    private String name;

    @Schema(title = "学生id")
    private Long studentId;

    @Schema(title = "是否已删除")
    private Boolean deleted;

    @Schema(title = "作业id")
    private Long zcHomeworkId;

    @Schema(title = "所属批次id")
    private Long batchId;

}
