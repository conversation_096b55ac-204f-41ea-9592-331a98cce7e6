package com.pgb.service.custom;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.TimeInterval;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.URLUtil;
import cn.hutool.json.JSONUtil;
import com.pgb.common.oss.service.OssService;
import com.pgb.service.db.PgAnswerService;
import com.pgb.service.db.PgUserConfigService;
import com.pgb.service.domain.answer.PgAnswer;
import com.pgb.service.domain.common.fabric.FabricJson;
import com.pgb.service.domain.common.fabric.MarkObjectImage;
import com.pgb.service.domain.common.image.FilePaperImg;
import com.pgb.service.domain.question.zwEssay.ZwEssayQuestion;
import com.pgb.service.domain.userConfig.ExportConfigDTO;
import com.pgb.service.domain.userConfig.PgUserConfig;
import com.pgb.service.enums.CorrectStatusEnum;
import com.pgb.service.enums.UserConfigEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Component("ZwCorrectService")
@RequiredArgsConstructor
@Slf4j
public class ZwCorrectService {

    private final PgAnswerService pgAnswerService;

    private final PgUserConfigService pgUserConfigService;

    private final CorrectService correctService;

    private final OssService ossService;

    public void correct(PgAnswer answer) {

        if (ObjectUtil.isNull(answer)) {
            log.info("无效的批改题目：{}", answer.getId());
            return;
        }

        if (!answer.getStatus().equals(CorrectStatusEnum.Uploaded)) {
            log.info("当前题目已被批改：{}", answer.getId());
            return;
        }

        // 兼容化图片
        processImg(answer);

        // 执行AI批改
        doCorrect(answer);

        // 设置状态为非归档
        answer.setIsArchive(false);

        pgAnswerService.updateById(answer);
    }

    // 开始批改题目
    public void doCorrect(PgAnswer answer) {
        // 开始统计批改时长
        TimeInterval timer = DateUtil.timer();
        log.info("开始批改作文题：{}", answer.getId());

        ZwEssayQuestion essay = JSONUtil.toBean(JSONUtil.toJsonStr(answer.getAnswer()), ZwEssayQuestion.class);

        // 无效答案
        if (CollUtil.isEmpty(essay.getUserImgAnswerList())) {
            answer.setAiTokens(0L);
        } else {

            // 获取配置信息
            PgUserConfig config = pgUserConfigService.getByKey(UserConfigEnum.EXPORT, answer.getUserId());

            if (ObjectUtil.isNull(config)) {
                // 默认是评级
                essay.setScoreStyle(0);
            } else {
                ExportConfigDTO configDTO = BeanUtil.toBean(config.getValue(), ExportConfigDTO.class);
                // 保存分数样式  评级/分数
                essay.setScoreStyle(configDTO.getScoreStyle());
            }

            // 执行批改
            ZwEssayQuestion correctResult = correctService.zwCorrect(essay);

            // 渲染图片
            correctResult = correctService.renderImg(correctResult.getUserImgAnswerList(), correctResult, answer.getUserId(), true, false);

            // 将批改结果赋值
            essay.setOverallComment(correctResult.getOverallComment());
            essay.setComment(correctResult.getComment());
            essay.setPolish(correctResult.getPolish());
            essay.setMarkJsonList(correctResult.getMarkJsonList());
            essay.setUserImgAnswerList(correctResult.getUserImgAnswerList());
            essay.setFullText(correctResult.getFullText());
            essay.setPolishDiffList(correctResult.getPolishDiffList());
            essay.setScoreItemList(correctResult.getScoreItemList());
            essay.setSurface(correctResult.getSurface());

            // 用户分数 -- 默认为30
            essay.setUserScore(correctResult.getUserScore());
            essay.setAiScore(correctResult.getAiScore());

            // 脑力值
            answer.setAiTokens(correctResult.getAiTokens());

            // 保存批改结果
            answer.setCorrectResult(essay);
        }

        // 更新批改状态
        answer.setStatus(CorrectStatusEnum.Corrected);
        // 批改时长
        answer.setCorrectTime(new Date());
        answer.setCorrectDuration((int) timer.intervalSecond());

        log.info("批改完成：{}，总用时：{}秒", answer.getId(), timer.intervalSecond());
    }

    // 图片兼容化
    public void processImg(PgAnswer answer) {

        ZwEssayQuestion essay = JSONUtil.toBean(answer.getAnswer().toString(), ZwEssayQuestion.class);

        // 最多留9张
        if (essay.getUserImgAnswerList().size() >= 10) {
            essay.setUserImgAnswerList(
                    CollUtil.sub(essay.getUserImgAnswerList(), 0, 9)
            );
        }

        // 图片兼容化处理
        for (int i = 0; i < essay.getUserImgAnswerList().size(); i++) {
            // 兼容 tmp 情况
            FilePaperImg img = essay.getUserImgAnswerList().get(i);

            // 兼容图片处理
            correctService.processImgV2(img);
        }

        answer.setAnswer(essay);

        pgAnswerService.updateById(answer);
    }

    public void validZw(PgAnswer answer, String comment) {
        answer.setAiTokens(0L);

        ZwEssayQuestion essay = JSONUtil.toBean(JSONUtil.toJsonStr(answer.getAnswer()), ZwEssayQuestion.class);

        List<FabricJson> markJsonList = new ArrayList<>();
        for (FilePaperImg img : essay.getUserImgAnswerList()) {
            FabricJson json = new FabricJson();
            json.setBackgroundImage(
                    MarkObjectImage.builder()
                            .src(img.getImgUrl())
                            .build()
            );
            json.setObjects(new ArrayList<>());
            markJsonList.add(json);
        }

        essay.setMarkJsonList(markJsonList);
        essay.setScoreStyle(0);
        essay.setFullText("");
        essay.setComment(ObjectUtil.defaultIfBlank(comment, "提交的作文为无效作文，请重试后提交"));
        essay.setScoreItemList(new ArrayList<>());
        essay.setUserScore(0);
        answer.setCorrectResult(essay);
        answer.setStatus(CorrectStatusEnum.Corrected);
        answer.setCorrectTime(new Date());
        answer.setCorrectDuration(0);

        log.info("无效作文处理完成：{}", answer.getId());
    }
}
