package com.pgb.manage;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.pgb.ManageApplication;
import com.pgb.service.db.PgTagService;
import com.pgb.service.db.PgTagUserService;
import com.pgb.service.db.PgUsersService;
import com.pgb.service.db.PgVipService;
import com.pgb.service.domain.tag.PgTag;
import com.pgb.service.domain.tag.PgTagUser;
import com.pgb.service.domain.user.PgUsers;
import com.pgb.service.domain.vip.PgVip;
import com.pgb.service.enums.VipTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.Date;
import java.util.List;

@SpringBootTest(classes = ManageApplication.class, webEnvironment = SpringBootTest.WebEnvironment.DEFINED_PORT)
@Slf4j
public class ManageApplicationTests {

    @Autowired
    private PgVipService pgVipService;

    @Autowired
    private PgTagUserService pgTagUserService;

    @Autowired
    private PgTagService pgTagService;

    @Autowired
    private PgUsersService pgUsersService;

    /**
     * 给符合条件的用户打标签
     */
    @Test
    void addTag() {

        // 创建标签
//        PgTag tag = new PgTag();
//        tag.setName("2025年新年活动用户");
//        tag.setCreateTime(new Date());
//        pgTagService.save(tag);

        // 2025年新年活动用户
        PgTag tag = pgTagService.getById(1873712800328937474L);

        // 购买过周卡或者月卡的
        List<Long> userIds = pgVipService.list(new LambdaQueryWrapper<PgVip>()
                        .in(PgVip::getVipType, VipTypeEnum.WEEK, VipTypeEnum.MONTH))
                .stream()
                .map(PgVip::getUserId)
                .distinct()
                .toList();

        // 获取购买过年卡的
        List<Long> userIds2 = pgVipService.list(new LambdaQueryWrapper<PgVip>()
                        .eq(PgVip::getVipType, VipTypeEnum.YEAR))
                .stream()
                .map(PgVip::getUserId)
                .distinct()
                .toList();

        // 目标用户
        List<Long> resultUserIds = userIds.stream()
                .filter(userId -> !userIds2.contains(userId))
                .toList();

        // 筛选符合条件的用户
        List<PgUsers> users = pgUsersService.list(new LambdaQueryWrapper<PgUsers>()
                .in(PgUsers::getId, resultUserIds));

        log.info("用户数：{}", users.size());

        users.forEach(user -> {

            if (!pgTagUserService.exists(new LambdaQueryWrapper<PgTagUser>()
                    .eq(PgTagUser::getUserId, user.getId())
                    .eq(PgTagUser::getTagId, tag.getId()))) {

                // 保存关联关系
                PgTagUser tagUser = new PgTagUser();
                tagUser.setUserId(user.getId());
                tagUser.setTagId(tag.getId());
                tagUser.setCreateTime(new Date());

                pgTagUserService.save(tagUser);
            }
        });
    }
}
