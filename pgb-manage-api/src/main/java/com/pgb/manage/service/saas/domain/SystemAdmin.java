package com.pgb.manage.service.saas.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 系统用户
 * @TableName system_admin
 */
@TableName(value ="system_admin")
@Data
public class SystemAdmin implements Serializable {
    /**
     * 系统用户Id
     */
    @TableId
    private Long id;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 姓名
     */
    private String name;

    /**
     * 所属角色id
     */
    private Long roleId;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 创建时间
     */
    private String password;

    private Boolean isValid;
}
