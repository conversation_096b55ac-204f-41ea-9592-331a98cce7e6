package com.pgb.ai.domain;

import cn.hutool.json.JSONUtil;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ChatRes {

    @Schema(title = "类型")
    public ChatResType type;

    @Schema(title = "状态码", description = "0：正常，1：正常结束, 2:无效数据")
    public CharResCode code = CharResCode.SUCCESS;

    @Schema(title = "chatId")
    public Long chatId;

    @Schema(title = "输出内容")
    public String result;

    @Schema(title = "发送时间戳")
    public Long timestamp;

    @Schema(title = "返回类型")
    private BotType botType = BotType.Bot;

    @Schema(title = "json 化")
    public String toJSON() {
        return JSONUtil.toJsonStr(this);
    }

    // 返回类型
    public enum ChatResType {
        @Schema(title = "心跳")
        HEART,

        @Schema(title = "开始")
        START,

        @Schema(title = "消息")
        MESSAGE,

        @Schema(title = "异常")
        ERROR,

        @Schema(title = "结束")
        END
    }

    public enum CharResCode {
        SUCCESS,
        ERROR
    }

    public enum BotType {
        Bot,
        Reason,
        ReasonFinish
    }
}
