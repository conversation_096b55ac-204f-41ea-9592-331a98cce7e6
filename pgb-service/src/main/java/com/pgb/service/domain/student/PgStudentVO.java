package com.pgb.service.domain.student;

import java.io.Serializable;

import java.util.Date;

import cn.hutool.core.util.ObjectUtil;
import com.fasterxml.jackson.annotation.JsonGetter;
import com.pgb.service.enums.CorrectStatusEnum;
import com.pgb.service.enums.IdentityTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.ToString;
import lombok.Builder;

/**
 * 学生表
 *
 * @TableName pg_student
 */
@Schema(description = "学生表 VO")
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode
@ToString
@Builder
public class PgStudentVO implements Serializable {

    @Schema(title = "学生id")
    private Long id;

    @Schema(title = "学生名字")
    private String name;

    @Schema(title = "创建时间")
    private Date createTime;

    @Schema(title = "学号")
    private String studentNo;

    @Schema(title = "所属班级id")
    private Long classId;

    @Schema(title = "关联用户id")
    private Long studentUserId;

    @Schema(title = "关联作答id")
    private Long answerId;

    @Schema(title = "提交状态")
    private CorrectStatusEnum status;

    @Schema(title = "作业id")
    private Long homeworkId;

    @Schema(title = "是否已关联学生用户")
    private Boolean isBindUser;

    @Schema(title = "分数")
    private Double userScore;

    @Schema(title = "是否是范文")
    private Boolean isModel;

    @JsonGetter
    public Boolean getIsBindUser() {
        return ObjectUtil.isNotNull(this.getStudentUserId());
    }
}
