package com.pgb.xcx.controller.application;

import cn.dev33.satoken.annotation.SaCheckLogin;
import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.pgb.ai.LLMService;
import com.pgb.ai.domain.agent.AgentForm;
import com.pgb.ai.domain.agent.AgentFormField;
import com.pgb.ai.domain.ChatRecord;
import com.pgb.ai.domain.ChatRes;
import com.pgb.ai.domain.agent.FileItem;
import com.pgb.ai.enums.AgentFormFieldType;
import com.pgb.ai.enums.FileTypeEnum;
import com.pgb.common.core.global.BaseResult;
import com.pgb.common.core.global.GlobalCode;
import com.pgb.common.core.global.GlobalConstants;
import com.pgb.common.core.utils.EnvUtils;
import com.pgb.service.db.*;
import com.pgb.service.domain.agent.*;
import com.pgb.service.domain.agent.category.PgAgentCategory;
import com.pgb.service.domain.agent.category.PgAgentCategoryVO;
import com.pgb.service.domain.agent.collect.PgAgentCollect;
import com.pgb.service.domain.chat.PgChat;
import com.pgb.service.domain.chat.PgChatVO;
import com.pgb.service.domain.chat.msg.ChatAttachItem;
import com.pgb.service.domain.chat.msg.PgChatMsg;
import com.pgb.service.domain.chat.msg.PgChatMsgVO;
import com.pgb.service.domain.query.ChatRecordQuery;
import com.pgb.service.enums.FeeTypeEnum;
import com.pgb.service.enums.RoleTypeEnum;
import com.pgb.service.factory.LLMServiceFactory;
import com.pgb.service.model.ChatFrom;
import com.pgb.xcx.common.UserBehaviorUtil;
import com.pgb.xcx.service.AgentLLMService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.time.Duration;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

@Tag(name = "用户端/智能体/教学")
@RestController("AiAgentController")
@Transactional(rollbackFor = Exception.class)
@RequestMapping("/user/agent/teaching")
@RequiredArgsConstructor
@Slf4j
public class AgentController {

    private final PgChatService pgChatService;

    private final ModelSocket modelSocket;

    private final PgChatMsgService pgChatMsgService;

    private final PgAgentCategoryService pgAgentCategoryService;

    private final PgAgentService pgAgentService;

    private final PgAgentCollectService pgAgentCollectService;

    private final AgentLLMService agentLLMService;

    private final PgUsersService pgUsersService;

    private final LLMServiceFactory llmServiceFactory;

    // 动态获取 LLMService
    private LLMService getLLMService() {
        return llmServiceFactory.getLLMService("baidu");
    }

    @Data
    public static class AgentQuery {
        private String name;
    }

    @Operation(summary = "获取智能体分类以及相关智能体")
    @PostMapping("list")
    public BaseResult<List<PgAgentCategoryVO>> list(@RequestBody AgentQuery query) {

        List<PgAgentCategoryVO> list = pgAgentCategoryService.list(new LambdaQueryWrapper<PgAgentCategory>()
                        .orderByAsc(PgAgentCategory::getSort))
                .stream()
                .map(pgAgentCategory -> {

                    PgAgentCategoryVO categoryVO = BeanUtil.copyProperties(pgAgentCategory, PgAgentCategoryVO.class);

                    if (StrUtil.isBlank(pgAgentCategory.getAgentIds())) {
                        return categoryVO;
                    }
                    // 分割ids
                    List<Long> agentIds = StrUtil.split(pgAgentCategory.getAgentIds(), StrUtil.COMMA)
                            .stream()
                            .map(Long::parseLong)
                            .toList();

                    List<PgAgentVO> agentVOList = pgAgentService.list(new LambdaQueryWrapper<PgAgent>()
                                    .like(StrUtil.isNotBlank(query.getName()), PgAgent::getName, query.getName())
                                    .eq(PgAgent::getIsEnable, true)
                                    .in(PgAgent::getId, agentIds)
                                    .orderByAsc(PgAgent::getCreateTime))
                            .stream()
                            .map(agent -> {
                                        PgAgentVO agentVO = BeanUtil.toBean(agent, PgAgentVO.class);

                                        // 如果没有登录 默认没收藏
                                        if (!StpUtil.isLogin()) {
                                            agentVO.setIsCollect(false);
                                        } else {
                                            boolean isCollect = pgAgentCollectService.exists(new LambdaQueryWrapper<PgAgentCollect>()
                                                    .eq(PgAgentCollect::getAgentId, agent.getId())
                                                    .eq(PgAgentCollect::getUserId, StpUtil.getLoginIdAsLong()));

                                            agentVO.setIsCollect(isCollect);
                                        }
                                        return agentVO;
                                    }

                            )
                            .collect(Collectors.toList());

                    // 根据 agentIds 进行 排序
                    agentVOList.sort((o1, o2) -> {
                        int index1 = agentIds.indexOf(o1.getId());
                        int index2 = agentIds.indexOf(o2.getId());
                        return index1 - index2;
                    });

                    categoryVO.setAgentList(agentVOList);

                    return categoryVO;
                })
                .collect(Collectors.toList());

        list.removeIf(item -> StrUtil.isBlank(item.getAgentIds()));

        return BaseResult.success(list);

    }

    @Operation(summary = "获取智能体分类")
    @GetMapping("category/list")
    public BaseResult<List<PgAgentCategory>> listCategory() {

        List<PgAgentCategory> list = pgAgentCategoryService.list(new LambdaQueryWrapper<PgAgentCategory>()
                .orderByAsc(PgAgentCategory::getSort)
        );

        // 过滤掉没有关联智能体的分类
        list.removeIf(item -> StrUtil.isBlank(item.getAgentIds()));

        return BaseResult.success(list);
    }

//    @Deprecated
    @Operation(summary = "根据分类id，获取分类下的智能体列表")
    @GetMapping("agent/listByCateId/{categoryId}")
    public BaseResult<List<PgAgentVO>> listByCateId(@PathVariable Long categoryId) {

        PgAgentCategory category = pgAgentCategoryService.getById(categoryId);

        if (ObjectUtil.isNull(category)) {
            return BaseResult.code(GlobalCode.Item_Null);
        }

        if (StrUtil.isBlank(category.getAgentIds())) {
            return BaseResult.success(new ArrayList<>());
        }

        // 分割id
        List<Long> agentIds = StrUtil.split(category.getAgentIds(), StrUtil.COMMA)
                .stream()
                .map(Long::parseLong)
                .toList();

        List<PgAgentVO> list = pgAgentService.list(new LambdaQueryWrapper<PgAgent>()
                        // 已启用
                        .eq(PgAgent::getIsEnable, true)
                        .in(PgAgent::getId, agentIds))
                .stream()
                .map(item -> BeanUtil.toBean(item, PgAgentVO.class))
                .collect(Collectors.toList());  // 使用 collect(Collectors.toList()) 创建可变列表

        // 根据 agentIds 进行 排序
        list.sort((o1, o2) -> {
            int index1 = agentIds.indexOf(o1.getId());
            int index2 = agentIds.indexOf(o2.getId());
            return index1 - index2;
        });

        return BaseResult.success(list);
    }

    @Operation(summary = "获取单个智能体")
    @GetMapping("agent/detail/{id}")
    public BaseResult<PgAgentDTO> getAgentDetail(@PathVariable Long id) {
        PgAgent agent = pgAgentService.getById(id);

        if (ObjectUtil.isNull(agent)) {
            return BaseResult.code(GlobalCode.Item_Null);
        }

        return BaseResult.success(
                BeanUtil.toBean(agent, PgAgentDTO.class)
        );
    }


    @Operation(summary = "会话列表")
    @PostMapping("chat/page")
    public BaseResult<IPage<PgChatVO>> chatPage(@RequestBody ChatRecordQuery query) {
        IPage<PgChatVO> page = pgChatService.page(query.toMpPageSortByCreateTime(), new LambdaQueryWrapper<PgChat>()
                .eq(PgChat::getUserId, StpUtil.getLoginIdAsLong())
                .like(StrUtil.isNotBlank(query.getTitle()), PgChat::getTitle, query.getTitle())
        ).convert(chat -> {
                    PgChatVO chatVO = BeanUtil.copyProperties(chat, PgChatVO.class);

                    PgChatMsg chatMsg = pgChatMsgService.getOne(new LambdaQueryWrapper<PgChatMsg>()
                            .eq(PgChatMsg::getChatId, chat.getId())
                            .orderByAsc(PgChatMsg::getSort)
                            .eq(PgChatMsg::getRole, RoleTypeEnum.USER)
                            .last("LIMIT 1")
                    );
                    if (ObjectUtil.isNotNull(chatMsg)) {
                        chatVO.setFirstMsg(chatMsg.getContent());
                    }
                    return chatVO;
                }
        );

        return BaseResult.success(page);
    }

    @Operation(summary = "创建智能体会话")
    @PostMapping("create/form/{agentId}")
    public BaseResult<Long> createAgentForm(@PathVariable Long agentId, @RequestBody AgentForm form) {

        PgAgent agent = pgAgentService.getById(agentId);

        if (ObjectUtil.isNull(agent)) {
            return BaseResult.code(GlobalCode.Item_Null);
        }

        // 收费类型 --【会员无限次】
        if (agent.getFeeType().equals(FeeTypeEnum.Vip_Free)) {
            // 如果不是会员
            if (!pgUsersService.getById(StpUtil.getLoginIdAsLong()).getIsVip()) {
                return BaseResult.error("请先升级会员");
            }
        }

        // 生成提示词
        String prompt = agentLLMService.generatePromptV2(agent.getPrompt(), form.getForm());
        form.setPrompt(prompt);

        // 创建会话
        PgChat chat = new PgChat();
        chat.setUserId(StpUtil.getLoginIdAsLong());
        chat.setCreateTime(new Date());
        // 智能体id
        chat.setAgentId(agentId);
        chat.setTitle(agent.getName());
        chat.setForm(form);
        pgChatService.save(chat);

        // 创建智能体提示信息
        // PgChatMsg aiMsg = new PgChatMsg();
        // aiMsg.setChatId(chat.getId());
        // aiMsg.setRole(RoleTypeEnum.BOT);
        // aiMsg.setContent(
        //         agent.getDescription()
        // );
        // aiMsg.setSort(0);
        // aiMsg.setCreateTime(new Date());
        // pgChatMsgService.save(aiMsg);

        // 创建用户消息
        PgChatMsg chatMsg = new PgChatMsg();
        chatMsg.setChatId(chat.getId());
        chatMsg.setRole(RoleTypeEnum.USER);
        chatMsg.setContent(
                agentLLMService.generateUserMsg(agent.getName(), form.getForm())
        );
        chatMsg.setSort(pgChatMsgService.getNextSortNumber(chat.getId()));
        chatMsg.setCreateTime(new Date());
        // 类型：普通文本
        chatMsg.setContentType(0);
        pgChatMsgService.save(chatMsg);


        // 如果有文件 或者图片 也加入用户消息中
        List<FileItem> fileItems = new ArrayList<>();
        List<FileItem> imageItems = new ArrayList<>();

        if (ObjectUtil.isNotNull(form.getForm())) {
            form.getForm().stream()
                    .filter(field -> field.getType().equals(AgentFormFieldType.upload) && CollUtil.isNotEmpty(field.getFileList()))
                    .flatMap(field -> field.getFileList().stream())
                    .forEach(file -> {
                        if (file.getFileType().equals(FileTypeEnum.image)) {
                            imageItems.add(file);
                        } else {
                            fileItems.add(file);
                        }
                    });
        }
        if (!fileItems.isEmpty()) {
            PgChatMsg docMsg = new PgChatMsg();
            docMsg.setChatId(chat.getId());
            docMsg.setRole(RoleTypeEnum.USER);
            docMsg.setContent("文档附件:" + fileItems.stream()
                    .map(FileItem::getUrl)
                    .collect(Collectors.joining(";")));
            docMsg.setSort(pgChatMsgService.getNextSortNumber(chat.getId()));
            docMsg.setCreateTime(new Date());
            // 类型：文档
            docMsg.setContentType(1);

            // 附件内容json
            List<ChatAttachItem> contentJson = fileItems.stream()
                    .map(file -> {
                        ChatAttachItem item = new ChatAttachItem();
                        item.setName(file.getName());
                        item.setFileUrl(file.getUrl());
                        return item;
                    })
                    .collect(Collectors.toList());

            docMsg.setContentJson(contentJson);
            pgChatMsgService.save(docMsg);
        }

        // 保存图片消息
        if (!imageItems.isEmpty()) {
            PgChatMsg imgMsg = new PgChatMsg();
            imgMsg.setChatId(chat.getId());
            imgMsg.setRole(RoleTypeEnum.USER);
            imgMsg.setContent("图片附件:" + imageItems.stream()
                    .map(FileItem::getUrl)
                    .collect(Collectors.joining(";")));
            imgMsg.setSort(0);
            imgMsg.setCreateTime(new Date());
            // 类型：图片
            imgMsg.setContentType(2);

            // 附件内容json
            List<ChatAttachItem> contentJson = imageItems.stream()
                    .map(file -> {
                        ChatAttachItem item = new ChatAttachItem();
                        item.setName(file.getName());
                        item.setFileUrl(file.getUrl());
                        return item;
                    })
                    .collect(Collectors.toList());
            imgMsg.setContentJson(contentJson);

            pgChatMsgService.save(imgMsg);
        }

//        // 保存智能体会话消耗明细
//        PgAgentCost cost = new PgAgentCost();
//        cost.setAgentId(agentId);
//        cost.setUserId(StpUtil.getLoginIdAsLong());
//        cost.setChatId(chat.getId());
//        cost.setCreateTime(new Date());
//        pgAgentCostService.save(cost);

        return BaseResult.success(chat.getId());
    }

    @Operation(summary = "【小程序】生成AI模型返回内容")
    @PostMapping("generate/{chatId}")
    @SaCheckLogin
    public BaseResult<Long> generate(@RequestBody ChatFrom form, @PathVariable Long chatId) {

        // 判断是否超过限制
        if (isLimited()) {
            return BaseResult.code(GlobalCode.Limit_Over);
        }

        if (!modelSocket.isExist(chatId)) {
            log.info("当前socket未连接，请稍后再试");
            return BaseResult.error("当前socket未连接，请稍后再试");
        }

        // 获取会话
        PgChat chat = pgChatService.getById(chatId);

        AgentForm agentFrom = JSONUtil.toBean(chat.getForm().toString(), AgentForm.class);

        // 获取上传的图片以及文档链接
        List<AgentFormField> fields = agentFrom.getForm();

        List<List<FileItem>> fileList = fields.stream()
                .filter(item -> item.getType().equals(AgentFormFieldType.upload))
                .map(AgentFormField::getFileList)
                .filter(list -> list.stream().noneMatch(file -> file.getFileType().equals(FileTypeEnum.image)))
                .toList();

        // 保存用户发送的追加消息
        if (!ObjectUtil.defaultIfNull(form.getIsFirst(), false) && StrUtil.isNotBlank(form.getUserContent())) {
            PgChatMsg chatMsg = new PgChatMsg();
            chatMsg.setChatId(chat.getId());
            chatMsg.setRole(RoleTypeEnum.USER);
            chatMsg.setContent(form.getUserContent());
            chatMsg.setSort(
                    pgChatMsgService.getNextSortNumber(chat.getId())
            );
            chatMsg.setCreateTime(new Date());
            // 类型：普通文本
            chatMsg.setContentType(0);
            pgChatMsgService.save(chatMsg);
        }

        // 初始化返回内容
        StringBuilder resultBuilder = new StringBuilder();
        StringBuilder reasonBuilder = new StringBuilder();

        String userContent = form.getUserContent();

        // 保存会话信息记录
        PgChatMsg chatMsg = new PgChatMsg();
        chatMsg.setChatId(chatId);
        chatMsg.setRole(RoleTypeEnum.BOT);
        chatMsg.setSort(
                pgChatMsgService.getNextSortNumber(chatId)
        );
        chatMsg.setCreateTime(new Date());

        pgChatMsgService.save(chatMsg);

        // 执行大模型内容
        CompletableFuture.runAsync(() -> {
            try {
                log.info("开始生成内容");

                // 历史会话信息
                List<ChatRecord> recordList = pgChatService.getHistory(chat);

                // 用户最新回复的内容
                recordList.add(ChatRecord.builder()
                        .role(ChatRecord.Role.User)
                        .content(userContent)
                        .build());
                // 百度
                LLMService llmService = getLLMService();

                llmService.chatSocket(fileList, recordList, resultBuilder, reasonBuilder, chatRes -> {
                    modelSocket.sendMessage(chatId, chatRes);
                }, ObjectUtil.defaultIfNull(form.getIsReason(), false));

                // 返回结束标识
                modelSocket.sendMessage(chatId,
                        ChatRes.builder()
                                .type(ChatRes.ChatResType.END)
                                .build()
                );

                chatMsg.setContent(resultBuilder.toString());
                chatMsg.setReason(reasonBuilder.toString());
                pgChatMsgService.updateById(chatMsg);
            } catch (Exception e) {
                log.error("【AgentController-generate-异常】：", e);
                if (EnvUtils.isProd()) {
                    chatMsg.setContent("当前服务器繁忙，请稍后再试");
                } else {
                    chatMsg.setContent(e.toString());
                }
            }
        });

        return BaseResult.success(chatMsg.getId());
    }

    @Operation(summary = "【PC】生成AI模型返回内容")
    @PostMapping(value = "generate/pc/{chatId}")
    @SaCheckLogin
    public SseEmitter generatePc(@RequestBody ChatFrom form, @PathVariable Long chatId) {

        log.info("【PC】生成AI模型返回内容");

        // 创建SSE emitter用于流式响应
        SseEmitter emitter = new SseEmitter(300000L); // 5分钟超时

        if (isLimited()) {
            try {
                ChatRes errorRes = ChatRes.builder()
                        .type(ChatRes.ChatResType.ERROR)
                        .code(ChatRes.CharResCode.ERROR)
                        .result("请求过于频繁，请稍后再试")
                        .build();
                emitter.send(errorRes.toJSON());
                emitter.complete();
            } catch (Exception e) {
                log.error("发送限制错误消息失败", e);
                emitter.completeWithError(e);
            }
            return emitter;
        }
        // 获取会话
        PgChat chat = pgChatService.getById(chatId);

        AgentForm agentFrom = JSONUtil.toBean(chat.getForm().toString(), AgentForm.class);

        // 获取上传的图片以及文档链接
        List<AgentFormField> fields = agentFrom.getForm();

        List<List<FileItem>> fileList = fields.stream()
                .filter(item -> item.getType().equals(AgentFormFieldType.upload))
                .map(AgentFormField::getFileList)
                .filter(list -> list.stream().noneMatch(file -> file.getFileType().equals(FileTypeEnum.image)))
                .toList();

        // 保存用户发送的追加消息
        if (!ObjectUtil.defaultIfNull(form.getIsFirst(), false) && StrUtil.isNotBlank(form.getUserContent())) {
            PgChatMsg chatMsg = new PgChatMsg();
            chatMsg.setChatId(chat.getId());
            chatMsg.setRole(RoleTypeEnum.USER);
            chatMsg.setContent(form.getUserContent());
            chatMsg.setSort(
                    pgChatMsgService.getNextSortNumber(chat.getId())
            );
            chatMsg.setCreateTime(new Date());
            // 类型：普通文本
            chatMsg.setContentType(0);
            pgChatMsgService.save(chatMsg);
        }

        // 初始化返回内容
        StringBuilder resultBuilder = new StringBuilder();
        StringBuilder reasonBuilder = new StringBuilder();

        String userContent = form.getUserContent();

        // 保存会话信息记录
        PgChatMsg chatMsg = new PgChatMsg();
        chatMsg.setChatId(chatId);
        chatMsg.setRole(RoleTypeEnum.BOT);
        chatMsg.setSort(
                pgChatMsgService.getNextSortNumber(chatId)
        );
        chatMsg.setCreateTime(new Date());

        pgChatMsgService.save(chatMsg);

        // 发送包含chatMsgId的开始消息 用于重新生成使用
        try {
            ChatRes startRes = ChatRes.builder()
                    .type(ChatRes.ChatResType.START)
                    .code(ChatRes.CharResCode.SUCCESS)
                    .chatId(chatId)
                    .result(chatMsg.getId().toString()) // 将chatMsgId放在result字段中
                    .build();
            emitter.send(startRes.toJSON());
        } catch (Exception e) {
            log.error("发送开始消息失败", e);
            emitter.completeWithError(e);
            return emitter;
        }

        // 执行大模型内容
        CompletableFuture.runAsync(() -> {
            try {
                log.info("开始生成内容");

                // 历史会话信息
                List<ChatRecord> recordList = pgChatService.getHistory(chat);

                // 用户最新回复的内容
                recordList.add(ChatRecord.builder()
                        .role(ChatRecord.Role.User)
                        .content(userContent)
                        .build());

                // 百度
                LLMService llmService = getLLMService();

                // 使用chatStream进行流式处理
                llmService.chatStream(fileList, recordList, emitter, resultBuilder, reasonBuilder,
                        ObjectUtil.defaultIfNull(form.getIsReason(), false));

                // 发送结束标识
                ChatRes endRes = ChatRes.builder()
                        .type(ChatRes.ChatResType.END)
                        .code(ChatRes.CharResCode.SUCCESS)
                        .build();
                emitter.send(endRes.toJSON());
                // 关闭连接
                emitter.complete();

                // 更新聊天消息内容
                chatMsg.setContent(resultBuilder.toString());
                chatMsg.setReason(reasonBuilder.toString());
                pgChatMsgService.updateById(chatMsg);

            } catch (Exception e) {
                log.error("【AgentController-generatePc-异常】：", e);
                try {
                    ChatRes errorRes = ChatRes.builder()
                            .type(ChatRes.ChatResType.ERROR)
                            .code(ChatRes.CharResCode.ERROR)
                            .result(EnvUtils.isProd() ? "当前服务器繁忙，请稍后再试" : e.toString())
                            .build();
                    emitter.send(errorRes.toJSON());
                    emitter.complete();
                } catch (Exception sendError) {
                    log.error("发送错误消息失败", sendError);
                    // 错误处理
                    emitter.completeWithError(sendError);
                }

                // 更新聊天消息内容
                chatMsg.setContent(EnvUtils.isProd() ? "当前服务器繁忙，请稍后再试" : e.toString());
                pgChatMsgService.updateById(chatMsg);
            }
        });

        return emitter;
    }


    @Operation(summary = "【小程序】重新生成模型内容")
    @PostMapping("refresh/{chatId}/{chatMsgId}")
    @SaCheckLogin
    public BaseResult<Long> refresh(@PathVariable Long chatId, @PathVariable Long chatMsgId) {
        // 判断是否超过限制
        if (isLimited()) {
            return BaseResult.code(GlobalCode.Limit_Over);
        }

        if (!modelSocket.isExist(chatId)) {
            log.info("当前socket未连接，请稍后再试");
            return BaseResult.error("当前socket未连接，请稍后再试");
        }

        // 获取会话
        PgChat chat = pgChatService.getById(chatId);

        // 获取需要重新生成的内容
        PgChatMsg chatMsg = pgChatMsgService.getById(chatMsgId);

        // 必须要AI生成的才能重新生成
        if (!chatMsg.getRole().equals(RoleTypeEnum.BOT)) {
            return BaseResult.error("只能重新生成AI生成的内容");
        }

        AgentForm agentFrom = JSONUtil.toBean(chat.getForm().toString(), AgentForm.class);

        // 获取上传的图片以及文档链接
        List<AgentFormField> fields = agentFrom.getForm();

        List<List<FileItem>> fileList = fields.stream()
                .filter(item -> item.getType().equals(AgentFormFieldType.upload))
                .map(AgentFormField::getFileList)
                .filter(list -> list.stream().noneMatch(file -> file.getFileType().equals(FileTypeEnum.image)))
                .toList();

        // 历史会话信息
        List<ChatRecord> recordList = pgChatService.getHistory(chat);

        // 初始化返回内容
        StringBuilder resultBuilder = new StringBuilder();
        StringBuilder reasonBuilder = new StringBuilder();

        try {

            // 百度
            LLMService llmService = getLLMService();

            llmService.chatSocket(fileList, recordList, resultBuilder, reasonBuilder, chatRes -> {
                modelSocket.sendMessage(chatId, chatRes);
            }, StrUtil.isNotBlank(chatMsg.getReason()));

            // 返回结束标识
            modelSocket.sendMessage(chatId,
                    ChatRes.builder()
                            .type(ChatRes.ChatResType.END)
                            .build()
            );

            chatMsg.setContent(resultBuilder.toString());
            chatMsg.setReason(reasonBuilder.toString());
        } catch (Exception e) {
            log.error("【AgentController-refresh-异常】：", e);
            if (EnvUtils.isProd()) {
                chatMsg.setContent("当前服务器繁忙，请稍后再试");
            } else {
                chatMsg.setContent(e.toString());
            }
        }

        pgChatMsgService.updateById(chatMsg);

        return BaseResult.success(chatMsg.getId());
    }

    @Operation(summary = "【PC】重新生成模型内容")
    @PostMapping(value = "refresh/pc/{chatId}/{chatMsgId}")
    @SaCheckLogin
    public SseEmitter refreshPc(@PathVariable Long chatId, @PathVariable Long chatMsgId) {

        // 创建SSE emitter用于流式响应
        SseEmitter emitter = new SseEmitter(300000L); // 5分钟超时

        if (isLimited()) {
            try {
                ChatRes errorRes = ChatRes.builder()
                        .type(ChatRes.ChatResType.ERROR)
                        .code(ChatRes.CharResCode.ERROR)
                        .result("请求过于频繁，请稍后再试")
                        .build();
                emitter.send(errorRes.toJSON());
                emitter.complete();
            } catch (Exception e) {
                log.error("发送频率限制错误消息失败", e);
                emitter.completeWithError(e);
            }
            return emitter;
        }

        // 获取会话
        PgChat chat = pgChatService.getById(chatId);

        // 获取需要重新生成的内容
        PgChatMsg chatMsg = pgChatMsgService.getById(chatMsgId);

        // 必须要AI生成的才能重新生成
        if (!chatMsg.getRole().equals(RoleTypeEnum.BOT)) {
            try {
                ChatRes errorRes = ChatRes.builder()
                        .type(ChatRes.ChatResType.ERROR)
                        .code(ChatRes.CharResCode.ERROR)
                        .result("只能重新生成AI生成的内容")
                        .build();
                emitter.send(errorRes.toJSON());
                emitter.complete();
            } catch (Exception e) {
                log.error("发送错误消息失败", e);
                emitter.completeWithError(e);
            }
            return emitter;
        }

        AgentForm agentFrom = JSONUtil.toBean(chat.getForm().toString(), AgentForm.class);

        // 获取上传的图片以及文档链接
        List<AgentFormField> fields = agentFrom.getForm();

        List<List<FileItem>> fileList = fields.stream()
                .filter(item -> item.getType().equals(AgentFormFieldType.upload))
                .map(AgentFormField::getFileList)
                .filter(list -> list.stream().noneMatch(file -> file.getFileType().equals(FileTypeEnum.image)))
                .toList();

        // 初始化返回内容
        StringBuilder resultBuilder = new StringBuilder();
        StringBuilder reasonBuilder = new StringBuilder();

        // 执行大模型内容
        CompletableFuture.runAsync(() -> {
            try {
                log.info("开始重新生成内容");

                // 历史会话信息
                List<ChatRecord> recordList = pgChatService.getHistory(chat);

                // 百度
                LLMService llmService = getLLMService();

                // 使用chatStream进行流式处理
                llmService.chatStream(fileList, recordList, emitter, resultBuilder, reasonBuilder, false);

                // 发送结束标识
                ChatRes endRes = ChatRes.builder()
                        .type(ChatRes.ChatResType.END)
                        .code(ChatRes.CharResCode.SUCCESS)
                        .build();
                emitter.send(endRes.toJSON());
                emitter.complete();

                // 更新聊天消息内容
                chatMsg.setContent(resultBuilder.toString());
                chatMsg.setReason(reasonBuilder.toString());
                pgChatMsgService.updateById(chatMsg);

            } catch (Exception e) {
                log.error("【AgentController-refreshPc-异常】：", e);
                try {
                    ChatRes errorRes = ChatRes.builder()
                            .type(ChatRes.ChatResType.ERROR)
                            .code(ChatRes.CharResCode.ERROR)
                            .result(EnvUtils.isProd() ? "当前服务器繁忙，请稍后再试" : e.toString())
                            .build();
                    emitter.send(errorRes.toJSON());
                    emitter.complete();
                } catch (Exception sendError) {
                    log.error("发送错误消息失败", sendError);
                    emitter.completeWithError(sendError);
                }

                // 更新聊天消息内容
                chatMsg.setContent(EnvUtils.isProd() ? "当前服务器繁忙，请稍后再试" : e.toString());
                pgChatMsgService.updateById(chatMsg);
            }
        });

        return emitter;
    }


    private boolean isLimited() {
        // 频率限制
        long userId = StpUtil.getLoginIdAsLong();

        String redisKey = GlobalConstants.PGB_MODEL_LIMIT_REDIS_KEY + userId;

        if (UserBehaviorUtil.isLimitOver(redisKey, 5)) {
            return true;
        } else {
            UserBehaviorUtil.addLimitOverNum(redisKey, Duration.ofMinutes(1));
            return false;
        }
    }

    @Operation(summary = "删除会话")
    @PostMapping("delete/{id}")
    public BaseResult<Boolean> delete(@PathVariable Long id) {

        // 获取
        PgChat chat = pgChatService.getById(id);

        if (ObjectUtil.isNull(chat)) {
            return BaseResult.code(GlobalCode.Item_Null);
        }

        // 删除会话本身
        pgChatService.removeById(id);

        // 删除会话下的消息内容
        pgChatMsgService.remove(new LambdaQueryWrapper<PgChatMsg>()
                .eq(PgChatMsg::getChatId, id));

        return BaseResult.success(true);
    }


    @Schema(title = "会话消息列表 VO")
    @Data
    public static class ChatMsgInfo {

        @Schema(title = "是否是第一次发消息")
        Boolean isFirstMsg;

        @Schema(title = "消息内容记录")
        List<PgChatMsgVO> chatMsgList;

        @Schema(title = "对应的agent")
        public PgAgentVO agentInfo;
    }

    @Operation(summary = "会话消息列表")
    @PostMapping("chatMsgList/{chatId}")
    public BaseResult<ChatMsgInfo> chatMsgList(@PathVariable Long chatId) {

        PgChat chat = pgChatService.getById(chatId);

        if (ObjectUtil.isNull(chat)) {
            return BaseResult.code(GlobalCode.Item_Null);
        }

        // 获取对应智能体
        PgAgent agent = pgAgentService.getById(chat.getAgentId());

        if (ObjectUtil.isNull(agent)) {
            return BaseResult.code(GlobalCode.Item_Null);
        }

        List<PgChatMsgVO> list = pgChatMsgService.list(new LambdaQueryWrapper<PgChatMsg>()
                        .eq(PgChatMsg::getChatId, chatId)
                        .orderByAsc(PgChatMsg::getSort))
                .stream()
                .map(pgChatMsg -> {

                            PgChatMsgVO msgVO = BeanUtil.copyProperties(pgChatMsg, PgChatMsgVO.class, "contentJson");
                            if (ObjectUtil.isNull(pgChatMsg.getContentType()) || !(pgChatMsg.getContentType() == 0)) {

                                // 内容json
                                if (ObjectUtil.isNotNull(pgChatMsg.getContentJson())) {
                                    List<ChatAttachItem> contentJson = JSONUtil.toList(
                                            JSONUtil.toJsonStr(pgChatMsg.getContentJson()),
                                            ChatAttachItem.class
                                    );
                                    msgVO.setContentJson(contentJson);
                                }
                            }
                            return msgVO;
                        }
                )
                .toList();

        boolean isFirstMsg = !pgChatMsgService.exists(new LambdaQueryWrapper<PgChatMsg>()
                .eq(PgChatMsg::getChatId, chatId)
                .eq(PgChatMsg::getRole, RoleTypeEnum.BOT)
        );

        ChatMsgInfo info = new ChatMsgInfo();

        info.setChatMsgList(list);
        info.setIsFirstMsg(isFirstMsg);
        info.setAgentInfo(
                BeanUtil.toBean(agent, PgAgentVO.class)
        );

        return BaseResult.success(info);
    }

}
