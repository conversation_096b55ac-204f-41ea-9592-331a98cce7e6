package com.pgb.service.domain.agent.cost;

import com.fasterxml.jackson.annotation.JsonGetter;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * <AUTHOR>
 * Created by 2025/2/14 17:16
 */
@Data
@AllArgsConstructor
public class TodayNum {

    @Schema(title = "使用次数")
    private Integer useNum;

    @Schema(title = "总次数")
    private Integer totalNum;

    @JsonGetter
    public Integer getRemainNum() {
        return Math.max(totalNum - useNum, 0);
    }
}
