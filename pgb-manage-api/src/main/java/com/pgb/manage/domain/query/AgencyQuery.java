package com.pgb.manage.domain.query;

import com.pgb.common.mybatis.global.PageQuery;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * Created by 2025/2/28 11:06
 */
@EqualsAndHashCode(callSuper = true)
@Schema(description = "代理 搜索实体")
@Data
public class AgencyQuery extends PageQuery {

    @Schema(title = "手机号")
    private String phone;
}
