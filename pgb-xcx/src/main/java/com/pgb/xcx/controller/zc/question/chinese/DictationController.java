package com.pgb.xcx.controller.zc.question.chinese;

import cn.dev33.satoken.annotation.SaCheckLogin;
import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.pinyin.PinyinUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.github.houbb.pinyin.util.PinyinHelper;
import com.pgb.ai.LLMService;
import com.pgb.ai.domain.GPTAnswer;
import com.pgb.common.core.global.BaseResult;
import com.pgb.common.core.global.GlobalCode;
import com.pgb.common.core.global.GlobalConstants;
import com.pgb.service.custom.ProcessImgUtil;
import com.pgb.service.db.PgZcChineseWordService;
import com.pgb.service.db.PgZcQuestionService;
import com.pgb.service.domain.zc.question.PgZcQuestion;
import com.pgb.service.domain.zc.question.chinese.dictation.ZcDictation;
import com.pgb.service.domain.zc.word.chinese.PgZcChineseWord;
import com.pgb.service.domain.zc.word.chinese.PgZcChineseWordVO;
import com.pgb.service.enums.ExportStatusEnum;
import com.pgb.service.enums.GenerateStatusEnum;
import com.pgb.service.enums.SubjectEnum;
import com.pgb.service.enums.ZcQuestionTypeEnum;
import com.pgb.service.factory.LLMServiceFactory;
import com.pgb.xcx.common.UserBehaviorUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.time.Duration;
import java.util.*;
import java.util.stream.Collectors;

@Tag(name = "用户端/字词/题目/语文/听写默写")
@RestController("DictationController")
@Transactional(rollbackFor = Exception.class)
@RequestMapping("/user/zc/question/dictation")
@RequiredArgsConstructor
@Slf4j
public class DictationController {

    private final PgZcQuestionService pgZcQuestionService;

    private final LLMServiceFactory llmServiceFactory;

    private LLMService getLLMService() {
        return llmServiceFactory.getLLMService("ali");
    }

    private final PgZcChineseWordService pgZcChineseWordService;

    @Operation(summary = "新增题目【听写默写】")
    @PostMapping("save")
    @SaCheckLogin
    public BaseResult<Long> savePinyinAndWord(@RequestBody ZcDictation form) {

        // 保存题目基本信息
        PgZcQuestion zcQuestion = new PgZcQuestion();

        // 用户id
        zcQuestion.setUserId(StpUtil.getLoginIdAsLong());
        // 科目
        zcQuestion.setSubject(SubjectEnum.Chinese);
        // 字词类型
        zcQuestion.setType(
                form.getType() == ZcQuestionTypeEnum.Dictation ? ZcQuestionTypeEnum.Dictation : ZcQuestionTypeEnum.TextWriting
        );
        // 分数，默认100分
        zcQuestion.setScore(100);
        // 题目名称
        zcQuestion.setName(
                StrUtil.isBlank(form.getName()) ? "听写默写" : form.getName()
        );
        // 是否官方
        zcQuestion.setIsOfficial(false);
        zcQuestion.setCreateTime(new Date());
        zcQuestion.setUpdateTime(new Date());

        // 分割词语 判断每个词语的字符不能超过15个字
        List<String> finalWordList = new ArrayList<>();

        List<String> wordList = Arrays.stream(StrUtil.trim(form.getText()).split("\\s+")).toList();
        if (CollUtil.isNotEmpty(wordList)) {
            for (String word : wordList) {
                if (word.length() > 5) {
                    // 如果大于6个字 就截断保存
                    word = word.substring(0, 5);
                    finalWordList.add(word);
                } else {
                    finalWordList.add(word);
                }
            }
        }
        // 重新按空格分割拼接
        form.setText(finalWordList.stream()
                .map(String::trim)
                .collect(Collectors.joining(" ")));

        // 保存题目内容
        zcQuestion.setContentJson(
                form
        );

        // 初始化pdf生成状态
        zcQuestion.setPdfStatus(ExportStatusEnum.Init);

        pgZcQuestionService.save(zcQuestion);

        // 将词语保存至数据库中
        if (CollUtil.isNotEmpty(finalWordList)) {

            finalWordList.stream()
                    .map(String::trim)
                    .filter(StrUtil::isNotBlank)
                    .forEach(pgZcChineseWordService::getOrCreateWordInfo);
        }

        return BaseResult.success(zcQuestion.getId());
    }

    @Operation(summary = "编辑题目【听写默写】")
    @PostMapping("update/{id}")
    @SaCheckLogin
    public BaseResult<Long> updatePinyinAndWord(@RequestBody ZcDictation form, @PathVariable Long id) {

        // 获取题目
        PgZcQuestion zcQuestion = pgZcQuestionService.getById(id);

        if (ObjectUtil.isNull(zcQuestion)) {
            return BaseResult.code(GlobalCode.Item_Null);
        }

        // 判断是否为当前用户
        if (StpUtil.getLoginIdAsLong() != zcQuestion.getUserId()) {
            return BaseResult.code(GlobalCode.Permission_Not);
        }

        // 保存题目基本信息
        // 题目名称
        String name = form.getName();
        zcQuestion.setName(StrUtil.isBlank(name) ? "听写默写" : name);

        // 字词类型
        zcQuestion.setType(
                form.getType() == ZcQuestionTypeEnum.Dictation ? ZcQuestionTypeEnum.Dictation : ZcQuestionTypeEnum.TextWriting
        );

        // 分割词语 判断每个词语的字符不能超过15个字
        List<String> finalWordList = new ArrayList<>();

        List<String> wordList = Arrays.stream(StrUtil.trim(form.getText()).split("\\s+")).toList();
        if (CollUtil.isNotEmpty(wordList)) {
            for (String word : wordList) {
                if (word.length() > 5) {
                    // 如果大于6个字 就截断保存
                    word = word.substring(0, 5 );
                    finalWordList.add(word);
                } else {
                    finalWordList.add(word);
                }
            }
        }

        // 重新按空格分割拼接
        form.setText(finalWordList.stream()
                .map(String::trim)
                .collect(Collectors.joining(" ")));

        zcQuestion.setContentJson(
                form
        );
        zcQuestion.setUpdateTime(new Date());

        pgZcQuestionService.updateById(zcQuestion);

        // 将词语保存至数据库中
        if (CollUtil.isNotEmpty(finalWordList)) {

            finalWordList.stream()
                    .map(String::trim)
                    .filter(StrUtil::isNotBlank)
                    .forEach(pgZcChineseWordService::getOrCreateWordInfo);
        }

        return BaseResult.success(zcQuestion.getId());
    }

    @Operation(summary = "获取题目")
    @GetMapping("detail/{id}")
    public BaseResult<ZcDictation> detail(@PathVariable Long id) {

        PgZcQuestion zcQuestion = pgZcQuestionService.getById(id);

        if (ObjectUtil.isNull(zcQuestion)) {
            return BaseResult.code(GlobalCode.Item_Null);
        }

        ZcDictation data = JSONUtil.toBean(JSONUtil.toJsonStr(zcQuestion.getContentJson()), ZcDictation.class);

        return BaseResult.success(data);
    }

    @Data
    public static class ZcVlForm {
        private String imgUrl;

        @Schema(title = "识别类型", description = "0：录词、1：录课文")
        private Integer type;
    }

    @Operation(summary = "拍照录词")
    @PostMapping("vl/word")
    @SaCheckLogin
    public BaseResult<String> vlWord(@RequestBody ZcVlForm form) {

        // 图片预处理
        String url = ProcessImgUtil.processImg(form.imgUrl);

        // 限制频率，1分钟最多2次
        boolean isLimit = UserBehaviorUtil.isLimit(
                GlobalConstants.TENANT_OCR_LIMIT_REDIS_KEY + StpUtil.getLoginIdAsLong(),
                10,
                Duration.ofMinutes(1)
        );

        if (isLimit) {
            return BaseResult.error("您当前频率过高，请稍后等待30秒后重试");
        }

        // 识别内容
        String prompt = "";
        // 识别词语
        if (form.getType() == 0) {
            prompt = """
                    请识别图片中的中文词语，并以空格为间隔进行返回，不需要返回其他任何东西和提示，只需要识别出的中文词语就可以。
                    请过滤掉图片中和中文词语无关的内容。
                    """;
        }
        // 识别课文
        else if (form.getType() == 1) {
            prompt = """
                    请识别图片中的课文，，不需要返回其他任何东西，只需要识别词语就可以。
                    请过滤掉图片中和课文无关的内容。
                    """;
        }

        LLMService llmService = getLLMService();
        GPTAnswer answer = llmService.vl(url, prompt, "");

        return BaseResult.success(answer.getAnswer());
    }

    @Data
    public static class WordAudioForm {

        @Schema(title = "生字词语")
        private String word;

    }

    @Operation(summary = "获取单个播报音频")
    @PostMapping("getAudio")
    public BaseResult<String> getAudio(@RequestBody WordAudioForm form) {

        if (StrUtil.isBlank(form.getWord())){
            return BaseResult.error("请输入词语");
        }

        PgZcChineseWord wordInfo = pgZcChineseWordService.getOrCreateWordInfo(form.getWord());

        return BaseResult.success(
                wordInfo.getAudioUrl()
        );
    }

    @Data
    public static class WordAudio {

        @Schema(title = "词语列表")
        private List<String> words;

        @Schema(title = "顺序", description = "1：顺序，2：乱序")
        private Integer orderType;

    }

    @Operation(summary = "进行语文词语语音播报")
    @PostMapping("audioList")
    public BaseResult<List<PgZcChineseWordVO>> audioList(@RequestBody WordAudio form) {

        // 初始化返回数据
        List<PgZcChineseWordVO> wordList = new ArrayList<>();

        List<String> words = form.getWords();

        if (CollUtil.isEmpty(words)) {
            return BaseResult.success(wordList);
        }

        // 乱序
        if (form.getOrderType() == 2) {
            Collections.shuffle(words);
        }

        words.stream()
                .map(String::trim)
                .filter(StrUtil::isNotBlank)
                .forEach(word -> {
            PgZcChineseWord wordInfo = pgZcChineseWordService.getOrCreateWordInfo(word);

            if (ObjectUtil.isNotNull(wordInfo)) {
                PgZcChineseWordVO wordVO = BeanUtil.copyProperties(wordInfo, PgZcChineseWordVO.class);

                wordList.add(wordVO);
            }

        });
        return BaseResult.success(wordList);
    }


}
