package com.pgb.service.custom;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.TimeInterval;
import cn.hutool.core.img.Img;
import cn.hutool.core.img.ImgUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.IORuntimeException;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.URLUtil;
import cn.hutool.crypto.digest.DigestUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.pgb.common.oss.service.OssService;
import com.pgb.service.domain.common.image.FilePaperImg;
import lombok.extern.slf4j.Slf4j;

import java.awt.image.BufferedImage;
import java.io.File;
import java.util.Objects;

@Slf4j
public class ProcessImgUtil {

    private static final OssService ossService = SpringUtil.getBean(OssService.class);;

    public static void processImg(FilePaperImg img) {
        TimeInterval timer = DateUtil.timer();
        BufferedImage bufferedImage = null;
        File imgFile = null;

        try {
            // 0. 创建临时文件
            imgFile = FileUtil.createTempFile(".jpg", true);

            // 1. 读取原始图片
            bufferedImage = readImageWithFallback(img.getImgUrl());

            // 2. 处理图片尺寸
            bufferedImage = processImageDimensions(bufferedImage);

            // 3. 处理图片大小和格式
            bufferedImage = compressImage(bufferedImage);

            // 4. 应用最终处理逻辑
            uploadFile(img, bufferedImage, timer, imgFile);

        } catch (Exception e) {
            log.error("图片处理失败，将使用无效图片替换: {}", e.getMessage());
            // 图片处理失败时，使用无效图片URL替换
            img.setImgUrl("https://cdn.pigaibang.com/common/xcx-zw/invalid-image.png");
            log.info("已将图片替换为无效图片: {}", img.getImgUrl());
        } finally {
            // 6. 强制释放资源
            releaseResources(bufferedImage, imgFile);
        }
    }

    public static String processImg(String imgUrl) {
        FilePaperImg img = new FilePaperImg();
        img.setImgUrl(imgUrl);

        processImg(img);

        return img.getImgUrl();
    }

    public BufferedImage processImg(BufferedImage bufferedImage) {
        TimeInterval timer = DateUtil.timer();
        File imgFile = null;

        try {
            // 0. 创建临时文件
            imgFile = FileUtil.createTempFile(".jpg", true);

            // 2. 处理图片尺寸
            bufferedImage = processImageDimensions(bufferedImage);

            // 3. 处理图片大小和格式
            bufferedImage = compressImage(bufferedImage);

            // 图片处理完成
            log.info("图片处理完成，用时：{}", timer.intervalPretty());

        } catch (Exception e) {
            log.error("图片处理失败: {}", e.getMessage());
            throw new RuntimeException("图片处理失败", e);
        } finally {
            // 6. 强制释放资源
            FileUtil.del(imgFile);
        }

        return bufferedImage;
    }

    // 辅助方法：读取图片（含格式回退逻辑）
    private static BufferedImage readImageWithFallback(String imgUrl) {
        try {
            // return ImgUtil.read(URLUtil.url(ossService.getFormat(imgUrl, "jpg")));
            return ImgUtil.read(URLUtil.url(imgUrl));
        } catch (IllegalArgumentException | IllegalStateException | IORuntimeException e) {
            log.info("图片格式无效，尝试转换格式: {}", e.getMessage());
            return ImgUtil.read(URLUtil.url(ossService.getFormat(imgUrl, "jpg")));
        }
    }

    // 辅助方法：处理图片尺寸
    private static BufferedImage processImageDimensions(BufferedImage image) {
        if (image.getHeight() > 4000 || image.getWidth() > 4000) {
            double ratio = Math.min(4000.0 / image.getHeight(), 4000.0 / image.getWidth());
            BufferedImage scaledImage = ImgUtil.toBufferedImage(ImgUtil.scale(image, (float) ratio), ImgUtil.IMAGE_TYPE_JPG);
            image.flush(); // 释放原始图片内存
            image = scaledImage;
            log.info("图片尺寸过大，执行图片缩放：当前尺寸：{}-{}", image.getWidth(), image.getHeight());
        }

        return image;
    }

    // 辅助方法：处理图片大小
    private static BufferedImage compressImage(BufferedImage image) {
        Img imgProcessor = Img.from(image);
        imgProcessor.setQuality(0.8);
        BufferedImage sizeImage = ImgUtil.toBufferedImage(imgProcessor.getImg(), ImgUtil.IMAGE_TYPE_JPG);
        image.flush(); // 释放原始图片内存
        image = sizeImage;

        return image;
    }

    // 辅助方法：应用最终处理逻辑
    private static void uploadFile(FilePaperImg img, BufferedImage image, TimeInterval timer, File imgFile) {
        String key = buildOssKey(img, image);

        // 如果相同
        if (URLUtil.getPath(img.getImgUrl()).endsWith(key)) {
            log.info("\n图片无需处理，跳过上传：{}", img.getImgUrl());
            return;
        }

        // 写入图片
        ImgUtil.write(image, imgFile);

        // 上传新图片

        String newUrl = ossService.putFile(key, imgFile);
        // 如果不是一个图，标记删除旧图片
        if (!StrUtil.equals(newUrl, img.getImgUrl())) {
            ossService.setTagList(URLUtil.getPath(img.getImgUrl()), "Deleted");
        }

        // 更新图片信息
        img.setImgUrl(newUrl);
        img.setImgH(image.getHeight());
        img.setImgW(image.getWidth());

        log.info("\n预处理图片：{}；耗时：{}，压缩后大小：{}",
                newUrl,
                timer.intervalPretty(),
                FileUtil.readableFileSize(imgFile)
        );
    }

    // 辅助方法：生成OSS Key
    private static String buildOssKey(FilePaperImg img, BufferedImage image) {
        String path = URLUtil.getPath(img.getImgUrl());
        if (path.startsWith("/")) {
            path = StrUtil.subSuf(path, 1);
        }
        if (path.contains("/")) {
            path = StrUtil.subBefore(path, "/", true);
            path += "/";
        } else {
            path = "";
        }
        return path + DigestUtil.md5Hex(ImgUtil.toBytes(image, ImgUtil.IMAGE_TYPE_JPG)) + ".jpg";
    }

    // 辅助方法：强制释放资源
    private static void releaseResources(BufferedImage image, File... files) {
        if (image != null) {
            image.flush(); // 显式释放内存
        }
        for (File f : files) {
            if (f != null && f.exists()) {
                FileUtil.del(f); // 确保删除临时文件
            }
        }
    }
}
