package com.pgb.service.db.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.pgb.service.db.PgClassesService;
import com.pgb.service.db.PgHomeworkService;
import com.pgb.service.db.PgStudentService;
import com.pgb.service.domain.classes.PgClasses;
import com.pgb.service.domain.homework.PgHomework;
import com.pgb.service.domain.userAction.PgUserAction;
import com.pgb.service.db.PgUserActionService;
import com.pgb.service.enums.GradeEnum;
import com.pgb.service.enums.UserActionType;
import com.pgb.service.mapper.PgUserActionMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Date;

/**
* <AUTHOR>
* @description 针对表【pg_user_action】的数据库操作Service实现
* @createDate 2025-01-16 14:42:02
*/
@Service
@RequiredArgsConstructor
public class PgUserActionServiceImpl extends ServiceImpl<PgUserActionMapper, PgUserAction>
    implements PgUserActionService{

    private final PgHomeworkService pgHomeworkService;

    private final PgClassesService pgClassesService;

    private final PgStudentService pgStudentService;

    @Override
    public boolean creatExampleClassHomework(Long userId) {

        // 查看是否创建过班级
        boolean isHaveClass = pgClassesService.exists(new LambdaQueryWrapper<PgClasses>()
                .eq(PgClasses::getUserId, userId)
        );

        // 初始化班级
        PgClasses classes = new PgClasses();

        // 如果没有
        if (isHaveClass) return false;

        // 查看是否创建过作业
        boolean isHaveHomework = pgHomeworkService.exists(new LambdaQueryWrapper<PgHomework>()
                .eq(PgHomework::getCreatorId, userId)
        );

        // 如果没有
        if (isHaveHomework) return false;

        // 查看是否创建过示例班级
        Boolean isCreate = isCreateClassHomework(userId, UserActionType.Sample_Homework);

        // 如果没有创建过示例班级
        if (isCreate) return false;

        // 创建示例班级
        classes = pgClassesService.createClass(
                userId,
                "示例班级",
                "1班",
                GradeEnum.GRADE_3
        );

        // 创建示例学生
        pgStudentService.createStudent(classes.getId(), "示例学生");

        // 创建示例作业
        pgHomeworkService.createSampleHomework(userId, classes.getId());

        // 创建作业示例记录
        create(userId, UserActionType.Sample_Homework);

        return true;
    }

    @Override
    public Boolean isCreateClassHomework(Long userId, UserActionType type) {

        PgUserAction classAction = getOne(new LambdaQueryWrapper<PgUserAction>()
                .eq(PgUserAction::getUserId, userId)
                .eq(PgUserAction::getType, type)
                .eq(PgUserAction::getStatus, 1));

        return ObjectUtil.isNotNull(classAction);
    }

    @Override
    public void create(Long userId, UserActionType type) {

        PgUserAction classAction = new PgUserAction();
        classAction.setUserId(userId);
        classAction.setType(type);
        classAction.setCreateTime(new Date());
        classAction.setStatus(1);
        save(classAction);
    }
}




