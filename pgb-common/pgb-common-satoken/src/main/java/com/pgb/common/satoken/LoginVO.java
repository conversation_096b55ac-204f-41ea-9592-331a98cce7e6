package com.pgb.common.satoken;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

@Data
@ToString
@EqualsAndHashCode
@AllArgsConstructor
@NoArgsConstructor
@Schema(title = "统一登录返回 VO")
@Builder
public class LoginVO<T> {
    @Schema(title = "手机号")
    private String phone;

    @Schema(title = "token")
    private String token;

    @Schema(title = "令牌过期时间", description = "时间戳类型")
    private Long expireTime;

    @Schema(title = "头像 URL")
    private String avatarUrl;

    @Schema(title = "名称")
    private String name;

    @Schema(title = "用户其余信息")
    private T userInfo;
}
