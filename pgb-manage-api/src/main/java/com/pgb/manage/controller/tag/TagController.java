package com.pgb.manage.controller.tag;

import cn.dev33.satoken.annotation.SaCheckRole;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.pgb.common.core.global.BaseResult;
import com.pgb.common.core.global.RoleConstants;
import com.pgb.common.core.validate.AddGroup;
import com.pgb.common.core.validate.UpdateGroup;
import com.pgb.service.db.PgTagService;
import com.pgb.service.db.PgTagUserService;
import com.pgb.service.domain.query.TagQuery;
import com.pgb.service.domain.tag.PgTag;
import com.pgb.service.domain.tag.PgTagDTO;
import com.pgb.service.domain.tag.PgTagUser;
import com.pgb.service.domain.tag.PgTagVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * Created by 2024/12/23 18:26
 */
@Tag(name = "管理端/标签管理")
@RestController("ManageTagController")
@Transactional(rollbackFor = Exception.class)
@RequestMapping("/manage/tag")
@RequiredArgsConstructor
@Slf4j
@SaCheckRole(RoleConstants.Manager)
public class TagController {

    private final PgTagService pgTagService;

    private final PgTagUserService pgTagUserService;

    @Operation(summary = "标签列表")
    @GetMapping("list")
    public BaseResult<List<PgTagVO>> list() {

        List<PgTagVO> list = pgTagService.list().stream()
                .map(
                        tag -> {
                            PgTagVO pgTagVO = BeanUtil.copyProperties(tag, PgTagVO.class);

                            // 使用该标签人数
                            pgTagVO.setUserNum(pgTagUserService.count(new LambdaQueryWrapper<PgTagUser>()
                                    .eq(PgTagUser::getTagId, tag.getId())));

                            return pgTagVO;
                        }
                ).toList();

        return BaseResult.success(list);
    }

    @Operation(summary = "标签列表")
    @PostMapping("page")
    public BaseResult<IPage<PgTagVO>> page(@RequestBody TagQuery query) {

        IPage<PgTagVO> page = pgTagService.page(query.toMpPageSortByCreateTime(), new LambdaQueryWrapper<PgTag>()
                .like(StrUtil.isNotBlank(query.getName()), PgTag::getName, query.getName())
        ).convert(
                tag -> {
                    PgTagVO pgTagVO = BeanUtil.copyProperties(tag, PgTagVO.class);

                    // 使用该标签人数
                    pgTagVO.setUserNum(pgTagUserService.count(new LambdaQueryWrapper<PgTagUser>()
                            .eq(PgTagUser::getTagId, tag.getId())));

                    return pgTagVO;
                }
        );

        return BaseResult.success(page);
    }

    @Operation(summary = "新增标签")
    @PostMapping()
    public BaseResult<Boolean> post(@Validated({AddGroup.class}) @RequestBody PgTagDTO tagDTO) {
        PgTag pgTag = BeanUtil.copyProperties(tagDTO, PgTag.class);

        pgTag.setCreateTime(new Date());

        return BaseResult.success(
                pgTagService.save(pgTag)
        );
    }

    @Operation(summary = "修改标签")
    @PutMapping
    public BaseResult<Boolean> update(@Validated({UpdateGroup.class}) @RequestBody PgTagDTO cateDTO) {
        PgTag pgTag = BeanUtil.copyProperties(cateDTO, PgTag.class);
        return BaseResult.success(
                pgTagService.updateById(pgTag)
        );
    }

    @Operation(summary = "删除标签")
    @DeleteMapping("{id}")
    public BaseResult<Boolean> delete(@PathVariable Long id) {

        // 删除标签-用户 关联关系
        pgTagUserService.remove(new LambdaQueryWrapper<PgTagUser>()
                .eq(PgTagUser::getTagId, id)
        );

        return BaseResult.success(
                pgTagService.removeById(id)
        );
    }

}
