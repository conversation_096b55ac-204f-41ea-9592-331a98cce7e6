package com.pgb.service.domain.userAction;

import java.io.Serializable;

import java.util.Date;

import com.pgb.service.enums.UserActionType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.ToString;
import lombok.Builder;

/**
*
* @TableName pg_user_action
*/
@Schema(description = " VO")
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode
@ToString
@Builder
public class PgUserActionVO implements Serializable {

    @Schema(title = "用户行为状态", type = "string")
    private Long id;

    @Schema(title = "用户id")
    private Long userId;

    @Schema(title = "行为类型")
    private UserActionType type;

    @Schema(title = "状态")
    private Integer status;

    @Schema(title = "创建时间")
    private Date createTime;

    @Schema(title = "更新时间")
    private Date updateTime;

}
