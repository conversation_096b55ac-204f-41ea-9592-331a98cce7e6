package com.pgb.service.domain.distribution;

import java.io.Serializable;

import java.util.Date;

import com.pgb.common.core.validate.UpdateGroup;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.ToString;
import lombok.Builder;
import org.springframework.validation.annotation.Validated;
import jakarta.validation.constraints.NotNull;

/**
* 分销关系表
* @TableName pg_distribution
*/
@Schema(description = "分销关系表 DTO")
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode
@ToString
@Validated
@Builder
public class PgDistributionDTO implements Serializable {

    @Schema(title = "分销 id")
    @NotNull(message="[分销 id]不能为空", groups = {UpdateGroup.class})
    private Long id;

    @Schema(title = "邀请人id")
    private Long shareUserId;

    @Schema(title = "被邀请人 id")
    private Long userId;

    @Schema(title = "邀请是否成功")
    private Boolean isSuccess;

    @Schema(title = "邀请失败的原因")
    private Integer reason;

    @Schema(title = "创建时间")
    private Date createTime;

}
