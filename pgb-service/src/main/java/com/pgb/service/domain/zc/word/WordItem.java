package com.pgb.service.domain.zc.word;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.fasterxml.jackson.annotation.JsonGetter;
import com.pgb.service.domain.zc.common.MarkLoc;
import com.pgb.service.domain.zc.common.ZcChar;
import com.pgb.service.domain.zc.common.ZcLocation;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * Created by 2025/4/23 11:35
 */
@Data
public class WordItem {

    @Schema(title = "文字")
    private String word;

    @Schema(title = "拼音")
    private String pinyin;

    @Schema(title = "位置", description = "如果是批改结果，则是用户作答的位置")
    private ZcLocation location;

    @Schema(title = "标注位置", description = "包含正确及错误的")
    private List<MarkLoc> markLocation;

    @Schema(title = "字符信息")
    private List<ZcChar> charList;

    @Schema(title = "拼音信息")
    private List<ZcChar> pinyinList;

//    @Schema(title = "批改正误情况：0：未作答，1：正确，2：错误，3：无需标注")
//    private Integer rightType;

    @Schema(title = "用户填写的内容")
    private String userContent;

    @Schema(title = "学生汉字坐标")
    private List<ZcLocation> userTextLocationList = new ArrayList<>();


    @JsonGetter
    public List<ZcChar> getCharList() {
        if (CollUtil.isEmpty(this.charList)) {
            List<ZcChar> tempList = new ArrayList<>();
            String[] characters = ObjectUtil.defaultIfNull(this.word, "").split("");

            for (String character : characters) {
                ZcChar chars = new ZcChar();

                chars.setChars(character);
                tempList.add(chars);
            }
            this.charList = tempList;
        }

        return this.charList;
    }

    @JsonGetter
    public List<ZcChar> getPinyinList() {
        if (CollUtil.isEmpty(this.pinyinList)) {
            List<ZcChar> result = new ArrayList<>();
            String[] pinyins = ObjectUtil.defaultIfNull(this.pinyin, "").split(" ");

            for (String pinyin : pinyins) {
                ZcChar chars = new ZcChar();
                chars.setChars(pinyin);
                result.add(chars);
            }
            this.pinyinList = result;
        }

        return this.pinyinList;
    }

    @Schema(title = "当前区域坐标")
    @JsonGetter
    public ZcLocation getLocation() {

        if (CollUtil.isEmpty(this.userTextLocationList)) {
            return this.location;
        }

        ZcLocation location = new ZcLocation();
        // 最小的左边
        location.setLeft(
                (float) userTextLocationList.stream().mapToDouble(ZcLocation::getLeft).min().getAsDouble()
        );
        // 最大的width
        location.setWidth(
                (float) userTextLocationList.stream().mapToDouble(ZcLocation::getWidth).sum()
        );
        location.setTop(
                (float) userTextLocationList.stream().mapToDouble(ZcLocation::getTop).min().getAsDouble()
        );
        location.setHeight(
                (float) userTextLocationList.stream().mapToDouble(ZcLocation::getHeight).max().getAsDouble()
        );

        location.setPageNo(userTextLocationList.get(0).getPageNo());
        return location;
    }
}
