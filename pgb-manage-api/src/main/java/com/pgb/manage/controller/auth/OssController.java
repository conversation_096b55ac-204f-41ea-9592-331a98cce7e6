package com.pgb.manage.controller.auth;

import cn.dev33.satoken.annotation.SaCheckLogin;
import cn.dev33.satoken.annotation.SaCheckRole;
import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.util.IdUtil;
import com.pgb.common.core.global.BaseResult;
import com.pgb.common.core.global.RoleConstants;
import com.pgb.common.oss.domain.OssPresignUrl;
import com.pgb.common.oss.domain.UploadConfigForm;
import com.pgb.common.oss.service.OssService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.Duration;

@Tag(name = "管理端/权限/oss")
@RestController("UserAuthOssController")
@Transactional(rollbackFor = Exception.class)
@RequestMapping("/manage/auth/oss")
@RequiredArgsConstructor
@Slf4j
public class OssController {

    private final OssService ossService;

    @Operation(summary = "获取上传预签名")
    @PostMapping("/presignUrl")
    @SaCheckRole(RoleConstants.Manager)
    public BaseResult<OssPresignUrl> presignUrl(@Validated @RequestBody UploadConfigForm conf) {
        // 文件全路径
        StringBuilder key = new StringBuilder();
        // 用户
        key.append("system/zw").append("/");
        // 文件类型
        key.append(conf.getFileType()).append("/");
        // 文件名称
        key.append(IdUtil.fastSimpleUUID()).append(conf.getSuffix().startsWith(".") ? "" : ".").append(conf.getSuffix());

        return BaseResult.success(
                OssPresignUrl.builder()
                        .uploadUrl(
                                ossService.presignedUrl(key.toString(), Duration.ofMinutes(30))
                        )
                        .keyValue(key.toString())
                        .fileUrl(
                                ossService.getCdnUrl(key.toString())
                        )
                        .build()
        );
    }
}
