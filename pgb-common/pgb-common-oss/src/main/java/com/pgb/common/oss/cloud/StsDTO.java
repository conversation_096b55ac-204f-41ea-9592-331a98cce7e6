package com.pgb.common.oss.cloud;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;

@Schema(title = "OSS临时访问凭证 DTO")
@Data
@Builder
public class StsDTO {
    /**
     * 账户相关属性
     */
    @Schema(title = "secretId")
    private String tmpSecretId;

    @Schema(title = "TmpSecretKey")
    private String tmpSecretKey;

    @Schema(title = "Token")
    private String token;

    @Schema(title = "过期时间")
    private Long expiredTime;

    @Schema(title = "开始时间")
    private Long startTime;

    /**
     * OSS 相关属性
     */
    @Schema(title = "地域")
    private String region;

    @Schema(title = "存储空间名")
    private String bucketName;
}
