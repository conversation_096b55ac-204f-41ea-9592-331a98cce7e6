package com.pgb.service.domain.query;

import com.pgb.common.mybatis.global.PageQuery;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

/**
 * <AUTHOR>
 * Created by 2024/11/12 14:34
 */
@NoArgsConstructor // 生成无参构造方法
@AllArgsConstructor // 生成有参构造方法
@EqualsAndHashCode(callSuper = true)
@ToString
@Schema(name = "StudentAnswerQuery", description = "学生完成作业情况 搜索实体")
@Data
public class StudentAnswerQuery extends PageQuery {

    @Schema(title = "作答状态")
    private String status;

    @Schema(title = "学生姓名")
    private String studentName;
}
