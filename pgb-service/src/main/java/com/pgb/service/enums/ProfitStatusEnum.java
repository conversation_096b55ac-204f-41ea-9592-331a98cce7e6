package com.pgb.service.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;

/**
 * <AUTHOR>
 * Created by 2024/7/8 18:15
 */
@Schema(description = "分账 状态")
@AllArgsConstructor
public enum ProfitStatusEnum {

    @Schema(title = "待分账")
    PENDING(0, "待分账"),

    @Schema(title = "分账成功")
    SUCCESS(1, "分账成功"),

    @Schema(title = "已关闭")
    CLOSED(2, "已关闭");


    /**
     * 枚举值对应的整数值
     */
    @EnumValue
    public final int value;

    /**
     * 文字说明
     */
    public final String desc;
}
