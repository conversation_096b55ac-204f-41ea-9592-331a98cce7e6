package com.pgb.service.custom;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.crypto.digest.DigestUtil;
import com.pgb.common.core.global.BaseResult;
import com.pgb.common.core.global.GlobalCode;
import com.pgb.common.redis.RedisUtils;
import com.pgb.service.domain.GlobalXcxConstants;
import com.pgb.service.interfaces.Debounce;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.util.Arrays;

/**
 * <AUTHOR>
 * Created by 2025/3/31 16:18
 */
@Aspect
@Component
@Slf4j
public class DebounceAop {

    @Around("@annotation(debounce)")
    public Object handleDebounce(ProceedingJoinPoint joinPoint, Debounce debounce) throws Throwable {

        try {
            // 唯一key
            String key = generateKey(joinPoint);

            // 获取
            Boolean isExist = RedisUtils.getCacheObject(key);
            if (ObjectUtil.isNotNull(isExist)) {
                return BaseResult.code(GlobalCode.Repeat_Submit);
            }

            // 设置key过期时间
            RedisUtils.setCacheObject(key, true, Duration.ofMillis(debounce.timeout()));
        } catch (Exception e) {
            log.error("防抖中间--操作异常", e);
        }

        return joinPoint.proceed();
    }

    private String generateKey(ProceedingJoinPoint joinPoint) {
        String method = joinPoint.getSignature().getName();
        long userId = StpUtil.getLoginIdAsLong();
        String md5 = DigestUtil.md5Hex(Arrays.toString(joinPoint.getArgs()));
//        log.info("参数：{}",md5);

        return GlobalXcxConstants.XCX_DEBOUNCE + method + ":" + userId + ":" + md5;
    }


}
