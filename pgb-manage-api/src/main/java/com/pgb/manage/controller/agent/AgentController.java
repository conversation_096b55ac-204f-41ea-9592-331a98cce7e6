package com.pgb.manage.controller.agent;

import cn.dev33.satoken.annotation.SaCheckRole;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.pgb.common.core.global.BaseResult;
import com.pgb.common.core.global.GlobalCode;
import com.pgb.common.core.global.RoleConstants;
import com.pgb.service.db.PgAgentCategoryService;
import com.pgb.service.db.PgAgentCollectService;
import com.pgb.service.db.PgAgentService;
import com.pgb.service.domain.agent.*;
import com.pgb.service.domain.agent.category.PgAgentCategory;
import com.pgb.service.domain.agent.collect.PgAgentCollect;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Tag(name = "管理端/智能体")
@RestController("AgentController")
@Transactional(rollbackFor = Exception.class)
@RequestMapping("/manage/agent")
@RequiredArgsConstructor
@Slf4j
@SaCheckRole(RoleConstants.Manager)
public class AgentController {
    private final PgAgentCategoryService pgAgentCategoryService;

    private final PgAgentService pgAgentService;

    private final PgAgentCollectService pgAgentCollectService;

    public record AgentInfoQuery(String name, Long categoryId) {
    }

    @Operation(summary = "获取智能体列表")
    @PostMapping("list")
    public BaseResult<List<PgAgent>> list(@RequestBody AgentInfoQuery query) {

        LambdaQueryWrapper<PgAgent> queryWrapper = new LambdaQueryWrapper<>();
        // 模糊查询
        queryWrapper.like(StrUtil.isNotBlank(query.name()), PgAgent::getName, query.name());

        // 分类查询
        if (ObjectUtil.isNotNull(query.categoryId())) {
            PgAgentCategory category = pgAgentCategoryService.getById(query.categoryId());

            if (ObjectUtil.isNull(category) || StrUtil.isBlank(category.getAgentIds())) {
                return BaseResult.success(new ArrayList<>());
            }

            // 分割id，保持原始顺序
            List<Long> agentIds = StrUtil.split(category.getAgentIds(), StrUtil.COMMA)
                    .stream()
                    .map(Long::parseLong)
                    .toList();

            if (agentIds.isEmpty()) {
                return BaseResult.success(new ArrayList<>());
            }

            queryWrapper.in(PgAgent::getId, agentIds);

            // 查询智能体列表
            List<PgAgent> agentList = pgAgentService.list(queryWrapper);

            // 按照分类中agentIds的顺序重新排序
            Map<Long, PgAgent> agentMap = agentList.stream()
                    .collect(Collectors.toMap(PgAgent::getId, agent -> agent));

            List<PgAgent> sortedList = agentIds.stream()
                    .map(agentMap::get)
                    .filter(ObjectUtil::isNotNull) // 过滤掉不存在的智能体
                    .collect(Collectors.toList());

            return BaseResult.success(sortedList);
        }

        // 非分类查询，按创建时间降序排序
        List<PgAgent> list = pgAgentService.list(queryWrapper
                .orderByDesc(PgAgent::getCreateTime));

        return BaseResult.success(list);
    }

    @Operation(summary = "删除智能体")
    @DeleteMapping("delete/{id}")
    public BaseResult<Boolean> delete(@PathVariable Long id) {

        PgAgent agent = pgAgentService.getById(id);

        if (ObjectUtil.isNull(agent)) {
            return BaseResult.code(GlobalCode.Item_Null);
        }

        // 删除用户收藏关联
        pgAgentCollectService.remove(new LambdaQueryWrapper<PgAgentCollect>()
                .eq(PgAgentCollect::getAgentId, id));

        return BaseResult.success(
                pgAgentService.removeById(id)
        );
    }

    @Operation(summary = "修改智能体")
    @PostMapping("update/{id}")
    public BaseResult<Boolean> update(@PathVariable Long id, @RequestBody PgAgentDTO dto) {

        PgAgent agent = pgAgentService.getById(id);

        if (ObjectUtil.isNull(agent)) {
            return BaseResult.code(GlobalCode.Item_Null);
        }

        BeanUtil.copyProperties(dto, agent);

        return BaseResult.success(
                pgAgentService.updateById(agent)
        );
    }

    @Operation(summary = "新增智能体")
    @PostMapping("create")
    public BaseResult<Boolean> create(@RequestBody PgAgentDTO dto) {

        PgAgent pgAgent = JSONUtil.toBean(JSONUtil.toJsonStr(dto), PgAgent.class);

        pgAgent.setCreateTime(new Date());
        return BaseResult.success(
                pgAgentService.save(pgAgent)
        );
    }

    @Operation(summary = "根据id获取智能体内容")
    @GetMapping("detail/{id}")
    public BaseResult<PgAgentDTO> getAgentDetail(@PathVariable Long id) {

        PgAgent agent = pgAgentService.getById(id);

        if (ObjectUtil.isNull(agent)) {
            return BaseResult.code(GlobalCode.Item_Null);
        }

        return BaseResult.success(
                JSONUtil.toBean(JSONUtil.toJsonStr(agent), PgAgentDTO.class)
        );
    }



}
