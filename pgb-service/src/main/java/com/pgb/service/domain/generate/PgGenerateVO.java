package com.pgb.service.domain.generate;

import java.io.Serializable;

import java.util.Date;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.ToString;
import lombok.Builder;

/**
* 【万能写】记录表
* @TableName pg_generate
*/
@Schema(description = "【万能写】记录表 VO")
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode
@ToString
@Builder
public class PgGenerateVO implements Serializable {

    @Schema(title = "万能写记录", type = "string")
    private Long id;

    @Schema(title = "创建时间")
    private Date createTime;

    @Schema(title = "用户id")
    private Long userId;

    @Schema(title = "类型（全文/段落/英文）")
    private Integer type;

    @Schema(title = "参数表单")
    private Object form;

    @Schema(title = "AI返回内容")
    private String content;

    @Schema(title = "纯文本段落")
    private String plainText;


}
