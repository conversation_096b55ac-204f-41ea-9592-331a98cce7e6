package com.pgb.service.domain.query;

import com.pgb.common.mybatis.global.PageQuery;
import com.pgb.service.enums.GradeEnum;
import com.pgb.service.enums.SubjectEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * Created by 2024/7/17 10:48
 */

@EqualsAndHashCode(callSuper = true)
@Schema(description = "题目 搜索实体")
@Data
public class QuestionQuery extends PageQuery {

    @Schema(title = "关键词")
    private String keyword;

    @Schema(title = "年级")
    private GradeEnum grade;

    @Schema(title = "科目类型")
    private SubjectEnum subject;

    @Schema(title = "册别/学期")
    private String semester;
}
