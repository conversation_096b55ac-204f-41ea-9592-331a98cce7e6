package com.pgb.service.domain.answer.model;

import java.io.Serializable;

import java.util.Date;
import java.util.List;

import com.pgb.service.domain.answer.PgAnswer;
import com.pgb.service.domain.common.image.FilePaperImg;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.ToString;
import lombok.Builder;

/**
 * 范文表
 *
 * @TableName pg_answer_model
 */
@Schema(description = "范文表 VO")
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode
@ToString
@Builder
public class PgAnswerModelVO implements Serializable {

    @Schema(title = "id")
    private Long id;

    @Schema(title = "关联范文id")
    private Long answerId;

    @Schema(title = "评语（如：被选为范文的原因）")
    private String comment;

    @Schema(title = "创建时间")
    private Date createTime;

    @Schema(title = "所属作业id")
    private Long homeworkId;

    @Schema(title = "所属用户id")
    private Long userId;

    @Schema(title = "用户作答图片 URL")
    private List<FilePaperImg> userImgAnswerList;

    @Schema(title = "得分")
    private Integer userScore;

    @Schema(title = "学生姓名")
    private String studentName;

    @Schema(title = "学生学号")
    private String studentNo;

    @Schema(title = "作文创建时间")
    private Date zwCreateTime;

    @Schema(title = "排序")
    private Integer sort;

}
