package com.pgb.manage.controller.crm;

import cn.dev33.satoken.annotation.SaCheckRole;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.pgb.common.core.global.BaseResult;
import com.pgb.common.core.global.GlobalCode;
import com.pgb.common.core.global.RoleConstants;
import com.pgb.common.pay.enums.OrderTypeEnum;
import com.pgb.manage.domain.dto.OpenMemberDTO;
import com.pgb.manage.domain.dto.UserVipTagDTO;
import com.pgb.manage.domain.query.MemberQuery;
import com.pgb.service.db.*;
import com.pgb.service.domain.tag.PgTag;
import com.pgb.service.domain.tag.PgTagUser;
import com.pgb.service.domain.user.PgUsers;
import com.pgb.service.domain.user.PgUsersVO;
import com.pgb.service.domain.vip.PgVip;
import com.pgb.service.domain.vip.PgVipVO;
import com.pgb.service.enums.ChannelTypeEnum;
import com.pgb.service.enums.VipTypeEnum;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * Created by 2024/8/5 12:06
 */
@Tag(name = "管理端/会员管理")
@RestController("ManageMemberController")
@Transactional(rollbackFor = Exception.class)
@RequestMapping("/manage/member")
@RequiredArgsConstructor
@Slf4j
@SaCheckRole(RoleConstants.Manager)
public class MemberController {

    private final PgUsersService pgUsersService;

    private final PgVipService pgVipService;

    private final PgOrderService pgOrderService;

    private final PgTagService pgTagService;

    private final PgTagUserService pgTagUserService;


    @Operation(summary = "根据会员一键贴标签")
    @PostMapping("tag/batch")
    public BaseResult<Integer> tagBatch(@RequestBody UserVipTagDTO form) {

        // 添加 是 的逻辑
        List<Long> isUserIds = new ArrayList<>();
        for (VipTypeEnum vipTypeEnum : form.getIsTypeList()) {
            List<Long> userIds = pgVipService.list(new LambdaQueryWrapper<PgVip>()
                            .in(PgVip::getVipType, vipTypeEnum)
                    )
                    .stream()
                    .map(PgVip::getUserId)
                    .distinct()
                    .toList();

            isUserIds.addAll(userIds);
        }

        // 添加 否 的逻辑
        List<Long> notUserIds = new ArrayList<>();
        for (VipTypeEnum vipTypeEnum : form.getNotTypeList()) {
            List<Long> userIds = pgVipService.list(new LambdaQueryWrapper<PgVip>()
                            .in(PgVip::getVipType, vipTypeEnum)
                    )
                    .stream()
                    .map(PgVip::getUserId)
                    .distinct()
                    .toList();

            notUserIds.addAll(userIds);
        }

        // 目标用户
        List<Long> resultUserIds = isUserIds.stream()
                .filter(userId -> !notUserIds.contains(userId))
                .toList();

        // 筛选符合条件的用户
        List<PgUsers> users = pgUsersService.list(new LambdaQueryWrapper<PgUsers>()
                .in(PgUsers::getId, resultUserIds));

        for (Long tagId : form.getTagIds()) {
            PgTag tag = pgTagService.getById(tagId);

            users.forEach(user -> {
                if (!pgTagUserService.exists(new LambdaQueryWrapper<PgTagUser>()
                        .eq(PgTagUser::getUserId, user.getId())
                        .eq(PgTagUser::getTagId, tag.getId()))) {

                    // 保存关联关系
                    PgTagUser tagUser = new PgTagUser();
                    tagUser.setUserId(user.getId());
                    tagUser.setTagId(tag.getId());
                    tagUser.setCreateTime(new Date());

                    pgTagUserService.save(tagUser);
                }
            });
        }

        return BaseResult.success(users.size());
    }

    @Operation(summary = "后台开通会员")
    @PostMapping("open/{key}")
    public BaseResult<Boolean> open(@RequestBody OpenMemberDTO dto, @PathVariable String key) {

        if (!"pgb_manage".equals(key)) {
            return BaseResult.code(GlobalCode.Permission_Not);
        }

        // 校验手机号
        if (StrUtil.isBlank(dto.getPhone()) || dto.getPhone().length() != 11) {
            return BaseResult.code(GlobalCode.Param_Wrong);
        }

        // 开通会员
        pgOrderService.openVip(dto.getPhone(), dto.getType(), ChannelTypeEnum.SYSTEM, OrderTypeEnum.Import);

        return BaseResult.success(true);
    }

    @Operation(summary = "会员充值列表")
    @PostMapping("list")
    public BaseResult<IPage<PgVipVO>> list(@RequestBody MemberQuery query) {

        LambdaQueryWrapper<PgVip> queryWrapper = new LambdaQueryWrapper<>();

        // 根据手机号筛选
        if (StrUtil.isNotEmpty(query.getPhone())) {

            List<Long> userIds = pgUsersService.list(new LambdaQueryWrapper<PgUsers>()
                    .like(StrUtil.isNotEmpty(query.getPhone()), PgUsers::getPhone, query.getPhone())
            ).stream().map(PgUsers::getId).toList();

            if (userIds.isEmpty()) {
                return BaseResult.success(new Page<>());
            } else {
                queryWrapper.in(PgVip::getUserId, userIds);
            }
        }

        IPage<PgVipVO> page = pgVipService.page(query.toMpPageSortByCreateTime(), queryWrapper
                        .eq(ObjectUtil.isNotNull(query.getType()), PgVip::getVipType, query.getType())
                        .and(ObjectUtil.isNotEmpty(query.getTimes()), i -> i.between(
                                PgVip::getCreateTime,
                                query.getTimes().get(0),
                                query.getTimes().get(1)
                        )))
                .convert(vip -> {
                    PgVipVO vipVO = BeanUtil.copyProperties(vip, PgVipVO.class);
                    PgUsers user = pgUsersService.getById(vip.getUserId());

                    if (ObjectUtil.isNotNull(user)) {

                        // 用户手机号
                        vipVO.setPhone(user.getPhone());

                        // 会员过期时间
                        vipVO.setExpireTime(user.getVipExpireTime());
                    }

                    return vipVO;
                });

        return BaseResult.success(page);
    }

    @Operation(summary = "会员列表")
    @PostMapping("vip/user")
    public BaseResult<IPage<PgUsersVO>> vipUsers(@RequestBody MemberQuery query) {

        LambdaQueryWrapper<PgUsers> queryWrapper = new LambdaQueryWrapper<>();

        // 购买过的会员类型 年卡/月卡/周卡
        if (!query.getBuyTypeList().isEmpty()) {
            List<Long> vipUserIds = pgVipService.list(new LambdaQueryWrapper<PgVip>()
                            .in(PgVip::getVipType, query.getBuyTypeList()))
                    .stream()
                    .map(PgVip::getUserId)
                    .toList();

            if (vipUserIds.isEmpty()) {
                return BaseResult.success(new Page<>());
            } else {
                queryWrapper.in(PgUsers::getId, vipUserIds);
            }
        }

        // 最新开通时间范围筛选
        if (ObjectUtil.isNotNull(query.getTimes()) && query.getTimes().size() == 2) {
            List<Long> timeUserIds = pgVipService.list(new LambdaQueryWrapper<PgVip>()
                            .select(PgVip::getUserId)
                            .between(PgVip::getCreateTime, query.getTimes().get(0), query.getTimes().get(1))
                            // 最新时间
                            .orderByDesc(PgVip::getCreateTime)
                            .groupBy(PgVip::getUserId, PgVip::getCreateTime))
                    .stream()
                    .map(PgVip::getUserId)
                    .toList();

            if (timeUserIds.isEmpty()) {
                return BaseResult.success(new Page<>());
            } else {
                queryWrapper.in(PgUsers::getId, timeUserIds);
            }
        }

        // 筛选是新购还是续费
        if (ObjectUtil.isNotNull(query.getBuyType())) {

            List<Long> buyTypeUserIds = pgVipService.list(new LambdaQueryWrapper<PgVip>()
                            .select(PgVip::getUserId)
                            .groupBy(PgVip::getUserId)
                            .having(query.getBuyType() == 0 ? "COUNT(*)=1" : "COUNT(*)>=2"))
                    .stream()
                    .map(PgVip::getUserId)
                    .toList();

            if (buyTypeUserIds.isEmpty()) {
                return BaseResult.success(new Page<>());
            } else {
                queryWrapper.in(PgUsers::getId, buyTypeUserIds);
            }
        }

        IPage<PgUsersVO> page = pgUsersService.page(query.toMpPageSortByCreateTime(), queryWrapper
                        // 有会员过期时间  包括已过期的
                        .isNotNull(PgUsers::getVipExpireTime)
                        .like(ObjectUtil.isNotNull(query.getPhone()), PgUsers::getPhone, query.getPhone())
                        // 已过期
                        .le(ObjectUtil.isNotNull(query.getIsExpired()) && query.getIsExpired().equals(true), PgUsers::getVipExpireTime, new Date())
                        // 未过期
                        .ge(ObjectUtil.isNotNull(query.getIsExpired()) && query.getIsExpired().equals(false), PgUsers::getVipExpireTime, new Date())
                )
                .convert(user -> {
                            PgUsersVO usersVO = BeanUtil.copyProperties(user, PgUsersVO.class);

                            List<PgVip> list = pgVipService.list(new LambdaQueryWrapper<PgVip>()
                                    .eq(PgVip::getUserId, user.getId()));

                            PgVip vip = pgVipService.getOne(new LambdaQueryWrapper<PgVip>()
                                    .eq(PgVip::getUserId, user.getId())
                                    .orderByDesc(PgVip::getCreateTime)
                                    .last("LIMIT 1"));
                            if (ObjectUtil.isNotNull(vip)) {
                                // 最新开通时间
                                usersVO.setOpenTime(vip.getCreateTime());
                                usersVO.setVipType(vip.getVipType());
                            }

                            // 判断是续费 还是新购
                            if (!list.isEmpty()) {
                                if (list.size() > 1) {
                                    usersVO.setBuyType(1);
                                }
                                // 新购
                                else {
                                    usersVO.setBuyType(0);
                                }
                            }
                            return usersVO;
                        }
                );

        return BaseResult.success(page);
    }

}



