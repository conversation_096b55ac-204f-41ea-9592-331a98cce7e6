package com.pgb.student.controller.auth;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.pgb.common.core.global.BaseResult;
import com.pgb.service.db.PgAnswerService;
import com.pgb.service.db.PgUserConfigService;
import com.pgb.service.db.PgUsersService;
import com.pgb.service.domain.answer.PgAnswer;
import com.pgb.service.domain.user.PgUsers;
import com.pgb.service.domain.userConfig.PgUserConfig;
import com.pgb.service.domain.userConfig.StudentReportConfigDTO;
import com.pgb.service.enums.UserConfigEnum;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * Created by 2025/3/13 17:35
 */
@Tag(name = "学生端/权限/配置")
@RestController("StudentAuthConfigController")
@RequestMapping("/student/auth/config")
@RequiredArgsConstructor
@Slf4j
@DS("pgb")
public class ConfigController {

    private final PgUserConfigService pgUserConfigService;

    private final PgAnswerService pgAnswerService;

    private final PgUsersService pgUsersService;

    @Operation(summary = "获取学生端查看批改结果配置")
    @GetMapping("{answerId}")
    public BaseResult<StudentReportConfigDTO> getConfig(@PathVariable Long answerId) {

        // 获取作业对应的老师
        PgAnswer answer = pgAnswerService.getById(answerId);

        if (ObjectUtil.isNull(answer.getUserId())) {
            return BaseResult.error("班级老师不存在");
        }

        PgUsers users = pgUsersService.getById(answer.getUserId());
        PgUserConfig config = pgUserConfigService.getByKey(UserConfigEnum.STUDENT_REPORT, users.getId());

        // 若没设置过
        if (ObjectUtil.isNull(config)) {
            return BaseResult.success(new StudentReportConfigDTO());
        }

        return BaseResult.success(
                BeanUtil.toBean(config.getValue(), StudentReportConfigDTO.class)
        );
    }
}
