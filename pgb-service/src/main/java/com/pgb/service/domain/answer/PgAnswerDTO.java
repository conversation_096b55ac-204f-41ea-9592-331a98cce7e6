package com.pgb.service.domain.answer;

import java.io.Serializable;

import java.util.Date;
import java.util.List;

import com.pgb.service.domain.answer.polish.PolishInfo;
import com.pgb.service.domain.common.image.FilePaperImg;
import com.pgb.service.enums.CorrectStatusEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.ToString;
import lombok.Builder;
import org.springframework.validation.annotation.Validated;
import jakarta.validation.constraints.NotNull;
import com.pgb.common.core.validate.UpdateGroup;

/**
 * @TableName pg_answer
 */
@Schema(description = " DTO")
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode
@ToString
@Validated
@Builder
public class PgAnswerDTO implements Serializable {

    @Schema(title = "题库，做题记录", type = "string")
    @NotNull(message = "[题库，做题记录]不能为空", groups = {UpdateGroup.class})
    private Long id;

    @Schema(title = "用户id")
    private Long userId;

    @Schema(title = "用户本题作答，含题目及批改信息")
    private Object answer;

    @Schema(title = "批改作答")
    private Object correctResult;

//    @Schema(title = "用户得分")
//    private BigDecimal userScore;
//
//    @Schema(title = "总分")
//    private Integer totalScore;

    @Schema(title = "批改状态")
    private CorrectStatusEnum status;

    @Schema(title = "本题消耗脑力值数量")
    private Long aiTokens;

    @Schema(title = "本题批改满意度，如果开启批改反馈")
    private Integer feedback;

    @Schema(title = "批改所需时长，单位：秒")
    private Integer correctDuration;

    @Schema(title = "AI批改完成时间")
    private Date correctTime;

    @Schema(title = "创建时间")
    private Date createTime;

    @Schema(title = "备注名称")
    private String name;

    @Schema(title = "是否是范文")
    private Boolean isModel;

    @Schema(title = "润色记录")
    private List<PolishInfo> polishList;

}
