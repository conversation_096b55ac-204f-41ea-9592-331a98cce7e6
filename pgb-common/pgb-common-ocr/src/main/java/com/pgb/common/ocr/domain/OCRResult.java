package com.pgb.common.ocr.domain;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
@Schema(title = "OCR 识别结果")
public class OCRResult {

    private String words_result_num;

    @Schema(title = "识别结果")
    private List<OCRWordResult> words_result;

    @Schema(title = "AI token 值")
    private Long token;

    @Schema(title = "朝向", description = "-1:未定义，0:正向，1: 逆时针90度，2:逆时针180度，3:逆时针270度")
    private Integer direction;

    public String getAllTxt() {
        StringBuilder allTxt = new StringBuilder();
        for (OCRWordResult result : this.words_result) {
            allTxt.append(result.getWords());
        }
        return allTxt.toString();
    }
}
