package com.pgb.service.custom.textbook;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.lang.TypeReference;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.json.JSONUtil;
import com.pgb.common.core.global.BaseResult;
import com.pgb.common.core.global.GlobalCode;
import com.pgb.service.domain.zc.common.textbook.dto.*;
import com.pgb.service.domain.zc.common.textbook.entity.*;
import com.pgb.service.domain.zc.common.textbook.vo.IdResultVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * Textbook服务调用类
 *
 * <AUTHOR>
 * Created by 2025/7/25 19:40
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class TextbookService {

    @Value("${textbook.base-url}")
    private String baseUrl;

    /**
     * 发送POST请求
     */
    private String post(String url, Object body) {
        return HttpRequest.post(url)
                .body(JSONUtil.toJsonStr(body))
                .timeout(10 * 60 * 1000)
                .execute()
                .body();
    }

    /**
     * 发送PUT请求
     */
    private String put(String url, Object body) {
        return HttpRequest.put(url)
                .body(JSONUtil.toJsonStr(body))
                .timeout(10 * 60 * 1000)
                .execute()
                .body();
    }

    /**
     * 发送GET请求
     */
    private String get(String url) {
        return HttpRequest.get(url)
                .timeout(10 * 60 * 1000)
                .execute()
                .body();
    }

    /**
     * 发送DELETE请求
     */
    private String delete(String url) {
        return HttpRequest.delete(url)
                .timeout(10 * 60 * 1000)
                .execute()
                .body();
    }

    // ==================== 教材类型相关接口 ====================

    /**
     * 获取教材类型列表
     */
    public BaseResult<List<PgTextbookType>> getTextbookTypeList() {
        String result = this.get(
                StrUtil.format("{}/textbook/type/list", baseUrl)
        );

        log.info("获取教材类型列表结果：{}", result);

        BaseResult<List<PgTextbookType>> convert = Convert.convert(new TypeReference<BaseResult<List<PgTextbookType>>>() {
        }, JSONUtil.parseObj(result));

        if (convert.isSuccess()) {
            return convert;
        }

        return BaseResult.code(GlobalCode.Error);
    }

    /**
     * 创建教材类型
     */
    public BaseResult<IdResultVO> createTextbookType(PgTextbookTypeDTO dto) {
        String result = this.post(
                StrUtil.format("{}/textbook/type", baseUrl),
                dto
        );

        log.info("创建教材类型结果：{}", result);

        BaseResult<IdResultVO> convert = Convert.convert(new TypeReference<BaseResult<IdResultVO>>() {
        }, JSONUtil.parseObj(result));

        if (convert.isSuccess()) {
            return convert;
        }

        return BaseResult.code(GlobalCode.Error);
    }

    /**
     * 更新教材类型
     */
    public BaseResult<Boolean> updateTextbookType(Long id, PgTextbookTypeDTO dto) {
        String result = this.put(
                StrUtil.format("{}/textbook/type/{}", baseUrl, id),
                dto
        );

        log.info("更新教材类型结果：{}", result);

        BaseResult<Boolean> convert = Convert.convert(new TypeReference<BaseResult<Boolean>>() {
        }, JSONUtil.parseObj(result));

        if (convert.isSuccess()) {
            return convert;
        }

        return BaseResult.code(GlobalCode.Error);
    }

    /**
     * 删除教材类型
     */
    public BaseResult<Boolean> deleteTextbookType(Long id) {
        String result = this.delete(
                StrUtil.format("{}/textbook/type/{}", baseUrl, id)
        );

        log.info("删除教材类型结果：{}", result);

        BaseResult<Boolean> convert = Convert.convert(new TypeReference<BaseResult<Boolean>>() {
        }, JSONUtil.parseObj(result));

        if (convert.isSuccess()) {
            return convert;
        }

        return BaseResult.code(GlobalCode.Error);
    }

    /**
     * 教材类型排序
     */
    public BaseResult<Boolean> sortTextbookType(List<SortItemDTO> sortList) {
        String result = this.post(
                StrUtil.format("{}/textbook/type/sort", baseUrl),
                sortList
        );

        log.info("教材类型排序结果：{}", result);

        BaseResult<Boolean> convert = Convert.convert(new TypeReference<BaseResult<Boolean>>() {
        }, JSONUtil.parseObj(result));

        if (convert.isSuccess()) {
            return convert;
        }

        return BaseResult.code(GlobalCode.Error);
    }

    // ==================== 年级相关接口 ====================

    /**
     * 获取年级列表
     */
    public BaseResult<List<PgTextbookGrade>> getTextbookGradeList(Long versionId) {
        String result = this.get(
                StrUtil.format("{}/textbook/grade/list?versionId={}", baseUrl, versionId)
        );

        log.info("获取年级列表结果：{}", result);

        BaseResult<List<PgTextbookGrade>> convert = Convert.convert(new TypeReference<BaseResult<List<PgTextbookGrade>>>() {
        }, JSONUtil.parseObj(result));

        if (convert.isSuccess()) {
            return convert;
        }

        return BaseResult.code(GlobalCode.Error);
    }

    /**
     * 创建年级
     */
    public BaseResult<IdResultVO> createTextbookGrade(PgTextbookGradeDTO dto) {
        String result = this.post(
                StrUtil.format("{}/textbook/grade", baseUrl),
                dto
        );

        log.info("创建年级结果：{}", result);

        BaseResult<IdResultVO> convert = Convert.convert(new TypeReference<BaseResult<IdResultVO>>() {
        }, JSONUtil.parseObj(result));

        if (convert.isSuccess()) {
            return convert;
        }

        return BaseResult.code(GlobalCode.Error);
    }

    /**
     * 更新年级
     */
    public BaseResult<Boolean> updateTextbookGrade(Long id, PgTextbookGradeDTO dto) {
        String result = this.put(
                StrUtil.format("{}/textbook/grade/{}", baseUrl, id),
                dto
        );

        log.info("更新年级结果：{}", result);

        BaseResult<Boolean> convert = Convert.convert(new TypeReference<BaseResult<Boolean>>() {
        }, JSONUtil.parseObj(result));

        if (convert.isSuccess()) {
            return convert;
        }

        return BaseResult.code(GlobalCode.Error);
    }

    /**
     * 删除年级
     */
    public BaseResult<Boolean> deleteTextbookGrade(Long id) {
        String result = this.delete(
                StrUtil.format("{}/textbook/grade/{}", baseUrl, id)
        );

        log.info("删除年级结果：{}", result);

        BaseResult<Boolean> convert = Convert.convert(new TypeReference<BaseResult<Boolean>>() {
        }, JSONUtil.parseObj(result));

        if (convert.isSuccess()) {
            return convert;
        }

        return BaseResult.code(GlobalCode.Error);
    }

    /**
     * 年级排序
     */
    public BaseResult<Boolean> sortTextbookGrade(List<SortItemDTO> sortList) {
        String result = this.post(
                StrUtil.format("{}/textbook/grade/sort", baseUrl),
                sortList
        );

        log.info("年级排序结果：{}", result);

        BaseResult<Boolean> convert = Convert.convert(new TypeReference<BaseResult<Boolean>>() {
        }, JSONUtil.parseObj(result));

        if (convert.isSuccess()) {
            return convert;
        }

        return BaseResult.code(GlobalCode.Error);
    }

    // ==================== 版本相关接口 ====================

    /**
     * 获取版本列表
     */
    public BaseResult<List<PgTextbookVersion>> getTextbookVersionList(Long typeId) {
        String result = this.get(
                StrUtil.format("{}/textbook/version/list?typeId={}", baseUrl, typeId)
        );

        log.info("获取版本列表结果：{}", result);

        BaseResult<List<PgTextbookVersion>> convert = Convert.convert(new TypeReference<BaseResult<List<PgTextbookVersion>>>() {
        }, JSONUtil.parseObj(result));

        if (convert.isSuccess()) {
            return convert;
        }

        return BaseResult.code(GlobalCode.Error);
    }

    /**
     * 创建版本
     */
    public BaseResult<IdResultVO> createTextbookVersion(PgTextbookVersionDTO dto) {
        String result = this.post(
                StrUtil.format("{}/textbook/version", baseUrl),
                dto
        );

        log.info("创建版本结果：{}", result);

        BaseResult<IdResultVO> convert = Convert.convert(new TypeReference<BaseResult<IdResultVO>>() {
        }, JSONUtil.parseObj(result));

        if (convert.isSuccess()) {
            return convert;
        }

        return BaseResult.code(GlobalCode.Error);
    }

    /**
     * 更新版本
     */
    public BaseResult<Boolean> updateTextbookVersion(Long id, PgTextbookVersionDTO dto) {
        String result = this.put(
                StrUtil.format("{}/textbook/version/{}", baseUrl, id),
                dto
        );

        log.info("更新版本结果：{}", result);

        BaseResult<Boolean> convert = Convert.convert(new TypeReference<BaseResult<Boolean>>() {
        }, JSONUtil.parseObj(result));

        if (convert.isSuccess()) {
            return convert;
        }

        return BaseResult.code(GlobalCode.Error);
    }

    /**
     * 删除版本
     */
    public BaseResult<Boolean> deleteTextbookVersion(Long id) {
        String result = this.delete(
                StrUtil.format("{}/textbook/version/{}", baseUrl, id)
        );

        log.info("删除版本结果：{}", result);

        BaseResult<Boolean> convert = Convert.convert(new TypeReference<BaseResult<Boolean>>() {
        }, JSONUtil.parseObj(result));

        if (convert.isSuccess()) {
            return convert;
        }

        return BaseResult.code(GlobalCode.Error);
    }

    /**
     * 版本排序
     */
    public BaseResult<Boolean> sortTextbookVersion(List<SortItemDTO> sortList) {
        String result = this.post(
                StrUtil.format("{}/textbook/version/sort", baseUrl),
                sortList
        );

        log.info("版本排序结果：{}", result);

        BaseResult<Boolean> convert = Convert.convert(new TypeReference<BaseResult<Boolean>>() {
        }, JSONUtil.parseObj(result));

        if (convert.isSuccess()) {
            return convert;
        }

        return BaseResult.code(GlobalCode.Error);
    }

    // ==================== 册别相关接口 ====================

    /**
     * 获取册别列表
     */
    public BaseResult<List<PgTextbookVolume>> getTextbookVolumeList(Long gradeId) {
        String result = this.get(
                StrUtil.format("{}/textbook/volume/list?gradeId={}", baseUrl, gradeId)
        );

        log.info("获取册别列表结果：{}", result);

        BaseResult<List<PgTextbookVolume>> convert = Convert.convert(new TypeReference<BaseResult<List<PgTextbookVolume>>>() {
        }, JSONUtil.parseObj(result));

        if (convert.isSuccess()) {
            return convert;
        }

        return BaseResult.code(GlobalCode.Error);
    }

    /**
     * 创建册别
     */
    public BaseResult<IdResultVO> createTextbookVolume(PgTextbookVolumeDTO dto) {
        String result = this.post(
                StrUtil.format("{}/textbook/volume", baseUrl),
                dto
        );

        log.info("创建册别结果：{}", result);

        BaseResult<IdResultVO> convert = Convert.convert(new TypeReference<BaseResult<IdResultVO>>() {
        }, JSONUtil.parseObj(result));

        if (convert.isSuccess()) {
            return convert;
        }

        return BaseResult.code(GlobalCode.Error);
    }

    /**
     * 更新册别
     */
    public BaseResult<Boolean> updateTextbookVolume(Long id, PgTextbookVolumeDTO dto) {
        String result = this.put(
                StrUtil.format("{}/textbook/volume/{}", baseUrl, id),
                dto
        );

        log.info("更新册别结果：{}", result);

        BaseResult<Boolean> convert = Convert.convert(new TypeReference<BaseResult<Boolean>>() {
        }, JSONUtil.parseObj(result));

        if (convert.isSuccess()) {
            return convert;
        }

        return BaseResult.code(GlobalCode.Error);
    }

    /**
     * 删除册别
     */
    public BaseResult<Boolean> deleteTextbookVolume(Long id) {
        String result = this.delete(
                StrUtil.format("{}/textbook/volume/{}", baseUrl, id)
        );

        log.info("删除册别结果：{}", result);

        BaseResult<Boolean> convert = Convert.convert(new TypeReference<BaseResult<Boolean>>() {
        }, JSONUtil.parseObj(result));

        if (convert.isSuccess()) {
            return convert;
        }

        return BaseResult.code(GlobalCode.Error);
    }

    /**
     * 册别排序
     */
    public BaseResult<Boolean> sortTextbookVolume(List<SortItemDTO> sortList) {
        String result = this.post(
                StrUtil.format("{}/textbook/volume/sort", baseUrl),
                sortList
        );

        log.info("册别排序结果：{}", result);

        BaseResult<Boolean> convert = Convert.convert(new TypeReference<BaseResult<Boolean>>() {
        }, JSONUtil.parseObj(result));

        if (convert.isSuccess()) {
            return convert;
        }

        return BaseResult.code(GlobalCode.Error);
    }

    // ==================== 单元相关接口 ====================

    /**
     * 获取单元列表
     */
    public BaseResult<List<PgTextbookUnit>> getTextbookUnitList(Long volumeId) {
        String result = this.get(
                StrUtil.format("{}/textbook/unit/list?volumeId={}", baseUrl, volumeId)
        );

        log.info("获取单元列表结果：{}", result);

        BaseResult<List<PgTextbookUnit>> convert = Convert.convert(new TypeReference<BaseResult<List<PgTextbookUnit>>>() {
        }, JSONUtil.parseObj(result));

        if (convert.isSuccess()) {
            return convert;
        }

        return BaseResult.code(GlobalCode.Error);
    }

    /**
     * 创建单元
     */
    public BaseResult<IdResultVO> createTextbookUnit(PgTextbookUnitDTO dto) {
        String result = this.post(
                StrUtil.format("{}/textbook/unit", baseUrl),
                dto
        );

        log.info("创建单元结果：{}", result);

        BaseResult<IdResultVO> convert = Convert.convert(new TypeReference<BaseResult<IdResultVO>>() {
        }, JSONUtil.parseObj(result));

        if (convert.isSuccess()) {
            return convert;
        }

        return BaseResult.code(GlobalCode.Error);
    }

    /**
     * 更新单元
     */
    public BaseResult<Boolean> updateTextbookUnit(Long id, PgTextbookUnitDTO dto) {
        String result = this.put(
                StrUtil.format("{}/textbook/unit/{}", baseUrl, id),
                dto
        );

        log.info("更新单元结果：{}", result);

        BaseResult<Boolean> convert = Convert.convert(new TypeReference<BaseResult<Boolean>>() {
        }, JSONUtil.parseObj(result));

        if (convert.isSuccess()) {
            return convert;
        }

        return BaseResult.code(GlobalCode.Error);
    }

    /**
     * 删除单元
     */
    public BaseResult<Boolean> deleteTextbookUnit(Long id) {
        String result = this.delete(
                StrUtil.format("{}/textbook/unit/{}", baseUrl, id)
        );

        log.info("删除单元结果：{}", result);

        BaseResult<Boolean> convert = Convert.convert(new TypeReference<BaseResult<Boolean>>() {
        }, JSONUtil.parseObj(result));

        if (convert.isSuccess()) {
            return convert;
        }

        return BaseResult.code(GlobalCode.Error);
    }

    /**
     * 单元排序
     */
    public BaseResult<Boolean> sortTextbookUnit(List<SortItemDTO> sortList) {
        String result = this.post(
                StrUtil.format("{}/textbook/unit/sort", baseUrl),
                sortList
        );

        log.info("单元排序结果：{}", result);

        BaseResult<Boolean> convert = Convert.convert(new TypeReference<BaseResult<Boolean>>() {
        }, JSONUtil.parseObj(result));

        if (convert.isSuccess()) {
            return convert;
        }

        return BaseResult.code(GlobalCode.Error);
    }

}
