<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.pgb</groupId>
        <artifactId>pgb-server</artifactId>
        <version>${revision}</version>
    </parent>

    <artifactId>pgb-student</artifactId>
    <packaging>jar</packaging>

    <description>
        批改邦 学生端
    </description>

    <dependencies>

        <!-- Mybatis -->
        <dependency>
            <groupId>com.pgb</groupId>
            <artifactId>pgb-common-mybatis</artifactId>
        </dependency>

        <!-- satoken -->
        <dependency>
            <groupId>com.pgb</groupId>
            <artifactId>pgb-common-satoken</artifactId>
        </dependency>

        <!--  短信  -->
        <dependency>
            <groupId>com.pgb</groupId>
            <artifactId>pgb-common-sms</artifactId>
        </dependency>

        <!-- Redis 缓存-->
        <dependency>
            <groupId>com.pgb</groupId>
            <artifactId>pgb-common-redis</artifactId>
        </dependency>

        <!-- 支付 -->
        <dependency>
            <groupId>com.pgb</groupId>
            <artifactId>pgb-common-pay</artifactId>
        </dependency>

        <!--微信小程序-->
        <dependency>
            <groupId>com.github.binarywang</groupId>
            <artifactId>weixin-java-miniapp</artifactId>
        </dependency>

        <!-- SpringBoot Web容器 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>spring-boot-starter-tomcat</artifactId>
                    <groupId>org.springframework.boot</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- web 容器使用 undertow 性能更强 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-undertow</artifactId>
        </dependency>

        <!--  springboot 测试  -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>

        <!--多数据源支持-->
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>dynamic-datasource-spring-boot3-starter</artifactId>
        </dependency>

        <!--  db  -->
        <dependency>
            <groupId>com.pgb</groupId>
            <artifactId>pgb-service</artifactId>
        </dependency>

    </dependencies>

    <build>
        <finalName>pgb-student-v2</finalName>
        <plugins>
            <!-- 可生成可执行的jar包或war包。插件的核心goal  -->
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>${spring-boot.version}</version>
                <configuration>
                    <includeSystemScope>true</includeSystemScope>
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <!--   打包jar包  -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-jar-plugin</artifactId>
                <version>${maven-jar-plugin.version}</version>
            </plugin>
        </plugins>
    </build>

</project>
