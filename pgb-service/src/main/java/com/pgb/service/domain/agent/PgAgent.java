package com.pgb.service.domain.agent;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;

import com.pgb.common.mybatis.handler.JsonbTypeHandler;
import com.pgb.service.enums.FeeTypeEnum;
import com.pgb.service.enums.LLMTypeEnum;
import lombok.Data;

/**
 *
 * @TableName pg_agent
 */
@TableName(value ="pg_agent")
@Data
public class PgAgent {
    /**
     * 智能体
     */
    @TableId
    private Long id;

    /**
     * 名称
     */
    private String name;

    /**
     * 一句话描述
     */
    private String description;

    /**
     * 图标图片
     */
    private String avatar;

    /**
     * 智能体表单
     */
    @TableField(typeHandler = JsonbTypeHandler.class)
    private Object form;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 收费类型，枚举，0：免费，1：会员无限次，2：限次
     */
    private FeeTypeEnum feeType;

    /**
     * AI 提示词，敏感数据
     */
    private String prompt;

    /**
     * 是否启用
     */
    private Boolean isEnable;

    /**
     * 大模型类型
     */
    private LLMTypeEnum llmType;

    /**
     * html链接
     */
    private String htmlUrl;

    /**
     * 智能体类型 0：普通对话，1：html
     */
    private Integer type;

}
