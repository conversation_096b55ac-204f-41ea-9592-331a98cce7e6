package com.pgb.service.domain.userConfig;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * Created by 2025/3/14 14:50
 */
@Schema(title = "批改配置 DTO")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CorrectConfigDTO {

    @Schema(title = "评分标准", description = "30分制，50分制，60分制，百分制，自定义")
    private Double scoreStandard = 30.;

    @Schema(title = "润色幅度", description = "1：低，2：中，3：高")
    private Integer polishRange = 2;

    @Schema(title = "评分")
    private Boolean score = true;

    @Schema(title = "旁批风格", description = "1：简洁，2：详细")
    private Integer commentStyle = 1;

    @Schema(title = "旁批字体", description = "1：手写体，2：印刷体")
    private Integer commentFont = 2;

    @Schema(title = "批改严格程度", description = "1：严格 2：适中 3：宽松")
    private Integer correctLevel = 2;

    @Schema(title = "是否将总评放到旁批图片下方")
    private Boolean isImgOverallComment = true;

    @Schema(title = "精彩句子划线样式", description = "1：荧光笔，2：波浪线")
    private Integer sentenceMarkStyle = 2;

    @Schema(title = "总评")
    private Boolean overallComment = true;

    @Schema(title = "润色字数严格程度", description = "0：适中，1：非常严格")
    private Integer polishLimitLevel = 0;

    //  --- 详细点评 ---
    @Schema(title = "详细点评")
    private Boolean comment = true;

    @Schema(title = "改写标题和结尾")
    private Boolean rewriteTitle = true;

    @Schema(title = "纠正错误")
    private Boolean correctError = true;

    @Schema(title = "分析")
    private Boolean analysis = true;

    @Schema(title = "文章亮点")
    private Boolean highlight = true;

    @Schema(title = "不足和建议")
    private Boolean advice = true;

    @Schema(title = "提问")
    private Boolean question = true;

    @Schema(title = "学生作文润色")
    private Boolean polish = true;

    @Schema(title = "原文对比")
    private Boolean compare = true;


}
