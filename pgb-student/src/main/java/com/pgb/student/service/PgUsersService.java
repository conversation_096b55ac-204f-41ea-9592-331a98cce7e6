package com.pgb.student.service;

import com.pgb.common.satoken.LoginVO;
import com.pgb.service.enums.DeviceTypeEnum;
import com.pgb.student.domain.PgUsers;
import com.baomidou.mybatisplus.extension.service.IService;
import com.pgb.student.domain.vo.PgUsersVO;
import me.chanjar.weixin.common.bean.WxOAuth2UserInfo;

/**
* <AUTHOR>
* @description 针对表【pg_users(用户表)】的数据库操作Service
* @createDate 2025-02-24 18:34:22
*/
public interface PgUsersService extends IService<PgUsers> {

    /**
     * 根据手机号获取用户, 如果不存在则创建
     * @param phone
     * @return
     */
    PgUsers getOrCreateUserByPhone(String phone);

    /**
     * 根据openId获取用户, 如果不存在则创建
     * @param openId
     * @return
     */
    PgUsers getOrCreateUserByOpenId(String openId, String unionId);

    /**
     * 注册用户
     * @param phone
     * @param openId
     * @param unionId
     * @param wxMpUser
     * @return
     */
    PgUsers registerUser(String phone, String openId, String unionId, WxOAuth2UserInfo wxMpUser);

    /**
     * 通用登录成功
     * @param user
     * @return
     */
    LoginVO<PgUsersVO> loginSuccess(PgUsers user, DeviceTypeEnum deviceType);
}
