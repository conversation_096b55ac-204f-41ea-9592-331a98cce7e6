package com.pgb.service.domain.distribution.verify;

import java.io.Serializable;

import java.util.Date;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.ToString;
import lombok.Builder;

/**
* 分销核销表
* @TableName pg_distribution_verify
*/
@Schema(description = "分销核销表 VO")
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode
@ToString
@Builder
public class PgDistributionVerifyVO implements Serializable {

    @Schema(title = "分销核销id", type = "string")
    private Long id;

    @Schema(title = "订单id")
    private Long orderId;

    @Schema(title = "分销金额（单位：分）")
    private Integer distributeAmount;

    @Schema(title = "分销订单状态（0待处理，1已处理）")
    private Integer status;

    @Schema(title = "创建时间")
    private Date createTime;

    @Schema(title = "用户实际支付金额（单位：分）")
    private Integer payAmount;

    @Schema(title = "邀请人id")
    private Long shareUserId;

    @Schema(title = "被邀请人id")
    private Long userId;

}
