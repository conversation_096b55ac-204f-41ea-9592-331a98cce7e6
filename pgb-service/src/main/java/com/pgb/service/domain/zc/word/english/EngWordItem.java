package com.pgb.service.domain.zc.word.english;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.fasterxml.jackson.annotation.JsonGetter;
import com.pgb.service.domain.zc.common.ZcChar;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * Created by 2025/6/6 19:05
 */
@Schema(title = "英语单词")
@Data
public class EngWordItem {

    @Schema(title = "单词")
    private String word;

    @Schema(title = "音标")
    private String phonetic;

    @Schema(title = "中文释义")
    private String chinese;

    @Schema(title = "词性")
    private String wordClass;

    @Schema(title = "单词信息")
    private ZcChar wordInfo;

    @Schema(title = "中文释义信息")
    private ZcChar chineseInfo;

    @Schema(title = "英式发音url")
    private String ukAudioUrl;

    @Schema(title = "美式发音url")
    private String usAudioUrl;

    @JsonGetter
    public ZcChar getWordInfo() {

        if (ObjectUtil.isNull(this.wordInfo)) {
            ZcChar zcChar = new ZcChar();
            zcChar.setChars(word);
            this.wordInfo = zcChar;
        }
        return this.wordInfo;
    }

    @JsonGetter
    public ZcChar getChineseInfo() {

        if (ObjectUtil.isNull(this.chineseInfo)) {
            ZcChar zcChar = new ZcChar();
            zcChar.setChars(chinese);
            this.chineseInfo = zcChar;
        }
        return this.chineseInfo;
    }

}


