package com.pgb.subscribe.subcribe.zc;

import cn.binarywang.wx.miniapp.api.WxMaService;
import cn.binarywang.wx.miniapp.bean.WxMaSubscribeMessage;
import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.TimeInterval;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.digest.DigestUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.pgb.common.core.global.BaseResult;
import com.pgb.common.core.global.GlobalCode;
import com.pgb.common.core.utils.EnvUtils;
import com.pgb.common.oss.service.OssService;
import com.pgb.common.redis.QueueUtils;
import com.pgb.common.redis.RedisUtils;
import com.pgb.service.custom.ZcQuesPdfService;
import com.pgb.service.db.PgUsersService;
import com.pgb.service.db.PgZcQuestionService;
import com.pgb.service.domain.GlobQueueConstants;
import com.pgb.service.domain.GlobalXcxConstants;
import com.pgb.service.domain.user.PgUsers;
import com.pgb.service.domain.zc.question.PgZcQuestion;
import com.pgb.service.enums.ExportStatusEnum;
import com.pgb.service.enums.ZcQuestionTypeEnum;
import com.pgb.subscribe.service.QuesPdfSubscribeService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.error.WxErrorException;
import org.apache.pdfbox.Loader;
import org.apache.pdfbox.multipdf.Splitter;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.redisson.api.RBlockingQueue;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.time.Duration;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.atomic.AtomicReference;

import static com.pgb.service.custom.QwMsgService.sendErrorMessage;
import static com.pgb.service.custom.QwMsgService.sendPdfErrorMessage;

/**
 * 字词 渲染pdf 队列监听
 */
@Component("ZcPdfQueueSubscribe")
@Slf4j
@RequiredArgsConstructor
public class ZcPdfQueueSubscribe implements CommandLineRunner {

    private final OssService ossService;

    private final PgZcQuestionService pgZcQuestionService;

    private final PgUsersService pgUsersService;

    private final WxMaService wxMaService;

    private final QuesPdfSubscribeService quesPdfSubscribeService;

    private final ZcQuesPdfService zcQuesPdfService;

    @Override
    public void run(String... args) throws Exception {

        if (EnvUtils.isProd()) {
            log.info("监听字词题目渲染PDF队列 --> 启动");
            // 立即执行一次
            pgZcQuestionService.queryToRender();
            quesPdfSubscribeService.cloudScribe(GlobQueueConstants.PGB_XCX_ZC_QUES_PDF_QUEUE.name());
        }

        if (EnvUtils.isDev()) {

            log.info("监听字词题目渲染PDF队列 --> 启动");
            // 下载队列
            reRender();
            // 立即执行一次
            pgZcQuestionService.queryToRender();
        }

        //PgZcQuestion zcQuestion = pgZcQuestionService.getById(1940051009582002177L);
        ////执行渲染
        //zcQuesPdfService.renderPdf(zcQuestion);

    }

    private void reRender() {

        // 获取下载队列
        RBlockingQueue<Long> queue = QueueUtils.getClient().getBlockingQueue(GlobQueueConstants.PGB_XCX_ZC_QUES_PDF_QUEUE.name());

        AtomicReference<Long> zcQuestionId = new AtomicReference<>();

        CompletableFuture<Void> f = CompletableFuture.runAsync(() -> {

            try {
                // 阻塞获取pgf下载任务
                zcQuestionId.set(queue.take());

                log.info("【接收字词题目渲染pdf队列-监听信息】：{}", zcQuestionId.get());

                // 设置渲染中状态 10分钟
                RedisUtils.setCacheObject(GlobalXcxConstants.XCX_ZC_QUES_PDF_RENDERING + zcQuestionId.get(), true, Duration.ofMinutes(10));

                // 执行渲染
                render(zcQuestionId.get());
            } catch (InterruptedException e) {
                e.printStackTrace();
            }

        });

        f.whenComplete((v, e) -> {

            // 清空 渲染中 状态
            RedisUtils.deleteObject(GlobalXcxConstants.XCX_ZC_QUES_PDF_RENDERING + zcQuestionId.get());

            if (e != null) {
                // 如果是 RedissonShutdownException 则直接跳过
                if (e.getCause().getMessage().contains("Redisson is shutdown")) {
                    log.error("字词渲染pdf队列监听异常 --> redisson 已中断！");
                    return;
                }

                log.error("字词渲染pdf队列监听异常 -->  异常", e);
            }

            this.reRender();
        });
    }


    /**
     * 渲染pdf
     *
     * @param zcQuestionId
     */
    private void render(Long zcQuestionId) {

        PgZcQuestion pgZcQuestion = pgZcQuestionService.getById(zcQuestionId);

        // 执行渲染
        zcQuesPdfService.renderPdf(pgZcQuestion);

        // 发送导出结果通知
//        sendWxMsg(pgZcQuestion.getUserId());

    }


    /**
     * TODO 修改模板
     *
     * @param userId
     */
    private void sendWxMsg(Long userId) {

        // 发送通知
        PgUsers users = pgUsersService.getById(userId);

        WxMaSubscribeMessage message = new WxMaSubscribeMessage();
        message.setToUser(users.getWxOpenId());
        message.setTemplateId("UsNIR66iF_rjs593zh4uPwFGspXNqtU1Lws97gefIRw");
        message.setPage("/pages/my/export/index");
        message.setData(ListUtil.toList(
                new WxMaSubscribeMessage.MsgData("phrase1", "已完成"),
                new WxMaSubscribeMessage.MsgData("thing2", DateUtil.format(new Date(), "HH:mm"))
        ));
        if (EnvUtils.isDev()) {
            message.setMiniprogramState("developer");
        }

        try {
            wxMaService.getMsgService().sendSubscribeMsg(message);
        } catch (WxErrorException e) {
            log.error("小程序模板发送失败：{}", e.getError());
        }
    }

    private void queryToRender() {

        List<PgZcQuestion> allList = pgZcQuestionService.list(new LambdaQueryWrapper<PgZcQuestion>()
                .eq(PgZcQuestion::getPdfStatus, ExportStatusEnum.Queuing)
        );

        List<PgZcQuestion> list = new ArrayList<>();

        // 排除正在渲染中的
        for (PgZcQuestion zcQuestion : allList) {

            // 是否在渲染中
            if (RedisUtils.isExistsObject(GlobalXcxConstants.XCX_ZC_QUES_PDF_RENDERING + zcQuestion.getId())) {
                log.info("当前题目正在渲染中：{}，跳过", zcQuestion.getId());
                continue;
            }

            // 是否已存在队列
            if (QueueUtils.containsQueueObject(GlobQueueConstants.PGB_XCX_ZC_QUES_PDF_QUEUE.name(), zcQuestion.getId())) {
                log.info("当前题目已存在渲染队列中：{}，跳过", zcQuestion.getId());
                continue;
            }

            list.add(zcQuestion);
        }

        log.info("【待渲染题目扫描】数量:{}个", list.size());

        list.forEach(zcQuestion -> {
            // 加入渲染队列
            QueueUtils.addQueueObject(GlobQueueConstants.PGB_XCX_ZC_QUES_PDF_QUEUE.name(), zcQuestion.getId());
        });
    }

}
