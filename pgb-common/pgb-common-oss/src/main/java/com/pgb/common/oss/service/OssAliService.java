package com.pgb.common.oss.service;

import cn.hutool.core.img.ImgUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.net.url.UrlBuilder;
import cn.hutool.core.util.StrUtil;
import com.aliyun.oss.*;
import com.aliyun.oss.common.auth.CredentialsProvider;
import com.aliyun.oss.common.auth.DefaultCredentialProvider;
import com.aliyun.oss.common.comm.Protocol;
import com.aliyun.oss.common.utils.BinaryUtil;
import com.aliyun.oss.model.*;
import com.aliyuncs.DefaultAcsClient;
import com.aliyuncs.IAcsClient;
import com.aliyuncs.auth.sts.AssumeRoleRequest;
import com.aliyuncs.auth.sts.AssumeRoleResponse;
import com.aliyuncs.profile.DefaultProfile;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.pgb.common.oss.property.OssAliProperty;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.awt.image.BufferedImage;
import java.io.File;
import java.net.URL;
import java.time.Duration;
import java.time.Instant;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

@Slf4j
@RequiredArgsConstructor
public class OssAliService extends OssService {

    private OSS client;

    private OssAliProperty property;

    public OssAliService(OssAliProperty property) {

        // 构建凭证
        CredentialsProvider cred = new DefaultCredentialProvider(property.getSecretId(), property.getSecretKey());

        // 创建OSSClient实例。
        ClientBuilderConfiguration config = new ClientBuilderConfiguration();
        config.setProtocol(Protocol.HTTPS);
        // config.setSignatureVersion(SignVersion.V4);

        this.client = OSSClientBuilder.create()
                .endpoint(property.getEndpoint())
                .credentialsProvider(cred)
                .clientConfiguration(config)
                .region(property.getRegion())
                .build();

        this.property = property;
    }

    @Override
    public String presignedUrl(String bucketName, String key, Duration expiration) {
        return presignedUrl(
                bucketName, key, expiration, "image/jpeg;image/png;"
        );
    }

    @Override
    public String presignedUrl(String key, Duration expiration) {
        return presignedUrl(
                this.property.getBucketName(), key, expiration
        );
    }

    @Override
    public String presignedUrl(String bucketName, String key, Duration expiration, String contentType) {
        // 设置签名过期时间(可选), 若未进行设置则默认使用 ClientConfig 中的签名过期时间(1小时)
        // 这里设置签名在半个小时后过期

        Date expirationDate = new Date(System.currentTimeMillis() + expiration.toMillis());
        GeneratePresignedUrlRequest request = new GeneratePresignedUrlRequest(bucketName, key);
        request.setExpiration(expirationDate);
        request.setContentType(contentType);
        request.setMethod(HttpMethod.PUT);

        URL url = this.client.generatePresignedUrl(request);

        return url.toString();
    }

    @Override
    public String presignedUrl(String key, Duration expiration, String contentType) {
        return presignedUrl(
                this.property.getBucketName(), key, expiration, contentType
        );
    }

    @Override
    public String getCdnUrl(String key) {
        if (StrUtil.isEmpty(this.property.getCdnDomain())) {
            throw new RuntimeException("当前 OSS 未配置 CND 域名");
        }

        return UrlBuilder.of(this.property.getCdnDomain()).addPath(key).toString();
    }

    @Override
    public String putCdnImg(String key, BufferedImage image) {
        // 删除临时文件，防止不会删除文件
        File tempFile = FileUtil.createTempFile();

        FileUtil.writeBytes(ImgUtil.toBytes(image, ImgUtil.IMAGE_TYPE_PNG), tempFile);

        try {
            this.client.putObject(
                    this.property.getBucketName(),
                    key,
                    tempFile
            );
        } catch (Exception e) {
            log.error("图片处理失败，立即进行重试", e);
            this.client.putObject(
                    this.property.getBucketName(),
                    key,
                    tempFile
            );
        }

        // 主动删除临时文件
        FileUtil.del(tempFile);

        return UrlBuilder.of(this.property.getCdnDomain()).addPath(key).toString();
    }

    @Override
    public String putFile(String key, File file) {

        try {
            this.client.putObject(
                    this.property.getBucketName(),
                    key,
                    file
            );
        } catch (Exception e) {
            log.error("图片处理失败，立即进行重试", e);
            this.client.putObject(
                    this.property.getBucketName(),
                    key,
                    file
            );
        }

        return UrlBuilder.of(this.property.getCdnDomain()).addPath(key).toString();
    }

    @Override
    public Boolean isExist(String key) {
        try {
            return client.doesObjectExist(
                    this.property.getBucketName(),
                    key
            );
        } catch (Exception e) {
            log.error("【OOS】判断文件对象是否存在失败：{}\n", this.property, e);
        }

        return false;
    }

    @Override
    public void setTagList(String key, String... tags) {
        // 移除首符号
        if (StrUtil.startWith(key, "/")) {
            key = StrUtil.subSuf(key, 1);
        }

        // 转为tagList
        Map<String, String> tagsMap = new HashMap<>();
        for (String tag : tags) {
            tagsMap.put(tag, tag);
        }

        try {
            this.client.setObjectTagging(
                    this.property.getBucketName(),
                    key,
                    tagsMap
            );
        } catch (Exception e) {
            log.error("设置图片标签失败：{}", e.getMessage());
        }
    }

    @Override
    public void delete(String key) {
        // 移除首符号
        if (StrUtil.startWith(key, "/")) {
            key = StrUtil.subSuf(key, 1);
        }

        try{
            this.client.deleteObject(
                    this.property.getBucketName(),
                    key
            );
        } catch (Exception e) {
            log.error("删除图片失败：{}", e.getMessage());
        }
    }

    @Override
    public String getResizeUrl(String url) {
        // ?x-oss-process=image/resize,p_50
        return UrlBuilder.of(url).addQuery("x-oss-process", "image/resize,p_50").build();
    }

    @Override
    public String getFormat(String url, String format) {
        // ?x-oss-process=image/format,png
        return UrlBuilder.of(url).addQuery("x-oss-process", "image/format," + format).build();
    }

    /**
     * 列举指定前缀的文件
     */
    @Override
    public List<String> listObjects(String path) {

        String nextContinuationToken = null;
        ListObjectsV2Result result;

        List<String> imgKeyList = new ArrayList<>();

        // 分页列举指定前缀的文件。
        do {
            ListObjectsV2Request request = new ListObjectsV2Request(this.property.getBucketName());
            // https://cdn.pigaibang.com/zw/user/1806233697075216385/IMG/00ca575ec9054d799357991800e25683.jpg
            // 单次最多数量
            request.setMaxKeys(200);
            // 指定前缀
            request.setPrefix(path);
            // 下一次列举文件的起点
            request.setContinuationToken(nextContinuationToken);

            result = this.client.listObjectsV2(request);

            List<OSSObjectSummary> summaries = result.getObjectSummaries();

            imgKeyList.addAll(summaries.stream()
                    .map(OSSObjectSummary::getKey)
                    .toList()
            );

            nextContinuationToken = result.getNextContinuationToken();

        } while (result.isTruncated());

        return imgKeyList;
    }

    @Override
    public void download(String key, String pathName) {

        client.getObject(new GetObjectRequest("pigaibang", key), new File(pathName));

//        log.info(UrlBuilder.of(this.property.getCdnDomain()).addPath(objectName).toString());
    }

    /**
     * 重命名文件
     *
     * @param sourceKey      源文件
     * @param destinationKey 目标文件
     */
    @Override
    public void renameFile(String sourceKey, String destinationKey) {

        // 移除首符号 /
        if (sourceKey.startsWith("/")) {
            sourceKey = StrUtil.subSuf(sourceKey, 1);
        }
        if (destinationKey.startsWith("/")) {
            destinationKey = StrUtil.subSuf(destinationKey, 1);
        }

        // 拷贝
        try {
            client.copyObject(this.property.getBucketName(), sourceKey, this.property.getBucketName(), destinationKey);
        } catch (Exception e) {
            log.error("拷贝图片失败：{}", e.getMessage());
            return;
        }

        // 删除源文件
        setTagList(sourceKey, "Deleted");
    }

    public String getUploadUrl() {

        return StrUtil.format(
                """
                        https://{}.{}
                        """,
                this.property.getBucketName(), this.property.getEndpoint()
        );
    }

    /**
     * 客户端直传签名，默认3小时过期
     *
     * @return
     * @throws JsonProcessingException
     */
    public Map<String, String> getSignature() {

        // 获取发送STS请求基础信息
        Long durationSeconds = 3600L;   // 临时访问凭证的有效时间

        // 初始化客户端
        DefaultProfile profile = DefaultProfile.getProfile(
                this.property.getRegion(),
                this.property.getSecretId(),
                this.property.getSecretKey()
        );
        IAcsClient clientTemp = new DefaultAcsClient(profile);

        AssumeRoleRequest request = new AssumeRoleRequest();
        request.setRoleArn("acs:ram::1466332595565727:role/admin");
        request.setRoleSessionName("pigaibang_xcx_oss");
        request.setDurationSeconds(durationSeconds);
        request.setSysEndpoint(
                this.property.getStsEndpoint()
        );

        // 定义STS临时访问凭证变量
        String STSaccessKeyId = null;
        String STSsecretAccessKey = null;
        String securityToken = null;

        try {
            AssumeRoleResponse response = clientTemp.getAcsResponse(request);

            // 将请求返回的STS临时访问凭证赋值到自定义变量中
            STSaccessKeyId = response.getCredentials().getAccessKeyId();
            STSsecretAccessKey = response.getCredentials().getAccessKeySecret();
            securityToken = response.getCredentials().getSecurityToken();

        } catch (com.aliyuncs.exceptions.ClientException e) {
            throw new RuntimeException(e);
        }

        // 格式化请求日期
        long now = System.currentTimeMillis() / 1000;
        ZonedDateTime dtObj = ZonedDateTime.ofInstant(Instant.ofEpochSecond(now), ZoneId.of("UTC"));
        ZonedDateTime dtObjPlus3h = dtObj.plusHours(3);
        // 请求时间
        DateTimeFormatter dtObj1Formatter = DateTimeFormatter.ofPattern("yyyyMMdd'T'HHmmss'Z'");
        String dtObj1 = dtObj.format(dtObj1Formatter);
        // 请求日期
        DateTimeFormatter dtObj2Formatter = DateTimeFormatter.ofPattern("yyyyMMdd");
        String dtObj2 = dtObj.format(dtObj2Formatter);
        // 请求过期时间
        DateTimeFormatter expirationTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'");
        String expirationTime = dtObjPlus3h.format(expirationTimeFormatter);

        // 创建policy
        // 示例policy表单域只列举必填字段，如有其他需求可参考文档：https://help.aliyun.com/zh/oss/developer-reference/signature-version-4-recommend
        ObjectMapper mapper = new ObjectMapper();

        Map<String, Object> policy = new HashMap<>();
        policy.put("expiration", expirationTime);

        List<Object> conditions = new ArrayList<>();

        Map<String, String> bucketCondition = new HashMap<>();
        // 请将<bucketname>替换为您的实际Bucket名称
        bucketCondition.put("bucket", this.property.getBucketName());
        conditions.add(bucketCondition);

        Map<String, String> signatureVersionCondition = new HashMap<>();
        signatureVersionCondition.put("x-oss-signature-version", "OSS4-HMAC-SHA256");
        conditions.add(signatureVersionCondition);

        Map<String, String> credentialCondition = new HashMap<>();
        // 请将<cn-hangzhou>替换为您的实际Bucket所处地域，例如北京地域为：cn-beijing
        credentialCondition.put("x-oss-credential", STSaccessKeyId + "/" + dtObj2 + "/cn-beijing/oss/aliyun_v4_request");
        conditions.add(credentialCondition);

        Map<String, String> token = new HashMap<>();
        token.put("x-oss-security-token", securityToken);
        conditions.add(token);

        Map<String, String> dateCondition = new HashMap<>();
        dateCondition.put("x-oss-date", dtObj1);
        conditions.add(dateCondition);

        policy.put("conditions", conditions);
        String jsonPolicy = null;
        try {
            jsonPolicy = mapper.writeValueAsString(policy);
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }

        // 构造待签名字符串（StringToSign）
        String stringToSign = new String(
                Base64.getEncoder().encode(jsonPolicy.getBytes())
        );

        // 计算SigningKey
        byte[] dateKey = hmacsha256(("aliyun_v4" + STSsecretAccessKey).getBytes(), dtObj2);
        // 请将<cn-hangzhou>替换为您的实际Bucket所处地域，例如北京地域为：cn-beijing
        byte[] dateRegionKey = hmacsha256(dateKey, "cn-beijing");
        byte[] dateRegionServiceKey = hmacsha256(dateRegionKey, "oss");
        byte[] signingKey = hmacsha256(dateRegionServiceKey, "aliyun_v4_request");

        // 计算Signature
        byte[] result = hmacsha256(signingKey, stringToSign);
        String signature = BinaryUtil.toHex(result);

        Map<String, String> messageMap = new HashMap<>();
        messageMap.put("security_token", securityToken);
        messageMap.put("signature", signature);
        messageMap.put("x_oss_date", dtObj1);
        // 请将<cn-hangzhou>替换为您的实际Bucket所处地域，例如北京地域为：cn-beijing
        messageMap.put("x_oss_credential", STSaccessKeyId + "/" + dtObj2 + "/cn-beijing/oss/aliyun_v4_request");
        messageMap.put("x_oss_signature_version", "OSS4-HMAC-SHA256");
        messageMap.put("policy", stringToSign);

        // 打印返回至客户端的签名信息
        return messageMap;
    }

    /**
     * 使用HMAC-SHA256算法计算给定密钥和数据的哈希值的静态方法
     *
     * @param key
     * @param data
     * @return
     */
    private static byte[] hmacsha256(byte[] key, String data) {
        try {
            SecretKeySpec secretKeySpec = new SecretKeySpec(key, "HmacSHA256");
            Mac mac = Mac.getInstance("HmacSHA256");
            mac.init(secretKeySpec);
            byte[] hmacBytes = mac.doFinal(data.getBytes());
            return hmacBytes;
        } catch (Exception e) {
            throw new RuntimeException("Failed to calculate HMAC-SHA256", e);
        }
    }
}
