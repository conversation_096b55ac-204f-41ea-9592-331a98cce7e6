package com.pgb.service.db.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.pgb.service.db.*;
import com.pgb.service.domain.popup.PgPopup;
import com.pgb.service.domain.popup.PgPopupUser;
import com.pgb.service.domain.tag.PgTagUser;
import com.pgb.service.domain.vip.PgVip;
import com.pgb.service.enums.PopupTypeEnum;
import com.pgb.service.enums.ShowOnTypeEnum;
import com.pgb.service.mapper.PgPopupMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【pg_popup(弹窗表)】的数据库操作Service实现
 * @createDate 2024-12-23 16:19:57
 */
@Service
@RequiredArgsConstructor
public class PgPopupServiceImpl extends ServiceImpl<PgPopupMapper, PgPopup> implements PgPopupService {

    private final PgTagUserService pgTagUserService;

    private final PgPopupUserService pgPopupUserService;

    private final PgActivityService pgActivityService;

    @Override
    public List<PgPopup> filterPopup(Long userId, PopupTypeEnum type) {
        // 获取有效弹窗
        List<PgPopup> popups = list(new LambdaQueryWrapper<PgPopup>()
                .eq(PgPopup::getIsValid, true)
                // 弹窗类型
                .eq(PgPopup::getType, type)
                // 在有效期内
                .le(PgPopup::getStartTime, new Date())
                .ge(PgPopup::getEndTime, new Date())
                .orderByAsc(PgPopup::getSort));

        // 筛选符合要求的弹窗
        popups = popups.stream().filter(popup -> {

            /**
             * 1. 如果是弹窗，需要判断是否参与过当前活动
             * 如果参与过，则不需要再弹窗了
             */
            if (type.equals(PopupTypeEnum.POPUP) && pgActivityService.isParticipate(userId, popup.getActivityId())) {
                return false;
            }

            // 先判断弹窗时机
            boolean isShowOn = false;

            // 根据弹窗规则进行返回
            if (popup.getShowOn().equals(ShowOnTypeEnum.ALWAYS)) {
                isShowOn = true;
            }
            // 当天一次
            else if (popup.getShowOn().equals(ShowOnTypeEnum.TODAY_FIRST)) {
                // 判断是否有过记录,弹窗-用户关联记录
                PgPopupUser popupUser = pgPopupUserService.getOne(new LambdaQueryWrapper<PgPopupUser>()
                        .eq(PgPopupUser::getPopupId, popup.getId())
                        .eq(PgPopupUser::getUserId, userId)
                        .last("LIMIT 1")
                );

                // 如果没有，创建一个
                if (ObjectUtil.isNull(popupUser)) {
                    isShowOn = true;
                }

                // 如果有，判断是否当天弹窗过
                // 当天没有弹窗，记录
                isShowOn = !DateUtil.isSameDay(popupUser.getUpdateTime(), new Date());
            }
            // 仅一次
            else if (popup.getShowOn().equals(ShowOnTypeEnum.ONLY_ONCE)) {
                // 判断是否有过记录,弹窗-用户关联记录
                PgPopupUser popupUser = pgPopupUserService.getOne(new LambdaQueryWrapper<PgPopupUser>()
                        .eq(PgPopupUser::getPopupId, popup.getId())
                        .eq(PgPopupUser::getUserId, userId)
                        .last("LIMIT 1")
                );

                // 如果存在
                isShowOn = ObjectUtil.isNull(popupUser);
            }

            // 不需要弹，直接跳过
            if (!isShowOn) {
                return false;
            }

            // 获取弹窗排除的人群标签
            List<String> popupNotTags = new ArrayList<>();
            if (StrUtil.isNotBlank(popup.getNotTagIds())) {
                popupNotTags = ListUtil.toList(StrUtil.split(popup.getNotTagIds(), StrUtil.COMMA));
            }

            // 获取用户标签
            List<String> userTags = pgTagUserService.list(new LambdaQueryWrapper<PgTagUser>()
                    .eq(PgTagUser::getUserId, userId)
            ).stream().map(PgTagUser::getTagId).map(Object::toString).toList();

            boolean isNotMatch = CollUtil.containsAny(userTags, popupNotTags);

            if (isNotMatch) {
                return false;
            }

            // 需要人群标签匹配
            if (StrUtil.isNotBlank(popup.getTagIds())) {
                // 获取弹窗对应人群标签
                List<String> popupTags = ListUtil.toList(StrUtil.split(popup.getTagIds(), StrUtil.COMMA));

                boolean isMatch = CollUtil.containsAny(userTags, popupTags);

                // 没有匹配
                if (!isMatch) {
                    return false;
                }
            }

            // 确定要弹了，添加一次弹窗记录，不存在就创建一次
            pgPopupUserService.createOrAddRecord(userId, popup.getId());

            return true;
        }).toList();

        return popups;
    }
}




