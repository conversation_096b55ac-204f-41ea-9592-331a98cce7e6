package com.pgb.service.domain.homework;

import java.io.Serializable;

import java.util.Date;

import com.pgb.service.domain.question.PgQuestionDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.ToString;
import lombok.Builder;
import org.springframework.validation.annotation.Validated;
import jakarta.validation.constraints.Size;

/**
* 作业表
* @TableName pg_homework
*/
@Schema(description = "作业表 DTO")
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode
@ToString
@Validated
@Builder
public class PgHomeworkDTO implements Serializable {

    @Schema(title = "作业id", type = "string")
    private Long id;

    @Schema(title = "创建人id")
    private Long creatorId;

    @Schema(title = "作业名称")
    @Size(max= 255)
    private String name;

    @Schema(title = "作业题目信息")
    private PgQuestionDTO questionInfo;

    @Schema(title = "创建时间")
    private Date createTime;

    @Schema(title = "更新时间")
    private Date updateTime;

    @Schema(title = "班级id")
    private Long classId;

}
