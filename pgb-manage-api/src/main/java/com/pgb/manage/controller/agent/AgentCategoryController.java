package com.pgb.manage.controller.agent;

import cn.dev33.satoken.annotation.SaCheckLogin;
import cn.dev33.satoken.annotation.SaCheckRole;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.pgb.common.core.global.BaseResult;
import com.pgb.common.core.global.GlobalCode;
import com.pgb.common.core.global.RoleConstants;
import com.pgb.manage.domain.query.AgentCategoryQuery;
import com.pgb.service.db.PgAgentCategoryService;
import com.pgb.service.db.PgAgentService;
import com.pgb.service.domain.agent.PgAgent;
import com.pgb.service.domain.agent.category.PgAgentCategory;
import com.pgb.service.domain.agent.category.PgAgentCategoryDTO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Tag(name = "管理端/智能体分类")
@RestController("AgentCategoryController")
@Transactional(rollbackFor = Exception.class)
@RequestMapping("/manage/agent/category")
@RequiredArgsConstructor
@Slf4j
@SaCheckRole(RoleConstants.Manager)
public class AgentCategoryController {
    
    private final PgAgentCategoryService pgAgentCategoryService;
    private final PgAgentService pgAgentService;

    @Operation(summary = "获取智能体分类", description = "获取全部")
    @PostMapping("list")
    public BaseResult<List<PgAgentCategoryDTO>> list() {
        List<PgAgentCategoryDTO> list = pgAgentCategoryService.list(new LambdaQueryWrapper<PgAgentCategory>()
                .orderByAsc(PgAgentCategory::getSort)
        ).stream().map(item -> {
            PgAgentCategoryDTO dto = BeanUtil.copyProperties(item, PgAgentCategoryDTO.class);
            dto.setAgentIds(
                    StrUtil.isBlank(item.getAgentIds()) ? new ArrayList<>() :
                            StrUtil.split(
                                    item.getAgentIds(),
                                    StrUtil.COMMA
                            ).stream().mapToLong(Long::parseLong).boxed().toList()
            );
            return dto;
        }).toList();

        return BaseResult.success(list);
    }

    @Operation(summary = "分页获取智能体分类")
    @PostMapping("page")
    public BaseResult<IPage<PgAgentCategoryDTO>> page(@RequestBody AgentCategoryQuery query) {

        IPage<PgAgentCategoryDTO> page = pgAgentCategoryService.page(query.toMpPage(), new LambdaQueryWrapper<PgAgentCategory>()
                .like(StrUtil.isNotBlank(query.getName()), PgAgentCategory::getName, query.getName())
                .orderByAsc(PgAgentCategory::getSort)
        ).convert(item -> {
            PgAgentCategoryDTO dto = BeanUtil.copyProperties(item, PgAgentCategoryDTO.class);
            dto.setAgentIds(
                    StrUtil.isBlank(item.getAgentIds()) ? new ArrayList<>() :
                            StrUtil.split(
                                    item.getAgentIds(),
                                    StrUtil.COMMA
                            ).stream().mapToLong(Long::parseLong).boxed().toList()
            );
            return dto;
        });
        return BaseResult.success(page);
    }

    @Operation(summary = "根据id获取智能体分类详情")
    @GetMapping("detail/{id}")
    public BaseResult<PgAgentCategoryDTO> detail(@PathVariable Long id) {
        PgAgentCategory category = pgAgentCategoryService.getById(id);

        if (ObjectUtil.isNull(category)) {
            return BaseResult.code(GlobalCode.Item_Null);
        }

        PgAgentCategoryDTO dto = BeanUtil.copyProperties(category, PgAgentCategoryDTO.class);
        dto.setAgentIds(
                StrUtil.isBlank(category.getAgentIds()) ? new ArrayList<>() :
                        StrUtil.split(
                                category.getAgentIds(),
                                StrUtil.COMMA
                        ).stream().mapToLong(Long::parseLong).boxed().toList()
        );

        return BaseResult.success(dto);
    }

    @Operation(summary = "新增智能体分类")
    @PostMapping("create")
    public BaseResult<Boolean> create(@RequestBody PgAgentCategoryDTO dto) {
        log.info("新增智能体分类，参数：{}", dto);

        PgAgentCategory category = new PgAgentCategory();
        BeanUtil.copyProperties(dto, category, "id", "agentIds");

        boolean result = pgAgentCategoryService.save(category);
        log.info("新增智能体分类结果：{}", result);

        return BaseResult.success(result);
    }

    @Operation(summary = "修改智能体分类")
    @PostMapping("update/{id}")
    public BaseResult<Boolean> update(@PathVariable Long id, @RequestBody PgAgentCategoryDTO dto) {
        log.info("修改智能体分类，id：{}，参数：{}", id, dto);

        PgAgentCategory category = pgAgentCategoryService.getById(id);

        if (ObjectUtil.isNull(category)) {
            return BaseResult.code(GlobalCode.Item_Null);
        }

        // 只更新基本信息，不直接修改agentIds字段
        BeanUtil.copyProperties(dto, category, "id", "agentIds");

        boolean result = pgAgentCategoryService.updateById(category);
        log.info("修改智能体分类结果：{}", result);

        return BaseResult.success(result);
    }

    public record CategorySortForm(Integer sort) {
    }

    @Operation(summary = "修改智能体分类排序")
    @PostMapping("update/sort/{id}")
    public BaseResult<Boolean> updateSort(@PathVariable Long id, @RequestBody CategorySortForm form) {
        log.info("修改智能体分类排序，id：{}，排序：{}", id, form.sort);

        PgAgentCategory category = pgAgentCategoryService.getById(id);

        if (ObjectUtil.isNull(category)) {
            return BaseResult.code(GlobalCode.Item_Null);
        }

        // 更新排序
        category.setSort(form.sort);

        boolean result = pgAgentCategoryService.updateById(category);
        log.info("修改智能体分类排序结果：{}", result);

        return BaseResult.success(result);
    }

    @Operation(summary = "删除智能体分类")
    @DeleteMapping("delete/{id}")
    public BaseResult<Boolean> delete(@PathVariable Long id) {
        log.info("删除智能体分类，id：{}", id);

        PgAgentCategory category = pgAgentCategoryService.getById(id);

        if (ObjectUtil.isNull(category)) {
            return BaseResult.code(GlobalCode.Item_Null);
        }

        boolean result = pgAgentCategoryService.removeById(id);
        log.info("删除智能体分类结果：{}", result);

        return BaseResult.success(result);
    }

    public record AgentAssociationForm(List<Long> agentIds) {
    }

    @Operation(summary = "关联智能体到分类")
    @PostMapping("associate/{categoryId}")
    public BaseResult<Boolean> associateAgents(@PathVariable Long categoryId, @RequestBody AgentAssociationForm form) {
        log.info("关联智能体到分类，分类id：{}，智能体ids：{}", categoryId, form.agentIds);

        PgAgentCategory category = pgAgentCategoryService.getById(categoryId);

        if (ObjectUtil.isNull(category)) {
            return BaseResult.code(GlobalCode.Item_Null);
        }

        // 验证智能体是否存在
        if (CollUtil.isNotEmpty(form.agentIds)) {
            long existCount = pgAgentService.count(new LambdaQueryWrapper<com.pgb.service.domain.agent.PgAgent>()
                    .in(com.pgb.service.domain.agent.PgAgent::getId, form.agentIds));
            if (existCount != form.agentIds.size()) {
                return BaseResult.error("部分智能体不存在");
            }
        }

        // 获取当前已关联的智能体ID列表
        List<Long> currentAgentIds = new ArrayList<>();
        if (StrUtil.isNotBlank(category.getAgentIds())) {
            currentAgentIds = StrUtil.split(category.getAgentIds(), StrUtil.COMMA)
                    .stream()
                    .map(Long::parseLong)
                    .collect(Collectors.toList());
        }

        // 合并新的智能体ID（去重）
        if (CollUtil.isNotEmpty(form.agentIds)) {
            for (Long agentId : form.agentIds) {
                if (!currentAgentIds.contains(agentId)) {
                    currentAgentIds.add(agentId);
                }
            }
        }

        // 更新agentIds字段
        String newAgentIds = currentAgentIds.isEmpty() ? null : CollUtil.join(currentAgentIds, StrUtil.COMMA);
        category.setAgentIds(newAgentIds);

        boolean result = pgAgentCategoryService.updateById(category);
        log.info("关联智能体到分类结果：{}", result);

        return BaseResult.success(result);
    }

    public record AgentDisassociationForm(List<Long> agentIds) {
    }

    @Operation(summary = "取消智能体与分类的关联")
    @PostMapping("disassociate/{categoryId}")
    public BaseResult<Boolean> disassociateAgents(@PathVariable Long categoryId, @RequestBody AgentDisassociationForm form) {
        log.info("取消智能体与分类的关联，分类id：{}，智能体ids：{}", categoryId, form.agentIds);

        PgAgentCategory category = pgAgentCategoryService.getById(categoryId);

        if (ObjectUtil.isNull(category)) {
            return BaseResult.code(GlobalCode.Item_Null);
        }

        // 获取当前已关联的智能体ID列表
        List<Long> currentAgentIds = new ArrayList<>();
        if (StrUtil.isNotBlank(category.getAgentIds())) {
            currentAgentIds = StrUtil.split(category.getAgentIds(), StrUtil.COMMA)
                    .stream()
                    .map(Long::parseLong)
                    .collect(Collectors.toList());
        }

        // 移除指定的智能体ID
        if (CollUtil.isNotEmpty(form.agentIds)) {
            currentAgentIds.removeAll(form.agentIds);
        }

        // 更新agentIds字段
        String newAgentIds = currentAgentIds.isEmpty() ? null : CollUtil.join(currentAgentIds, StrUtil.COMMA);
        category.setAgentIds(newAgentIds);

        boolean result = pgAgentCategoryService.updateById(category);
        log.info("取消智能体与分类的关联结果：{}", result);

        return BaseResult.success(result);
    }

    @Operation(summary = "添加单个智能体关联")
    @PostMapping("{categoryId}/agents/{agentId}")
    public BaseResult<Boolean> addSingleAgent(@PathVariable Long categoryId, @PathVariable Long agentId) {
        log.info("添加单个智能体关联，分类id：{}，智能体id：{}", categoryId, agentId);

        // 验证分类是否存在
        PgAgentCategory category = pgAgentCategoryService.getById(categoryId);
        if (ObjectUtil.isNull(category)) {
            return BaseResult.code(GlobalCode.Item_Null);
        }

        // 验证智能体是否存在
        PgAgent agent = pgAgentService.getById(agentId);
        if (ObjectUtil.isNull(agent)) {
            return BaseResult.error("智能体不存在");
        }

        // 获取当前已关联的智能体ID列表
        List<Long> currentAgentIds = new ArrayList<>();
        if (StrUtil.isNotBlank(category.getAgentIds())) {
            currentAgentIds = StrUtil.split(category.getAgentIds(), StrUtil.COMMA)
                    .stream()
                    .map(Long::parseLong)
                    .collect(Collectors.toList());
        }

        // 检查是否已经关联
        if (currentAgentIds.contains(agentId)) {
            return BaseResult.error("智能体已经关联到该分类");
        }

        // 添加新的智能体ID
        currentAgentIds.add(agentId);

        // 更新agentIds字段
        String newAgentIds = CollUtil.join(currentAgentIds, StrUtil.COMMA);
        category.setAgentIds(newAgentIds);

        boolean result = pgAgentCategoryService.updateById(category);
        log.info("添加单个智能体关联结果：{}", result);

        return BaseResult.success(result);
    }

    @Operation(summary = "删除单个智能体关联")
    @DeleteMapping("{categoryId}/agents/{agentId}")
    public BaseResult<Boolean> removeSingleAgent(@PathVariable Long categoryId, @PathVariable Long agentId) {
        log.info("删除单个智能体关联，分类id：{}，智能体id：{}", categoryId, agentId);

        // 验证分类是否存在
        PgAgentCategory category = pgAgentCategoryService.getById(categoryId);
        if (ObjectUtil.isNull(category)) {
            return BaseResult.code(GlobalCode.Item_Null);
        }

        // 获取当前已关联的智能体ID列表
        List<Long> currentAgentIds = new ArrayList<>();
        if (StrUtil.isNotBlank(category.getAgentIds())) {
            currentAgentIds = StrUtil.split(category.getAgentIds(), StrUtil.COMMA)
                    .stream()
                    .map(Long::parseLong)
                    .collect(Collectors.toList());
        }

        // 检查是否存在关联
        if (!currentAgentIds.contains(agentId)) {
            return BaseResult.error("智能体未关联到该分类");
        }

        // 移除指定的智能体ID
        currentAgentIds.remove(agentId);

        // 更新agentIds字段
        String newAgentIds = currentAgentIds.isEmpty() ? null : CollUtil.join(currentAgentIds, StrUtil.COMMA);
        category.setAgentIds(newAgentIds);

        boolean result = pgAgentCategoryService.updateById(category);
        log.info("删除单个智能体关联结果：{}", result);

        return BaseResult.success(result);
    }

    @Operation(summary = "批量关联智能体")
    @PostMapping("{categoryId}/agents/batch")
    public BaseResult<Boolean> addBatchAgents(@PathVariable Long categoryId, @RequestBody AgentAssociationForm form) {
        log.info("批量关联智能体，分类id：{}，智能体ids：{}", categoryId, form.agentIds);

        // 验证分类是否存在
        PgAgentCategory category = pgAgentCategoryService.getById(categoryId);
        if (ObjectUtil.isNull(category)) {
            return BaseResult.code(GlobalCode.Item_Null);
        }

        // 验证请求参数
        if (CollUtil.isEmpty(form.agentIds)) {
            return BaseResult.error("智能体ID列表不能为空");
        }

        // 验证智能体是否存在
        long existCount = pgAgentService.count(new LambdaQueryWrapper<PgAgent>()
                .in(PgAgent::getId, form.agentIds));
        if (existCount != form.agentIds.size()) {
            return BaseResult.error("部分智能体不存在");
        }

        // 获取当前已关联的智能体ID列表
        List<Long> currentAgentIds = new ArrayList<>();
        if (StrUtil.isNotBlank(category.getAgentIds())) {
            currentAgentIds = StrUtil.split(category.getAgentIds(), StrUtil.COMMA)
                    .stream()
                    .map(Long::parseLong)
                    .collect(Collectors.toList());
        }

        // 合并新的智能体ID（去重）
        List<Long> duplicateIds = new ArrayList<>();
        for (Long agentId : form.agentIds) {
            if (currentAgentIds.contains(agentId)) {
                duplicateIds.add(agentId);
            } else {
                currentAgentIds.add(agentId);
            }
        }

        // 如果有重复的智能体ID，返回警告信息
        if (!duplicateIds.isEmpty()) {
            log.warn("存在重复关联的智能体ID：{}", duplicateIds);
        }

        // 更新agentIds字段
        String newAgentIds = CollUtil.join(currentAgentIds, StrUtil.COMMA);
        category.setAgentIds(newAgentIds);

        boolean result = pgAgentCategoryService.updateById(category);
        log.info("批量关联智能体结果：{}", result);

        return BaseResult.success(result);
    }

    @Operation(summary = "批量删除智能体关联")
    @DeleteMapping("{categoryId}/agents/batch")
    public BaseResult<Boolean> removeBatchAgents(@PathVariable Long categoryId, @RequestBody AgentDisassociationForm form) {
        log.info("批量删除智能体关联，分类id：{}，智能体ids：{}", categoryId, form.agentIds);

        // 验证分类是否存在
        PgAgentCategory category = pgAgentCategoryService.getById(categoryId);
        if (ObjectUtil.isNull(category)) {
            return BaseResult.code(GlobalCode.Item_Null);
        }

        // 验证请求参数
        if (CollUtil.isEmpty(form.agentIds)) {
            return BaseResult.error("智能体ID列表不能为空");
        }

        // 获取当前已关联的智能体ID列表
        List<Long> currentAgentIds = new ArrayList<>();
        if (StrUtil.isNotBlank(category.getAgentIds())) {
            currentAgentIds = StrUtil.split(category.getAgentIds(), StrUtil.COMMA)
                    .stream()
                    .map(Long::parseLong)
                    .collect(Collectors.toList());
        }

        // 检查要删除的智能体是否存在关联
        List<Long> notFoundIds = new ArrayList<>();
        for (Long agentId : form.agentIds) {
            if (!currentAgentIds.contains(agentId)) {
                notFoundIds.add(agentId);
            }
        }

        if (!notFoundIds.isEmpty()) {
            log.warn("部分智能体未关联到该分类：{}", notFoundIds);
        }

        // 移除指定的智能体ID
        currentAgentIds.removeAll(form.agentIds);

        // 更新agentIds字段
        String newAgentIds = currentAgentIds.isEmpty() ? null : CollUtil.join(currentAgentIds, StrUtil.COMMA);
        category.setAgentIds(newAgentIds);

        boolean result = pgAgentCategoryService.updateById(category);
        log.info("批量删除智能体关联结果：{}", result);

        return BaseResult.success(result);
    }

    /**
     * 智能体排序请求表单
     */
    public record AgentSortForm(
            @Schema(description = "智能体ID列表，按新的排序顺序排列", example = "[1, 3, 2, 4]")
            List<Long> agentIds
    ) {}

    @Operation(
            summary = "对指定分类下的智能体进行排序",
            description = "重新排序指定分类下的智能体列表，传入的智能体ID列表顺序即为新的排序顺序"
    )
    @PostMapping("{categoryId}/agents/sort")
    @SaCheckLogin
    public BaseResult<Boolean> sortAgents(
            @Parameter(description = "分类ID", required = true, example = "1")
            @PathVariable Long categoryId,
            @io.swagger.v3.oas.annotations.parameters.RequestBody(
                    description = "排序请求参数",
                    required = true
            )
            @RequestBody AgentSortForm form
    ) {

        // 验证分类是否存在
        PgAgentCategory category = pgAgentCategoryService.getById(categoryId);
        if (ObjectUtil.isNull(category)) {
            log.warn("分类不存在，分类id：{}", categoryId);
            return BaseResult.code(GlobalCode.Item_Null);
        }

        //log.info("对指定分类下的智能体进行排序，分类id：{}，\n 智能体ids：{}", category.getAgentIds(), form.agentIds);

        // 验证请求参数
        if (CollUtil.isEmpty(form.agentIds)) {
            return BaseResult.error("智能体ID列表不能为空");
        }

        // 验证智能体是否存在
        long existCount = pgAgentService.count(new LambdaQueryWrapper<PgAgent>()
                .in(PgAgent::getId, form.agentIds));
        if (existCount != form.agentIds.size()) {
            log.warn("部分智能体不存在，期望数量：{}，实际数量：{}", form.agentIds.size(), existCount);
            return BaseResult.error("部分智能体不存在");
        }

        // 获取当前分类已关联的智能体ID列表
        List<Long> currentAgentIds = new ArrayList<>();
        if (StrUtil.isNotBlank(category.getAgentIds())) {
            currentAgentIds = StrUtil.split(category.getAgentIds(), StrUtil.COMMA)
                    .stream()
                    .map(Long::parseLong)
                    .toList();
        }

        // 验证传入的智能体ID是否都属于该分类
        List<Long> notInCategoryIds = new ArrayList<>();
        for (Long agentId : form.agentIds) {
            if (!currentAgentIds.contains(agentId)) {
                notInCategoryIds.add(agentId);
            }
        }

        if (!notInCategoryIds.isEmpty()) {
            log.warn("部分智能体不属于该分类，分类id：{}，智能体ids：{}", categoryId, notInCategoryIds);
            return BaseResult.error("部分智能体不属于该分类：" + CollUtil.join(notInCategoryIds, ","));
        }

        // 验证是否包含了所有已关联的智能体
        if (form.agentIds.size() != currentAgentIds.size()) {
            log.warn("排序列表数量与分类关联智能体数量不匹配，排序列表：{}，分类关联：{}",
                    form.agentIds.size(), currentAgentIds.size());
            return BaseResult.error("排序列表必须包含该分类下的所有智能体");
        }

        // 更新agentIds字段为新的排序
        String newAgentIds = CollUtil.join(form.agentIds, StrUtil.COMMA);
        category.setAgentIds(newAgentIds);

        boolean result = pgAgentCategoryService.updateById(category);

        return BaseResult.success(result);
    }
}
