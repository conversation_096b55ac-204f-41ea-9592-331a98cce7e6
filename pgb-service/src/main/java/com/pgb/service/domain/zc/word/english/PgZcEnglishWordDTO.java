package com.pgb.service.domain.zc.word.english;

import java.io.Serializable;

import java.util.Date;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.ToString;
import lombok.Builder;
import org.springframework.validation.annotation.Validated;
import jakarta.validation.constraints.Size;
/**
* 英语单词表
* @TableName pg_english_word
*/
@Schema(description = "英语单词表 DTO")
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode
@ToString
@Validated
@Builder
public class PgZcEnglishWordDTO implements Serializable {

    @Schema(title = "", type = "string")
    private Long id;

    @Schema(title = "是否是官方教材单词")
    private Boolean isOfficial;

    @Schema(title = "用户id")
    private Long userId;

    @Schema(title = "阶段和版本（小学人教PEP版）")
    @Size(max= 255)
    private String version;

    @Schema(title = "年级册别")
    @Size(max= 255)
    private String grade;

    @Schema(title = "单元")
    @Size(max= 255)
    private String unit;

    @Schema(title = "英文单词")
    @Size(max= 255)
    private String word;

    @Schema(title = "释义")
    @Size(max= 255)
    private String definition;

    @Schema(title = "音标")
    @Size(max= 255)
    private String phonetic;

    @Schema(title = "词性（动词）")
    @Size(max= 255)
    private String wordClass;

    @Schema(title = "创建时间")
    private Date createTime;

}
