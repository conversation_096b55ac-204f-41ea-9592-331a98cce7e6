package com.pgb.service.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;

/**
 * <AUTHOR>
 * Created by 2025/2/8 19:07
 */
@Schema(description = "角色 类型")
@AllArgsConstructor
public enum RoleTypeEnum {

    @Schema(title = "用户")
    USER(0, "用户"),

    @Schema(title = "机器人")
    BOT(1, "机器人");

    /**
     * 枚举值对应的整数值
     */
    @EnumValue
    public final int value;

    /**
     * 文字说明
     */
    public final String desc;
}
