package com.pgb.service.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;

@Schema(title = "全局 购买类型")
@AllArgsConstructor
public enum VipTypeEnum {

    YEAR(0, "年卡", 298 * 100, 366),

    MONTH(1, "月卡", (int) (29.9 * 100), 31),

    WEEK(2, "周卡", (int) (9.9 * 100), 7),

    TERM(3, "学期卡", (88 * 100), 124),

    SEASON(4, "季卡", (68 * 100), 93),

    YEAR_198(5, "年卡（活动价）", 198 * 100, 366),

    DOUBLE_MONTH(6, "双月卡", (int) (48 * 100), 62);

    /**
     * 枚举值对应的整数值
     */
    @EnumValue
    public final int value;

    /**
     * 文字说明
     */
    public final String desc;

    public final Integer price;

    public final Integer dayNum;
}
