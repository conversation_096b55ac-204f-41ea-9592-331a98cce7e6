package com.pgb.common.core.global;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;

@Schema(title = "全局状态码")
@AllArgsConstructor
public enum GlobalCode {

    @Schema(title = "成功", defaultValue = "200")
    Success(200, "成功"),

    @Schema(title = "系统异常")
    Error(500, "系统异常"),

    @Schema(title = "参数异常")
    Param_Wrong(1001, "参数异常"),

    @Schema(title = "内容不存在")
    Item_Null(1002, "内容不存在"),

    @Schema(title = "次数超限", description = "登录次数超限、短信发送超限、OCR超限等")
    Limit_Over(1003, "次数超限"),

    @Schema(title = "未登录")
    Login_Not(2001, "未登录"),

    @Schema(title = "无此权限")
    Permission_Not(2002, "无此权限"),

    @Schema(title = "无此角色")
    Role_Not(2003, "无此角色"),

    @Schema(title = "二级校验失败")
    Safe_Not(2004, "二级校验失败"),

    @Schema(title = "账号已封禁")
    Disable_Service(2005, "账号已封禁"),

    @Schema(title = "验证码错误")
    Login_Captcha_Error(2011, "验证码错误"),

    @Schema(title = "账号不存在")
    Login_User_Null(2012, "账号不存在，请先注册"),

    @Schema(title = "登录密码错误")
    Login_Password_Error(2013, "登录密码错误"),

    @Schema(title = "微信未授权")
    Login_Wx_Error(2014, "微信未授权"),

    @Schema(title = "短信发送失败")
    Sms_Send_Fail(2023, "短信发送失败"),

    @Schema(title = "短信验证码错误")
    Sms_Code_Error(2023, "短信验证码错误"),

    @Schema(title = "验证码已过期")
    Sms_Code_Expire(2024, "验证码已过期"),

    @Schema(title = "验证码手机号不对应")
    Sms_Phone_Error(2025, "验证码手机号不对应"),

    @Schema(title = "账号已存在")
    Account_Exists(2026, "账号已存在"),

    @Schema(title = "重复提交")
    Repeat_Submit(2027, "重复提交");

    /**
     * 枚举值对应的整数值
     */
    public final int value;

    /**
     * 文字说明
     */
    public final String desc;
}
