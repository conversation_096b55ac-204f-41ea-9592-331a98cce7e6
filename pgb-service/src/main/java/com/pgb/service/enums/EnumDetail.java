package com.pgb.service.enums;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * Created by 2024/8/16 14:12
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class EnumDetail {

    @Schema(title = "枚举值")
    private String value;

    @Schema(title = "名称")
    private String desc;

    @Schema(title = "描述")
    private String comment;

    @Schema(title = "图标")
    private String iconUrl;
}
