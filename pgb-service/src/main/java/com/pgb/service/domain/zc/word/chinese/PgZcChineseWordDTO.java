package com.pgb.service.domain.zc.word.chinese;

import java.io.Serializable;

import java.util.Date;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.ToString;
import lombok.Builder;
import org.springframework.validation.annotation.Validated;
import jakarta.validation.constraints.Size;

/**
* 语文教材字词表
* @TableName pg_chinese_word
*/
@Schema(description = "语文教材字词表 DTO")
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode
@ToString
@Validated
@Builder
public class PgZcChineseWordDTO implements Serializable {

    @Schema(title = "", type = "string")
    private Long id;

    @Schema(title = "字词")
    @Size(max= 255)
    private String word;

    @Schema(title = "拼音")
    @Size(max= 255)
    private String pinyin;

    @Schema(title = "创建时间")
    private Date createTime;

    @Schema(title = "更新时间")
    private Date updateTime;

    @Schema(title = "播报音频url")
    private String audioUrl;

}
