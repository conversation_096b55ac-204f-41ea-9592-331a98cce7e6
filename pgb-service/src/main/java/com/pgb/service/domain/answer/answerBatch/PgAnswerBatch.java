package com.pgb.service.domain.answer.answerBatch;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.util.Date;

import com.pgb.service.domain.question.zwEssay.ZwEssayTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 批次提交表
 *
 * @TableName pg_answer_batch
 */
@TableName(value = "pg_answer_batch")
@Data
public class PgAnswerBatch implements Serializable {
    /**
     * 批次id
     */
    private Long id;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 关联题目id
     */
    private Long questionId;

}
