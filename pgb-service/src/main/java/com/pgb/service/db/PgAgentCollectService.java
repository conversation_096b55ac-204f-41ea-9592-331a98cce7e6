package com.pgb.service.db;

import com.pgb.service.domain.agent.collect.PgAgentCollect;
import com.baomidou.mybatisplus.extension.service.IService;

/**
* <AUTHOR>
* @description 针对表【pg_agent_collect】的数据库操作Service
* @createDate 2025-02-13 15:55:27
*/
public interface PgAgentCollectService extends IService<PgAgentCollect> {
    /**
     * 收藏智能体
     * @param agentId
     * @param userId
     */
    public void collect(Long agentId, Long userId);

    /**
     * 取消收藏智能体
     * @param agentId
     * @param userId
     */
    public void unCollect(Long agentId, Long userId);
}
