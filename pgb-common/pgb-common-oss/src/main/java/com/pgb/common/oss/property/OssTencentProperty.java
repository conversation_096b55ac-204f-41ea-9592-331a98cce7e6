package com.pgb.common.oss.property;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

import java.util.List;

@Schema(title = "腾讯云OSS配置")
@ConfigurationProperties(prefix = "oss.tencent")
@Data
public class OssTencentProperty {

    @Schema(title = "密钥 id")
    private String secretId;

    @Schema(title = "密钥 key")
    private String secretKey;

    @Schema(title = "存储区域")
    private String region;

    @Schema(title = "自定义域名")
    private String cdnDomain;

    @Schema(title = "桶名称")
    private List<String> bucketName;
}
