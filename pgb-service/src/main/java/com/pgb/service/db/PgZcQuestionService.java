package com.pgb.service.db;

import com.pgb.service.domain.zc.question.PgZcQuestion;
import com.baomidou.mybatisplus.extension.service.IService;

/**
* <AUTHOR>
* @description 针对表【pg_zc_question】的数据库操作Service
* @createDate 2025-04-21 14:52:43
*/
public interface PgZcQuestionService extends IService<PgZcQuestion> {

    /**
     * 查询待渲染的题目，并加入渲染队列
     * @return
     */
    public Integer queryToRender();

    /**
     * 加入执行生成pdf队列
     * @param id
     */
    void generatePdfQueue(Long id);

    /**
     * md5操作
     * @param zcQuestion
     * @return
     */
    String getZcQuestionMd5(PgZcQuestion zcQuestion);

    /**
     * 根据 type 返回对应的 PDF 链接
     * @param pdfUrl
     * @param type
     * @return
     */
    String getPdfUrlByType(String pdfUrl, String type);

}
