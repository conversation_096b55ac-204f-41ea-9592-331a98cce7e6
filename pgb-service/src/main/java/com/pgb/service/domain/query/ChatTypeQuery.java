package com.pgb.service.domain.query;

import com.pgb.common.mybatis.global.PageQuery;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Min;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * Created by 2025/2/12 15:36
 */
@EqualsAndHashCode(callSuper = true)
@Schema(description = "会话文档记录 搜索实体")
@Data
public class ChatTypeQuery extends PageQuery {

    @Schema(title = "名称")
    private String name;

    @Schema(title = "分组id")
    private Long cateId;

}
