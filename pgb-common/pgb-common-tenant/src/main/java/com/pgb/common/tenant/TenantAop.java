package com.pgb.common.tenant;

import cn.dev33.satoken.stp.StpUtil;
import com.pgb.common.core.global.RoleConstants;
import com.pgb.common.core.utils.Id2CodeUtil;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.After;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import java.util.Objects;

/**
 * 多租户 切面
 */
@Aspect
@Component
@Slf4j
public class TenantAop {

    @Before(value = "execution(* com.pgb..controller..*.*(..))")
    public void Before(JoinPoint joinPoint) {

        // 如果已经登录
        if (StpUtil.isLogin()) {

            // 判断是否管理员角色
            if (StpUtil.getRoleList().contains(RoleConstants.Manager)) {
                TenantContext.setIsManage(true);
                return;
            } else {
                TenantContext.setIsManage(false);
            }

            // 判断是否登录成功，且已有多租户 店铺 ID
            if (StpUtil.getSession().has("tenantId")) {
                TenantContext.setTenantId(StpUtil.getSession().getLong("tenantId"));
                return;
            }
        }

        // 方法执行前的处理，相当于前置通知
        HttpServletRequest request = ((ServletRequestAttributes) Objects.requireNonNull(RequestContextHolder.getRequestAttributes())).getRequest();
        String tenantId = request.getHeader("tenantId");
        if (tenantId != null) {
            // log.info("设置tenantId：{}", tenantId);
            TenantContext.setTenantId(Id2CodeUtil.decode(tenantId));
        }
    }

    @After(value = "execution(* com.pgb..controller..*.*(..))")
    public void After(JoinPoint joinPoint) {
        // log.info("移除，tenantId = {}", TenantContext.TENANT_ID.get());
        // 务必每次都移除，否则会出现，值 串乱的情况
        TenantContext.TENANT_ID.remove();
        TenantContext.IS_MANAGE.remove();
    }

}
