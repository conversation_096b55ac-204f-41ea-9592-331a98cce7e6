package com.pgb.common.core.utils;

import cn.hutool.core.codec.Base64;
import cn.hutool.core.img.ImgUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.file.FileNameUtil;
import cn.hutool.core.util.NumberUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.multipart.MultipartFile;

import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.IOException;
import java.text.DecimalFormat;
import java.util.UUID;

@Slf4j
public class FileUtils {

    public static String name2uuid(String name){
        String uuid = UUID.randomUUID().toString().replaceAll("-", "");
        int lastIndexOf = name.lastIndexOf(".");
        String suffix = name.substring(lastIndexOf);
        return uuid + suffix;
    }

    public static File transferToFile(MultipartFile multipartFile) {
        //  选择用缓冲区来实现这个转换即使用java 创建的临时文件 使用 MultipartFile.transferto()方法 。
        File file = null;
        try {
            String originalFilename = multipartFile.getOriginalFilename();
            String[] filename = originalFilename.split("\\.");
            String uuid = UUID.randomUUID().toString().replaceAll("-", "");
            file=File.createTempFile(uuid, filename[1]);
            multipartFile.transferTo(file);
            file.deleteOnExit();
        } catch (IOException e) {
            e.printStackTrace();
        }
        return file;
    }

    public static File transToFile(MultipartFile multipartFile) {

        File file = FileUtil.createTempFile(
                FileNameUtil.getPrefix(multipartFile.getOriginalFilename()),
                "." + FileNameUtil.getSuffix(multipartFile.getOriginalFilename()),
                false
        );

        try {
            file = FileUtil.writeBytes(multipartFile.getBytes(), file);
            file.deleteOnExit();
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        return file;
    }

    /**
     * 根据文件获取文件大小
     * @param file
     * @return
     */
    public static String getFileSize(File file){
        String size = "";
        if(file.exists() && file.isFile()){
            long fileS = file.length();
            size = getFileSizeByNum(fileS);
        }else if(file.exists() && file.isDirectory()){
            size = "";
        }else{
            size = "0BT";
        }
        return size;
    }

    /**
     * 将字节数据大小，转变为字符串
     * @param fileS
     * @return
     */
    public static String getFileSizeByNum(long fileS) {
        String size = "";
        DecimalFormat df = new DecimalFormat("#.00");
        if (fileS < 1024) {
            size = df.format((double) fileS) + "BT";
        } else if (fileS < 1048576) {
            size = df.format((double) fileS / 1024) + "KB";
        } else if (fileS < 1073741824) {
            size = df.format((double) fileS / 1048576) + "MB";
        } else {
            size = df.format((double) fileS / 1073741824) + "GB";
        }
        return size;
    }

    /**
     * 返回KB大小值，保留两位小数
     * @param file
     * @return
     */
    public static Double getFileSizeByKB(File file) {
        long fileS = file.length();
        return NumberUtil.div(fileS, 1024, 2);
    }

    /**
     * 将缓冲区图片转为base64位图片
     * 同位代替：ImgUtil.toBase64(image, "png")
     * @param image
     * @return
     */
    public static String bufferImgToBase64(BufferedImage image) {
        ByteArrayOutputStream baos = new ByteArrayOutputStream();//io流
        ImgUtil.write(image, "png", baos);
        return Base64.encode(baos.toByteArray());
    }

    /**
     * 获取文件后缀
     * @param url
     * @return
     */
    public static String getFileExt(String url) {
        int dotIndex = url.lastIndexOf('.');
        int questionMarkIndex = url.indexOf('?', dotIndex);

        // 没有找到有效的后缀或者后缀在问号之后
        if (dotIndex == -1 || (questionMarkIndex != -1 && dotIndex > questionMarkIndex)) {
            return "";
        }

        return url.substring(dotIndex, questionMarkIndex != -1 ? questionMarkIndex : url.length());
    }
}
