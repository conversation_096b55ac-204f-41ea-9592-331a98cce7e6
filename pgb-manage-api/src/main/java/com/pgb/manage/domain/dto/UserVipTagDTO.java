package com.pgb.manage.domain.dto;

import com.pgb.service.enums.VipTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
public class UserVipTagDTO {
    @Schema(title = "需要贴的标签id列表")
    private List<Long> tagIds;

    @Schema(title = "对应会员类型")
    private List<VipTypeEnum> isTypeList;

    @Schema(title = "(非)不是的会员类型")
    private List<VipTypeEnum> notTypeList;
}
