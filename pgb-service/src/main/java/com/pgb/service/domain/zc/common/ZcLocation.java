package com.pgb.service.domain.zc.common;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Schema(title = "字词坐标")
@Data
public class ZcLocation {

    private Float top;

    private Float left;

    private Float width;

    private Float height;

    @Schema(title = "所属页数")
    private Integer pageNo;

    @Schema(title = "第几课")
    private String textNo;

    public ZcLocation() {

    }

    public ZcLocation(Float left, Float top, Float width, Float height, Integer pageNo) {
        this.top = top;
        this.left = left;
        this.width = width;
        this.height = height;
        this.pageNo = pageNo;
    }

}
