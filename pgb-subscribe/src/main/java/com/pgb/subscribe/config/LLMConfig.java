package com.pgb.subscribe.config;

import com.pgb.ai.LLMService;
import com.pgb.ai.models.AliLLM;
import com.pgb.ai.models.BaiduLLM;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
@RequiredArgsConstructor
public class LLMConfig {

    @Bean("aliLLMService")
    public LLMService aliLLMService() {
        return new AliLLM("sk-bc1d347165ab4e02a16ea2cf10921dc8");
    }

    @Bean("baiduLLMService")
    public LLMService baiduLLMService() {
        return new BaiduLLM("bce-v3/ALTAK-iJ6t4JgE9kjQN4TI1bdGA/904f225682637506380c046585f82b4a248f3829");
    }
}
