package com.pgb.service.domain.export;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;

import com.pgb.service.enums.ExportStatusEnum;
import lombok.Data;

/**
 * @TableName pg_export_record
 */
@TableName(value = "pg_export_record")
@Data
public class PgExportRecord implements Serializable {
    /**
     * 导出记录
     */
    @TableId
    private Long id;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 导出篇数
     */
    private Integer totalNum;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 导出时间
     */
    private Date exportTime;

    /**
     * 导出状态
     */
    private ExportStatusEnum status;

    /**
     * 文件地址
     */
    private String zipUrl;

    /**
     * 导出md5
     */
    private String md5;

    /**
     * 导出id列表，用分号分隔
     */
    private String answerIds;

    /**
     * 所属作业id
     */
    private Long homeworkId;

    /**
     * 名称
     */
    private String name;

    /**
     * 学生id列表，用分号分隔
     */
    private String studentIds;

    /**
     * 作业id列表，用分号分隔
     */
    private String homeworkIds;

    /**
     * 内容类型 0：作文，1：字词，2：作文集
     */
    private Integer type;

}
