package com.pgb.common.core.global;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;

@Schema(description = "系统，全局角色")
@AllArgsConstructor
public class RoleConstants {
    /**
     * 店铺-员工角色
     */
    public static final String Account = "Account";

    /**
     * 店铺-店铺角色
     */
    public static final String Tenant = "Tenant";

    /**
     * 系统-管理角色
     */
    public static final String Manager = "Manager";

    /**
     * 店铺-普通用户角色
     */
    public static final String User = "User";
}
