package com.pgb.service.domain.query;

import com.pgb.service.enums.SubjectEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * Created by 2024/7/24 11:04
 */

@Schema(description = "教材作文题目 搜索实体")
@Data
public class OfficialQuestionQuery {

    @Schema(title = "关键词")
    private String keyword;

    @Schema(title = "题目类型")
    private SubjectEnum subject;
}
