package com.pgb.service.custom;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.TimeInterval;
import cn.hutool.core.img.Img;
import cn.hutool.core.img.ImgUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.IORuntimeException;
import cn.hutool.core.lang.TypeReference;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.*;
import cn.hutool.crypto.digest.DigestUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.pgb.common.core.global.BaseResult;
import com.pgb.common.core.global.exception.BaseException;
import com.pgb.common.core.utils.EnvUtils;
import com.pgb.common.oss.service.OssService;
import com.pgb.service.domain.answer.gridPaper.GridPaperResult;
import com.pgb.service.domain.common.fabric.FabricJson;
import com.pgb.service.domain.common.image.FilePaperImg;
import com.pgb.service.domain.question.homework.HomeworkStatistic;
import com.pgb.service.domain.question.zwEssay.ZwEssayQuestion;
import com.pgb.service.domain.zc.question.chinese.pinyinAndWord.PinyinAndWordResult;
import com.pgb.service.domain.zc.question.ZcQuestion;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.awt.image.BufferedImage;
import java.io.File;
import java.io.InputStream;
import java.time.Duration;
import java.util.List;

/**
 * 批改服务
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class CorrectService {

    @Value("${correct.api-key}")
    private String apiKey;

    @Value("${correct.base-url}")
    private String baseUrl;

    @Value("${correct.render-url}")
    private String renderUrl;

    @Value("${correct.render-url-user}")
    private String renderUrlUser;

    // @Value("${correct.grid-split-url}")
    // private String splitUrl;

    @Setter
    private boolean correcting = true;

    private final OssService ossService;

    @Async // 标记此方法为异步执行
    public void executeAsyncTask() {
        try {
            System.out.println("开始执行长时间任务...");
            Thread.sleep(50000); // 模拟长时间运行的任务
            System.out.println("长时间任务完成");
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt(); // 恢复中断状态
            System.err.println("任务被中断: " + e.getMessage());
        }
    }

    // 获取当前批改状态
    public boolean getCorrecting() {
        return ObjectUtil.defaultIfNull(correcting, true);
    }

    private String post(String url, JSONObject body) {
        return HttpRequest.post(url)
                .body(body.toString())
                .timeout(20 * 60 * 1000)
                .execute()
                .body();
    }

    // 识别并返回分割后的图片
    public GridPaperResult gridPaperResult(String userImg) {
        // 发送请求
        // TODO 测试后再更改环境
        String resultStr = HttpRequest.post(StrUtil.format("{}/api/correct/zw/grid_paper/{}",
                        (EnvUtils.isBalance() || EnvUtils.isDev() || EnvUtils.isProd()) ? "http://model-java.pigaibang.com" : "http://127.0.0.1:8081",
                        apiKey)
                )
                .body(
                        JSONUtil.createObj()
                                .putOnce("imgUrl", userImg)
                                .toString()
                )
                .execute()
                .body();

        BaseResult<GridPaperResult> result = Convert.convert(new TypeReference<BaseResult<GridPaperResult>>() {
        }, JSONUtil.parseObj(resultStr));

        if (result.isSuccess()) {
            return result.getData();
        } else {
            log.error("识别标准格子纸失败，错误信息：{}", result);
            throw new BaseException("识别标准格子纸失败");
        }
    }

    // 校验图片有效性，并持久化图片处理
    public void processImgV2(FilePaperImg img) {
        ProcessImgUtil.processImg(img);
    }

    // 校验图片有效性，并持久化图片处理
    public FilePaperImg processImg(FilePaperImg img) {
        TimeInterval timer = DateUtil.timer();
        String style = "?x-oss-process=image";
        BufferedImage bufferedImage;
        File file = FileUtil.createTempFile(".jpg", true);
        try {
            bufferedImage = ImgUtil.read(URLUtil.url(img.getImgUrl()));
        } catch (IllegalArgumentException | IllegalStateException | IORuntimeException e) {
            log.info("图片为无效格式，执行图片格式转化：{}", e.getMessage());
            style += "/format,jpg";
            bufferedImage = ImgUtil.read(URLUtil.url(ossService.getFormat(img.getImgUrl(), "jpg")));
        }

        ImgUtil.write(bufferedImage, file);

        // 图片尺寸不能超过 4000
        if (bufferedImage.getHeight() > 4000 || bufferedImage.getWidth() > 4000) {
            style += "/resize,l_4000";
            // 低于4000压缩
            double ratio = NumberUtil.min(4000.0 / bufferedImage.getHeight(), 4000.0 / bufferedImage.getWidth());
            bufferedImage = ImgUtil.toBufferedImage(ImgUtil.scale(bufferedImage, (float) ratio), ImgUtil.IMAGE_TYPE_JPG);
        }

        // 如果图片大小大于 1M
        if (FileUtil.size(file) > 1 * 1024 * 1024 || StrUtil.endWith(img.getImgUrl(), "png")) {
            style += "/resize,p_50";
            bufferedImage = ImgUtil.toBufferedImage(Img.from(bufferedImage)
                            //压缩比率
                            .setQuality(0.8)
                            .getImg(),
                    ImgUtil.IMAGE_TYPE_JPG);
        }

        style += "/resize,p_50";
        bufferedImage = ImgUtil.toBufferedImage(Img.from(bufferedImage)
                        //压缩比率
                        .setQuality(0.8)
                        .getImg(),
                ImgUtil.IMAGE_TYPE_JPG);

        if (!style.equals("?x-oss-process=image")) {
            log.info("图片处理，执行图片处理：{}", style);

            // 获取处理好的图片
            //bufferedImage = ImgUtil.read(
            //        URLUtil.url(img.getImgUrl() + style)
            //);
            // 重构键值
            String key = URLUtil.getPath(img.getImgUrl());
            if (key.startsWith("/")) {
                key = StrUtil.subSuf(key, 1);
            }
            if (key.contains("/")) {
                key = StrUtil.subBefore(key, "/", true);
                key += "/";
            } else {
                key = "";
            }
            key += DigestUtil.md5Hex(ImgUtil.toBytes(bufferedImage, ImgUtil.IMAGE_TYPE_JPG));

            // 统一处理成jpg格式
            key += ".jpg";

            File tempFile = FileUtil.createTempFile(".jpg", true);
            ImgUtil.write(bufferedImage, tempFile);
            String newUrl = ossService.putFile(key, tempFile);

            ossService.setTagList(
                    URLUtil.getPath(img.getImgUrl()),
                    "Deleted"
            );

            img.setImgUrl(newUrl);

            log.info("\n 图片预处理完成：{}；耗时：{}，原图大小：{}，压缩大小：{}", newUrl, timer.intervalSecond(), FileUtil.readableFileSize(file), FileUtil.readableFileSize(tempFile));

            // 删除临时文件
            FileUtil.del(tempFile);

        }

        // 更新 宽高
        img.setImgH(bufferedImage.getHeight());
        img.setImgW(bufferedImage.getWidth());

        // 删除临时文件
        FileUtil.del(file);

        return img;
    }

    /**
     * 英文默写卡批改
     *
     * @param essay
     * @return
     */
    public ZwEssayQuestion englishWritingCorrect(ZwEssayQuestion essay) {
        // 发送请求
        String resultStr = this.post(
                StrUtil.format("{}/api/correct/writing/english/card/{}", baseUrl, apiKey),
                JSONUtil.parseObj(essay)
        );

        BaseResult<ZwEssayQuestion> result = Convert.convert(new TypeReference<BaseResult<ZwEssayQuestion>>() {
        }, JSONUtil.parseObj(resultStr));

        if (result.isSuccess()) {
            return result.getData();
        } else {
            log.error("英语默写卡批改失败，错误信息：{}", result);
            throw new BaseException("英语默写卡批改失败");
        }
    }

    public HomeworkStatistic zwStatistic(HomeworkStatistic statistic) {
        // 发送请求
        String resultStr = this.post(
                StrUtil.format("{}/api/correct/zw/homework/analyse/{}",
                        (EnvUtils.isBalance() || EnvUtils.isTest() || EnvUtils.isProd()) ? "http://model-java.pigaibang.com" : baseUrl,
                        apiKey
                ),
                JSONUtil.parseObj(statistic)
        );

        BaseResult<HomeworkStatistic> result = Convert.convert(new TypeReference<BaseResult<HomeworkStatistic>>() {
        }, JSONUtil.parseObj(resultStr));

        if (result.isSuccess()) {
            return result.getData();
        } else {
            log.error("班级作文分析失败，错误信息：{}", result);
            throw new BaseException("班级作文分析失败");
        }
    }

    /**
     * 作文批改
     *
     * @param essay
     * @return
     */
    public ZwEssayQuestion zwCorrect(ZwEssayQuestion essay) {
        // 发送请求
        String resultStr = this.post(
                StrUtil.format("{}/api/correct/zw/img/{}", baseUrl, apiKey),
                JSONUtil.parseObj(essay)
        );

        BaseResult<ZwEssayQuestion> result = Convert.convert(new TypeReference<BaseResult<ZwEssayQuestion>>() {
        }, JSONUtil.parseObj(resultStr));

        if (result.isSuccess()) {
            return result.getData();
        } else {
            log.error("作文批改失败，错误信息：{}", result);
            throw new BaseException("作文批改失败");
        }
    }


    /**
     * 获取json渲染后的图片
     */
    public BufferedImage getImg(Integer imgHs, Integer imgWs, FabricJson markJson, Integer retryNum, boolean isUser) {
        if (EnvUtils.isDev()) {
            isUser = true;
        }
        HttpResponse response = HttpRequest.post(isUser ? renderUrlUser : renderUrl)
                .body(JSONUtil.createObj()
                        // imgHs * (800. / imgWs) + 100
                        .putOnce("height", imgHs)
                        .putOnce("width", imgWs)
                        .putOnce("markJson", markJson).toString())
                .timeout(5 * 60 * 1000)
                .execute();

        if (response.isOk()) {
            return ImgUtil.read(response.bodyStream());
        }
        // 如果是并发限制，进行重试
        else if (response.getStatus() == 429) {
            log.info("并发超过限制，随机等待2-5秒，重新渲染：{}，重试次数：{}", response.getStatus(), retryNum);
            ThreadUtil.sleep(Duration.ofMillis(RandomUtil.randomInt(20, 50) * 100L).toMillis());
            return getImg(imgHs, imgWs, markJson, retryNum + 1, isUser);
        }
        // 异常重试机制
        else {
            if (retryNum < 8) {
                log.info("渲染失败，随机等待2-5秒，重新渲染：{}，重试次数：{}", response.getStatus(), retryNum);
                ThreadUtil.sleep(Duration.ofMillis(RandomUtil.randomInt(20, 50) * 100L).toMillis());
                return getImg(imgHs, imgWs, markJson, retryNum + 1, isUser);
            }

            log.error("获取渲染图片失败，错误信息：{}", response.body());
            throw new RuntimeException("获取渲染图片失败");
        }
    }

    public ZwEssayQuestion renderImg(List<FilePaperImg> userImgList, ZwEssayQuestion question, Long userId, boolean isDel, boolean isUser) {

        // 提前渲染好图片，然后保存到 oss 里面去
        for (int i = 0; i < question.getMarkJsonList().size(); i++) {

            String url = renderFabricImg(
                    question.getMarkJsonList().get(i),
                    question.getUserImgAnswerList().get(i).getImgUrl(),
                    userId,
                    ObjectUtil.defaultIfNull(userImgList.get(i).getCanvasH(), userImgList.get(i).getImgH()),
                    userImgList.get(i).getImgW(),
                    isDel,
                    isUser
            );

            question.getUserImgAnswerList().get(i).setImgUrl(url);
        }

        return question;
    }

    /**
     * 渲染
     *
     * @param markJson
     * @param url
     * @return
     */
    public String renderFabricImg(FabricJson markJson, String url, Long userId, Integer imgHs, Integer imgWs, boolean isDel, boolean isUser) {

        // 图片生成唯一值
        String md5 = DigestUtil.md5Hex(markJson.toString());
        // https://cdn.pigaibang.com/zw/user/1804462231628812289/CORRECT/efd9cf2e66821a4dbf85cebf5b7efa36.jpg
        // key efd9cf2e66821a4dbf85cebf5b7efa36
        String suffix = FileUtil.getSuffix(url);

        String[] split = FileUtil.getName(url).split("\\.");
        // 上传的后缀 key
        String key = split[0];

        // 判断md5 与 key是否相同
        if (md5.equals(key)) {
            return url;
        } else {
            TimeInterval timer = DateUtil.timer();

            BufferedImage image = getImg(imgHs, imgWs, markJson, 0, isUser);

            // key /zw/correct/1804462231628812289/efd9cf2e66821a4dbf85cebf5b7efa36.jpg
            String path = StrUtil.format("zw/correct/{}/{}", userId, md5 + "." + suffix);

            // 删除原文件
            if (isDel && !url.contains("user")) {

                log.info("渲染图片删除：{}", url);
                // 删除之前的文件
                ossService.delete(
                        URLUtil.getPath(url)
                );

                if (EnvUtils.isDev()) {
                    log.info("删除 = " + URLUtil.getPath(url));
                }
            }

            if (EnvUtils.isDev()) {
                log.info("上传 = " + path);
            }

            // 保存oss
            // 执行压缩算法
            File tempFile = FileUtil.createTempFile(".jpg", true);
            Img.from(image).setQuality(0.8).write(tempFile);

            // 返回最终的图片url
            String imgUrl = ossService.putFile(path, tempFile);

            log.info("新渲染图片：{}；耗时：{}，大小：{}", imgUrl, timer.intervalSecond(), FileUtil.readableFileSize(tempFile));

            // 删除临时图片
            FileUtil.del(tempFile);

            return imgUrl;
        }
    }


    /**
     * 默写批改
     * <p>
     * //     * @param zcQuestion
     *
     * @return
     */
//    public ZcQuestion zcCorrect(ZcQuestion zcQuestion) {
//        // 发送请求
//        String resultStr = this.post(
//                StrUtil.format("{}/api/correct/zw/img/{}", baseUrl, apiKey),
//                JSONUtil.parseObj(zcQuestion)
//        );
//
//        BaseResult<ZcQuestion> result = Convert.convert(new TypeReference<BaseResult<ZcQuestion>>() {
//        }, JSONUtil.parseObj(resultStr));
//
//        if (result.isSuccess()) {
//            return result.getData();
//        } else {
//            log.error("默写批改失败，错误信息：{}", result);
//            throw new BaseException("默写批改失败");
//        }
//    }
    public ZcQuestion renderZcImg(List<FilePaperImg> userImgList, ZcQuestion question, Long userId, boolean isDel, boolean isUser) {

        // 提前渲染好图片，然后保存到 oss 里面去
        for (int i = 0; i < question.getMarkJsonList().size(); i++) {

            String url = renderFabricImg(
                    question.getMarkJsonList().get(i),
                    question.getUserImgAnswerList().get(i).getImgUrl(),
                    userId,
                    ObjectUtil.defaultIfNull(userImgList.get(i).getCanvasH(), userImgList.get(i).getImgH()),
                    userImgList.get(i).getImgW(),
                    isDel,
                    isUser
            );

            question.getUserImgAnswerList().get(i).setImgUrl(url);
        }

        return question;
    }

    /**
     * 矫正图片
     *
     * @param userImgUrl
     * @return
     */
    public BufferedImage correctImg(String userImgUrl) {

        // 创建临时文件
        File userImg = FileUtil.createTempFile();

        HttpRequest.get(userImgUrl)
                .execute()
                .writeBody(userImg);

        // 请求地址
        String baseUrl = EnvUtils.isDev() ? "https://cv-doctr-ukbigacefh.cn-beijing.fcapp.run/doc_tr" : "https://cv-doctr-ukbigacefh.cn-beijing-vpc.fcapp.run/doc_tr";

        HttpResponse response = HttpRequest.post(baseUrl)
                .form("image", userImg)
                .execute();

        if (response.isOk()) {

            InputStream inputStream = response.bodyStream();

            return ImgUtil.read(inputStream);

        } else {
            log.error("矫正图片失败，错误信息：{}", response.body());

            throw new BaseException("矫正图片失败");
        }
    }

    /**
     * 是否是扫描件
     *
     * @param userImgUrl
     * @return
     */
    public Boolean isScan(String userImgUrl) {

        // 创建临时文件
        File userImg = FileUtil.createTempFile();

        HttpRequest.get(userImgUrl)
                .execute()
                .writeBody(userImg);

        String baseUrl = EnvUtils.isDev() ? "https://cv-doctr-ukbigacefh.cn-beijing.fcapp.run/is_scan" : "https://cv-doctr-ukbigacefh.cn-beijing-vpc.fcapp.run/is_scan";

        HttpResponse response = HttpRequest.post(baseUrl)
                .form("image", userImg)
                .execute();

        if (response.isOk()) {

            JSONObject object = JSONUtil.parseObj(response.body());

            return object.getBool("data");

        } else {
            log.error("矫正图片失败，错误信息：{}", response.body());

            throw new BaseException("矫正图片失败");
        }
    }
}
