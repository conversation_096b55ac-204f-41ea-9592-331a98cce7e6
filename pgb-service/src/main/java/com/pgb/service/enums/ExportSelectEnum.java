package com.pgb.service.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;

/**
 * <AUTHOR>
 * Created by 2024/9/19 19:12
 */
@Schema(description = "自定义导出内容 类型")
@AllArgsConstructor
public enum ExportSelectEnum {

    @Schema(title = "作文分数")
    Score(0,"作文分数"),

    @Schema(title = "老师总评")
    Comment(1,"老师总评"),

    @Schema(title = "作文润色")
    Polish(2,"作文润色"),

    @Schema(title = "作文旁批图片")
    Image(3,"作文图片"),

    @Schema(title = "写作指导")
    Guide(4,"写作指导");

    /**
     * 枚举值对应的整数值
     */
    @EnumValue
    public final int value;

    /**
     * 文字说明
     */
    public final String desc;
}
