package com.pgb.service.domain.userConfig;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Schema(title = "学生端查看批改报告配置 DTO")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class StudentReportConfigDTO {

    @Schema(title = "是否开启审核模式", description = "审核后家长才能查看批改结果")
    private Boolean checkMode = false;

    @Schema(title = "是否允许家长导出word")
    private Boolean exportWord = false;

    @Schema(title = "分数样式", description = "0:评级,1:分数")
    private Integer scoreStyle = 1;

    @Schema(title = "是否允许家长错篇重提")
    private Boolean reSubmit = false;

    // ---- 家长可查看的批改结果项 -----

    @Schema(title = "作文分数")
    private Boolean score = true;

    @Schema(title = "原图旁批")
    private Boolean answerImg = true;

    @Schema(title = "老师总评")
    private Boolean overallComment = true;

    @Schema(title = "详细点评")
    private Boolean comment = true;

    @Schema(title = "作文润色")
    private Boolean polish = true;

    @Schema(title = "原文对比")
    private Boolean compare = true;

    @Schema(title = "写作指导")
    private Boolean writingGuide = true;

    @Schema(title = "题目相关信息")
    private Boolean questionInfo = true;
}
