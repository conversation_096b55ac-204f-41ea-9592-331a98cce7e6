package com.pgb.service.db;

import com.baomidou.mybatisplus.extension.service.IService;
import com.pgb.service.domain.answer.PgAnswer;
import com.pgb.service.domain.common.TodayNum;
import com.pgb.service.domain.homework.statistic.LevelNumInfo;
import com.pgb.service.domain.question.zwEssay.ZwEssayQuestion;


import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【pg_answer】的数据库操作Service
 * @createDate 2024-06-19 13:40:14
 */
public interface PgAnswerService extends IService<PgAnswer> {

    /**
     * 将某份作业下的所有作文进行重批
     */
    public void reCorrectByHomeworkId(Long homeworkId, boolean isCost);

    /**
     * 重批指定批改结果
     * @param answer
     * @return
     */
    public PgAnswer reCorrect(PgAnswer answer, boolean isCost);

    /**
     * 删除作文提交记录
     * @param answer
     */
    public void deleteAnswer(PgAnswer answer);

    /**
     * 提交班级作文
     *
     * @param essayForm
     * @return
     */
    public PgAnswer submitHomeworkZwAnswer(ZwEssayQuestion essayForm, Long userId);

    /**
     * 将所有的批改结果图片累加，做 md5 操作
     *
     * @param ids
     * @return
     */
    public String getImgMd5(List<Long> ids, Long userId);

    /**
     * 查询超时未批改的题目，并决定是否加入批改队列
     * @param isReCorrect
     * @param overMinute
     * @return
     */
    public Integer queryToCorrect(Boolean isReCorrect, Integer overMinute);

    /**
     * 查询超时未批改的题目，并加入批改队列
     */
    public Integer queryToCorrect();

    /**
     * 获取作业的成绩统计
     *
     * @param homeworkId 作业id
     * @param studentIds 学生id
     * @return
     */
    List<Integer> levelNum(Long homeworkId, List<Long> studentIds, List<Integer> levelNum);

    /**
     * 获取作业成绩占比统计
     * @param homeworkId
     * @param studentIds
     * @return
     */
    List<LevelNumInfo> LevelPercentage(Long homeworkId, List<Long> studentIds);
}
