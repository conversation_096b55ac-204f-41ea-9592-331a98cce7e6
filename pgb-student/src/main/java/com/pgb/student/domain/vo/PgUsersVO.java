package com.pgb.student.domain.vo;

import java.io.Serializable;

import java.util.Date;
import org.hibernate.validator.constraints.Length;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.ToString;
import lombok.Builder;

/**
* 用户表
* @TableName pg_users
*/
@Schema(description = "用户表 VO")
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode
@ToString
@Builder
public class PgUsersVO implements Serializable {

    @Schema(title = "用户id", type = "string")
    private Long id;

    @Schema(title = "昵称")
    private String nickName;

    @Schema(title = "头像")
    private String avatar;

    @Schema(title = "手机号")
    private String phone;

    @Schema(title = "用户来源")
    private Integer userFrom;

    @Schema(title = "微信openId")
    private String wxOpenId;

    @Schema(title = "微信unionId")
    private String wxUnionId;

    @Schema(title = "最近登录时间")
    private Date lastLoginTime;

    @Schema(title = "会员过期时间")
    private Date vipExpireTime;

    @Schema(title = "创建时间")
    private Date createTime;

    @Schema(title = "备注信息")
    private String remark;

}
