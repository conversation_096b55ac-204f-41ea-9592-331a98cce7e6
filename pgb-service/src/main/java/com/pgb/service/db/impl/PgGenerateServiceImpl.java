package com.pgb.service.db.impl;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.pgb.service.db.PgGenerateService;
import com.pgb.service.db.PgUsersService;
import com.pgb.service.domain.generate.PgGenerate;
import com.pgb.service.domain.user.PgUsers;
import com.pgb.service.domain.common.TodayNum;
import com.pgb.service.mapper.PgGenerateMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Date;

/**
* <AUTHOR>
* @description 针对表【pg_generate(【万能写】记录表)】的数据库操作Service实现
* @createDate 2024-08-24 19:34:23
*/
@Service
@RequiredArgsConstructor
public class PgGenerateServiceImpl extends ServiceImpl<PgGenerateMapper, PgGenerate>
    implements PgGenerateService {

    private final PgUsersService pgUsersService;

    @Override
    public TodayNum getTodayUseNum(Long userId) {

        // 判断当前用户是否是会员
        PgUsers users = pgUsersService.getById(userId);

        int total = users.getIsVip() ? 100 : 3;

        long todayNum = count(new LambdaQueryWrapper<PgGenerate>()
                .eq(PgGenerate::getUserId, StpUtil.getLoginIdAsLong())
                .between(PgGenerate::getCreateTime, DateUtil.beginOfDay(new Date()), DateUtil.endOfDay(new Date()))
        );

        return new TodayNum((int) todayNum, total);
    }
}




