package com.pgb.common.doc;

import io.swagger.v3.oas.models.ExternalDocumentation;
import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Contact;
import io.swagger.v3.oas.models.info.Info;
import org.springdoc.core.models.GroupedOpenApi;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class OpenApiConfig {

    /**
     * 配置 openapi 基本信息
     */
    @Bean
    public OpenAPI createRestApi() {
        return new OpenAPI()
                // API基础信息
                .info(new Info()
                        // 页面标题
                        .title("批改邦接口文档")
                        // 描述
                        .description("批改邦")
                        .version("1.0.0")
                        // 创建人信息
                        .contact(new Contact().name("批改邦").url("https://pigaibang.com")))
                .externalDocs(new ExternalDocumentation()
                        .description("SpringDoc Wiki Documentation")
                        .url("https://springdoc.org/v2"));
    }

    /**
     * 【商家】接口分组
     * @return
     */
    @Bean
    public GroupedOpenApi setTenantGroup() {
        return GroupedOpenApi.builder()
                .group("商家后台接口")
                .pathsToMatch("/admin/**")
                .build();
    }

    /**
     * 【商家】接口分组
     * @return
     */
    @Bean
    public GroupedOpenApi setWfGroup() {
        return GroupedOpenApi.builder()
                .group("晚辅接口")
                .pathsToMatch("/wf/**")
                .build();
    }

    /**
     * 【回调】接口分组
     * @return
     */
    @Bean
    public GroupedOpenApi setCallbackGroup() {
        return GroupedOpenApi.builder()
                .group("回调接口")
                .pathsToMatch("/callback/**")
                .build();
    }

    /**
     * 【系统】接口分组
     * @return
     */
    @Bean
    public GroupedOpenApi setSystemGroup() {
        return GroupedOpenApi.builder()
                .group("系统内部接口")
                .pathsToMatch("/system/**")
                .build();
    }

}
