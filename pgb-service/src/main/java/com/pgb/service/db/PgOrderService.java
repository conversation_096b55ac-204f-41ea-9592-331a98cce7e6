package com.pgb.service.db;

import com.baomidou.mybatisplus.extension.service.IService;
import com.pgb.common.pay.enums.BuyTypeEnum;
import com.pgb.common.pay.enums.OrderTypeEnum;
import com.pgb.common.pay.enums.PayTypeEnum;
import com.pgb.service.domain.order.PgOrder;
import com.pgb.service.domain.vip.PgVipCode;
import com.pgb.service.enums.ChannelTypeEnum;
import com.pgb.service.enums.VipTypeEnum;


/**
* <AUTHOR>
* @description 针对表【pg_order(订单系统)】的数据库操作Service
* @createDate 2024-06-25 16:26:25
*/
public interface PgOrderService extends IService<PgOrder> {
    /**
     * 生成订单
     * @param vipType
     * @param payType
     * @param buyType
     * @param clientIp
     * @return
     */
    PgOrder generateOrder(VipTypeEnum vipType, PayTypeEnum payType, BuyTypeEnum buyType, String clientIp, Long userId);

    /**
     * 支付成功
     * @param orderId
     * @param payAmount
     * @param platformData
     */
    void paySuccess(Long orderId, Integer payAmount, Object platformData);

    /**
     * 支付成功
     * @param orderId
     * @param platformData
     */
    void paySuccess(Long orderId, Object platformData);

    /**
     * 开通会员
     * @param phone
     * @param type
     * @param openType
     * @return
     */
    PgOrder openVip(String phone, VipTypeEnum type, ChannelTypeEnum openType, OrderTypeEnum orderType);

    /**
     * 开通会员
     * @param userId
     * @param type
     * @param openType
     * @param orderType
     * @return
     */
    PgOrder openVip(Long userId, VipTypeEnum type, ChannelTypeEnum openType, OrderTypeEnum orderType);

    /**
     * 开通会员
     * @param userId
     * @param vipCode
     * @param order
     * @return
     */
    PgOrder openActivityVip(Long userId, PgVipCode vipCode, PgOrder order);


}
