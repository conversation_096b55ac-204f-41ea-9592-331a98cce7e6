package com.pgb.service.util;

import com.github.difflib.DiffUtils;
import com.github.difflib.patch.Patch;
import com.pgb.service.domain.question.zwEssay.ZwTextDiff;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

public class ZwUtil {
    // 获取原文和润色的差异
    public static List<ZwTextDiff> getPolishDiff(String fullText, String polish) {

        List<ZwTextDiff> diffList = new ArrayList<>();

        List<String> fullTextCharList = Arrays.asList(fullText.split(""));

        List<String> polishCharList = Arrays.asList(polish.split(""));

        // 获取差异
        Patch<String> diffPatch = DiffUtils.diff(fullTextCharList, polishCharList, true);

        // 生成差异内容
        diffPatch.getDeltas().forEach(delta -> {
            ZwTextDiff diff = new ZwTextDiff();
            diff.setType(ZwTextDiff.ZwTextDiffTypeEnum.valueOf(delta.getType().name()));
            diff.setSource(String.join("", delta.getSource().getLines()));
            diff.setTarget(String.join("", delta.getTarget().getLines()));
            diffList.add(diff);
        });

        return diffList;
    }
}
