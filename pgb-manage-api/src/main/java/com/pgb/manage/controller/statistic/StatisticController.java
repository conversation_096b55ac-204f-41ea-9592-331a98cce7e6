package com.pgb.manage.controller.statistic;

import cn.dev33.satoken.annotation.SaCheckRole;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.pgb.common.core.global.BaseResult;
import com.pgb.common.core.global.GlobalCode;
import com.pgb.common.core.global.RoleConstants;
import com.pgb.common.redis.RedisUtils;
import com.pgb.manage.domain.dto.StatisticFormDTO;
import com.pgb.manage.domain.vo.UserStatisticVO;
import com.pgb.service.db.PgAnswerCostService;
import com.pgb.service.db.PgAnswerService;
import com.pgb.service.db.PgUsersService;
import com.pgb.service.domain.answer.PgAnswer;
import com.pgb.service.domain.answer.answerCost.PgAnswerCost;
import com.pgb.service.domain.user.PgUsers;
import com.pgb.service.enums.CorrectStatusEnum;
import com.pgb.service.enums.QueryPeriodEnum;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.time.Duration;
import java.util.*;

@Tag(name = "管理端/统计")
@RestController("ManageStatisticController")
@RequestMapping("/manage/statistic")
@RequiredArgsConstructor
@Slf4j
public class StatisticController {

    private final PgUsersService pgUsersService;

    private final PgAnswerService pgAnswerService;

    private final PgAnswerCostService pgAnswerCostService;

    @Operation(summary = "作文提交统计数据")
    @GetMapping("info")
    @SaCheckRole(RoleConstants.Manager)
    public BaseResult<UserStatisticVO> statistic() {

        UserStatisticVO statistic = new UserStatisticVO();

        // 作文待批改数量
        long totalCorrect = pgAnswerService.count(new LambdaQueryWrapper<PgAnswer>()
                .eq(PgAnswer::getStatus, CorrectStatusEnum.Uploaded));

        statistic.setTotalCorrect(totalCorrect);

        // 今天的新用户 id List
        List<Long> newUserIds = pgUsersService.list(new LambdaQueryWrapper<PgUsers>()
                        .between(PgUsers::getCreateTime, DateUtil.beginOfDay(new Date()), DateUtil.endOfDay(new Date())))
                .stream()
                .map(PgUsers::getId)
                .toList();

        // 昨天的新用户
        List<Long> yesterdayNewUserIds = pgUsersService.list(new LambdaQueryWrapper<PgUsers>()
                        .between(PgUsers::getCreateTime, DateUtil.beginOfDay(DateUtil.yesterday()), DateUtil.endOfDay(DateUtil.yesterday())))
                .stream()
                .map(PgUsers::getId)
                .toList();

        // 查看今天有多少新用户
        statistic.setNewUser(ObjectUtil.defaultIfNull(newUserIds.size(), 0));

        // 查看昨天有多少新用户
        statistic.setYesterdayNewUser(ObjectUtil.defaultIfNull(yesterdayNewUserIds.size(), 0));

        // 今天提交作文的用户 包含重复的
        List<Long> submitUserIds = pgAnswerCostService.list(new LambdaQueryWrapper<PgAnswerCost>()
                        .between(PgAnswerCost::getCreateTime, DateUtil.beginOfDay(new Date()), DateUtil.endOfDay(new Date())))
                .stream()
                .map(PgAnswerCost::getUserId)
                .toList();

        // 昨天提交作文的用户 包含重复的
        List<Long> yesterdaySubmitUserIds = pgAnswerCostService.list(new LambdaQueryWrapper<PgAnswerCost>()
                        .between(PgAnswerCost::getCreateTime, DateUtil.beginOfDay(DateUtil.yesterday()), DateUtil.endOfDay(DateUtil.yesterday())))
                .stream()
                .map(PgAnswerCost::getUserId)
                .toList();

        // 有多少新用户提交作文 交集
        long newUserSubmit = newUserIds.stream().filter(submitUserIds::contains).count();
        statistic.setNewUserSubmit(newUserSubmit);

        // 有昨天多少新用户提交作文 交集
        long yesterdayNewUserSubmit = yesterdayNewUserIds.stream().filter(yesterdaySubmitUserIds::contains).count();
        statistic.setYesterdayNewUserSubmit(yesterdayNewUserSubmit);

        // 统计今天有多少用户提交作文，去重
        int userSubmit = CollUtil.distinct(submitUserIds).size();
        statistic.setUserSubmit(userSubmit);

        // 统计昨天有多少用户提交作文，去重
        int yesterdayUserSubmit = CollUtil.distinct(yesterdaySubmitUserIds).size();
        statistic.setYesterdayUserSubmit(yesterdayUserSubmit);

        // 今天总共提交多少份作文
        statistic.setTotalSubmit(
                pgAnswerCostService.count(new LambdaQueryWrapper<PgAnswerCost>()
                        .between(PgAnswerCost::getCreateTime, DateUtil.beginOfDay(new Date()), DateUtil.endOfDay(new Date()))
                ));

        // 昨天总共提交多少份作文
        statistic.setYesterdayTotalSubmit(
                pgAnswerCostService.count(new LambdaQueryWrapper<PgAnswerCost>()
                        .between(PgAnswerCost::getCreateTime, DateUtil.beginOfDay(DateUtil.yesterday()), DateUtil.endOfDay(DateUtil.yesterday()))
                ));

        // 作文总提交数量
        long count = pgAnswerService.count();
        statistic.setTotalSubmitCount(count);

        return BaseResult.success(statistic);
    }

    @Operation(summary = "小程序图表统计数据")
    @PostMapping("chart")
    @SaCheckRole(RoleConstants.Manager)
    public BaseResult<List<Map<String, Long>>> chart(@RequestBody StatisticFormDTO form) {

        // 生成缓存键，包含查询参数以确保唯一性
        String cacheKey = "pgb:manage:statistic:chart:" + form.getPeriod() + ":" + form.getContentType();
        
        // 尝试从缓存中获取数据
        List<Map<String, Long>> data = RedisUtils.getCacheObject(cacheKey);
        
        // 如果缓存命中，直接返回缓存数据
        if (ObjectUtil.isNotNull(data)) {
            log.info("缓存命中: {}", cacheKey);
            return BaseResult.success(data);
        }
        
        // 缓存未命中，执行原有业务逻辑
        data = new ArrayList<>();

        // 每天/每周/每月
        for (int i = 30; i >= 0; i--) {

            // 初始化
            Map<String, Long> statistic = new HashMap<>();
            Date startDate = null;
            Date endDate = null;

            // 天
            if (form.getPeriod().equals(QueryPeriodEnum.Day)) {
                startDate = DateUtil.beginOfDay(DateUtil.offsetDay(new Date(), -1 * i));
                endDate = DateUtil.endOfDay(DateUtil.offsetDay(new Date(), -1 * i));
            }
            // 周
            else if (form.getPeriod().equals(QueryPeriodEnum.Week)) {
                startDate = DateUtil.beginOfWeek(DateUtil.offsetWeek(new Date(), -1 * i));

                if (i == 0) {
                    endDate = new DateTime();
                } else {
                    endDate = DateUtil.endOfWeek(DateUtil.offsetWeek(new Date(), -1 * i));
                }
            }
            // 月
            else if (form.getPeriod().equals(QueryPeriodEnum.Month)) {
                startDate = DateUtil.beginOfMonth(DateUtil.offsetMonth(new Date(), -1 * i));

                if (i == 0) {
                    endDate = new DateTime();
                } else {
                    endDate = DateUtil.endOfMonth(DateUtil.offsetMonth(new Date(), -1 * i));
                }
            }
            long count = 0L;

            // 新注册用户
            List<Long> newUserIds = pgUsersService.list(new LambdaQueryWrapper<PgUsers>()
                            .between(PgUsers::getCreateTime, startDate, endDate))
                    .stream()
                    .map(PgUsers::getId)
                    .toList();

            // 提交作文的用户
            List<Long> submitUserIds = pgAnswerCostService.list(new LambdaQueryWrapper<PgAnswerCost>()
                            .between(PgAnswerCost::getCreateTime, startDate, endDate))
                    .stream()
                    .map(PgAnswerCost::getUserId)
                    .toList();

            // 统计内容
            count = switch (form.getContentType()) {
                // 注册用户
                case Register_Num -> newUserIds.size();

                // 注册用户体验数量
                case Register_User_Submit -> newUserIds.stream().filter(submitUserIds::contains).count();

                // 使用用户总数 去重
                case Use_User_Num -> CollUtil.distinct(submitUserIds).size();

                // 提交作文数量
                case Submit_Num -> pgAnswerCostService.count(new LambdaQueryWrapper<PgAnswerCost>()
                        .between(PgAnswerCost::getCreateTime, startDate, endDate));
            };
            // 将Date 转为 年月日
            statistic.put(DateUtil.format(endDate, "yyyy-MM-dd"), count);

            data.add(statistic);
        }
        
        // 将查询结果存入缓存，设置过期时间为1小时
        RedisUtils.setCacheObject(cacheKey, data, Duration.ofHours(1));
        log.info("缓存未命中，新数据已缓存: {}, 过期时间: 30分钟", cacheKey);
        
        return BaseResult.success(data);
    }

}
