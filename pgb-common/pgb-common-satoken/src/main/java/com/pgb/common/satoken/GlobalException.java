package com.pgb.common.satoken;

import cn.dev33.satoken.exception.*;
import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.pgb.common.core.global.BaseResult;
import com.pgb.common.core.global.GlobalCode;
import com.pgb.common.core.global.exception.BaseException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.method.annotation.MethodArgumentTypeMismatchException;

/**
 * 全局异常处理
 */
@Slf4j
@RestControllerAdvice
public class GlobalException {

    // 拦截：未登录异常
    @ExceptionHandler(NotLoginException.class)
    public BaseResult<String> handlerException(NotLoginException e) {

        return BaseResult.custom(GlobalCode.Login_Not.value, GlobalCode.Login_Not.desc, e.getMessage());
    }

    // 拦截：缺少权限异常
    @ExceptionHandler(NotPermissionException.class)
    public BaseResult<String> handlerException(NotPermissionException e) {
        return BaseResult.custom(GlobalCode.Permission_Not.value, GlobalCode.Permission_Not.desc, e.getMessage());
    }

    // 拦截：缺少角色异常
    @ExceptionHandler(NotRoleException.class)
    public BaseResult<String> handlerException(NotRoleException e) {

        String roles = StpUtil.isLogin() ? CollUtil.join(StpUtil.getRoleList(), "，") : "";

        return BaseResult.custom(GlobalCode.Role_Not.value, GlobalCode.Role_Not.desc + e.getRole() + "当前：" + roles, roles);
    }

    // 拦截：二级认证校验失败异常
    @ExceptionHandler(NotSafeException.class)
    public BaseResult<String> handlerException(NotSafeException e) {
        return BaseResult.custom(GlobalCode.Safe_Not.value, GlobalCode.Safe_Not.desc, e.getMessage());
    }

    // 拦截：服务封禁异常
    @ExceptionHandler(DisableServiceException.class)
    public BaseResult<String> handlerException(DisableServiceException e) {
        return BaseResult.custom(GlobalCode.Disable_Service.value,GlobalCode.Disable_Service.desc,"当前账号 " + e.getService() + " 服务已被封禁 (level=" + e.getLevel() + ")：" + e.getDisableTime() + "秒后解封");
    }

    // 拦截：Http Basic 校验失败异常
    @ExceptionHandler(NotBasicAuthException.class)
    public BaseResult<String> handlerException(NotBasicAuthException e) {
        return BaseResult.custom(GlobalCode.Safe_Not.value, "Http Basic 校验失败异常", e.getMessage());
    }

    @ExceptionHandler(MethodArgumentTypeMismatchException.class)
    public BaseResult<String> handleException(MethodArgumentTypeMismatchException e) {
        log.info("异常：{}：{}：{}", e.getMessage(), e.getName(), e.getParameter());

        return BaseResult.custom(
                GlobalCode.Param_Wrong.value,
                e.getMessage(),
                StrUtil.format("异常：{}：{}", e.getName(), e.getParameter())
        );
    }

    // 拦截，请求参数错误
    @ExceptionHandler(MethodArgumentNotValidException.class)
    public BaseResult<String> handleException(MethodArgumentNotValidException e) {
        log.info("handleException 异常：{}", e.getMessage());

        // 如果字段信息，为 null
        if (e.getBindingResult().getFieldError() ==null) {
            return BaseResult.custom(
                    GlobalCode.Param_Wrong.value,
                    "参数异常",
                    StrUtil.format("异常字段：【{}】", e.getBindingResult().getFieldError().getField())
            );
        }

        // 获取默认提示信息
        String message = e.getBindingResult().getFieldError().getDefaultMessage();

        // 如果默认提示信息为null，自动生成对应原因
        if (StrUtil.isBlank(message)) {
            String code = e.getBindingResult().getFieldError().getCode();
            // 如果状态码为 null
            if (StrUtil.isBlank(code)) {
                return BaseResult.custom(
                        GlobalCode.Param_Wrong.value,
                        GlobalCode.Param_Wrong.desc,
                        StrUtil.format("异常字段：【{}】", e.getBindingResult().getFieldError().getField())
                );
            }

            message = switch (code) {
                case "NotBlank" -> "值为空";
                case "NotNull" -> "值为 null";
                default -> message;
            };
        }

        return BaseResult.custom(
                GlobalCode.Param_Wrong.value,
                message,
                StrUtil.format("异常字段：【{}】，异常原因：{}",
                        e.getBindingResult().getFieldError().getField(),
                        message
                )
        );
    }

    // 通用 自定义异常
    @ExceptionHandler(BaseException.class)
    public BaseResult<String> handlerException(BaseException e) {
        e.printStackTrace();
        return BaseResult.custom(e.getCode().value, e.getMsg(), e.getMsg());
    }

    // 拦截：其它所有异常
    @ExceptionHandler(Exception.class)
    public BaseResult<String> handlerException(Exception e) {
        e.printStackTrace();
        log.error(e.getMessage());
        return BaseResult.custom(GlobalCode.Error.value, GlobalCode.Error.desc, e.getMessage());
    }
}

