package com.pgb.service.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;

/**
 * <AUTHOR>
 * Created by 2024/10/28 12:31
 */
@Schema(title = "用户身份类型")
@AllArgsConstructor
public enum IdentityTypeEnum {

    Self(0, "本人"),

    Father(1, "爸爸"),

    <PERSON>(2, "妈妈"),

    <PERSON><PERSON><PERSON>_<PERSON>(3, "爷爷"),

    <PERSON><PERSON><PERSON>_<PERSON>(4, "奶奶"),

    <PERSON><PERSON><PERSON>_<PERSON>(5, "外公"),

    <PERSON><PERSON><PERSON>_<PERSON>(6, "外婆"),

    <PERSON>(7, "其他");


    /**
     * 枚举值对应的整数值
     */
    @EnumValue
    public final int value;

    /**
     * 文字说明
     */
    public final String desc;
}
