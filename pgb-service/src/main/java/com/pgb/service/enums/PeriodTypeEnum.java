package com.pgb.service.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;

/**
 * <AUTHOR>
 * Created by 2024/8/27 16:53
 */
@Schema(description = "时间范围 类型")
@AllArgsConstructor
public enum PeriodTypeEnum {

    @Schema(title = "本周")
    Week(0,"本周"),

    @Schema(title = "上周")
    Last_Week(1,"上周"),

    @Schema(title = "本月")
    Month(2,"本月"),

    @Schema(title = "上月")
    Last_Month(3,"上月");

    /**
     * 枚举值对应的整数值
     */
    @EnumValue
    public final int value;

    /**
     * 文字说明
     */
    public final String desc;
}
