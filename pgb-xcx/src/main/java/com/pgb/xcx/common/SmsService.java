package com.pgb.xcx.common;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.pgb.common.core.global.BaseResult;
import com.pgb.common.core.global.GlobalCode;
import com.pgb.common.core.utils.EnvUtils;
import com.pgb.common.redis.RedisUtils;
import com.pgb.common.sms.SmsResDTO;
import com.pgb.common.sms.SmsTemplateConfig;
import com.pgb.common.sms.service.CommonSmsUtil;
import com.pgb.xcx.config.property.SmsConfigProperty;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.sms4j.api.SmsBlend;
import org.dromara.sms4j.api.entity.SmsResponse;
import org.dromara.sms4j.core.factory.SmsFactory;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.LinkedHashMap;

/**
 * <AUTHOR>
 * Created by 2024/8/13 17:56
 */

@Component
@Slf4j
@RequiredArgsConstructor
@EnableConfigurationProperties(SmsConfigProperty.class)
public class SmsService {

    private final SmsConfigProperty smsConfigProperty;

    public SmsConfigProperty getSmsConfigProperty() {
        return smsConfigProperty;
    }

    /**
     * 发送短信
     * @param config
     * @param phone
     * @param params
     * @return
     */
    public SmsResDTO sendSms(SmsTemplateConfig config, String phone, String... params) {
        // 将params转化为map
        LinkedHashMap<String, String> varMap = new LinkedHashMap<>();

        LinkedHashMap<String, String> map = new LinkedHashMap<>(1);
        for (int i = 0; i < params.length; i++) {
            varMap.put(config.getName().get(i), params[i]);
        }

        return sendSms(config, phone, varMap);
    }

    public SmsResDTO sendSms(SmsTemplateConfig config, String phone, LinkedHashMap<String, String> varMap) {
        return CommonSmsUtil.sendSms(config, phone, varMap);
    }


    /**
     * 短信验证码校验
     * @param phone
     * @param smsCode
     * @return
     */
    public BaseResult<Boolean> smsIsSuccess(String phone, String smsCode, String keyPrefix) {
        JSONObject json;
        //  获取Redis中的手机和验证码
        if (!EnvUtils.isProd()) {
            json = JSONUtil.createObj()
                    .putOnce("verifyCode", 1234)
                    .putOnce("phone", phone)
                    .putOnce("createTime", new Date());
        } else {
            json = JSONUtil.parseObj(RedisUtils.getCacheObject(keyPrefix + phone));
        }

        // 测试账号密码
        if ("15600892399".equals(phone) && "1234".equals(smsCode)) {
            return BaseResult.code(GlobalCode.Success);
        }

        // 验证码是否为空
        if (ObjectUtil.isNull(json) || StrUtil.hasEmpty(json.getStr("verifyCode"))) {
            return BaseResult.code(GlobalCode.Sms_Code_Error);
        }
        // 验证码是否过期
        else if ((System.currentTimeMillis() - json.getLong("createTime")) > 1000 * 60 * 10) {
            return BaseResult.code(GlobalCode.Sms_Code_Expire);
        }
        // 验证码是否正确
        else if (!json.getStr("verifyCode").equals(smsCode)) {
            return BaseResult.code(GlobalCode.Sms_Code_Error);
        }
        // 验证码所对应的手机号是否正确
        else if (!json.getStr("phone").equals(phone)) {
            return BaseResult.code(GlobalCode.Sms_Phone_Error);
        }

        return BaseResult.code(GlobalCode.Success);
    }
}
