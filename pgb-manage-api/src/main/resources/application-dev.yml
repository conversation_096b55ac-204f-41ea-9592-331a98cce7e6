# 数据库
# http://192.168.231.128:21356/a27f5d41
spring:
  datasource:
    dynamic:
      enabled: true #启用动态数据源，默认true
      primary: master #设置默认的数据源或者数据源组,默认值即为master
      strict: false #严格匹配数据源,默认false. true未匹配到指定数据源时抛异常,false使用默认数据源
      grace-destroy: false #是否优雅关闭数据源，默认为false，设置为true时，关闭数据源时如果数据源中还存在活跃连接，至多等待10s后强制关闭
      datasource:
        master:
          driver-class-name: org.postgresql.Driver
          url: jdbc:postgresql://*************:5432/pgb_user_dev
          #     url: jdbc:postgresql://*************:5432/pgb_user_prod
          username: postgres
          password: Snros2022..
        manage:
          driver-class-name: org.postgresql.Driver
          url: jdbc:postgresql://*************:5432/pgb_saas_test
          username: postgres
          password: Snros2022..
  data:
    redis:
      host: 127.0.0.1
      port: 6379
      database: 1

#  data:
#    redis:
#      #      host: *************
#      host: **************
#      port: 6379
#      password: Snros2022..
#      database: 2

# 接口文档
springdoc:
  swagger-ui:
    enabled: true
  api-docs:
    enabled: true

# 支付回调
pay:
  #  notifyUrl: https://e464008q58.oicp.vip/api
  notifyUrl: https://test-api.pigaibang.com/api

# 批改模型接口
correct:
  #  base-url: http://127.0.0.1:8081
  base-url: http://model-java.pigaibang.com
  api-key: pgb-api-correct-v2
  # 批改 url
  #  render-url: https://fabric-render-113555-5-1309220397.sh.run.tcloudbase.com
  render-url: http://*************:9000
  # 用户端保存，使用高并发
  render-url-user: http://fabric.fcv3.1466332595565727.cn-beijing.fc.devsapp.net


# redisson
redisson:
  # redis key前缀
  keyPrefix:
  # 线程池数量
  threads: 128
  # Netty线程池数量
  nettyThreads: 128
  # 单节点配置
  singleServerConfig:
    # 客户端名称
    clientName: pgb-server
    # 最小空闲连接数
    connectionMinimumIdleSize: 32
    # 连接池大小
    connectionPoolSize: 128
    # 连接空闲超时，单位：毫秒
    idleConnectionTimeout: 10000
    # 命令等待超时，单位：毫秒
    timeout: 3000
    # 发布和订阅连接池大小
    subscriptionConnectionPoolSize: 128

# textbook服务配置
textbook:
  base-url: http://127.0.0.1:8080/api
