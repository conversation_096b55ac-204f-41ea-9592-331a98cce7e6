package com.pgb.service.domain.distribution.record;

import java.io.Serializable;

import java.util.Date;

import com.pgb.service.enums.TransferStatusEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.ToString;
import lombok.Builder;
import org.springframework.validation.annotation.Validated;

/**
 * @TableName pg_distribution_record
 */
@Schema(description = " DTO")
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode
@ToString
@Validated
@Builder
public class PgDistributionRecordDTO implements Serializable {

    @Schema(title = "分销记录id")
    private Long id;

    @Schema(title = "邀请人id")
    private Long shareUserId;

    @Schema(title = "被邀请人id")
    private Long userId;

    @Schema(title = "支付金额")
    private Integer payAmount;

    @Schema(title = "佣金")
    private Integer payCommission;

    @Schema(title = "订单 id")
    private Long orderId;

    @Schema(title = "创建时间")
    private Date createTime;

    @Schema(title = "商家批次单号")
    private String outBatchNo;

    @Schema(title = "商家明细单号")
    private String outDetailNo;

    @Schema(title = "是否转账")
    private Boolean isTransfer;

    @Schema(title = "转账时间")
    private Date transferTime;

    @Schema(title = "转账状态")
    private TransferStatusEnum transferStatus;

    @Schema(title = "批次关闭原因")
    private String closeReason;
}
