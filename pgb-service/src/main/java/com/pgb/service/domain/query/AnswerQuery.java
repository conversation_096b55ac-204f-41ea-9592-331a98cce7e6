package com.pgb.service.domain.query;

import com.pgb.common.mybatis.global.PageQuery;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * Created by 2024/10/8 10:58
 */
@EqualsAndHashCode(callSuper = true)
@Schema(description = "题目 搜索实体")
@Data
public class AnswerQuery extends PageQuery {

    @Schema(title = "备注名称")
    private String name;
}
