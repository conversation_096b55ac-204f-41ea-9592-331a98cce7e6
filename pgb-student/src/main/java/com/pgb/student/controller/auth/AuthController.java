package com.pgb.student.controller.auth;

import cn.binarywang.wx.miniapp.api.WxMaService;
import cn.binarywang.wx.miniapp.bean.WxMaJscode2SessionResult;
import cn.binarywang.wx.miniapp.bean.WxMaPhoneNumberInfo;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.pgb.service.enums.DeviceTypeEnum;
import com.pgb.student.common.SmsService;
import com.pgb.common.core.global.BaseResult;
import com.pgb.common.core.global.GlobalCode;
import com.pgb.common.core.global.GlobalConstants;
import com.pgb.common.redis.RedisUtils;
import com.pgb.common.satoken.LoginVO;
import com.pgb.common.sms.SmsResDTO;
import com.pgb.student.domain.PgUsers;
import com.pgb.student.domain.login.LoginByPhoneFormDTO;
import com.pgb.student.domain.login.SmsFormDTO;
import com.pgb.student.domain.vo.PgUsersVO;
import com.pgb.student.service.PgUsersService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.error.WxErrorException;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.time.Duration;
import java.util.LinkedHashMap;

@Tag(name = "学生端/权限/登录")
@RestController("StudentAuthLoginController")
@RequestMapping("/student/auth/login")
@RequiredArgsConstructor
@Slf4j
public class AuthController {

    private final WxMaService wxMaService;

    private final PgUsersService pgUsersService;

    public record LoginByPhoneForm(String code, String openId, String unionId, Long shareUserId) {
    }

    private final SmsService smsService;

    @Operation(summary = "授权手机号", description = "根据phoneCode获取")
    @PostMapping("wx/loginByCode")
    public BaseResult<LoginVO<PgUsersVO>> loginByCode(@RequestBody @NotNull LoginByPhoneForm form) {

        if (ObjectUtil.isNull(form.code)) {
            return BaseResult.code(GlobalCode.Param_Wrong);
        }

        try {
            // 获取小程序用户手机号
            WxMaPhoneNumberInfo phoneNoInfo = wxMaService.getUserService().getPhoneNoInfo(form.code);

            // 直接登录注册
            PgUsers pgUsers = pgUsersService.getOrCreateUserByPhone(phoneNoInfo.getPurePhoneNumber());

            // 默认小程序用户
            pgUsers.setUserFrom(0);

            // 保存用户 openid、unionId
            if (ObjectUtil.isNotNull(form.openId)) pgUsers.setWxOpenId(form.openId);
            if (ObjectUtil.isNotNull(form.unionId)) pgUsers.setWxUnionId(form.unionId);

            // 获取登录态
            LoginVO<PgUsersVO> loginVO = pgUsersService.loginSuccess(pgUsers, DeviceTypeEnum.XCX);

            return BaseResult.success(loginVO);

        } catch (WxErrorException e) {
            throw new RuntimeException(e);
        }
    }

    @Operation(summary = "获取授权信息(openId)", description = "根据登录的code获取")
    @GetMapping("wx/info")
    public BaseResult<WxMaJscode2SessionResult> getWxInfo(String code) {
        // 获取openId等信息
        try {
            WxMaJscode2SessionResult sessionInfo = wxMaService.getUserService().getSessionInfo(code);

            return BaseResult.success(sessionInfo);
        } catch (WxErrorException e) {
            throw new RuntimeException(e);
        }
    }

    @Operation(summary = "获取授权信息(openId)", description = "根据登录的code获取")
    @GetMapping("wx/loginByWx")
    public BaseResult<LoginVO<PgUsersVO>> loginByWx(String code) {
        // 获取openId等信息
        try {
            WxMaJscode2SessionResult sessionInfo = wxMaService.getUserService().getSessionInfo(code);

            // 4. 验证成功
            PgUsers pgUsers = pgUsersService.getOrCreateUserByOpenId(sessionInfo.getOpenid(), sessionInfo.getUnionid());

            // 保存用户 unionId
            if (ObjectUtil.isNotNull(sessionInfo.getUnionid())) pgUsers.setWxUnionId(sessionInfo.getUnionid());

            // 获取登录态
            LoginVO<PgUsersVO> loginVO = pgUsersService.loginSuccess(pgUsers, DeviceTypeEnum.XCX);

            return BaseResult.success(loginVO);
        } catch (WxErrorException e) {
            throw new RuntimeException(e);
        }
    }


    @Operation(summary = "发送短信验证码")
    @PostMapping("sms")
    public BaseResult<String> sms(HttpServletRequest request, @Validated @RequestBody SmsFormDTO form) {

        String phone = form.getPhone();

        // 校验接口安全性
        // 判断是否是手机号加密
//        boolean isVerify = DigestUtils.md5DigestAsHex(("pgb-server-main-sms-key" + phone).getBytes()).equals(form.getVerifyKey());

//        // 滑动验证码校验
//        if (!isVerify) {
//            // 判断验证码是否已被验证
//            CaptchaSliderVO captchaSliderVO = RedisUtils.getCacheObject(GlobalConstants.CAPTCHA_CODE_KEY + form.getVerifyKey());
//
//            // 校验验证码
//            if (ObjectUtil.isNull(captchaSliderVO) || !captchaSliderVO.getIsVerify()) {
//                log.warn("【sendAuthSmsCode】{}/IP 发送短信拦截", JakartaServletUtil.getClientIP(request));
//                return BaseResult.code(GlobalCode.Login_Captcha_Error);
//            }
//        }

        // 调用腾讯云，发短信
        // -- 1. 生成验证码
        String verifyCode = RandomUtil.randomNumbers(4);

        // -- 2 发送验证码短信
        LinkedHashMap<String, String> map = new LinkedHashMap<>(1);
        map.put("code", verifyCode);
        SmsResDTO smsRes = smsService.sendSms(SmsService.SmsTypeEnum.VerifyCode, phone, map);

        // 3. 判断短信验证结果
        if (!smsRes.getSuccess()) {
            return BaseResult.error(smsRes.getErrCode().value, smsRes.getMsg());
        }

        log.info("手机号登录：{}，验证码：{}", phone, verifyCode);

        // 4. 保存验证码信息
        JSONObject redisJson = JSONUtil.createObj()
                .putOnce("phone", phone)
                .putOnce("verifyCode", verifyCode)
                .putOnce("createTime", System.currentTimeMillis());

        //  5. Redis 执行保存，有效期 10分钟
        RedisUtils.setCacheObject(GlobalConstants.ADMIN_CAPTCHA_SMS_CODE_KEY + phone, redisJson.toString(), Duration.ofMinutes(10));

        return BaseResult.success("短信发送成功");
    }

    @Operation(summary = "使用手机登录账户")
    @PostMapping("phone")
    public BaseResult<LoginVO<PgUsersVO>> phone(@Validated @RequestBody LoginByPhoneFormDTO form) {

        // 校验短信验证码
        BaseResult<Boolean> result = smsService.smsIsSuccess(form.getPhone(), form.getSmsCode(), GlobalConstants.USER_CAPTCHA_SMS_CODE_KEY);

        // 验证失败
        if (result.getCode() != GlobalCode.Success.value) {
            return BaseResult.custom(result.getCode(), result.getMsg(), null);
        }

        // 4. 验证成功
        PgUsers pgUsers = pgUsersService.getOrCreateUserByPhone(form.getPhone());

        // 保存用户 openid、unionId
        if (ObjectUtil.isNotNull(form.getOpenId())) pgUsers.setWxOpenId(form.getOpenId());
        if (ObjectUtil.isNotNull(form.getUnionId())) pgUsers.setWxUnionId(form.getUnionId());

        // 获取登录态
        LoginVO<PgUsersVO> loginVO = pgUsersService.loginSuccess(pgUsers, form.getDeviceType());

        return BaseResult.success(loginVO);
    }
}
