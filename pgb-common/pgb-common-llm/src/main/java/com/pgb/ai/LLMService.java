package com.pgb.ai;


import com.pgb.ai.domain.ChatRecord;
import com.pgb.ai.domain.ChatRes;
import com.pgb.ai.domain.GPTAnswer;
import com.pgb.ai.domain.agent.FileItem;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.awt.image.BufferedImage;
import java.util.List;
import java.util.function.Consumer;

public interface LLMService {

    public void chatStream(List<ChatRecord> recordList, SseEmitter emitter, StringBuilder resultBuilder, StringBuilder reasonBuilder, boolean isReason);

    public void chatSocket(String answer, StringBuilder resultBuilder, StringBuilder reasonBuilder, Consumer<ChatRes> consumer, boolean isReason);

    public void chatSocket(List<ChatRecord> recordList, StringBuilder resultBuilder, StringBuilder reasonBuilder, Consumer<ChatRes> consumer, boolean isReason);

    public GPTAnswer vl(String imgUrl, String system, String user);

    public GPTAnswer vl(BufferedImage image, String system, String user);

    public GPTAnswer chatComplete(String system, List<String> user, boolean isJson, Float temperature);

    public GPTAnswer chatComplete(String system, List<String> user, boolean isJson, Float temperature, String model);

    /**
     * 阅读助手插件
     *
     * @param fileList
     * @param recordList
     * @param resultBuilder
     * @param reasonBuilder
     * @param consumer
     * @param isReason
     */
    public void chatSocket(List<List<FileItem>> fileList, List<ChatRecord> recordList, StringBuilder resultBuilder, StringBuilder reasonBuilder, Consumer<ChatRes> consumer, boolean isReason);

    /**
     * 阅读助手插件
     * @param recordList
     * @param emitter
     * @param resultBuilder
     * @param reasonBuilder
     * @param isReason
     */
    public void chatStream(List<List<FileItem>> fileList, List<ChatRecord> recordList, SseEmitter emitter, StringBuilder resultBuilder, StringBuilder reasonBuilder, boolean isReason);

}
