package com.pgb.ai.models;

import cn.hutool.core.codec.Base64;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.TimeInterval;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.pgb.ai.domain.doubao.DoubaoTTSProperty;
import com.pgb.ai.domain.doubao.DoubaoTTSResponse;
import com.pgb.ai.domain.doubao.DoubaoTTSResult;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.io.*;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;

/**
 * 豆包TTS（文本转语音）工具类
 *
 * <p>该工具类提供了豆包TTS服务的完整功能，包括：</p>
 * <ul>
 *   <li>文本转语音合成</li>
 *   <li>流式响应处理</li>
 *   <li>音频数据解码和拼接</li>
 *   <li>完整的异常处理机制</li>
 * </ul>
 *
 * <p>使用示例：</p>
 * <pre>{@code
 * DoubaoTTSConfig config = DoubaoTTSConfig.builder()
 *     .appId("your_app_id")
 *     .accessKey("your_access_key")
 *     .resourceId("your_resource_id")
 *     .speaker("zh_male_jieshuonansheng_mars_bigtts")
 *     .build();
 *
 * DoubaoTTSService ttsService = new DoubaoTTSService();
 * DoubaoTTSResult result = ttsService.textToSpeech("这是一段测试文本", config);
 *
 * if (result.getSuccess()) {
 *     byte[] audioData = result.getAudioData();
 *     // 处理音频数据...
 * }
 * }</pre>
 *
 * <AUTHOR>
 */
@Slf4j
@RequiredArgsConstructor
public class DoubaoTTSService {

    /**
     * 文本转语音
     *
     * @param text 待合成的文本内容
     * @param config TTS配置参数
     * @return TTS合成结果
     */
    public DoubaoTTSResult textToSpeech(String text, DoubaoTTSProperty config) {
        if (StrUtil.isBlank(text)) {
            return DoubaoTTSResult.failure("文本内容不能为空");
        }

        if (config == null) {
            return DoubaoTTSResult.failure("TTS配置参数不能为空");
        }

        if (StrUtil.hasBlank(config.getAppId(), config.getAccessKey(), config.getResourceId())) {
            return DoubaoTTSResult.failure("TTS配置参数不完整，请检查appId、accessKey、resourceId");
        }

        log.info("开始执行文本转语音，文本长度: {}, 音色: {}", text.length(), config.getSpeaker());

        // 开始计时
        TimeInterval timer = DateUtil.timer();

        try {
            // 构建请求参数
            JSONObject request = buildRequest(text, config);

            // 发送HTTP请求并处理流式响应
            DoubaoTTSResult result = sendRequestAndProcessResponse(request, config);

            // 设置处理时长
            result.setProcessingTime(timer.interval());

            if (result.getSuccess()) {
                log.info("文本转语音完成，音频大小: {} KB, 耗时: {} ms",
                        result.getAudioSizeInKB(), result.getProcessingTime());
            } else {
                log.error("文本转语音失败: {}", result.getErrorMessage());
            }

            return result;

        } catch (Exception e) {
            log.error("文本转语音异常，文本: {}", text, e);
            return DoubaoTTSResult.failure("文本转语音异常: " + e.getMessage());
        }
    }

    /**
     * 构建请求参数
     *
     * @param text 文本内容
     * @param config 配置参数
     * @return 请求参数JSON对象
     */
    private JSONObject buildRequest(String text, DoubaoTTSProperty config) {
//        log.info("=== 使用新版本的buildRequest方法，返回JSONObject ===");
        // 构建附加参数 - 使用下划线命名法与API保持一致
        JSONObject additions = JSONUtil.createObj()
                .set("disable_markdown_filter", config.getDisableMarkdownFilter())
                .set("enable_language_detector", config.getEnableLanguageDetector())
                .set("enable_latex_tn", config.getEnableLatexTn())
                .set("disable_default_bit_rate", config.getDisableDefaultBitRate())
                .set("max_length_to_filter_parenthesis", config.getMaxLengthToFilterParenthesis())
                .set("cache_config", JSONUtil.createObj()
                        .set("text_type", config.getTextType())
                        .set("use_cache", config.getUseCache()));

        // 将附加参数转换为JSON字符串
        String additionsJson = additions.toString();

        // 构建请求参数 - 完全按照Python示例的结构和命名
        return JSONUtil.createObj()
                .set("user", JSONUtil.createObj()
                        .set("uid", config.getUserId()))
                .set("req_params", JSONUtil.createObj()
                        .set("text", text)
                        .set("speaker", config.getSpeaker())
                        .set("additions", additionsJson)
                        .set("audio_params", JSONUtil.createObj()
                                .set("format", config.getAudioFormat())
                                .set("sample_rate", config.getSampleRate())));
    }

    /**
     * 发送HTTP请求并处理流式响应
     *
     * @param request 请求参数JSON对象
     * @param config 配置参数
     * @return TTS结果
     */
    private DoubaoTTSResult sendRequestAndProcessResponse(JSONObject request, DoubaoTTSProperty config) {
        // 构建请求头
        Map<String, String> headers = buildHeaders(config);

//        log.info("headers: {}", headers);
//        log.info("request: {}", request);

        // 发送HTTP请求并处理流式响应
        HttpResponse response = null;
        try {
            response = HttpRequest.post(config.getApiUrl())
                    .headerMap(headers, true)
                    .body(request.toString())
                    .timeout(config.getTimeout())
                    .execute();

            if (!response.isOk()) {
                log.error("HTTP请求失败，状态码: {}, 响应: {}", response.getStatus(), response.body());
                return DoubaoTTSResult.failure("HTTP请求失败，状态码: " + response.getStatus());
            }

            // 处理流式响应 - 使用InputStream进行逐行读取
            return processStreamResponseFromInputStream(response, config.getAudioFormat());

        } catch (Exception e) {
            log.error("发送HTTP请求异常", e);
            return DoubaoTTSResult.failure("发送HTTP请求异常: " + e.getMessage());
        } finally {
            if (response != null) {
                response.close();
            }
        }
    }

    /**
     * 构建请求头
     *
     * @param config 配置参数
     * @return 请求头Map
     */
    private Map<String, String> buildHeaders(DoubaoTTSProperty config) {
        Map<String, String> headers = new HashMap<>();
        headers.put("X-Api-App-Id", config.getAppId());
        headers.put("X-Api-Access-Key", config.getAccessKey());
        headers.put("X-Api-Resource-Id", config.getResourceId());
        headers.put("X-Api-App-Key", config.getAppKey());
        headers.put("Content-Type", "application/json");
        headers.put("Connection", "keep-alive");
        return headers;
    }

    /**
     * 处理流式响应数据
     *
     * @param responseBody 响应体内容
     * @param audioFormat 音频格式
     * @return TTS结果
     */
    private DoubaoTTSResult processStreamResponse(String responseBody, String audioFormat) {
        if (StrUtil.isBlank(responseBody)) {
            return DoubaoTTSResult.failure("响应体为空");
        }

        // 用于存储音频数据
        StringBuilder audioDataBuilder = new StringBuilder();
        long totalAudioSize = 0;
        String requestId = null;

        try (BufferedReader reader = new BufferedReader(new StringReader(responseBody))) {
            String line;
            while ((line = reader.readLine()) != null) {
                if (StrUtil.isBlank(line)) {
                    continue;
                }

                try {
                    // 解析JSON响应
                    JSONObject jsonData = JSONUtil.parseObj(line);
                    DoubaoTTSResponse response = parseResponse(jsonData);

                    // 记录请求ID
                    if (StrUtil.isNotBlank(response.getRequestId())) {
                        requestId = response.getRequestId();
                    }

                    log.debug("收到响应数据: code={}, hasData={}", response.getCode(), response.hasAudioData());

                    // 处理成功的音频数据
                    if (response.isSuccess() && response.hasAudioData()) {
                        audioDataBuilder.append(response.getData());
                        // 计算音频数据大小（Base64解码后的大小）
                        byte[] chunkAudio = Base64.decode(response.getData());
                        totalAudioSize += chunkAudio.length;
                    }

                    // 检查是否结束
                    if (response.isEnd()) {
                        log.debug("流式响应结束");
                        break;
                    }

                    // 检查是否有错误
                    if (response.hasError()) {
                        String errorMsg = String.format("TTS服务返回错误，code: %d, message: %s",
                                response.getCode(), response.getMessage());
                        log.error(errorMsg);
                        return DoubaoTTSResult.failure(errorMsg, requestId);
                    }

                } catch (Exception e) {
                    log.warn("解析响应行失败，跳过该行: {}", line, e);
                }
            }

            // 检查是否有音频数据
            if (audioDataBuilder.length() == 0) {
                return DoubaoTTSResult.failure("未获取到音频数据", requestId);
            }

            // 解码完整的音频数据
            byte[] completeAudioData = Base64.decode(audioDataBuilder.toString());

            log.info("音频数据处理完成，总大小: {} bytes", completeAudioData.length);

            return DoubaoTTSResult.success(completeAudioData, audioFormat, null, requestId);

        } catch (IOException e) {
            log.error("处理流式响应异常", e);
            return DoubaoTTSResult.failure("处理流式响应异常: " + e.getMessage(), requestId);
        }
    }

    /**
     * 从InputStream处理流式响应数据（按照Python方式修复）
     * 关键修复：每个chunk解码后立即拼接字节数据，而不是先拼接Base64字符串
     *
     * @param response HTTP响应对象
     * @param audioFormat 音频格式
     * @return TTS结果
     */
    private DoubaoTTSResult processStreamResponseFromInputStream(HttpResponse response, String audioFormat) {
        // 使用ByteArrayOutputStream来拼接字节数据，而不是StringBuilder拼接Base64字符串
        ByteArrayOutputStream audioDataStream = new ByteArrayOutputStream();
        String requestId = null;
        int totalAudioSize = 0;

//        log.info("开始处理流式响应数据...");

        try (InputStream inputStream = response.bodyStream();
             BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream, StandardCharsets.UTF_8))) {

            String line;
            int lineCount = 0;

            while ((line = reader.readLine()) != null) {
                lineCount++;

                // 跳过空行
                if (StrUtil.isBlank(line)) {
                    continue;
                }

                log.debug("处理第 {} 行响应数据: {}", lineCount, line);

                try {
                    // 解析JSON响应
                    JSONObject jsonData = JSONUtil.parseObj(line);
                    DoubaoTTSResponse ttsResponse = parseResponse(jsonData);

                    // 记录请求ID
                    if (StrUtil.isNotBlank(ttsResponse.getRequestId())) {
                        requestId = ttsResponse.getRequestId();
                    }

                    log.debug("收到响应数据: code={}, hasData={}, requestId={}",
                            ttsResponse.getCode(), ttsResponse.hasAudioData(), requestId);

                    // 关键修复：每个chunk立即解码并拼接字节数据（与Python一致）
                    if (ttsResponse.isSuccess() && ttsResponse.hasAudioData()) {
                        byte[] chunkAudio = Base64.decode(ttsResponse.getData());
                        audioDataStream.write(chunkAudio);  // 直接写入字节数据
                        totalAudioSize += chunkAudio.length;
                        log.debug("累积音频数据，当前块大小: {} bytes, 总大小: {} bytes",
                                chunkAudio.length, totalAudioSize);
                    }

                    // 检查是否结束
                    if (ttsResponse.isEnd()) {
//                        log.info("流式响应结束，共处理 {} 行数据", lineCount);
                        break;
                    }

                    // 检查是否有错误
                    if (ttsResponse.hasError()) {
                        String errorMsg = String.format("TTS服务返回错误，code: %d, message: %s",
                                ttsResponse.getCode(), ttsResponse.getMessage());
                        log.error(errorMsg);
                        return DoubaoTTSResult.failure(errorMsg, requestId);
                    }

                } catch (Exception e) {
                    log.warn("解析响应行失败，跳过该行: {}", line, e);
                }
            }

            // 检查是否有音频数据
            if (totalAudioSize == 0) {
                log.error("未获取到音频数据，总处理行数: {}", lineCount);
                return DoubaoTTSResult.failure("未获取到音频数据", requestId);
            }

            // 获取完整的音频数据（已经是解码后的字节数据）
            byte[] completeAudioData = audioDataStream.toByteArray();

//            log.info("音频数据处理完成，解码后大小: {} bytes", completeAudioData.length);

            return DoubaoTTSResult.success(completeAudioData, audioFormat, null, requestId);

        } catch (IOException e) {
            log.error("处理流式响应异常", e);
            return DoubaoTTSResult.failure("处理流式响应异常: " + e.getMessage(), requestId);
        }
    }

    /**
     * 解析响应JSON为响应对象
     *
     * @param jsonData JSON数据
     * @return 响应对象
     */
    private DoubaoTTSResponse parseResponse(JSONObject jsonData) {
        return DoubaoTTSResponse.builder()
                .code(jsonData.getInt("code", -1))
                .message(jsonData.getStr("message"))
                .data(jsonData.getStr("data"))
                .requestId(jsonData.getStr("request_id"))
                .timestamp(jsonData.getLong("timestamp"))
                .build();
    }

    /**
     * 保存音频数据到文件
     *
     * @param audioData 音频数据
     * @param filePath 文件路径
     * @return 是否保存成功
     */
    public boolean saveAudioToFile(byte[] audioData, String filePath) {
        if (audioData == null || audioData.length == 0) {
            log.error("音频数据为空，无法保存");
            return false;
        }

        if (StrUtil.isBlank(filePath)) {
            log.error("文件路径为空，无法保存");
            return false;
        }

        try {
            // 获取绝对路径用于调试
            java.io.File targetFile = new java.io.File(filePath);
            String absolutePath = targetFile.getAbsolutePath();
            log.info("准备保存音频文件到: {}", absolutePath);

            // 确保父目录存在
            java.io.File parentDir = targetFile.getParentFile();
            if (parentDir != null && !parentDir.exists()) {
                boolean created = parentDir.mkdirs();
                log.info("创建父目录: {}, 结果: {}", parentDir.getAbsolutePath(), created);
            }

            // 写入音频数据到文件
            cn.hutool.core.io.FileUtil.writeBytes(audioData, targetFile);

            // 验证文件是否真正写入成功
            if (targetFile.exists() && targetFile.length() > 0) {
                log.info("音频文件保存成功: {}, 大小: {} bytes", absolutePath, audioData.length);
                return true;
            } else {
                log.error("音频文件保存失败，文件不存在或大小为0: {}, 存在: {}, 大小: {}",
                        absolutePath, targetFile.exists(), targetFile.length());
                return false;
            }
        } catch (Exception e) {
            log.error("保存音频文件失败: {}", filePath, e);
            return false;
        }
    }
}
