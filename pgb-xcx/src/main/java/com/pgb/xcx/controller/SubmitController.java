package com.pgb.xcx.controller;

import cn.dev33.satoken.annotation.SaCheckLogin;
import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.img.Img;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.URLUtil;
import cn.hutool.crypto.digest.DigestUtil;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.pgb.common.core.global.BaseResult;
import com.pgb.common.core.global.GlobalCode;
import com.pgb.common.core.global.exception.BaseException;
import com.pgb.common.oss.domain.MaterialTypeEnum;
import com.pgb.common.oss.service.OssService;
import com.pgb.common.redis.QueueUtils;
import com.pgb.service.custom.CorrectService;
import com.pgb.service.db.*;
import com.pgb.service.domain.answer.PgAnswer;
import com.pgb.service.domain.answer.answerBatch.PgAnswerBatch;
import com.pgb.service.domain.classes.PgClasses;
import com.pgb.service.domain.common.TodayNum;
import com.pgb.service.domain.common.image.FilePaperImg;
import com.pgb.service.domain.homework.PgHomework;
import com.pgb.service.domain.question.PgQuestion;
import com.pgb.service.domain.question.PgQuestionVO;
import com.pgb.service.domain.question.zwEssay.ZwEssayQuestion;
import com.pgb.service.domain.question.zwEssay.ZwEssayTypeEnum;
import com.pgb.service.domain.question.zwEssay.ZwRequire;
import com.pgb.service.domain.student.PgStudent;
import com.pgb.service.domain.student.SubmitInfo;
import com.pgb.service.domain.user.PgUsers;
import com.pgb.service.domain.userConfig.CorrectConfigDTO;
import com.pgb.service.enums.CorrectStatusEnum;
import com.pgb.service.domain.GlobQueueConstants;
import com.pgb.service.enums.GradeEnum;
import com.pgb.service.interfaces.Debounce;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.pdfbox.Loader;
import org.apache.pdfbox.pdmodel.PDDocument;

import org.apache.pdfbox.rendering.PDFRenderer;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.awt.image.BufferedImage;
import java.io.*;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Tag(name = "用户端/作文")
@RestController("UserZwController")
@Transactional(rollbackFor = Exception.class)
@RequestMapping("/user/zw/info")
@RequiredArgsConstructor
@Slf4j
public class SubmitController {
    private final PgAnswerService pgAnswerService;

    private final PgQuestionService pgQuestionService;

    private final PgUsersService pgUsersService;

    private final PgAnswerCostService pgAnswerCostService;

    private final CorrectService correctService;

    private final PgUserConfigService pgUserConfigService;

    private final PgStudentService pgStudentService;

    private final PgHomeworkService pgHomeworkService;

    private final PgClassesService pgClassesService;

    private final PgAnswerBatchService pgAnswerBatchService;

    private final OssService ossService;

    @Operation(summary = "重新批改单篇作文")
    @PostMapping("reCorrectZw/{answerId}")
    @SaCheckLogin
    public BaseResult<Boolean> reCorrect(@PathVariable Long answerId) {

        long userId = StpUtil.getLoginIdAsLong();

        // 获取今日批改次数
        TodayNum submitNum = pgAnswerCostService.getTodaySubmitNum(userId);
        if (submitNum.getRemainNum() <= 0) {
            return BaseResult.error("今日批改次数已达上限");
        }

        PgAnswer answer = pgAnswerService.getById(answerId);

        if (ObjectUtil.isNull(answer)) {
            return BaseResult.code(GlobalCode.Item_Null);
        }

        try {
            pgAnswerService.reCorrect(answer, true);
        } catch (BaseException e) {
            return BaseResult.error(e.getMsg());
        }

        return BaseResult.success(true);
    }

    // http://127.0.0.1:8084/api/user/zw/info/addCorrect/addCorrect/1867165112285237249
    @Operation(summary = "添加指定id进入批改队列")
    @GetMapping("addCorrect/{key}/{id}")
    public BaseResult<Boolean> addCorrect(@PathVariable String key, @PathVariable Long id) {
        if (ObjectUtil.isNull(key)) {
            return BaseResult.error("参数错误");
        }

        if (!"addCorrect".equals(key)) {
            return BaseResult.error("参数错误");
        }

        // 加入批改队列
        if (!QueueUtils.containsQueueObject(GlobQueueConstants.PGB_XCX_CORRECT_QUEUE_VIP.name(), id) && !QueueUtils.containsQueueObject(GlobQueueConstants.PGB_XCX_CORRECT_QUEUE.name(), id)) {
            QueueUtils.addQueueObject(GlobQueueConstants.PGB_XCX_CORRECT_QUEUE_VIP.name(), id);
            log.info("题目已加入批改队列：{}", id);
        } else {
            log.info("当前题目已存在批改队列中：{}，跳过", id);
        }

        return BaseResult.success(true);
    }

    // http://127.0.0.1:8084/api/user/zw/info/setCorrectStatus/setCorrectStatus/false
    @Operation(summary = "停止当前批改")
    @GetMapping("setCorrectStatus/{key}/{status}")
    public BaseResult<Boolean> setCorrectStatus(@PathVariable String key, @PathVariable Boolean status) {
        if (ObjectUtil.isNull(key)) {
            return BaseResult.error("参数错误");
        }

        if (!"setCorrectStatus".equals(key)) {
            return BaseResult.error("参数错误");
        }

        // 停止批改
        correctService.setCorrecting(status);

        log.info("手动停止当前批改服务");

        return BaseResult.success(correctService.getCorrecting());
    }

    @Operation(summary = "重新批改超时题目")
    @PostMapping("reCorrectTime/{key}")
    public BaseResult<Integer> reCorrectTime(@PathVariable String key) {
        if (ObjectUtil.isNull(key)) {
            return BaseResult.error("参数错误");
        }

        if (!"reCorrectTime".equals(key)) {
            return BaseResult.error("参数错误");
        }

        return BaseResult.success(
                pgAnswerService.queryToCorrect()
        );
    }


    @Operation(summary = "重新批改全部作文")
    @PostMapping("reCorrect/{key}")
    public BaseResult<Boolean> reCorrect(@PathVariable String key) {
        if (ObjectUtil.isNull(key)) {
            return BaseResult.error("参数错误");
        }

        if (!"pgb_reCorrect".equals(key)) {
            return BaseResult.error("参数错误");
        }

        // 扫描当前需要批改的题目列表
        List<PgAnswer> list = pgAnswerService.list(new LambdaQueryWrapper<PgAnswer>().eq(PgAnswer::getStatus, CorrectStatusEnum.Uploaded));

        log.info("【待批改扫描】待批改题目数量:{}个", list.size());

        list.forEach(quesAnswer -> {
            // 加入批改队列
            if (!QueueUtils.containsQueueObject(GlobQueueConstants.PGB_XCX_CORRECT_QUEUE_VIP.name(), quesAnswer.getId()) && !QueueUtils.containsQueueObject(GlobQueueConstants.PGB_XCX_CORRECT_QUEUE.name(), quesAnswer.getId())) {
                QueueUtils.addQueueObject(GlobQueueConstants.PGB_XCX_CORRECT_QUEUE_VIP.name(), quesAnswer.getId());
            } else {
                log.info("当前题目已存在批改队列中：{}，跳过", quesAnswer.getId());
            }
        });

        return BaseResult.success(true);
    }

    @Operation(summary = "提交作文", description = "班级中的提交及批量提交共用")
    @PostMapping("submit")
    @SaCheckLogin
    @Debounce(timeout = 500)
    public BaseResult<Boolean> submit(@RequestBody ZwEssayQuestion essayForm) {

        // 获取图片
        List<FilePaperImg> imgList = essayForm.getUserImgAnswerList();

        if (ObjectUtil.isNotNull(essayForm.getHomeworkId())) {
            if (ObjectUtil.isNull(essayForm.getStudentId())) {
                return BaseResult.error("学生不存在，请返回重试");
            }
        }

        // 判断是否有效
        if (CollUtil.isEmpty(imgList)) {
            return BaseResult.error("请上传图片");
        }

        // 判断今天提交次数
        if (pgAnswerCostService.getTodaySubmitNum(StpUtil.getLoginIdAsLong()).getRemainNum() == 0) {
            return BaseResult.error("今日批改次数已耗尽");
        }

        try {
            // 判断是否已经提交过
            if (ObjectUtil.isNotNull(essayForm.getHomeworkId()) && ObjectUtil.isNotNull(essayForm.getStudentId())) {
                if (pgAnswerService.exists(new LambdaQueryWrapper<PgAnswer>()
                        .eq(PgAnswer::getHomeworkId, essayForm.getHomeworkId())
                        .eq(PgAnswer::getStudentId, essayForm.getStudentId())
                        .eq(PgAnswer::getDeleted, false)
                )) {
                    return BaseResult.error("当前学生作文已提交，请勿重复提交");
                }
            }

            // 处理图片 重命名
            for (FilePaperImg img : imgList) {
                // 源
                String sourceKey = URLUtil.getPath(img.getImgUrl());

                if (sourceKey.contains("/tmp/")) {

                    // 将 temp 替换为 zw
                    String destinationKey = StrUtil.replace(sourceKey, "/tmp/", "/zw/");
                    ossService.renameFile(sourceKey, destinationKey);
                    img.setImgUrl(
                            img.getImgUrl().replaceAll("/tmp/", "/zw/")
                    );
                }
            }

            // 提交班级作文
            PgAnswer answer = pgAnswerService.submitHomeworkZwAnswer(essayForm, StpUtil.getLoginIdAsLong());

            // 新增消耗情况
            pgAnswerCostService.addAnswerCost(StpUtil.getLoginIdAsLong(), answer.getId(), 0);

            PgUsers user = pgUsersService.getById(StpUtil.getLoginIdAsLong());

            // 加入批改队列，保存插入之后执行
            QueueUtils.addQueueObjectInTransaction(
                    user.getIsVip() ? GlobQueueConstants.PGB_XCX_CORRECT_QUEUE_VIP.name() : GlobQueueConstants.PGB_XCX_CORRECT_QUEUE.name(),
                    answer.getId()
            );

        } catch (BaseException e) {
            // 自定义异常
            return BaseResult.error(e.getMsg());
        }
        return BaseResult.success(true);
    }

    @Data
    public static class BatchSubmitForm {

        @Schema(title = "作文类型，中、英、其他")
        private ZwEssayTypeEnum zwType;

        @Schema(title = "上传的作文")
        private List<ZwEssayQuestion> zwList;

        @Schema(title = "所属题目id")
        private Long questionId;

        @Schema(title = "年级")
        private GradeEnum grade;
    }

    @Operation(summary = "批量提交作文", description = "首页提交作文时使用")
    @PostMapping("batch/submit")
    @SaCheckLogin
    @Debounce(timeout = 1500)
    public BaseResult<Boolean> batchSubmit(@RequestBody BatchSubmitForm form) {

//        long userId = 1806176110817902594L;
        long userId = StpUtil.getLoginIdAsLong();

        // 判断今天提交次数
        if (form.getZwList().size() > pgAnswerCostService.getTodaySubmitNum(userId).getRemainNum()) {
            return BaseResult.error("今日批改次数已达上限");
        }

        try {
            // 获取用户批改配置
            CorrectConfigDTO configDTO = pgUserConfigService.getCorrectConfig(userId);

            // 新增作文提交-批次记录
            PgAnswerBatch batch = new PgAnswerBatch();
            batch.setCreateTime(new Date());
            batch.setUserId(userId);
            if (ObjectUtil.isNotNull(form.getQuestionId())) {
                batch.setQuestionId(form.getQuestionId());
            }
            pgAnswerBatchService.save(batch);

            // 遍历每篇作文数据
            for (ZwEssayQuestion zw : form.getZwList()) {

                // 判断是否有效
                if (CollUtil.isEmpty(zw.getUserImgAnswerList())) {
                    return BaseResult.error("请上传图片");
                }

                // 处理图片 重命名
                for (FilePaperImg img : zw.getUserImgAnswerList()) {
                    // 源
                    String sourceKey = URLUtil.getPath(img.getImgUrl());

                    if (sourceKey.startsWith("/tmp/")) {

                        // 将 temp 替换为 zw
                        String destinationKey = StrUtil.replace(sourceKey, "/tmp/", "/zw/");
                        ossService.renameFile(sourceKey, destinationKey);
                        img.setImgUrl(
                                img.getImgUrl().replaceAll("/tmp/", "/zw/")
                        );
                    }
                }

                // 加入到提交列表中
                PgAnswer answer = new PgAnswer();
                // 设置批次id
                answer.setBatchId(batch.getId());
                // 所属题目
                PgQuestion question = null;

                // 【单独提交】
                if (ObjectUtil.isNotNull(form.getQuestionId())) {

                    question = pgQuestionService.getById(form.getQuestionId());

                    if (ObjectUtil.isNull(question)) {
                        return BaseResult.error("题目不存在");
                    }
                }

                // 初始化批改设置
                ZwRequire require = new ZwRequire();

                // 默认标准 选择年级
                if (ObjectUtil.isNotNull(form.getGrade())) {
                    require.setGrade(form.getGrade());
                }

                // 批改配置
                require.setCorrectConfig(configDTO);
                // 所属题目
                if (ObjectUtil.isNotNull(question)) {

                    // 复制批改标准
                    BeanUtil.copyProperties(question, require);
                    // 设置分数
                    zw.setScore(
                            Double.valueOf(
                                    ObjectUtil.defaultIfNull(question.getScore(), 30)
                            )
                    );
                    // 设置题目id
                    zw.setQuestionId(form.getQuestionId());
                    answer.setQuestionId(zw.getQuestionId());
                }
                // 如果没有关联题目 则按分数的配置来
                else {
                    // 评分标准
                    zw.setScore(configDTO.getScoreStandard());
                }
                // 赋值批改要求
                zw.setRequire(require);

                answer.setUserId(userId);
                answer.setStatus(CorrectStatusEnum.Uploaded);
                answer.setAiTokens(0L);
                answer.setCreateTime(new Date());
                // 备注名称
                if (StrUtil.isNotBlank(zw.getName())) {
                    answer.setName(zw.getName());
                }

                // 设置作文类型
                zw.setZwType(form.getZwType());

                answer.setAnswer(zw);
                // 设置未删除
                answer.setDeleted(false);
                // 未归档
                answer.setIsArchive(false);

                // 保存
                pgAnswerService.save(answer);

                // 新增消耗情况
                pgAnswerCostService.addAnswerCost(userId, answer.getId(), 0);

                PgUsers user = pgUsersService.getById(userId);

                // 加入批改队列，保存插入之后执行
                QueueUtils.addQueueObjectInTransaction(
                        user.getIsVip() ? GlobQueueConstants.PGB_XCX_CORRECT_QUEUE_VIP.name() : GlobQueueConstants.PGB_XCX_CORRECT_QUEUE.name(),
                        answer.getId()
                );
            }
        } catch (BaseException e) {
            // 自定义异常
            return BaseResult.error(e.getMsg());
        }

        return BaseResult.success(true);
    }

    @Operation(summary = "查看作业提交情况")
    @GetMapping("submitInfo/{homeworkId}")
    public BaseResult<SubmitInfo> submitInfo(@PathVariable Long homeworkId) {

        SubmitInfo submitInfo = new SubmitInfo();

        // 作业相关信息
        PgHomework homework = pgHomeworkService.getById(homeworkId);
        if (ObjectUtil.isNull(homework)) {
            return BaseResult.code(GlobalCode.Item_Null);
        }

        // 作业相关信息
        submitInfo.setHomeworkName(homework.getName());
        submitInfo.setHomeworkCreateTime(homework.getCreateTime());
        submitInfo.setQuestionInfo(JSONUtil.toBean(JSONUtil.toJsonStr(homework.getQuestionInfo()), PgQuestionVO.class));

        // 班级相关信息
        PgClasses classes = pgClassesService.getById(homework.getClassId());
        if (ObjectUtil.isNotNull(classes)) {
            submitInfo.setClassName(classes.getName());
            submitInfo.setGrade(classes.getGrade());
            submitInfo.setClassNum(classes.getClassNum());
            submitInfo.setPassword(classes.getPassword());
        }

        // 学生相关信息
        PgStudent student = pgStudentService.getOne(new LambdaQueryWrapper<PgStudent>()
                .eq(PgStudent::getStudentUserId, StpUtil.getLoginIdAsLong())
                .eq(PgStudent::getClassId, classes.getId())
                .last("LIMIT 1"));
        if (ObjectUtil.isNotNull(student)) {
            submitInfo.setStudentName(student.getName());
        }

        // 作答相关信息
        PgAnswer answer = pgAnswerService.getOne(new LambdaQueryWrapper<PgAnswer>()
                .eq(PgAnswer::getDeleted, false)
                .eq(PgAnswer::getHomeworkId, homeworkId)
                .eq(PgAnswer::getStudentId, StpUtil.getLoginIdAsLong())
                .last("LIMIT 1"));

        if (ObjectUtil.isNull(answer)) {
            submitInfo.setStatus(CorrectStatusEnum.UnSubmit);
        } else {
            submitInfo.setAnswerId(answer.getId());
            submitInfo.setStatus(answer.getStatus());
            submitInfo.setSubmitTime(answer.getCreateTime());
        }
        return BaseResult.success(submitInfo);
    }

    public record SubmitByWordOrPDF(String fileUrl) {
    }

    @Operation(summary = "使用pdf或word上传", description = "将文件转为图片")
    @PostMapping("file2Img")
    @SaCheckLogin
    public BaseResult<List<String>> file2Img(@RequestBody SubmitByWordOrPDF form) throws IOException {

        // 用户id
        Long userId = StpUtil.getLoginIdAsLong();

        // 初始化返回数据
        List<String> userImgList = new ArrayList<>();

        PDFRenderer renderer;

        PDDocument doc;

        // 使用PDF上传
        if (StrUtil.endWith(form.fileUrl(), ".pdf")) {
            String pdfUrl = form.fileUrl();
            File pdf = FileUtil.createTempFile(".pdf", true);
            HttpUtil.downloadFile(pdfUrl, pdf);
            doc = Loader.loadPDF(FileUtil.readBytes(pdf));
            renderer = new PDFRenderer(doc);
        }
        // 使用word上传
//        else if (StrUtil.endWith(form.fileUrl(), ".docx")) {
//
//            String wordUrl = form.fileUrl();
//
//            File word = FileUtil.createTempFile(".docx", true);
//            HttpUtil.downloadFile(wordUrl, word);
//
//            // 使用Apache POI读取Word文档
//            try (FileInputStream inputStream = new FileInputStream(word)) {
////                XWPFDocument document = new XWPFDocument(OPCPackage.open(word));
//                XWPFDocument document = new XWPFDocument(inputStream);
//
//                // 创建PDF文件
//                File pdfFile = FileUtil.createTempFile(".pdf", true);
//
//                // 转换
//                FileOutputStream out = new FileOutputStream(pdfFile);
//                PdfOptions options = PdfOptions.create();
//                PdfConverter.getInstance().convert(document, out, options);
//                out.close();
//                // 读取PDF文件并上传到OSS
//                doc = Loader.loadPDF(FileUtil.readBytes(pdfFile));
//                renderer = new PDFRenderer(doc);
//
//            }
//        }
        else {
            return BaseResult.error("不支持的文件格式");
        }

        // 遍历上传
        for (int i = 0; i < doc.getNumberOfPages(); i++) {

            // 生成 PDF 截取的图片，dpi越高，越大
            BufferedImage image = renderer.renderImageWithDPI(i, 200);

            // 压缩
            File tempFile = FileUtil.createTempFile(".jpg", true);
            Img.from(image).setQuality(0.8).write(tempFile);

            // 上传图片
            String key = StrUtil.format(
                    "zw/user/{}/{}/{}.jpg",
                    userId,
                    MaterialTypeEnum.IMG.name(),
                    DigestUtil.md5Hex(
                            FileUtil.readBytes(tempFile)
                    )
            );

            // 提交
            String imgUrl = ossService.putFile(key, tempFile);
            userImgList.add(imgUrl);
            FileUtil.del(tempFile);
        }

        // 删除pdf
        ossService.delete(
                URLUtil.getPath(form.fileUrl())
        );

        return BaseResult.success(userImgList);
    }


//    public static void main(String[] args) {
//        String inputFilePath = "C:\\Users\\<USER>\\Desktop\\word.docx";
//        String outputFilePath = "C:\\Users\\<USER>\\Desktop\\pdf.pdf";
//        String fontFilePath = "D:\\下载\\鼎猎宋刻体_猫啃网\\鼎猎宋刻体\\dingliesongtypeface20241217.ttf";
//        try (FileInputStream fis = new FileInputStream(inputFilePath);
//             XWPFDocument wordDocument = new XWPFDocument(fis);
//             PDDocument pdfDocument = new PDDocument()) {
//
//            // 初始页面
//            PDPage page = new PDPage();
//            pdfDocument.addPage(page);
//            File fontFile = new File(fontFilePath);
////            PDType1Font font = new PDType1Font(Standard14Fonts.FontName.TIMES_ROMAN);
//            PDType0Font font = PDType0Font.load(pdfDocument, fontFile);
//            float margin = 50; // 页面边距
//            float yStart = page.getMediaBox().getHeight() - margin; // 初始 Y 坐标
//            float xStart = margin; // 初始 X 坐标
//            float leading = 14; // 行间距
//            float currentY = yStart;
//
//            // 遍历Word文档中的所有段落
//            for (XWPFParagraph paragraph : wordDocument.getParagraphs()) {
//                String text = paragraph.getText();
//                log.info("text:{}", text);
//                if (text != null && !text.isEmpty()) {
//                    // 如果文本超出页面高度，则创建新页面
//                    if (currentY < margin) {
//                        page = new PDPage();
//                        pdfDocument.addPage(page);
//                        currentY = page.getMediaBox().getHeight() - margin;
//
//                        // 在新的页面上创建新的内容流
//                        try (PDPageContentStream newContentStream = new PDPageContentStream(pdfDocument, page)) {
//                            newContentStream.setFont(font, 12);
//                            newContentStream.beginText();
//                            newContentStream.newLineAtOffset(xStart, currentY);
//                            newContentStream.showText(text);
//                            newContentStream.endText();
//                        }
//                        currentY -= leading; // 更新 Y 坐标
//                        continue;
//                    }
//
//                    // 分割文本为多行（如果一行放不下）
//                    String[] lines = text.split("\n");
//                    for (String line : lines) {
//                        try (PDPageContentStream contentStream = new PDPageContentStream(pdfDocument, page, PDPageContentStream.AppendMode.APPEND, true)) {
//                            contentStream.setFont(font, 12);
//                            contentStream.beginText();
//                            contentStream.newLineAtOffset(xStart, currentY);
//                            contentStream.showText(line);
//                            contentStream.endText();
//                        }
//                        currentY -= leading; // 更新 Y 坐标
//                    }
//                }
//            }
//
//            // 保存PDF文件
//            pdfDocument.save(new FileOutputStream(outputFilePath));
//            System.out.println("PDF created successfully.");
//
//        } catch (IOException e) {
//            e.printStackTrace();
//        }
//    }
}
