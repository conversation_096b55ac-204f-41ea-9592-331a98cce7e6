package com.pgb.service.db.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.comparator.PinyinComparator;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.ReUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.pgb.service.db.PgStudentService;
import com.pgb.service.domain.student.PgStudent;
import com.pgb.service.domain.student.PgStudentVO;
import com.pgb.service.mapper.PgStudentMapper;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
* <AUTHOR>
* @description 针对表【pg_student(学生表)】的数据库操作Service实现
* @createDate 2024-12-10 17:19:44
*/
@Service
public class PgStudentServiceImpl extends ServiceImpl<PgStudentMapper, PgStudent>
    implements PgStudentService {

    @Override
    public void createStudent(Long classId, String name) {

        PgStudent student =  new PgStudent();

        student.setName(name);
        student.setCreateTime(new Date());
        student.setClassId(classId);
        save(student);
    }

    @Override
    public List<PgStudentVO> sortByNoName(List<PgStudentVO> list) {
        // 开始排序，规则，先按学号排，再按名字排
        list = ListUtil.toList(list);
        PinyinComparator comparator = new PinyinComparator();

        // 先按学号排序
        List<PgStudentVO> studentNoSubList = list.stream().filter(item -> StrUtil.isNotBlank(item.getStudentNo())).toList();
        studentNoSubList = ListUtil.toList(studentNoSubList);
        studentNoSubList = CollUtil.sort(studentNoSubList, (o1, o2) -> {
            String o1No = ReUtil.delAll("[^0-9]", o1.getStudentNo());
            String o2No = ReUtil.delAll("[^0-9]", o2.getStudentNo());

            if (NumberUtil.isNumber(o1No) && NumberUtil.isNumber(o2No)) {
                return NumberUtil.compare(
                        NumberUtil.parseLong(o1No),
                        NumberUtil.parseLong(o2No)
                );
            }

            // 如果没有数字,按照拼音进行排序
            // 如果不是，则默认拼音
            return comparator.compare(
                    ObjectUtil.defaultIfNull(o1.getStudentNo(), ""),
                    ObjectUtil.defaultIfNull(o2.getStudentNo(), "")
            );
        });

        // 按名字排
        List<PgStudentVO> studentNameSubList = CollUtil.subtractToList(list, studentNoSubList);
        studentNameSubList.sort((o1, o2) -> {
            // 按照名字顺序来排序
            String o1Name = ReUtil.delAll("[^0-9]", o1.getName());
            String o2Name = ReUtil.delAll("[^0-9]", o2.getName());

            // 判断如果有数字
            if (StrUtil.isNotBlank(o1Name) && StrUtil.isNotBlank(o2Name)) {
                if (NumberUtil.isNumber(o1Name) && NumberUtil.isNumber(o2Name)) {
                    return NumberUtil.compare(
                            NumberUtil.parseLong(o1Name),
                            NumberUtil.parseLong(o2Name)
                    );
                }
            }

            // 如果不是，则默认拼音
            return comparator.compare(
                    ObjectUtil.defaultIfNull(o1.getName(), ""),
                    ObjectUtil.defaultIfNull(o2.getName(), "")
            );
        });

        // 学号 + 名字累加
        studentNoSubList.addAll(studentNameSubList);

        return studentNoSubList;
    }
}




