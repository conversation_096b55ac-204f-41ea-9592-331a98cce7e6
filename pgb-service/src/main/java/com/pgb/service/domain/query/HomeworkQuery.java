package com.pgb.service.domain.query;

import com.pgb.common.mybatis.global.PageQuery;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * Created by 2024/12/11 10:55
 */
@EqualsAndHashCode(callSuper = true)
@Schema(description = "作业 搜索实体")
@Data
public class HomeworkQuery extends PageQuery {

    @Schema(title = "作业名称")
    private String name;
}
