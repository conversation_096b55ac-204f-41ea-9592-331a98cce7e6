package com.pgb.service.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;

/**
 * <AUTHOR>
 * Created by 2025/2/14 16:14
 */
@Schema(title = "智能体收费类型")
@AllArgsConstructor
public enum FeeTypeEnum {

    @Schema(title = "免费")
    Free(0, "免费"),

    @Schema(title = "会员无限次")
    Vip_Free(1, "会员无限次"),

    @Schema(title = "限次")
    Limit(2, "限次");

    /**
     * 枚举值对应的整数值
     */
    @EnumValue
    public final int value;

    /**
     * 文字说明
     */
    public final String desc;
}
