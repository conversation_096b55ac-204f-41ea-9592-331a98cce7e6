package com.pgb.service.domain.homework.statistic;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * Created by 2025/3/18 12:09
 */
@Data
public class StudentInfo {

    @Schema(title = "学生名字")
    private String name;

    @Schema(title = "学生学号")
    private String studentNo;

    @Schema(title = "用户分数")
    private Integer score;

    @Schema(title = "作答id")
    private Long answerId;
}
