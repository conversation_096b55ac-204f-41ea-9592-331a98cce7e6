package com.pgb.service.db.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.pgb.service.domain.agent.collect.PgAgentCollect;
import com.pgb.service.db.PgAgentCollectService;
import com.pgb.service.mapper.PgAgentCollectMapper;
import org.springframework.stereotype.Service;

import java.util.Date;

/**
 * <AUTHOR>
 * @description 针对表【pg_agent_collect】的数据库操作Service实现
 * @createDate 2025-02-13 15:55:27
 */
@Service
public class PgAgentCollectServiceImpl extends ServiceImpl<PgAgentCollectMapper, PgAgentCollect>
        implements PgAgentCollectService {

    @Override
    public void collect(Long agentId, Long userId) {

        // 如果存在
        if (exists(new LambdaQueryWrapper<PgAgentCollect>()
                .eq(PgAgentCollect::getAgentId, agentId)
                .eq(PgAgentCollect::getUserId, userId))) {
            return;
        }

        PgAgentCollect collect = new PgAgentCollect();
        collect.setAgentId(agentId);
        collect.setUserId(userId);

        long sort = count(new LambdaQueryWrapper<PgAgentCollect>()
                .eq(PgAgentCollect::getUserId, userId)
        );

        collect.setSort((int) sort);
        collect.setCreateTime(new Date());

        save(collect);
    }

    @Override
    public void unCollect(Long agentId, Long userId) {
        remove(new LambdaQueryWrapper<PgAgentCollect>()
                .eq(PgAgentCollect::getAgentId, agentId)
                .eq(PgAgentCollect::getUserId, userId)
        );
    }
}




