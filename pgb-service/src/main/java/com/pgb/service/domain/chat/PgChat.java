package com.pgb.service.domain.chat;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;

import com.pgb.common.mybatis.handler.JsonbTypeHandler;
import com.pgb.service.enums.ChatTypeEnum;
import com.pgb.service.enums.LLMTypeEnum;
import lombok.Data;

/**
 * AI会话表
 * @TableName pg_chat
 */
@TableName(value ="pg_chat")
@Data
public class PgChat implements Serializable {
    /**
     * 会话表
     */
    private Long id;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * agentId
     */
    private Long agentId;

    /**
     * 表单内容
     */
    @TableField(typeHandler = JsonbTypeHandler.class)
    private Object form;

    /**
     * 会话标题
     */
    private String title;
}
