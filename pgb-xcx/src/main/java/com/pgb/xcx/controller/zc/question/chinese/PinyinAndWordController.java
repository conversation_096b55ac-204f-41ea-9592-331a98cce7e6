package com.pgb.xcx.controller.zc.question.chinese;

import cn.dev33.satoken.annotation.SaCheckLogin;
import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.pgb.common.core.global.BaseResult;
import com.pgb.common.core.global.GlobalCode;
import com.pgb.service.db.PgZcQuestionService;
import com.pgb.service.domain.zc.question.PgZcQuestion;
import com.pgb.service.domain.zc.question.chinese.pinyinAndWord.PinyinAndWord;
import com.pgb.service.enums.ExportStatusEnum;
import com.pgb.service.enums.SubjectEnum;
import com.pgb.service.enums.ZcQuestionTypeEnum;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.util.Date;

@Tag(name = "用户端/字词/题目/语文/拼音和词语")
@RestController("PinyinAndWordController")
@Transactional(rollbackFor = Exception.class)
@RequestMapping("/user/zc/question/pyWord")
@RequiredArgsConstructor
@Slf4j
public class PinyinAndWordController {

    private final PgZcQuestionService pgZcQuestionService;

    @Operation(summary = "新增题目【拼音和词语】")
    @PostMapping("save")
    @SaCheckLogin
    public BaseResult<Long> savePinyinAndWord(@RequestBody PinyinAndWord form) {

        // 保存题目基本信息
        PgZcQuestion zcQuestion = new PgZcQuestion();

        // 用户id
        zcQuestion.setUserId(StpUtil.getLoginIdAsLong());
        // 科目
        zcQuestion.setSubject(SubjectEnum.Chinese);
        // 字词类型
        zcQuestion.setType(
                form.getConfig().getType() == 0 ? ZcQuestionTypeEnum.PyToWord : ZcQuestionTypeEnum.WordToPy
        );
        // 分数，默认100分
        zcQuestion.setScore(100);

        // 题目名称
        String title = form.getConfig().getTitle();
        zcQuestion.setName(StrUtil.isBlank(title) ? "看拼音写词语" : form.getConfig().getTitle());

        // 年级，暂时不写
        // 是否官方
        zcQuestion.setIsOfficial(false);
        zcQuestion.setCreateTime(new Date());
        zcQuestion.setUpdateTime(new Date());

        // 保存题目内容
        zcQuestion.setContentJson(
                form
        );
        // 未生成pdf
        zcQuestion.setPdfStatus(ExportStatusEnum.Init);

        pgZcQuestionService.save(zcQuestion);

        return BaseResult.success(zcQuestion.getId());
    }

    @Operation(summary = "编辑题目【拼音和词语】")
    @PostMapping("update/{id}")
    @SaCheckLogin
    public BaseResult<Long> updatePinyinAndWord(@RequestBody PinyinAndWord form, @PathVariable Long id) {

        // 获取题目
        PgZcQuestion zcQuestion = pgZcQuestionService.getById(id);

        if (ObjectUtil.isNull(zcQuestion)) {
            return BaseResult.code(GlobalCode.Item_Null);
        }
        // 计算旧题目的唯一值
        String md5 = pgZcQuestionService.getZcQuestionMd5(zcQuestion);

        // 判断是否为当前用户
        if (StpUtil.getLoginIdAsLong() != zcQuestion.getUserId()) {
            return BaseResult.code(GlobalCode.Permission_Not);
        }

        // 保存题目基本信息
        // 题目名称
        zcQuestion.setName(
                form.getConfig().getTitle()
        );
        // 字词类型
        zcQuestion.setType(
                form.getConfig().getType() == 0 ? ZcQuestionTypeEnum.PyToWord : ZcQuestionTypeEnum.WordToPy
        );
        zcQuestion.setContentJson(
                form
        );
        zcQuestion.setUpdateTime(new Date());

        pgZcQuestionService.updateById(zcQuestion);

        // 计算更新完后的唯一值
        PgZcQuestion newZcQues = pgZcQuestionService.getById(id);
        String newMd5 = pgZcQuestionService.getZcQuestionMd5(newZcQues);

        // 如果pdf的状态为 Completed
        if (zcQuestion.getPdfStatus().equals(ExportStatusEnum.Completed)) {

            // 查看内容是否发生变化
            if (!md5.equals(newMd5)) {
                // 更新状态
                zcQuestion.setPdfStatus(ExportStatusEnum.Init);
                pgZcQuestionService.updateById(zcQuestion);
            }
        }


        return BaseResult.success(zcQuestion.getId());
    }

    @Operation(summary = "更新题目位置信息")
    @PostMapping("update/location/{key}/{id}")
    public BaseResult<Boolean> updateLocation(@PathVariable Long id, @RequestBody PinyinAndWord form, @PathVariable String key) {

        if (!"updateLocation".equals(key)) {
            return BaseResult.code(GlobalCode.Permission_Not);
        }

        PgZcQuestion zcQuestion = pgZcQuestionService.getById(id);

        if (ObjectUtil.isNull(zcQuestion)) {
            return BaseResult.code(GlobalCode.Item_Null);
        }

        if (CollUtil.isNotEmpty(form.getContent())) {
            // 保存内容
            zcQuestion.setContentJson(form);

            pgZcQuestionService.updateById(zcQuestion);
        }

        log.info("【看拼音写词语】更新题目位置信息：{}", id);

        return BaseResult.success(true);
    }

    @Operation(summary = "获取题目")
    @GetMapping("detail/{id}")
    public BaseResult<PinyinAndWord> detail(@PathVariable Long id) {

        PgZcQuestion zcQuestion = pgZcQuestionService.getById(id);

        if (ObjectUtil.isNull(zcQuestion)) {
            return BaseResult.code(GlobalCode.Item_Null);
        }

        PinyinAndWord data = JSONUtil.toBean(JSONUtil.toJsonStr(zcQuestion.getContentJson()), PinyinAndWord.class);

        return BaseResult.success(data);
    }
}
