# 数据库
spring:
  datasource:
    driver-class-name: org.postgresql.Driver
    url: **********************************************
    username: postgres
    password: Snros2022..
    hikari:
      maximum-pool-size: 400
  data:
    redis:
      host: ************
      port: 6379
      password: Snros2022..

# redisson
redisson:
  # redis key前缀
  keyPrefix:
  # 线程池数量
  threads: 300
  # Netty线程池数量
  nettyThreads: 300
  # 单节点配置
  singleServerConfig:
    # 客户端名称
    clientName: pgb-server
    # 最小空闲连接数
    connectionMinimumIdleSize: 32
    # 连接池大小
    connectionPoolSize: 128
    # 连接空闲超时，单位：毫秒
    idleConnectionTimeout: 10000
    # 命令等待超时，单位：毫秒
    timeout: 3000
    # 发布和订阅连接池大小
    subscriptionConnectionPoolSize: 300

#  rabbitmq:
#    # 主机地址
#    host: 127.0.0.1
#    # 虚拟主机
#    virtual-host: /
#    # 端口号默认是5672，可以不写
#    port: 5672
#    # 用户名
#    username: admin
#    # 密码
#    password: 95d09682
#    # 开启消费者手动确认模式
#    listener:
#      simple:
#        acknowledge-mode: manual

# 接口文档
springdoc:
  api-docs:
    enabled: false
  swagger-ui:
    enabled: false

# 支付回调
pay:
  #  notifyUrl: https://xcx-api.pigaibang.com/api
  #  使用代理api，需和前端确认
  notifyUrl: https://proxy.pigaibang.com/xcx/api

# 批改模型接口
correct:
  base-url: http://model-java.pigaibang.com
  api-key: pgb-api-correct-v2
  # 批改 url
  render-url: http://127.0.0.1:9000
  # 用户端保存，使用高并发
  render-url-user: http://zw-render-url.pigaibang.com

# textbook服务配置
textbook:
  base-url: http://************:8080/api
