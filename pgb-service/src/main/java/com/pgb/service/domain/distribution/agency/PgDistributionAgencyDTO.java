package com.pgb.service.domain.distribution.agency;

import java.io.Serializable;

import java.util.Date;

import com.pgb.service.domain.user.PgUsers;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.ToString;
import lombok.Builder;
import org.springframework.validation.annotation.Validated;

/**
* 分销代理人员表
* @TableName pg_distribution_agency
*/
@Schema(description = "分销代理人员表 DTO")
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode
@ToString
@Validated
@Builder
public class PgDistributionAgencyDTO implements Serializable {

    @Schema(title = "分销代理人员id")
    private Long id;

    @Schema(title = "用户id")
    private Long userId;

    @Schema(title = "分销比例")
    private Integer rate;

    @Schema(title = "代理创建时间")
    private Date createTime;

}
