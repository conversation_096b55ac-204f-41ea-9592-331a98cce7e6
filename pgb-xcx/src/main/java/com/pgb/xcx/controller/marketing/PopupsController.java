package com.pgb.xcx.controller.marketing;

import cn.dev33.satoken.annotation.SaCheckLogin;
import cn.dev33.satoken.stp.StpUtil;
import com.pgb.common.core.global.BaseResult;
import com.pgb.service.db.PgPopupService;
import com.pgb.service.domain.popup.PgPopup;
import com.pgb.service.enums.PopupTypeEnum;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * Created by 2024/11/14 15:14
 */
@Tag(name = "用户端/营销/弹窗")
@RestController("UserMarketingPopupsController")
@Transactional(rollbackFor = Exception.class)
@RequestMapping("/user/marketing/popups")
@RequiredArgsConstructor
@Slf4j
public class PopupsController {

    private final PgPopupService pgPopupService;

    @Operation(summary = "贴片广告", description = "首页右下角")
    @GetMapping("showPatch")
    @SaCheckLogin
    public BaseResult<List<PgPopup>> showPatch() {
        Long userId = StpUtil.getLoginIdAsLong();

        List<PgPopup> popups = pgPopupService.filterPopup(
                userId,
                PopupTypeEnum.PATCH
        );

        return BaseResult.success(popups);
    }

    @Operation(summary = "用户获取弹窗内容列表",
            description = "前端获取弹窗的时机，是用户在首页且登录状态时，mounted；如果list是空，则不需要弹窗"
    )
    @GetMapping("showList")
    @SaCheckLogin
    public BaseResult<List<PgPopup>> showList() {

        Long userId = StpUtil.getLoginIdAsLong();

        List<PgPopup> popups = pgPopupService.filterPopup(
                userId,
                PopupTypeEnum.POPUP
        );

        return BaseResult.success(popups);
    }
}
