package com.pgb.xcx.controller.system;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.pgb.common.core.global.BaseResult;
import com.pgb.service.db.SystemConfigService;
import com.pgb.service.domain.systemConfig.SystemConfig;
import com.pgb.service.domain.systemConfig.group.GroupItem;
import com.pgb.service.domain.systemConfig.group.XcxGroupConfig;
import com.pgb.service.enums.SystemConfigEnum;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * Created by 2025/4/3 17:36
 */
@Tag(name = "系统管理/交流群")
@RestController("SystemGroupController")
@Transactional(rollbackFor = Exception.class)
@RequestMapping("/system/group")
@RequiredArgsConstructor
@Slf4j
public class GroupController {

    private final SystemConfigService systemConfigService;

    @Operation(summary = "获取交流群列表")
    @GetMapping("list")
    public BaseResult<List<GroupItem>> getGroup() {

        SystemConfig config = systemConfigService.getByKey(SystemConfigEnum.XCX_GROUP);

        if (ObjectUtil.isNull(config)) {

            return BaseResult.success(new ArrayList<>());
        }
        JSONObject object = JSONUtil.parseObj(config.getValue());

        List<GroupItem> list = JSONUtil.toList(JSONUtil.parseArray(object.get("groups")), GroupItem.class);

        return BaseResult.success(list);
    }


    @Operation(summary = "修改并保存交流群配置")
    @PostMapping("update")
    public BaseResult<Boolean> updateGroup(@RequestBody XcxGroupConfig config) {

        SystemConfig systemConfig = systemConfigService.getByKey(SystemConfigEnum.XCX_GROUP);

        if (ObjectUtil.isNull(systemConfig)) {
            systemConfig = new SystemConfig();
            systemConfig.setKey(SystemConfigEnum.XCX_GROUP.name());
            systemConfig.setValue(config);
            systemConfig.setIsValid(true);
            systemConfig.setIsShow(true);
            systemConfig.setRemark("小程序交流群");
            systemConfig.setCreateTime(new Date());
            systemConfigService.save(systemConfig);
        } else {

            systemConfig.setValue(config);
            systemConfig.setUpdateTime(new Date());
            systemConfigService.updateById(systemConfig);
        }
        return BaseResult.success(true);
    }

}
