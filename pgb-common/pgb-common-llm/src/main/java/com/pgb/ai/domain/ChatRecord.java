package com.pgb.ai.domain;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;

@Schema(title = "单条会话记录")
@Data
@Builder
public class ChatRecord {

    @Schema(title = "chatId")
    private Long chatId;

    @Schema(title = "会话内容")
    private String content;

    @Schema(title = "角色")
    private Role role;

    public String getALiRoleName() {
        return switch (role) {
            case System -> "system";
            case Assistant -> "assistant";
            default -> "user";
        };
    }

    public enum Role {
        System,
        Assistant,
        User
    }
}
