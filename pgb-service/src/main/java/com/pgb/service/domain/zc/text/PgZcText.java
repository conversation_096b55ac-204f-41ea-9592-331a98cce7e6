package com.pgb.service.domain.zc.text;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 课文表
 * @TableName pg_text
 */
@TableName(value ="pg_zc_text")
@Data
public class PgZcText implements Serializable {
    /**
     *
     */
    @TableId
    private Long id;

    /**
     * 课文名
     */
    private String name;

    /**
     * 识字ids
     */
    private String knowWordIds;

    /**
     * 生字ids
     */
    private String newWordIds;

    /**
     * 词语ids
     */
    private String ciyuIds;

    /**
     * 读读写写ids
     */
    private String readAndWriteIds;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 所属教材id
     */
    private Long textbookId;

    /**
     * 单元（1，2，3，4）
     */
    private Integer unit;

    /**
     * 第几课
     */
    private String lesson;

    /**
     * 排序（相同单元下的排序）
     */
    private Integer sort;

    /**
     * 内容
     */
    private String content;

    /**
     * 类型
     */
    private Integer type;

}
