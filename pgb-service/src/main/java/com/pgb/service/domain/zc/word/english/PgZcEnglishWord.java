package com.pgb.service.domain.zc.word.english;

import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 英语单词表
 * @TableName pg_english_word
 */
@TableName(value ="pg_zc_english_word")
@Data
public class PgZcEnglishWord implements Serializable {
    /**
     *
     */
    private Long id;

    /**
     * 是否是官方教材单词
     */
    private Boolean isOfficial;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 阶段和版本（小学人教PEP版）
     */
    private String version;

    /**
     * 年级册别
     */
    private String grade;

    /**
     * 单元
     */
    private String unit;

    /**
     * 英文单词
     */
    private String word;

    /**
     * 释义
     */
    private String definition;

    /**
     * 音标
     */
    private String phonetic;

    /**
     * 词性（动词）
     */
    private String wordClass;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 排序
     */
    private Integer sort;

}
