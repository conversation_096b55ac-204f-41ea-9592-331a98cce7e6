package com.pgb.common.core.global;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

@AllArgsConstructor
@NoArgsConstructor
@Schema(description = "素材属性")
@EqualsAndHashCode
@Data
@ToString
@Builder
public class MaterialProperty {

    @Schema(title = "宽", description = "图片、视频")
    private Long width;

    @Schema(title = "高", description = "图片、视频")
    private Long height;

    @Schema(title = "封面URL", description = "视频素材使用")
    private String coverUrl;

    @Schema(title = "时长", description = "音频、视频等 单位为s秒")
    private Float duration;

    @Schema(title = "文件格式后缀")
    private String suffix;
}
