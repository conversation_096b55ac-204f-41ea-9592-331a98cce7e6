package com.pgb.common.sms;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.*;

@Builder
@Data
@Schema(title = "短信配置", description = "通用版本，适用腾讯云和阿里云")
public class SmsProperty {

    @Schema(title = "供应商")
    @NotNull
    private String supplier;

    @Schema(title = "API key")
    private String key;

    @Schema(title = "API 密钥")
    private String secret;

    @Schema(title = "APP ID")
    private String appId;

    @Schema(title = "短信签名")
    private String signName;

    @Schema(title = "短信模板ID")
    private String templateId;
}
