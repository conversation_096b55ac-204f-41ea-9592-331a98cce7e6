package com.pgb.service.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;

@Schema(description = "批改 状态")
@AllArgsConstructor
public enum CorrectStatusEnum {
    @Schema(title = "已上传，未批改")
    Uploaded(0, "已上传"),

    @Schema(title = "AI 已批改")
    AI_Corrected(1, "AI 已批改"),

    @Schema(title = "人工审校 中")
    Manual_Correcting(2, "批改中"),

    @Schema(title = "已批改")
    Corrected(3, "已批改"),

    @Schema(title = "重批中")
    ReCorrecting(4, "重批中"),

    @Schema(title = "未提交")
    UnSubmit(5, "未提交"),

    @Schema(title = "已提交，待AI批改")
    Submitted(6, "已提交，待AI批改"),

    @Schema(title = "已审核")
    Checked(7, "已审核");

    /**
     * 枚举值对应的整数值
     */
    @EnumValue
    public final int value;

    /**
     * 文字说明
     */
    public final String desc;
}
