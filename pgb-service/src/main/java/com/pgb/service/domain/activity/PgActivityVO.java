package com.pgb.service.domain.activity;

import com.fasterxml.jackson.annotation.JsonGetter;
import com.pgb.service.enums.ActivityTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

@Data
public class PgActivityVO {

    @Schema(title = "id")
    private Long id;

    @Schema(title = "活动名称")
    private String name;

    @Schema(title = "活动类型，0：年卡立减，1：买多少送多少（天）")
    private ActivityTypeEnum type;

    @Schema(title = "购买类型，0：直购，1：兑换码")
    private Integer buyType;

    @Schema(title = "创建时间")
    private Date createTime;

    @Schema(title = "开始时间")
    private Date startTime;

    @Schema(title = "结束时间")
    private Date endTime;

    @Schema(title = "配置信息")
    private ActivityDiscountConfig config;

    @Schema(title = "是否已参与")
    private Boolean isBuy;

    @Schema(title = "活动参与人数")
    private Integer buyNum;

    @Schema(title = "活动状态")
    @JsonGetter
    public String getStatus() {

        Date now = new Date();

        if (now.before(startTime)){
            return "未开始";
        } else if (now.after(endTime)) {
            return "已结束";
        }else {
            return "进行中";
        }
    }
}
