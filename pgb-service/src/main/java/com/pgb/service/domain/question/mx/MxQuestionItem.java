package com.pgb.service.domain.question.mx;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;

/**
 * @Datetime: 2025年04月06日15:14
 * @Description:
 */
@Builder
@Schema(title = "单个默写项，默写情况")
@Data
public class MxQuestionItem {
    @Schema(title = "是否正确")
    private Boolean isRight;

    @Schema(title = "学生作答内容")
    private String userAnswer;

    @Schema(title = "正确内容")
    private String answer;
}
