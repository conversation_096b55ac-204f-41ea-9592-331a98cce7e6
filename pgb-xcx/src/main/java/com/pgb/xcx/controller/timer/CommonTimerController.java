package com.pgb.xcx.controller.timer;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.URLUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.pgb.common.core.global.BaseResult;
import com.pgb.common.core.global.GlobalCode;
import com.pgb.common.oss.service.OssService;
import com.pgb.common.sms.SmsResDTO;
import com.pgb.service.db.PgAnswerService;
import com.pgb.service.db.PgExportRecordService;
import com.pgb.service.db.PgUsersService;
import com.pgb.service.db.PgZcAnswerService;
import com.pgb.service.domain.answer.PgAnswer;
import com.pgb.service.domain.export.PgExportRecord;
import com.pgb.service.domain.question.zwEssay.ZwEssayQuestion;
import com.pgb.service.domain.user.PgUsers;
import com.pgb.xcx.common.SmsService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Tag(name = "用户端/定时器/会员")
@RestController("UserVipTimerController")
@Transactional(rollbackFor = Exception.class)
@RequestMapping("/user/timer/common")
@RequiredArgsConstructor
@Slf4j
public class CommonTimerController {

    private final PgUsersService pgUsersService;

    private final SmsService smsService;

    private final PgExportRecordService pgExportRecordService;

    private final OssService ossService;

    private final PgUsersService pgUserService;

    private final PgAnswerService pgAnswerService;

    private final PgZcAnswerService pgZcAnswerService;

    @Operation(summary = "查询是否有超过20分钟没有批改的题目，报警通知")
    @GetMapping("checkCorrectTimeout/{key}")
    public BaseResult<Integer> checkCorrectTimeout(@PathVariable String key) {
        // 验证权限管理
        if (!key.equals("checkCorrectTimeout")) {
            return BaseResult.code(GlobalCode.Param_Wrong);
        }

        // 发送通知
        return BaseResult.success(
                pgAnswerService.queryToCorrect(false, 20)
        );
    }

    @Operation(summary = "查询是否有超过20分钟没有批改的字词，报警通知")
    @GetMapping("zc/checkCorrectTimeout/{key}")
    public BaseResult<Integer> zcCheckCorrectTimeout(@PathVariable String key) {
        // 验证权限管理
        if (!key.equals("ZcCheckCorrectTimeout")) {
            return BaseResult.code(GlobalCode.Param_Wrong);
        }

        // 发送通知
        return BaseResult.success(
                pgZcAnswerService.queryToCorrect(false, 20)
        );
    }

    @Operation(summary = "查询即将过期的用户，并发送短信提醒", description = "通过定时函数来激活")
    @GetMapping("sendExpireSms/{key}")
    public BaseResult<String> sendExpireSms(@PathVariable String key) {
        // 验证权限管理
        if (!key.equals("sendExpireSms")) {
            return BaseResult.code(GlobalCode.Param_Wrong);
        }

        // 查看即将过期的所有用户  在第1天 和第2天之间
        List<String> phoneList = pgUsersService.list(new LambdaQueryWrapper<PgUsers>()
                        .ge(PgUsers::getVipExpireTime, DateUtil.offsetDay(new Date(), 1))
                        .le(PgUsers::getVipExpireTime, DateUtil.offsetDay(new Date(), 2)))
                .stream().map(PgUsers::getPhone).toList();

        // 循环发送短信
        int successNum = 0;
        int failNum = 0;
        for (String phone : phoneList) {
            SmsResDTO resDTO = smsService.sendSms(smsService.getSmsConfigProperty().getVipExpired(), phone);

            if (!resDTO.getSuccess()) {
                failNum++;
                log.error("会员过期发送短信失败，手机号：{}，原因：{}", phone, resDTO.getMsg());
            } else {
                successNum++;
            }
        }

        return BaseResult.success(
                StrUtil.format("发送总量：{}, 成功数量：{}；失败数量：{}", phoneList.size(), successNum, failNum)
        );
    }

    @Operation(summary = "查询超过30天的压缩包，删除压缩包")
    @GetMapping("clearExportExpireZip/{key}")
    public BaseResult<String> clearExportExpireZip(@PathVariable String key) {

        // 验证权限管理
        if (!key.equals("clearExportExpireZip")) {
            return BaseResult.code(GlobalCode.Param_Wrong);
        }

        List<PgExportRecord> recordList = pgExportRecordService.list(new LambdaQueryWrapper<PgExportRecord>()
                .le(PgExportRecord::getExportTime, DateUtil.offsetDay(new Date(), -30))
        );

        recordList.forEach(record -> {
            // 删除cos存储
            // ossService.setTagList(
            //         URLUtil.getPath(record.getZipUrl()),
            //         "Deleted"
            // );
            ossService.delete(
                    URLUtil.getPath(record.getZipUrl())
            );

            // 删除
            pgExportRecordService.removeById(record);
        });

        return BaseResult.success(
                StrUtil.format("清除过期数量：{}", recordList.size())
        );
    }


    /**
     * https://help.aliyun.com/zh/oss/developer-reference/list-objects-3?spm=a2c4g.11186623.help-menu-31815.d_3_3_0_6_2_4.78837857RHfLFS
     *
     * @return
     */
    @Operation(summary = "查询oss无效的作文图片数据", description = "oss有，数据中没有")
    @GetMapping("clearOssInvalidImg")
    public BaseResult<Boolean> clearOssInvalidImg() {

        // 获取所有用户
        List<Long> userIdList = pgUserService.list().stream().map(PgUsers::getId).toList();

//        Long userId = 1810610771813761026L;

        userIdList.forEach(userId -> {

                    // 当前用户的所有作文记录
                    List<PgAnswer> answers = pgAnswerService.list(new LambdaQueryWrapper<PgAnswer>()
                            .eq(PgAnswer::getUserId, userId)
                            .select(PgAnswer::getAnswer, PgAnswer::getUserId, PgAnswer::getId));

                    // 指定用户所有的图片列表
                    List<String> allImgKeyList = new ArrayList<>();

                    answers.forEach(answer -> {
                        // 获取数据库用户上传图片
                        ZwEssayQuestion userAnswer = JSONUtil.toBean(answer.getAnswer().toString(), ZwEssayQuestion.class);

                        // 获取上传图片的列表
                        List<String> answerImgUrlList = userAnswer.getUserImgAnswerList()
                                .stream()
                                .map(img -> {
                                    String key = URLUtil.getPath(img.getImgUrl());

                                    // 移除首符号
                                    if (StrUtil.startWith(key, "/")) {
                                        key = StrUtil.subSuf(key, 1);
                                    }

                                    return key;
                                }).toList();

                        allImgKeyList.addAll(answerImgUrlList);
                    });

                    // 获取oss图片列表
                    List<String> ossImgList = ossService.listObjects(
                            StrUtil.format("zw/user/{}/IMG", userId)
                    );

                    // 找出无效的图片
                    List<String> invalidImgList = ossImgList.stream()
                            .filter(img -> !allImgKeyList.contains(img))
                            .toList();

                    // 设置标签
                    for (int i = 0; i < invalidImgList.size(); i++) {
                        // 设置删除标签
                        ossService.setTagList(invalidImgList.get(i));
                        // 日志进度
                        log.info("当前用户：{}, 进度：{}/{}，删除的key：{}", userId, i + 1, invalidImgList.size(), invalidImgList.get(i));
                    }
                }
        );
        return BaseResult.success(true);
    }
}
