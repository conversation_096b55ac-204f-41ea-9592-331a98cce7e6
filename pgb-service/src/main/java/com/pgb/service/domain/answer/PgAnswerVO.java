package com.pgb.service.domain.answer;

import java.io.Serializable;

import java.util.Date;
import java.util.List;

import com.baomidou.mybatisplus.annotation.TableField;
import com.pgb.common.mybatis.handler.JsonbTypeHandler;
import com.pgb.service.domain.answer.polish.PolishInfo;
import com.pgb.service.domain.question.zwEssay.ZwEssayQuestion;
import com.pgb.service.enums.CorrectStatusEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.ToString;
import lombok.Builder;

/**
 * @TableName pg_answer
 */
@Schema(description = " VO")
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode
@ToString
@Builder
public class PgAnswerVO implements Serializable {

    @Schema(title = "题库，做题记录")
    private Long id;

    @Schema(title = "用户id")
    private Long userId;

    @Schema(title = "用户提交答案")
    private ZwEssayQuestion answer;

    @Schema(title = "批改状态")
    private CorrectStatusEnum status;

    @Schema(title = "本题消耗脑力值数量")
    private Long aiTokens;

    @Schema(title = "本题批改满意度，如果开启批改反馈")
    private Integer feedback;

    @Schema(title = "批改所需时长，单位：秒")
    private Integer correctDuration;

    @Schema(title = "AI批改完成时间")
    private Date correctTime;

    @Schema(title = "创建时间")
    private Date createTime;

    @Schema(title = "备注名称")
    private String name;

    @Schema(title = "润色记录")
    private List<PolishInfo> polishList;

    @Schema(title = "是否归档")
    private Boolean isArchive;

    @Schema(title = "归档后的批改结果json url")
    private String archiveUrl;

}
