package com.pgb.service.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;

/**
 * <AUTHOR>
 * Created by 2025/1/8 14:45
 */
@Schema(title = "时间范围类型")
@AllArgsConstructor
public enum QueryPeriodEnum {

    Day(0,"天"),

    Week(1,"周"),

    Month(2,"月");

//    Year(3,"年");

    /**
     * 枚举值对应的整数值
     */
    @EnumValue
    public final int value;

    /**
     * 文字说明
     */
    public final String desc;
}
