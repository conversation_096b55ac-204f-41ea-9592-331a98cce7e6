package com.pgb.service.domain.zc.word.english.common;

import java.io.Serializable;

import com.pgb.common.core.validate.UpdateGroup;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.ToString;
import lombok.Builder;
import org.springframework.validation.annotation.Validated;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;

/**
*
* @TableName pg_zc_eng_word_common
*/
@Schema(description = " DTO")
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode
@ToString
@Validated
@Builder
public class PgZcEngWordCommonDTO implements Serializable {

    @Schema(title = "词汇表，AI生成缓存表", type = "string")
    @NotNull(message="[词汇表，AI生成缓存表]不能为空", groups = {UpdateGroup.class})
    private Long id;

    @Schema(title = "单词")
    @Size(max= 255)
    private String word;

    @Schema(title = "词性")
    @Size(max= 255)
    private String wordclass;

    @Schema(title = "中文释义")
    @Size(max= 255)
    private String chinses;

    @Schema(title = "音标")
    @Size(max= 255)
    private String phonetic;

}
