package com.pgb.student.common;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.pgb.common.core.global.BaseResult;
import com.pgb.common.core.global.GlobalCode;
import com.pgb.common.core.utils.EnvUtils;
import com.pgb.common.redis.RedisUtils;
import com.pgb.common.sms.SmsResDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.sms4j.api.SmsBlend;
import org.dromara.sms4j.api.entity.SmsResponse;
import org.dromara.sms4j.core.factory.SmsFactory;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.LinkedHashMap;

/**
 * <AUTHOR>
 * Created by 2024/8/13 17:56
 */

@Component
@Slf4j
@RequiredArgsConstructor
public class SmsService {

    public enum SmsTypeEnum {
        VerifyCode,
        Vip_Expired_Notify,
        Vip_Exchange_Code
    }

    public SmsResDTO sendSms(SmsTypeEnum type, String phone, String... params) {
        // 将params转化为map
        LinkedHashMap<String, String> varMap = new LinkedHashMap<>();

        for (int i = 0; i < params.length; i++) {
            varMap.put("" + i, params[i]);
        }

        return sendSms(type, phone, varMap);
    }

    /**
     * 重载，默认不传参数
     * @param phone
     * @return
     */
    public SmsResDTO sendSms(SmsTypeEnum type, String phone) {
        return sendSms(type, phone, new LinkedHashMap<>());
    }

    public SmsResDTO sendSms(SmsTypeEnum type, String phone, LinkedHashMap<String, String> varMap) {
        // 验证码
        if (type.equals(SmsTypeEnum.VerifyCode)) {
            return sendSms(phone, "1850346", varMap);
        }
        // 会员过期通知
        else if (type.equals(SmsTypeEnum.Vip_Expired_Notify)) {
            return sendSms(phone, "2360188", varMap);
        }
        // 会员兑换
        else if (type.equals(SmsTypeEnum.Vip_Exchange_Code)) {
            return sendSms(phone, "2295530", varMap);
        }

        return null;
    }

    /**
     * 发送短信
     * @param phone
     * @param varMap
     * @return
     */
    public SmsResDTO sendSms(String phone, String templateId, LinkedHashMap<String, String> varMap) {

        SmsBlend blend = SmsFactory.getSmsBlend("tencent");

        SmsResponse response = blend.sendMessage(phone, templateId, varMap);

        try {
            // 为空，发送失败
            if (!response.isSuccess()) {
                log.error("短信发送异常 => {}", response);
                return SmsResDTO.builder()
                        .success(false)
                        .msg(response.getData().toString())
                        .errCode(GlobalCode.Sms_Code_Error)
                        .build();
            }
        } catch (Exception e) {
            log.error("短信发送异常 => {}", e.toString());
            return SmsResDTO.builder()
                    .success(false)
                    .msg("短信发送失败，请稍后再试")
                    .errCode(GlobalCode.Sms_Code_Error)
                    .build();
        }

        return SmsResDTO.builder()
                .success(true)
                .build();
    }


    /**
     * 短信验证码校验
     * @param phone
     * @param smsCode
     * @return
     */
    public BaseResult<Boolean> smsIsSuccess(String phone, String smsCode, String keyPrefix) {
        JSONObject json;
        //  获取Redis中的手机和验证码
        if (!EnvUtils.isProd()) {
            json = JSONUtil.createObj()
                    .putOnce("verifyCode", 1234)
                    .putOnce("phone", phone)
                    .putOnce("createTime", new Date());
        } else {
            json = JSONUtil.parseObj(RedisUtils.getCacheObject(keyPrefix + phone));
        }

        // 验证码是否为空
        if (ObjectUtil.isNull(json) || StrUtil.hasEmpty(json.getStr("verifyCode"))) {
            return BaseResult.code(GlobalCode.Sms_Code_Error);
        }
        // 验证码是否过期
        else if ((System.currentTimeMillis() - json.getLong("createTime")) > 1000 * 60 * 10) {
            return BaseResult.code(GlobalCode.Sms_Code_Expire);
        }
        // 验证码是否正确
        else if (!json.getStr("verifyCode").equals(smsCode)) {
            return BaseResult.code(GlobalCode.Sms_Code_Error);
        }
        // 验证码所对应的手机号是否正确
        else if (!json.getStr("phone").equals(phone)) {
            return BaseResult.code(GlobalCode.Sms_Phone_Error);
        }

        return BaseResult.code(GlobalCode.Success);
    }
}
