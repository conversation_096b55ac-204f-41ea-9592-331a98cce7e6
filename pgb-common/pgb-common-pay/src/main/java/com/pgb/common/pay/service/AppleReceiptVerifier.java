package com.pgb.common.pay.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.pgb.common.pay.domain.apple.AppleReceiptResponse;
import com.pgb.common.pay.domain.apple.AppleReceiptVerification;
import lombok.extern.slf4j.Slf4j;

/**
 * @Datetime: 2025年06月14日18:20
 * @Description:
 */
@Slf4j
public class AppleReceiptVerifier {

    private String sharedSecret;

    public AppleReceiptVerifier(String sharedSecret) {
        this.sharedSecret = sharedSecret;
    }

    private static final String PRODUCTION_URL = "https://buy.itunes.apple.com/verifyReceipt";
    private static final String SANDBOX_URL = "https://sandbox.itunes.apple.com/verifyReceipt";

    // 使用 Hutool JSONObject 代替 POJO
    public AppleReceiptResponse verifyReceipt(AppleReceiptVerification request, boolean isProd) {
        // 1. 构建请求参数
        JSONObject body = JSONUtil.createObj()
                .set("receipt-data", request.getReceiptData())
                .set("password", ObjectUtil.defaultIfBlank(request.getPassword(), sharedSecret));

        // 2. 先请求
        JSONObject response = verifyWithRetry(body, isProd ? PRODUCTION_URL : SANDBOX_URL, 3);
        int status = response.getInt("status", -1);

        // 4. 转换为响应对象
        AppleReceiptResponse data = BeanUtil.toBean(response, AppleReceiptResponse.class);

        // 5. 设置是否成功
        data.setIsSuccess(status == 0);

        return data;
    }

    private JSONObject verifyWithRetry(JSONObject body, String url, Integer retryNum) {
        // 使用 Hutool 的 HTTP 客户端 + 重试机制
        HttpResponse response = HttpRequest.post(url)
                .body(body.toString())
                .timeout(5000)
                .execute();

        if (!response.isOk() && retryNum > 0) {
            return verifyWithRetry(body, url, retryNum - 1);
        }

        return JSONUtil.parseObj(response.body());
    }
}
