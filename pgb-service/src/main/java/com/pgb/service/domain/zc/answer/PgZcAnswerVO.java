package com.pgb.service.domain.zc.answer;

import java.io.Serializable;

import java.util.Date;

import com.pgb.service.domain.zc.question.chinese.ZcSubmitForm;
import com.pgb.service.domain.zc.question.chinese.pinyinAndWord.PinyinAndWordResult;
import com.pgb.service.enums.CorrectStatusEnum;
import com.pgb.service.enums.ZcQuestionTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.ToString;
import lombok.Builder;

/**
*
* @TableName pg_zc_answer
*/
@Schema(description = " VO")
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode
@ToString
@Builder
public class PgZcAnswerVO implements Serializable {

    @Schema(title = "默写批改记录")
    private Long id;

    @Schema(title = "用户id")
    private Long userId;

    @Schema(title = "用户本题作答")
    private ZcSubmitForm answer;

    @Schema(title = "批改状态")
    private CorrectStatusEnum status;

    @Schema(title = "消耗token量")
    private Long aiTokens;

    @Schema(title = "批改所需时长，单位：秒")
    private Integer correctDuration;

    @Schema(title = "创建时间")
    private Date createTime;

    @Schema(title = "AI批改完成时间")
    private Date correctTime;

    @Schema(title = "批改结果")
    private PinyinAndWordResult correctResult;

    @Schema(title = "所属题目id")
    private Long zcQuestionId;

    @Schema(title = "备注名称")
    private String name;

    @Schema(title = "学生id")
    private Long studentId;

    @Schema(title = "是否已删除")
    private Boolean deleted;

    @Schema(title = "作业id")
    private Long zcHomeworkId;

    @Schema(title = "所属批次id")
    private Long batchId;

    @Schema(title = "字词题目类型，0：通用，1：情景式填空")
    private ZcQuestionTypeEnum quesType;

}
