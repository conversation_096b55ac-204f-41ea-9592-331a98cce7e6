package com.pgb.xcx.controller.pay;

import cn.dev33.satoken.annotation.SaCheckLogin;
import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.extra.servlet.JakartaServletUtil;
import com.alipay.api.AlipayApiException;
import com.alipay.api.AlipayClient;
import com.alipay.api.DefaultAlipayClient;
import com.alipay.api.domain.AlipayTradeAppPayModel;
import com.alipay.api.internal.util.AlipaySignature;
import com.alipay.api.request.AlipayTradeAppPayRequest;
import com.alipay.api.response.AlipayTradeAppPayResponse;
import com.pgb.common.core.global.BaseResult;
import com.pgb.common.core.utils.EnvUtils;
import com.pgb.common.pay.config.AliAppPayProperty;
import com.pgb.common.pay.enums.BuyTypeEnum;
import com.pgb.common.pay.enums.PayTypeEnum;
import com.pgb.service.db.PgOrderService;
import com.pgb.service.domain.order.OrderPayParam;
import com.pgb.service.domain.order.PgOrder;
import com.pgb.service.enums.VipTypeEnum;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;

@Tag(name = "用户端/订单/支付宝支付")
@RestController("AliPayController")
@Transactional(rollbackFor = Exception.class)
@RequestMapping("/user/order/aliPay")
@RequiredArgsConstructor
@Slf4j
public class AliPayController {

    private final AlipayClient alipayClient;

    private final PgOrderService pgOrderService;

    private final AliAppPayProperty aliAppPayProperty;

    @Value("${pay.notifyUrl}")
    private String notifyUrl;

    @Operation(summary = "获取支付信息")
    @GetMapping("pay/app")
    @SaCheckLogin
    public BaseResult<OrderPayParam<String>> pay(HttpServletRequest request, VipTypeEnum vipType) {

        // 生成订单
        PgOrder order = pgOrderService.generateOrder(
                vipType,
                PayTypeEnum.ALI_APP,
                BuyTypeEnum.PAY,
                JakartaServletUtil.getClientIP(request, null),
                StpUtil.getLoginIdAsLong()
        );

        // 构造请求参数以调用接口
        AlipayTradeAppPayRequest payRequest = new AlipayTradeAppPayRequest();
        AlipayTradeAppPayModel model = new AlipayTradeAppPayModel();

        // 设置商户订单号
        model.setOutTradeNo(order.getOrderNo());
        // 设置订单总金额
        model.setTotalAmount(
                EnvUtils.isDev() ? "0.01" : NumberUtil.decimalFormatMoney(order.getPayAmount() / 100.0)
        );
        // 设置订单标题
        model.setSubject(order.getDescription());
        // 设置订单绝对超时时间
        model.setTimeExpire(
                DateUtil.format(DateUtil.offsetMinute(order.getCreateTime(), 30), "yyyy-MM-dd'T'HH:mm:ssXXX")
        );
        // 设置公用回传参数
        model.setPassbackParams(order.getId().toString());
        // 设置商户的原始订单号
        model.setMerchantOrderNo(order.getOrderNo());
        // 设置请求参数
        payRequest.setBizModel(model);
        // 回调地址
        payRequest.setNotifyUrl(
                notifyUrl + "/user/order/aliPay/aliPayNotify"
        );

        try {
            AlipayTradeAppPayResponse response = alipayClient.sdkExecute(payRequest);
            String orderStr = response.getBody();
            log.info("【支付宝APP支付】{}", orderStr);
            if (response.isSuccess()) {
                System.out.println("调用成功");
            } else {
                System.out.println("调用失败");
                // sdk版本是"4.38.0.ALL"及以上,可以参考下面的示例获取诊断链接
                // String diagnosisUrl = DiagnosisUtils.getDiagnosisUrl(response);
                // System.out.println(diagnosisUrl);
            }

            OrderPayParam<String> payParam = new OrderPayParam<>();
            payParam.setOrderId(order.getId());
            payParam.setParams(orderStr);

            return BaseResult.success(payParam);
        } catch (AlipayApiException e) {
            throw new RuntimeException(e);
        }
    }

    @Operation(summary = "支付宝支付回调接口", description = "支付宝支付成功后，支付宝会调用此接口进行回调")
    @PostMapping("aliPayNotify")
    public String aliPayNotify(HttpServletRequest request, @RequestBody String data) {

        // 获取支付宝POST过来反馈信息
        Map<String, String> params = new HashMap<String, String>();
        Map<String, String[]> requestParams = request.getParameterMap();
        for (String name : requestParams.keySet()) {
            String[] values = (String[]) requestParams.get(name);
            String valueStr = "";
            for (int i = 0; i < values.length; i++) {
                valueStr = (i == values.length - 1) ? valueStr + values[i] : valueStr + values[i] + ",";
            }
            // 乱码解决，这段代码在出现乱码时使用。
            // valueStr = new String(valueStr.getBytes("ISO-8859-1"), "utf-8");
            params.put(name, valueStr);
        }

        // 切记alipaypublickey是支付宝的公钥，请去open.alipay.com对应应用下查看
        try {
            boolean signValid = AlipaySignature.rsaCheckV1(params, aliAppPayProperty.getAliPublicKey(), "UTF-8", "RSA2");

            if (!signValid) {
                log.error("支付宝验签失败：\n 【参数】\n{} \n【公钥】\n{}", params, aliAppPayProperty.getAliPublicKey());
                return "failure";
            }

            // 验证交易状态（TRADE_SUCCESS表示支付成功）
            if ("TRADE_SUCCESS".equals(params.get("trade_status"))) {
                // 商户订单id
                Long orderId = Long.valueOf(params.get("passback_params"));

                Integer amount = (int) (NumberUtil.parseDouble(params.get("receipt_amount")) * 100);
                pgOrderService.paySuccess(orderId, amount, params);

                log.info("【支付宝支付回调】：{}", params);

                return "success";
            }
        } catch (AlipayApiException e) {
            log.error("支付宝支付回调错误", e);
        }

        return "failure";
    }

}
