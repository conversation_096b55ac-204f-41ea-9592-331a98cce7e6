package com.pgb.service.domain.chat.msg;

import java.io.Serializable;

import java.util.AbstractList;
import java.util.Date;
import java.util.List;

import com.baomidou.mybatisplus.annotation.TableField;
import com.pgb.common.mybatis.handler.JsonbTypeHandler;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.ToString;
import lombok.Builder;

/**
* 会话消息内容记录表
* @TableName pg_chat_msg
*/
@Schema(description = "会话消息内容记录表 VO")
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode
@ToString
@Builder
public class PgChatMsgVO implements Serializable {

    @Schema(title = "会话消息内容记录id")
    private Long id;

    @Schema(title = "所属会话id")
    private Long chatId;

    @Schema(title = "用户类型（用户、机器人）")
    private Integer role;

    @Schema(title = "消息内容")
    private String content;

    @Schema(title = "文件")
    private String files;

    @Schema(title = "排序")
    private Integer sort;

    @Schema(title = "创建时间")
    private Date createTime;

    @Schema(title = "是否是第一次发消息")
    private Boolean isFirst;

    @Schema(title = "内容类型")
    private Integer contentType;

    @Schema(title = "内容json")
    private List<ChatAttachItem> contentJson;

}
