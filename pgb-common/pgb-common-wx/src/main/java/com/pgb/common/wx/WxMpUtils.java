package com.pgb.common.wx;

import cn.hutool.extra.spring.SpringUtil;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import me.chanjar.weixin.mp.api.WxMpService;
import me.chanjar.weixin.mp.api.impl.WxMpServiceImpl;
import me.chanjar.weixin.mp.config.WxMpConfigStorage;

/**
 * 工具化封装，调用无需使用注入
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class WxMpUtils {
    private static final WxMpService SERVICE = SpringUtil.getBean(WxMpServiceImpl.class);

    /**
     * 设置当前公众号配置
     * @param config
     */
    public static WxMpService setConfig(WxMpConfigStorage config) {
        // 添加最新配置
        SERVICE.addConfigStorage(config.getAppId(), config);

        // 返回当前配置下对应的实例
        return SERVICE.switchoverTo(config.getAppId());
    }
}
