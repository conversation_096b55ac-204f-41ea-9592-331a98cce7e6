package com.pgb.service.db;

import com.baomidou.mybatisplus.extension.service.IService;
import com.pgb.service.domain.vip.PgVip;

/**
* <AUTHOR>
* @description 针对表【pg_vip】的数据库操作Service
* @createDate 2024-06-25 16:44:48
*/
public interface PgVipService extends IService<PgVip> {
    /**
     * 判断是否为新购
     * @param userId
     * @return
     */
    boolean isNewBuy(Long userId);

    /**
     * 生成兑换码，此时并没有生效会有
     * @param userId
     * @return
     */
    boolean isCodeNewBuy(Long userId);

    /**
     * 用户购买会员次数
     * @param userId
     * @return
     */
    Long buyNum(Long userId);
}
