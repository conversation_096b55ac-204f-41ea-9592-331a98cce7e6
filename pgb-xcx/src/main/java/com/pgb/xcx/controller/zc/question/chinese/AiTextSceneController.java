package com.pgb.xcx.controller.zc.question.chinese;

import cn.dev33.satoken.annotation.SaCheckLogin;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.pgb.ai.LLMService;
import com.pgb.ai.domain.GPTAnswer;
import com.pgb.ai.utils.LLMUtils;
import com.pgb.common.core.global.BaseResult;
import com.pgb.common.core.utils.EnvUtils;
import com.pgb.service.domain.zc.question.chinese.textBlank.TextBlankItem;
import com.pgb.service.factory.LLMServiceFactory;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * Created by 2025/7/9 11:33
 */
@Tag(name = "用户端/字词/题目/语文/AI情景默写出题")
@RestController("AiTextSceneController")
@Transactional(rollbackFor = Exception.class)
@RequestMapping("/user/zc/question/aiTextScene")
@RequiredArgsConstructor
@Slf4j
public class AiTextSceneController {

    private final LLMService llmService;

    @Autowired
    public AiTextSceneController(LLMServiceFactory llmServiceFactory) {
        this.llmService = llmServiceFactory.getLLMService("ali");
    }

    @Data
    public static class SceneGenerateForm {
        @Schema(title = "词语列表，以空格间隔")
        private String text;

        @Schema(title = "年级")
        private String grade;

        @Schema(title = "图片")
        private String imgUrl;
    }


    @Operation(summary = "单个生成默写情景题目")
    @PostMapping("generate")
//    @SaCheckLogin
    public BaseResult<TextBlankItem> generatePoemScene(@RequestBody SceneGenerateForm form) {
        String prompt = StrUtil.format("""
                请根据{}年级学生的语文水平，基于提供的诗句片段设计情景式题目。
                        
                具体要求：
                1. 构建诗句应用场景：
                - 创设与诗句意境相符的生活场景或叙事片段
                - 自然融入目标诗句，上下文需提供语义线索
                - 避免直接解释诗句，通过情境暗示
                        
                2. 题目规范：
                - 语句符合该年级语言表达能力
                - 保留诗句的文学性和意境
                        
                请严格按照以下JSON格式返回：
                {
                  "text": "题目内容（包含完整诗句）",
                  "textMark": [
                  {
                    "word": "诗句片段",
                    "pinyin": "标准拼音（音节间空格分隔，若word中标点符号，则pinyin中也返回），如：pīn yīn"
                  }
                  ]
                }
                        
                示例：
                输入：少无适俗韵，性本爱丘山
                输出：
                {
                  "text": "陶渊明在《归园田居》(其一)中,用“少无适俗韵，性本爱丘山”两句阐明了自己本性不喜官场生活,渴望回归自然的原因。",
                  "textMark": [
                  {
                    "word": "少无适俗韵，性本爱丘山",
                    "pinyin": "shào wú shì sú yùn , xìng běn ài qiū shān"
                  }
                  ]
                }
                """, form.getGrade());

        List<String> userList = new ArrayList<>();
        userList.add(form.getText());

        GPTAnswer answer = llmService.chatComplete(prompt, userList, true, null);

        // 处理返回内容
        JSONObject json = JSONUtil.parseObj(answer.getAnswer());
        TextBlankItem item = parseTextBlankItem(json);

        return BaseResult.success(item);
    }


    @Operation(summary = "拍照生成默写情景题目")
    @PostMapping("generate/img")
    public BaseResult<List<TextBlankItem>> generateSceneImg(@RequestBody SceneGenerateForm form) {

        String vlPrompt = """
                请结构化提取图片中每个题目对应的完整内容
                如果图片中的题目没有答案，请补全填空上的答案后返回完整的题目内容
                """;
        GPTAnswer vlAnswer = llmService.vl(form.getImgUrl(), vlPrompt, "");

        String content = vlAnswer.getAnswer();
        String prompt = """
                请基于提供的默写题目列表返回
                
                **严格遵循以下规则：**
                1. 若填空部分没有答案，需补全答案后返回
                2. 题目text必须包含完整题目（包括题干和答案,若图片中没有答案，请补全后返回）
                3. 请严格按以下JSON格式返回：
                [
                {
                  "text": "必须包含完整题目内容（包括题干和答案，若图片中没有答案，请补全填空上的答案）",
                  "textMark": [
                  {
                    "word": "答案片段",
                    "pinyin": "标准拼音（音节之间空格分隔），如：pīn yīn"
                  }
                  ]
                }
                ]
                            
                示例：
                    [
                    {
                      "text": "陶渊明在《归园田居》(其一)中,用“少无适俗韵，性本爱丘山”两句阐明了自己本性不喜官场生活,渴望回归自然的原因。",
                      "textMark": [
                      {
                        "word": "少无适俗韵，性本爱丘山",
                        "pinyin": "shào wú shì sú yùn , xìng běn ài qiū shān"
                      }
                      ]
                    }
                    ]
                            
                """;
        GPTAnswer answer = llmService.chatComplete(prompt, List.of(content), true, null, "qwen-plus-latest");

        if (EnvUtils.isDev()) {
            log.info(answer.toString());
        }
        // 格式化
        List<TextBlankItem> results = new ArrayList<>();
        JSONArray allArray = LLMUtils.clearArray(answer.getAnswer());
        for (int i = 0; i < allArray.size(); i++) {

            JSONObject jsonItem = allArray.getJSONObject(i);
            TextBlankItem item = parseTextBlankItem(jsonItem);
            results.add(item);
        }

        return BaseResult.success(results);
    }


    @Operation(summary = "AI出题", description = "一行为一个题目")
    @PostMapping("generate/ai")
    public BaseResult<List<TextBlankItem>> generateSceneAi(@RequestBody SceneGenerateForm form) {

        // 按换行符分割诗句列表
        List<String> lines = StrUtil.split(form.getText(), "\n");

        // 构建提示词
        String prompt = StrUtil.format("""
                请根据{}年级学生的语文水平，基于提供的诗句列表设计情景式题目（每行诗句生成一个独立题目）。
                        
                **严格遵循以下规则：**
                1.题目设计要求： 
                每行诗句生成一个题目：
                - 必须先完整呈现原句（加引号），再展开情景描述
                - 必须从诗句的情感表达、文学内涵和结构作用切入
                - 需结合诗人背景或创作背景进行专业解读
                - 必须从文学赏析角度进行专业解读
                        
                2. 格式规范：
                - 诗句中的标点符号必须完整保留
                - 拼音标注需与诗句一致，包括标点符号
                - 每个题目必须是独立完整的
                                
                输入说明：
                可能会输入多个题目，如果涉及多个题目，则在json数组中，生成多个题目 
                       
                请严格按照以下JSON格式返回：
                [
                {
                  "text": "题目内容（包含完整诗句）",
                  "textMark": [
                  {
                    "word": "诗句片段",
                    "pinyin": "标准拼音（音节间空格分隔，若word中标点符号，则pinyin中也返回），如：pīn yīn"
                  }
                  ]
                }
                ]
                                      
                示例：
                    输入：少无适俗韵，性本爱丘山
                    输出：
                    [
                    {
                      "text": "陶渊明在《归园田居》(其一)中,用“少无适俗韵，性本爱丘山”两句阐明了自己本性不喜官场生活,渴望回归自然的原因。",
                      "textMark": [
                      {
                        "word": "少无适俗韵，性本爱丘山",
                        "pinyin": "shào wú shì sú yùn , xìng běn ài qiū shān"
                      }
                      ]
                    }
                    ]
                   
                """, form.getGrade());

        GPTAnswer answer = llmService.chatComplete(prompt, lines, true, null);

        if (EnvUtils.isDev()) {
            log.info(answer.toString());
        }
        // 格式化
        List<TextBlankItem> result = new ArrayList<>();
        JSONArray allArray = LLMUtils.clearArray(answer.getAnswer());
        for (int j = 0; j < allArray.size(); j++) {
            JSONObject json = allArray.getJSONObject(j);

            // 处理返回数据
            TextBlankItem item = parseTextBlankItem(json);

            result.add(item);
        }

        return BaseResult.success(result);
    }


    // 处理返回数据
    private TextBlankItem parseTextBlankItem(JSONObject json) {
        String text = json.getStr("text");
        TextBlankItem item = new TextBlankItem();
        item.setText(text);
        item.setTextMark(new ArrayList<>());

        JSONArray array = json.getJSONArray("textMark");
        for (int i = 0; i < array.size(); i++) {
            JSONObject jsonMark = array.getJSONObject(i);
            String word = jsonMark.getStr("word");
            String pinyin = jsonMark.getStr("pinyin");

            List<String> pinyinList = StrUtil.split(pinyin, " ");
            List<String> wordList = List.of(word.split(""));

            if (wordList.size() != pinyinList.size()) {
                continue;
            }

            // 查找对应词
            int index = StrUtil.indexOf(text, word, 0, true);

            if (index == -1) {
                continue;
            }

            for (int i1 = 0; i1 < wordList.size(); i1++) {
                String markWord = wordList.get(i1);

                if (!markWord.matches("[\\u4E00-\\u9FA5]+")){
                    continue;
                }

                TextBlankItem.TextBlankItemMark mark = new TextBlankItem.TextBlankItemMark();
                mark.setText(markWord);
                mark.setPinyin(pinyinList.get(i1));
                mark.setStart(index + i1);
                mark.setEnd(index + i1 + 1);
                item.getTextMark().add(mark);
            }
        }
        return item;
    }

}
