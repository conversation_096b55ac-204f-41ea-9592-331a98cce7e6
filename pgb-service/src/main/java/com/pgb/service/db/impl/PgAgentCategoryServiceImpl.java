package com.pgb.service.db.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.pgb.service.domain.agent.category.PgAgentCategory;
import com.pgb.service.db.PgAgentCategoryService;
import com.pgb.service.mapper.PgAgentCategoryMapper;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【pg_agent_category(分组表)】的数据库操作Service实现
* @createDate 2025-02-13 14:18:12
*/
@Service
public class PgAgentCategoryServiceImpl extends ServiceImpl<PgAgentCategoryMapper, PgAgentCategory>
    implements PgAgentCategoryService{

}




