package com.pgb;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.pgb.service.db.PgStudentService;
import com.pgb.service.domain.student.PgStudent;
import com.pgb.service.enums.IdentityTypeEnum;
import com.pgb.student.domain.PgUserStudent;
import com.pgb.student.domain.PgUsers;
import com.pgb.student.service.PgUserStudentService;
import com.pgb.student.service.PgUsersService;
import com.pgb.student.service.PgbCustomService;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * Created by 2025/2/24 19:26
 */
@SpringBootTest(classes = StudentApplication.class, webEnvironment = SpringBootTest.WebEnvironment.DEFINED_PORT)
@Slf4j
@DS("pgb")
public class StudentApplicationTests {

    @Autowired
    private PgStudentService pgStudentService;

    @Autowired
    private PgUsersService pgUsersService;

    @Autowired
    private PgUserStudentService pgUserStudentService;

    @Autowired
    private PgbCustomService pgbCustomService;

    // 清理空白关联的学生
    //@Test
    void clearEmptyUserStudent() {
        List<PgUserStudent> list = pgUserStudentService.list();

        log.info("共{}条记录", list.size());

        for (PgUserStudent userStudent : list) {
            List<PgStudent> studentList = pgbCustomService.getStudentListByStudentUserId(userStudent.getId());

            if (studentList.isEmpty()) {
                log.info("删除用户：{}，ID：{}", userStudent.getName(), userStudent.getId());
                pgUserStudentService.removeById(userStudent.getId());
            }
        }
    }

    //@Test
    void updateStudentUserId() {
        // 获取所有学生
        List<PgStudent> students = pgbCustomService.students();

        // 对每个学生遍历
        for (PgStudent student : students) {
            log.info("处理学生：{}，ID：{}", student.getName(), student.getId());

            // 查用户
            PgUsers user = pgUsersService.getById(student.getStudentUserId());

            if (ObjectUtil.isNull(user)) {
                continue;
            }

            // 查有没有创建过学生
            PgUserStudent one = pgUserStudentService.getOne(new LambdaQueryWrapper<PgUserStudent>()
                    .eq(PgUserStudent::getUserId, user.getId())
                    .eq(PgUserStudent::getName, student.getName())
                    .last("LIMIT 1")
            );

            // 新关联
            if (ObjectUtil.isNull(one)) {
                one = new PgUserStudent();
                one.setUserId(user.getId());
                one.setCreateTime(student.getCreateTime());
                one.setName(student.getName());
                one.setAvatar("https://cdn.pigaibang.com/common/parent/avatar.png");
                one.setIdentity(IdentityTypeEnum.Self);
                pgUserStudentService.save(one);
            }

            // 回填student
            student.setStudentUserId(one.getId());
            pgbCustomService.update(student);
            log.info("关联成功：{}, ID:{}", one.getName(), one.getId());
        }
    }
}
