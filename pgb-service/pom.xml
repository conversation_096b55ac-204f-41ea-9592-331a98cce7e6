<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.pgb</groupId>
        <artifactId>pgb-server</artifactId>
        <version>${revision}</version>
    </parent>

    <artifactId>pgb-service</artifactId>

    <dependencies>

        <!-- Mybatis -->
        <dependency>
            <groupId>com.pgb</groupId>
            <artifactId>pgb-common-mybatis</artifactId>
        </dependency>

        <!-- 支付 -->
        <dependency>
            <groupId>com.pgb</groupId>
            <artifactId>pgb-common-pay</artifactId>
        </dependency>

        <!-- satoken -->
        <dependency>
            <groupId>com.pgb</groupId>
            <artifactId>pgb-common-satoken</artifactId>
        </dependency>

        <!-- word poi -->
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi-ooxml</artifactId>
        </dependency>

        <!-- 添加 commons-io 依赖 -->
        <dependency>
            <groupId>commons-io</groupId>
            <artifactId>commons-io</artifactId>
            <version>2.11.0</version>
        </dependency>

        <!-- OSS 存储 -->
        <dependency>
            <groupId>com.pgb</groupId>
            <artifactId>pgb-common-oss</artifactId>
        </dependency>

        <dependency>
            <groupId>com.pgb</groupId>
            <artifactId>pgb-common-ocr</artifactId>
        </dependency>

        <!-- pdf 工具 -->
        <dependency>
            <groupId>org.apache.pdfbox</groupId>
            <artifactId>pdfbox</artifactId>
        </dependency>

        <!--  提取html文字 -->
        <dependency>
            <groupId>org.jsoup</groupId>
            <artifactId>jsoup</artifactId>
            <version>1.16.1</version>
        </dependency>

        <dependency>
            <groupId>com.pgb</groupId>
            <artifactId>pgb-common-llm</artifactId>
        </dependency>

        <!-- diff 工具  -->
        <dependency>
            <groupId>io.github.java-diff-utils</groupId>
            <artifactId>java-diff-utils</artifactId>
            <version>4.12</version>
        </dependency>

        <!-- 获取文字拼音 -->
        <dependency>
            <groupId>com.github.houbb</groupId>
            <artifactId>pinyin</artifactId>
            <version>0.4.0</version>
        </dependency>

    </dependencies>

</project>
