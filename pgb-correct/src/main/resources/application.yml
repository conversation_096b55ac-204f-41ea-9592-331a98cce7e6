# 【服务器】基本配置
server:
  port: 8082
  #  port: 8088
  servlet:
    context-path: /api
    session:
      timeout: 120s

# 【Spring】 配置
spring:
  profiles:
    active: @profileActive@
  jackson:
    time-zone: GMT+8
    date-format: yyyy-MM-dd HH:mm:ss
  servlet:
    multipart:
      max-file-size: 10MB
      max-request-size: 30MB

# 【对象存储】OSS
oss:
  tencent:
    secret-id: AKIDrwc3Dm95pKH1Zqtm9G7RZS3VheOGr8Bz
    secret-key: gV4NQDk23C6k8KHZdSQkpeEZ6nFTSXNy
    region: ap-beijing
    bucket-name:
      - pgb-1300104514
    cdn-domain: https://cdn.pigaibang.com
  ali:
    secret-id: LTAI5tEVz9RiUWrnX9We7H7a
    secret-key: ******************************
    endpoint: oss-cn-beijing.aliyuncs.com
    region: oss-cn-beijing
    cdn-domain: https://cdn.pigaibang.com
    bucket-name: pigaibang
  ali-gd:
    secret-id: LTAI5tEVz9RiUWrnX9We7H7a
    secret-key: ******************************
    endpoint: oss-cn-beijing.aliyuncs.com
    region: oss-cn-beijing
    cdn-domain: https://cdn-gd.pigaibang.com
    bucket-name: pgb-guidang
  baidu:
    secret-id: ALTAKKC0EdMoq61wyx0mi3L1sA
    secret-key: 224c72848ed64d6b8639e5fce9ac9a1c
    endpoint: bj.bcebos.com
    cdn-domain: https://bos-cdn.pigaibang.com
    bucket-name: pgb-cdn
    sts-endpoint: sts.bj.baidubce.com

# 【百度ocr】 密钥
baidu:
  ocr:
    api-id: 45511560
    api-key: Z89kUSyIiYhrkzq9KqDUPvGW
    api-secret: 4ExDFFGYH6IGVyGzVU72FF3mP1RuLZWx

# 豆包TTS配置
doubao:
  tts:
    app-id: 4381946153
    access-key: kAx-boB_DVgoLXzQHB9GKOYUD9cwd808
    resource-id: volc.service_type.10029
    default-speaker: zh_male_jieshuonansheng_mars_bigtts
    default-format: mp3
    default-sample-rate: 24000
    timeout: 30000
