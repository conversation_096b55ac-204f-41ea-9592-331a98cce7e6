package com.pgb.common.oss.service;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.img.ImgUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.net.url.UrlBuilder;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.URLUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import com.baidubce.BceClientConfiguration;
import com.baidubce.auth.BceCredentials;
import com.baidubce.auth.BceV1Signer;
import com.baidubce.auth.DefaultBceCredentials;
import com.baidubce.auth.SignOptions;
import com.baidubce.http.HttpMethodName;
import com.baidubce.internal.InternalRequest;
import com.baidubce.services.bos.BosClient;
import com.baidubce.services.bos.BosClientConfiguration;
import com.baidubce.services.bos.model.*;
import com.baidubce.services.sts.StsClient;
import com.baidubce.util.DateUtils;
import com.pgb.common.oss.property.OssBdProperty;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.awt.image.BufferedImage;
import java.io.File;
import java.net.URI;
import java.net.URL;
import java.time.Duration;
import java.util.*;

/**
 * @Datetime: 2025年06月04日23:32
 * @Description:
 */
@Slf4j
@RequiredArgsConstructor
public class OssBdService extends OssService {

    private final BosClient client;

    private OssBdProperty property;

    private StsClient stsClient;

    public OssBdService(OssBdProperty property) {
        // 构建凭证
        // 初始化一个BosClient
        BosClientConfiguration config = new BosClientConfiguration();
        config.setCredentials(new DefaultBceCredentials(property.getSecretId(), property.getSecretKey()));
        this.client = new BosClient(config);

        // 临时凭证
        BceCredentials credentials = new DefaultBceCredentials(property.getSecretId(), property.getSecretKey());
        this.stsClient = new StsClient(new BceClientConfiguration().withEndpoint(property.getEndpoint()).withCredentials(credentials));

        this.property = property;
    }

    @Override
    public String presignedUrl(String bucketName, String key, Duration expiration) {
        return presignedUrl(
                bucketName, key, expiration, "image/jpeg;image/png;"
        );
    }

    @Override
    public String presignedUrl(String key, Duration expiration) {
        return presignedUrl(
                this.property.getBucketName(), key, expiration
        );
    }

    @Override
    public String presignedUrl(String bucketName, String key, Duration expiration, String contentType) {

        GeneratePresignedUrlRequest request = new GeneratePresignedUrlRequest(bucketName, key);
        request.setExpiration((int) expiration.toSeconds());
        request.setContentType(contentType);
        request.setMethod(HttpMethodName.PUT);


        URL url = this.client.generatePresignedUrl(request);
        return StrUtil.replace(url.toString(), "http://", "https://");
    }

    @Override
    public String presignedUrl(String key, Duration expiration, String contentType) {
        return presignedUrl(
                this.property.getBucketName(), key, expiration, contentType
        );
    }

    @Override
    public String getCdnUrl(String key) {
        if (StrUtil.isEmpty(this.property.getCdnDomain())) {
            throw new RuntimeException("当前 OSS 未配置 CND 域名");
        }

        return UrlBuilder.of(this.property.getCdnDomain()).addPath(key).toString();
    }

    @Override
    public String putCdnImg(String key, BufferedImage image) {
        // 删除临时文件，防止不会删除文件
        File tempFile = FileUtil.createTempFile();

        FileUtil.writeBytes(ImgUtil.toBytes(image, ImgUtil.IMAGE_TYPE_PNG), tempFile);

        // 初始化上传输入流
        ObjectMetadata meta = new ObjectMetadata();

        // 设置ContentType
        meta.setContentType(
                FileUtil.getMimeType(key)
        );

        try {
            this.client.putObject(
                    this.property.getBucketName(),
                    key,
                    tempFile,
                    meta
            );
        } catch (Exception e) {
            log.error("图片处理失败，立即进行重试", e);
            this.client.putObject(
                    this.property.getBucketName(),
                    key,
                    tempFile,
                    meta
            );
        }

        // 主动删除临时文件
        FileUtil.del(tempFile);

        return UrlBuilder.of(this.property.getCdnDomain()).addPath(key).toString();
    }

    @Override
    public String putFile(String key, File file) {
        try {
            this.client.putObject(
                    this.property.getBucketName(),
                    key,
                    file
            );
        } catch (Exception e) {
            log.error("图片处理失败，立即进行重试", e);
            this.client.putObject(
                    this.property.getBucketName(),
                    key,
                    file
            );
        }

        return UrlBuilder.of(this.property.getCdnDomain()).addPath(key).toString();
    }

    @Override
    public Boolean isExist(String key) {
        try {
            return client.doesObjectExist(
                    this.property.getBucketName(),
                    key
            );
        } catch (Exception e) {
            log.error("【BOS】判断文件对象是否存在失败：{}\n", this.property, e);
        }

        return false;
    }

    /**
     * 签名调试链接：https://cloud.baidu.com/signature/index.html
     * @param key
     * @param tags
     */
    @Override
    public void setTagList(String key, String... tags) {
        // 移除首符号
        if (StrUtil.startWith(key, "/")) {
            key = StrUtil.subSuf(key, 1);
        }

        UrlBuilder urlBuilder = UrlBuilder.of("https://" + property.getBucketName() + '.' + property.getEndpoint() + "/" + key);


        // 构造请求头
        Map<String, String> headers = new HashMap<>();
        // 添加时间戳
        Date currentDate = new Date();
        headers.put("x-bce-date", DateUtils.formatAlternateIso8601Date(currentDate));
        // 添加标签
        Map<String, Object> paramMap = new HashMap<>();
        for (String tag : tags) {
            paramMap.put(tag, tag);
        }
        String params = HttpUtil.toParams(paramMap);
        headers.put("x-bce-tagging", params);
        // 添加请求类型
        headers.put("Content-Type", "application/json; charset=utf-8");
        // 添加长度
        headers.put("Content-Length", "0");
        headers.put("Host", property.getBucketName() + ".bj.bcebos.com");

        // 计算签名
        BceCredentials credentials = new DefaultBceCredentials(property.getSecretId(), property.getSecretKey());
        InternalRequest internalRequest = new InternalRequest(
                HttpMethodName.PUT,
                urlBuilder.toURI()
        );
        internalRequest.setHeaders(headers);
        Map<String, String> parameters = new HashMap<>();
        parameters.put("tagging", "");
        internalRequest.setParameters(parameters);

        BceV1Signer signer = new BceV1Signer();
        SignOptions signOptions = new SignOptions();
        signOptions.setTimestamp(currentDate);
        signOptions.setExpirationInSeconds(1800);
        signOptions.setHeadersToSign(new HashSet<>(Arrays.asList("content-length", "content-type", "host", "x-bce-date", "x-bce-tagging")));

        signer.sign(internalRequest, credentials, signOptions);

        HttpRequest request = HttpRequest.put(urlBuilder.toString() + "?tagging");
        request.addHeaders(internalRequest.getHeaders());
        HttpResponse response = request.execute();

        //this.client.getClient().execute(request, BosResponse.class, )
        // log.info(urlBuilder.toString());
        // log.info("请求头：{}",internalRequest.getHeaders());
        // log.info("bos贴标签处理结果:{}", response.body());
    }

    @Override
    public void delete(String key) {
        // 移除首符号
        if (StrUtil.startWith(key, "/")) {
            key = StrUtil.subSuf(key, 1);
        }

        try {
            this.client.deleteObject(
                    this.property.getBucketName(),
                    key
            );
        } catch (Exception e) {
            log.error("删除图片失败：{}", e.getMessage());
        }
    }

    @Override
    public String getResizeUrl(String url) {
        return UrlBuilder.of(url).addQuery("x-bce-process", "image/resize,p_50").build();
    }

    @Override
    public String getFormat(String url, String format) {
        return UrlBuilder.of(url).addQuery("x-bce-process", "image/format," + format).build();
    }


    @Override
    public List<String> listObjects(String path) {

        List<String> imgKeyList = new ArrayList<>();

        // 用户可设置每页最多500条记录
        ListObjectsRequest listObjectsRequest = new ListObjectsRequest(this.property.getBucketName());
        listObjectsRequest.withMaxKeys(500);
        listObjectsRequest.withPrefix(path);
        ListObjectsResponse listObjectsResponse;
        boolean isTruncated = true;
        while (isTruncated) {
            listObjectsResponse = client.listObjects(listObjectsRequest);
            List<BosObjectSummary> contents = listObjectsResponse.getContents();
            imgKeyList.addAll(contents.stream()
                    .map(BosObjectSummary::getKey)
                    .toList()
            );
            isTruncated = listObjectsResponse.isTruncated();
            if (listObjectsResponse.getNextMarker() != null) {
                listObjectsRequest.withMarker(listObjectsResponse.getNextMarker());
            }
        }

        return imgKeyList;
    }

    @Override
    public void download(String key, String pathName) {

    }

    @Override
    public void renameFile(String sourceKey, String destinationKey) {
        // 移除首符号 /
        if (sourceKey.startsWith("/")) {
            sourceKey = StrUtil.subSuf(sourceKey, 1);
        }
        if (destinationKey.startsWith("/")) {
            destinationKey = StrUtil.subSuf(destinationKey, 1);
        }

        try {
            // 拷贝
            client.copyObject(this.property.getBucketName(), sourceKey, this.property.getBucketName(), destinationKey);

            // 删除
            delete(sourceKey);
        } catch (Exception e) {
            log.error("拷贝图片失败：{}", e.getMessage());
            return;
        }
    }
}
