package com.pgb.service.domain.zc.common.textbook.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 批量单词DTO
 *
 * <AUTHOR>
 * Created by 2025/7/25 21:00
 */
@Data
@Schema(description = "批量单词DTO")
public class BatchWordDTO {

    @Schema(title = "单元ID")
    @NotNull(message = "单元ID不能为空")
    private Long unitId;

    @Schema(title = "单词列表")
    @NotEmpty(message = "单词列表不能为空")
    private List<String> words;
}
