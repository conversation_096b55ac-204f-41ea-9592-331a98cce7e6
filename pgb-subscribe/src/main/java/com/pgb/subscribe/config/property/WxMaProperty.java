package com.pgb.subscribe.config.property;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * <AUTHOR>
 * Created by 2024/6/4 19:05
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode
@ToString
@Builder
@Schema(title = "微信开放平台配置")
@ConfigurationProperties(prefix = "wx.ma")
public class WxMaProperty {
    /**
     * 设置微信小程序的appid
     */
    private String appid;

    /**
     * 设置微信小程序的Secret
     */
    private String secret;

    /**
     * 设置微信小程序消息服务器配置的token
     */
    private String token;

    /**
     * 设置微信小程序消息服务器配置的EncodingAESKey
     */
    private String aesKey;

    /**
     * 消息格式，XML或者JSON
     */
    private String msgDataFormat;
}

