package com.pgb.service.domain.answer.answerBatch;

import java.io.Serializable;

import java.util.Date;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.ToString;
import lombok.Builder;
import org.springframework.validation.annotation.Validated;

/**
* 批次提交表
* @TableName pg_answer_batch
*/
@Schema(description = "批次提交表 DTO")
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode
@ToString
@Validated
@Builder
public class PgAnswerBatchDTO implements Serializable {

    @Schema(title = "批次id", type = "string")
    private Long id;

    @Schema(title = "总数")
    private Integer totalNum;

    @Schema(title = "创建时间")
    private Date createTime;

    @Schema(title = "用户id")
    private Long userId;

}
