package com.pgb.xcx.subscribe;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.binarywang.wxpay.bean.profitsharing.request.ProfitSharingReceiverV3Request;
import com.github.binarywang.wxpay.bean.profitsharing.request.ProfitSharingUnfreezeV3Request;
import com.github.binarywang.wxpay.bean.profitsharing.request.ProfitSharingV3Request;
import com.github.binarywang.wxpay.bean.profitsharing.request.ProfitSharingV3Request.Receiver;
import com.github.binarywang.wxpay.bean.profitsharing.result.ProfitSharingReceiverV3Result;
import com.github.binarywang.wxpay.bean.profitsharing.result.ProfitSharingUnfreezeV3Result;
import com.github.binarywang.wxpay.bean.profitsharing.result.ProfitSharingV3Result;
import com.github.binarywang.wxpay.bean.result.WxPayOrderQueryV3Result;
import com.github.binarywang.wxpay.exception.WxPayException;
import com.github.binarywang.wxpay.service.WxPayService;
import com.pgb.common.core.global.GlobalConstants;
import com.pgb.common.core.utils.EnvUtils;
import com.pgb.common.pay.domain.WxPayConfigType;
import com.pgb.common.pay.enums.OrderStatusEnum;
import com.pgb.common.redis.QueueUtils;
import com.pgb.service.db.*;
import com.pgb.service.domain.GlobalXcxConstants;
import com.pgb.service.domain.activity.ActivityDiscountConfig;
import com.pgb.service.domain.activity.PgActivity;
import com.pgb.service.domain.activity.PgActivityUser;
import com.pgb.service.domain.distribution.PgDistribution;
import com.pgb.service.domain.distribution.agency.PgDistributionAgency;
import com.pgb.service.domain.distribution.record.PgDistributionRecord;
import com.pgb.service.domain.order.PgOrder;
import com.pgb.service.domain.user.PgUsers;
import com.pgb.service.domain.vip.PgVipCode;
import com.pgb.service.enums.ProfitStatusEnum;
import com.pgb.xcx.common.SmsService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

import static com.pgb.service.custom.QwMsgService.sendActivityMessage;
import static com.pgb.service.custom.QwMsgService.sendPayMessage;

@Component("OrderQueueSubscribe")
@Slf4j
@RequiredArgsConstructor
public class OrderQueueSubscribe implements CommandLineRunner {

    private final PgOrderService pgOrderService;

    private final PgUsersService pgUsersService;

    private final PgDistributionService pgDistributionService;

    private final PgDistributionRecordService pgDistributionRecordService;

    private final WxPayService wxPayService;

    private final PgVipService pgVipService;

    private final SmsService smsService;

    private final PgActivityUserService pgActivityUserService;

    private final PgVipCodeService pgVipCodeService;

    private final PgActivityService pgActivityService;

    private final PgDistributionAgencyService pgDistributionAgencyService;

    private final PgTagService pgTagService;

    @Value("${pay.notifyUrl}")
    private String notifyUrl;

    @Value("${wx.ma.appid}")
    private String appid;


    @Override
    public void run(String... args) throws Exception {
        if (EnvUtils.isProd()) {
            // 订单超时
            orderExpire();

            // 支付成功
            paySuccess();

            // 活动支付成功
            activityPaySuccess();
        }
    }

    // 订单超时
    private void orderExpire() {
        QueueUtils.subscribeBlockingQueue(GlobalConstants.ORDER_EXPIRE_QUEUE_KEY + ":xcx", (Long orderId) -> {
            try {
                PgOrder order = pgOrderService.getById(orderId);

                // 订单可被关闭的状态
                ArrayList<OrderStatusEnum> waitPay = ListUtil.toList(
                        OrderStatusEnum.Generate,
                        OrderStatusEnum.Paying,
                        OrderStatusEnum.PayFail
                );

                // 判断订单是否可被关闭
                if (ObjectUtil.isNotNull(order) && waitPay.contains(order.getStatus())) {
                    order.setStatus(OrderStatusEnum.Close);
                    // 更新订单
                    pgOrderService.updateById(order);

                    log.info("订单已关闭：{}", orderId);
                }
            } catch (Exception e) {
                log.error("订单关闭异常", e);
            }
        }, true);
    }

    // 支付成功后
    private void paySuccess() {

        QueueUtils.subscribeBlockingQueue(GlobalXcxConstants.XCX_PAY_SUCCESS, (Long orderId) -> {

            // 查订单
            PgOrder order = pgOrderService.getById(orderId);

            // 获取用户
            PgUsers user = pgUsersService.getById(order.getUserId());

            // 只有微信支付才走分账解冻逻辑
            if (ObjectUtil.isNotNull(order.getPayType()) && !order.getPayType().isWxPay()) {
                log.info("非微信支付，跳过分账解冻逻辑，订单号：{}，支付类型：{}", order.getOrderNo(), order.getPayType());
            } else {
                try {

                    WxPayOrderQueryV3Result orderResult = JSONUtil.toBean(order.getPlatformData().toString(), WxPayOrderQueryV3Result.class);

                    log.info("微信支付订单号:{}", orderResult.getTransactionId());

                    // 如果是被邀请的 则新增分销记录表
                    PgDistribution distribution = pgDistributionService.getOne(new LambdaQueryWrapper<PgDistribution>()
                            .eq(PgDistribution::getUserId, user.getId())
                            .eq(PgDistribution::getIsSuccess, true)
                            .last("LIMIT 1")
                    );


                    // 切换微信商户信息
                    wxPayService.switchoverTo(WxPayConfigType.MA.name());

                    // 说明是分销模式
                    if (ObjectUtil.isNotNull(distribution)) {
                        // 判断是代理分销，还是用户分销
                        // 判断邀请人是否是代理人员
                        PgDistributionAgency agency = pgDistributionAgencyService.getOne(new LambdaQueryWrapper<PgDistributionAgency>()
                                .eq(PgDistributionAgency::getUserId, distribution.getShareUserId()));

                        // 如果不是分销代理 才走分账逻辑
                        if (ObjectUtil.isNull(agency)) {
                            PgDistributionRecord record = pgDistributionRecordService.getOne(new LambdaQueryWrapper<PgDistributionRecord>()
                                    .eq(PgDistributionRecord::getOrderId, orderId)
                                    .last("LIMIT 1"));

                            // 根据订单查，是否已经有分销，有 就跳过
                            if (ObjectUtil.isNull(record)) {

                                record = new PgDistributionRecord();
                                record.setIsTransfer(false);
                                record.setShareUserId(distribution.getShareUserId());
                                record.setUserId(order.getUserId());
                                record.setPayAmount(order.getPayAmount());
//                        record.setPayAmount(10);

                                // 如果分销金额，不足 1，分销 1
                                // 29 / 10 = 2
                                record.setPayCommission(Math.max(order.getPayAmount() / 10, 1));
//                        record.setPayCommission(1);
                                record.setCreateTime(new Date());
                                record.setOrderId(orderId);

                                if (ObjectUtil.isNotNull(orderResult)) {
                                    // 微信订单号
                                    record.setTransactionId(orderResult.getTransactionId());
//                            record.setTransactionId("4200002331202407090392257917");
                                }

                                // 商户分账单号
                                record.setOutOrderNo(IdUtil.getSnowflakeNextIdStr());

                                // ------------------- 分账 转佣金 -----------------------------

                                // 先添加接收方信息
                                ProfitSharingReceiverV3Request add = addReceiver(record);

                                try {
                                    ProfitSharingReceiverV3Result addReceiver = wxPayService.getProfitSharingService().addReceiverV3(add);

                                    log.info("添加分账接收人 - {}", addReceiver);

                                    // 再请求分账
                                    ProfitSharingV3Request request = request(record);
                                    try {
                                        ProfitSharingV3Result result = wxPayService.getProfitSharingService().profitSharingV3(request);

                                        record.setStatus(ProfitStatusEnum.PENDING);

                                        pgDistributionRecordService.save(record);

                                        log.info("【请求分账结果】：{}", result);

                                    } catch (WxPayException e) {
                                        log.error("分账异常", e);
                                    }
                                } catch (WxPayException e) {
                                    log.error("添加分账接收人异常", e);
                                }
                            }

                            // ------------------ 异步查询分账状态 ---------------------
                            PgDistributionRecord queryRecord = record;
                            CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {

                                while (true) {
                                    boolean isBreak = queryResult(queryRecord);

                                    // 1 min
                                    ThreadUtil.sleep(1, TimeUnit.MINUTES);

                                    if (isBreak) {
                                        break;
                                    }
                                }
                            });
                            future.whenComplete((v, e) -> {
                                if (e != null) {
                                    log.error("异步查询分账状态 executeForce -->  异常", e);
                                }
                            });
                        }
                        // 是代理模式，直接解冻
                        else {
                            // 直接解冻
                            unfreeze(orderResult.getTransactionId());
                        }
                    } else {
                        // 直接解冻
                        unfreeze(orderResult.getTransactionId());
                    }
                } catch (Exception e) {
                    log.error("用户支付异常", e);
                }
            }

            // --------------- 企微  发支付成功通知 ----------------------
            if (EnvUtils.isProd()) {
                sendPayMessage(order, user.getPhone(), pgVipService.isNewBuy(user.getId()));
            }

        }, true);
    }

    /**
     * 请求分账
     *
     * @param record
     * @return
     */
    private ProfitSharingV3Request request(PgDistributionRecord record) {

        // 查邀请人
        PgUsers shareUser = pgUsersService.getById(record.getShareUserId());

        ProfitSharingV3Request request = new ProfitSharingV3Request();

        // 商户appId
        request.setAppid(appid);
        // 微信订单号
        request.setTransactionId(record.getTransactionId());
        // 分账单号
        request.setOutOrderNo(record.getOutOrderNo());
        // 是否解冻剩余未分资金
        request.setUnfreezeUnsplit(true);

        // 分账接收方
        List<Receiver> receivers = new ArrayList<>();
        Receiver receiver = new Receiver();
        // 个人
        receiver.setType("PERSONAL_OPENID");
        // 个人openId
        receiver.setAccount(shareUser.getWxOpenId());
        // 分账金额
        receiver.setAmount(record.getPayCommission());

        // 分账描述
        receiver.setDescription("红包奖励到账");
        receivers.add(receiver);

        request.setReceivers(receivers);
        return request;
    }

    /**
     * 查询分账结果
     *
     * @return 是否停止继续轮询查询
     */
    private boolean queryResult(PgDistributionRecord record) {

        ProfitSharingV3Result result = null;
        try {

            // 若分账不成功时，则查询分账结果
            if (!record.getStatus().equals(ProfitStatusEnum.SUCCESS)) {
                // 切换微信商户信息
                wxPayService.switchoverTo(WxPayConfigType.MA.name());
                // 查状态
                result = wxPayService.getProfitSharingService().profitSharingQueryV3(record.getOutOrderNo(), record.getTransactionId());

                // item -> item.getType().equals("PERSONAL_OPENID")
                Optional<ProfitSharingV3Result.Receiver> receiver = result.getReceivers().stream().filter(item -> item.getType().equals("PERSONAL_OPENID")).findFirst();

//                for (ProfitSharingV3Result.Receiver receiver : result.getReceivers()) {
//                    // 个人
//                    if (receiver.getType().equals("PERSONAL_OPENID")) {
//                        PgUsers shareUser = pgUsersService.getByOpenId(receiver.getAccount());
//
//                    }
//                }

                if (receiver.isPresent()) {
                    // 更新状态
                    record.setStatus(ProfitStatusEnum.valueOf(receiver.get().getResult()));

                    if (record.getStatus().equals(ProfitStatusEnum.SUCCESS)) {
                        // 佣金 分
                        record.setPayCommission(receiver.get().getAmount());
                        record.setIsTransfer(true);
                        record.setTransferTime(new Date());

                        // 保存
                        pgDistributionRecordService.updateById(record);

                        return true;
                    }

                    // 如果状态为关闭
                    if (record.getStatus().equals(ProfitStatusEnum.CLOSED)) {
                        // 失败原因
                        record.setCloseReason(receiver.get().getFailReason());

                        // 保存
                        pgDistributionRecordService.updateById(record);
                        return true;
                    }
                }
            } else {
                return true;
            }

            log.info("分账结果 - {}", result);
        } catch (WxPayException e) {
            log.error("查询分账结果异常", e);
        }

        return false;
    }

    /**
     * 添加分账接收方
     *
     * @param record
     * @return
     */
    private ProfitSharingReceiverV3Request addReceiver(PgDistributionRecord record) {

        // 查邀请人
        PgUsers shareUser = pgUsersService.getById(record.getShareUserId());

        ProfitSharingReceiverV3Request request = new ProfitSharingReceiverV3Request();
        request.setAppid(appid);
        request.setType("PERSONAL_OPENID");
        request.setAccount(shareUser.getWxOpenId());
        request.setRelationType("DISTRIBUTOR");

        return request;
    }

    /**
     * 解冻不分账资金
     */
    private void unfreeze(String transactionId) {

        ProfitSharingUnfreezeV3Request request = new ProfitSharingUnfreezeV3Request();

        // 微信订单号
        request.setTransactionId(transactionId);
        // 商户分账单号
        request.setOutOrderNo(transactionId);
        // 分账描述
        request.setDescription("解冻全部剩余资金");

        try {
            ProfitSharingUnfreezeV3Result unfreeze = wxPayService.getProfitSharingService().profitSharingUnfreeze(request);

            log.info("解冻剩余资金结果 - {}", unfreeze);
        } catch (WxPayException e) {
            log.error("解冻剩余资金异常", e);
        }
    }

    // 活动支付成功后
    private void activityPaySuccess() {
        QueueUtils.subscribeBlockingQueue(GlobalXcxConstants.XCX_ACTIVITY_PAY_SUCCESS, (Long activityUserId) -> {
            try {

                PgActivityUser activityUser = pgActivityUserService.getById(activityUserId);

                // 获取用户信息
                PgUsers users = pgUsersService.getById(activityUser.getUserId());

                // 获取兑换码
                PgVipCode vipCode = pgVipCodeService.getById(activityUser.getCodeId());

                // 订单
                PgOrder order = pgOrderService.getById(activityUser.getOrderId());

                // 活动
                PgActivity activity = pgActivityService.getById(activityUser.getActivityId());

                // 【兑换码类型】
                if (activity.getBuyType().equals(1)) {
                    if (ObjectUtil.isNotNull(users)) {
                        // 发送短信通知
                        smsService.sendSms(smsService.getSmsConfigProperty().getExchangeCode(), users.getPhone(), vipCode.getCode());
                        log.info("活动兑换码短信发送成功：{}，用户手机号：{}", vipCode.getCode(), users.getPhone());
                    } else {
                        log.error("活动兑换码短信发送失败：{}，用户：{}", vipCode.getCode(), "用户信息为空");
                    }
                    // 企微通知
                    sendActivityMessage(order, users.getPhone(), pgVipService.isCodeNewBuy(users.getId()), activity.getName());
                } else {
                    // 企微通知
                    sendActivityMessage(order, users.getPhone(), pgVipService.isNewBuy(users.getId()), activity.getName());
                }

                // 给用户贴标签
                ActivityDiscountConfig config = JSONUtil.toBean(activity.getConfig().toString(), ActivityDiscountConfig.class);

                if (!config.getBuyUserTagList().isEmpty()) {
                    // 贴标签
                    pgTagService.tagByUserId(users.getId(), config.getBuyUserTagList());
                }

            } catch (Exception e) {
                log.error("活动支付成功后处理", e);
            }

        }, true);
    }
}
