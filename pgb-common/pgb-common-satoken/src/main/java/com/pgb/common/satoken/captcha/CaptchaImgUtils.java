package com.pgb.common.satoken.captcha;

import cn.hutool.core.img.ImgUtil;
import cn.hutool.core.io.resource.ResourceUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import com.pgb.common.satoken.captcha.domain.CaptchaSliderVO;

import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.FileNotFoundException;
import java.util.Date;

public class CaptchaImgUtils {
    // 滑块验证码-随机选取一张地图
    public static CaptchaSliderVO getCaptchaImg() {
        // 1. 随机获取母图

        BufferedImage originalImg = ImgUtil.read(
                ResourceUtil.getResourceObj(StrUtil.format("captcha/original/{}.jpg", RandomUtil.randomInt(1, 9)))
        );

        // 2. 获取滑动图
        BufferedImage sliderImg = ImgUtil.read(
                ResourceUtil.getResourceObj(StrUtil.format("captcha/slider/{}.png", RandomUtil.randomInt(1, 9)))
        );

        // 3. 根据母图，来随机生成一个位置
        Point point = new Point(
                RandomUtil.randomInt(100, originalImg.getWidth() - sliderImg.getWidth()),
                5
        );

        /*
         * 4. 根据母图和滑动图，抠图
         */
        //4.1 滑动图和将母图区域内背景结合
        BufferedImage newSliderImage = new BufferedImage(sliderImg.getWidth(), sliderImg.getHeight(), sliderImg.getType());
        Graphics2D graphics = newSliderImage.createGraphics();
        newSliderImage = graphics.getDeviceConfiguration().createCompatibleImage(sliderImg.getWidth(), sliderImg.getHeight(), Transparency.TRANSLUCENT);
        // 新建的图像根据模板颜色赋值,源图生成遮罩
        // 此时 原图已有遮罩，newSliderImage 是用户移动的滑块图
        cutByTemplate(originalImg, sliderImg, newSliderImage, point.x, 0);

//        try {
//            ImgUtil.writePng(newSliderImage, new FileOutputStream("newSliderImage.png"));
//            ImgUtil.writePng(originalImg, new FileOutputStream("originalImg.png"));
//        } catch (Exception e) {
//            e.printStackTrace();
//        }

        // 设置“抗锯齿”的属性
        graphics.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
        graphics.setStroke(new BasicStroke(5, BasicStroke.CAP_BUTT, BasicStroke.JOIN_BEVEL));
        graphics.drawImage(newSliderImage, 0, 0, null);
        graphics.dispose();
        return CaptchaSliderVO.builder()
                .verifyKey(IdUtil.simpleUUID())
                .img(ImgUtil.toBase64(originalImg, "png"))
                .sliderImg(ImgUtil.toBase64(newSliderImage, "png"))
                .point(point)
                .createTime(new Date())
                .isVerify(false)
                .build();
    }

    public static void main(String[] args) throws FileNotFoundException {
        getCaptchaImg();
    }

    /**
     * @param oriImage      原图
     * @param templateImage 模板图
     * @param newImage      新抠出的小图
     * @param x             随机扣取坐标X
     * @param y             随机扣取坐标y
     * @throws Exception
     */
    private static void cutByTemplate(BufferedImage oriImage, BufferedImage templateImage, BufferedImage newImage, int x, int y) {
        //临时数组遍历用于高斯模糊存周边像素值
        int[][] martrix = new int[3][3];
        int[] values = new int[9];

        int xLength = templateImage.getWidth();
        int yLength = templateImage.getHeight();
        // 模板图像宽度
        for (int i = 0; i < xLength; i++) {
            // 模板图片高度
            for (int j = 0; j < yLength; j++) {
                // 如果模板图像当前像素点不是透明色 copy源文件信息到目标图片中
                int rgb = templateImage.getRGB(i, j);
                if (rgb < 0) {
                    newImage.setRGB(i, j, oriImage.getRGB(x + i, y + j));

                    //抠图区域高斯模糊
                    readPixel(oriImage, x + i, y + j, values);
                    fillMatrix(martrix, values);
//                    oriImage.setRGB(x + i, y + j, avgMatrix(martrix));
                    oriImage.setRGB(x + i, y + j, Color.black.getRGB());
                }

                //防止数组越界判断
                if (i == (xLength - 1) || j == (yLength - 1)) {
                    continue;
                }
                int rightRgb = templateImage.getRGB(i + 1, j);
                int downRgb = templateImage.getRGB(i, j + 1);
                //描边处理，,取带像素和无像素的界点，判断该点是不是临界轮廓点,如果是设置该坐标像素是白色
                if ((rgb >= 0 && rightRgb < 0) || (rgb < 0 && rightRgb >= 0) || (rgb >= 0 && downRgb < 0) || (rgb < 0 && downRgb >= 0)) {
                    newImage.setRGB(i, j, Color.white.getRGB());
                    oriImage.setRGB(x + i, y + j, Color.white.getRGB());
                }
            }
        }

    }

    private static void readPixel(BufferedImage img, int x, int y, int[] pixels) {
        int xStart = x - 1;
        int yStart = y - 1;
        int current = 0;
        for (int i = xStart; i < 3 + xStart; i++) {
            for (int j = yStart; j < 3 + yStart; j++) {
                int tx = i;
                if (tx < 0) {
                    tx = -tx;

                } else if (tx >= img.getWidth()) {
                    tx = x;
                }
                int ty = j;
                if (ty < 0) {
                    ty = -ty;
                } else if (ty >= img.getHeight()) {
                    ty = y;
                }
                pixels[current++] = img.getRGB(tx, ty);

            }
        }
    }

    private static void fillMatrix(int[][] matrix, int[] values) {
        int filled = 0;
        for (int i = 0; i < matrix.length; i++) {
            int[] x = matrix[i];
            for (int j = 0; j < x.length; j++) {
                x[j] = values[filled++];
            }
        }
    }

    private static int avgMatrix(int[][] matrix) {
        int r = 0;
        int g = 0;
        int b = 0;
        for (int i = 0; i < matrix.length; i++) {
            int[] x = matrix[i];
            for (int j = 0; j < x.length; j++) {
                if (j == 1) {
                    continue;
                }
                Color c = new Color(x[j]);
                r += c.getRed();
                g += c.getGreen();
                b += c.getBlue();
            }
        }
        return new Color(r / 8, g / 8, b / 8).getRGB();
    }
}
