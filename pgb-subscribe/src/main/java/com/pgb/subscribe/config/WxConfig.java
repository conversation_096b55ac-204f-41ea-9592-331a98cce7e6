package com.pgb.subscribe.config;

import cn.binarywang.wx.miniapp.api.WxMaService;
import cn.binarywang.wx.miniapp.api.impl.WxMaServiceImpl;
import cn.binarywang.wx.miniapp.config.impl.WxMaRedissonConfigImpl;
import cn.hutool.core.bean.BeanUtil;
import com.github.binarywang.wxpay.config.WxPayConfig;
import com.github.binarywang.wxpay.service.WxPayService;
import com.github.binarywang.wxpay.service.impl.WxPayServiceImpl;
import com.pgb.common.pay.config.WxMaPayProperty;
import com.pgb.common.redis.RedisUtils;
import com.pgb.subscribe.config.property.WxMaProperty;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Slf4j
@Configuration
@EnableConfigurationProperties({WxMaProperty.class, WxMaPayProperty.class})
@RequiredArgsConstructor
public class WxConfig {

    /**
     * 初始化并配置一个WxMaService的实例
     * @return
     */
    @Bean
    public WxMaService wxMaService(WxMaProperty wxMaProperty) {

        WxMaService maService = new WxMaServiceImpl();

        // 生成 redis 缓存配置
        WxMaRedissonConfigImpl redissonConfig = new WxMaRedissonConfigImpl(RedisUtils.getClient());
        // 配置微信小程序信息
        BeanUtil.copyProperties(wxMaProperty, redissonConfig);

        maService.setWxMaConfig(redissonConfig);

        return maService;
    }
}
