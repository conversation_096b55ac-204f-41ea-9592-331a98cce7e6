package com.pgb.service.domain.zc.question.chinese.textBlank;

import com.pgb.service.domain.zc.common.ZcLocation;
import com.pgb.service.enums.ZcQuestionTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
@Schema(title = "课文填空")
public class TextBlank {

    @Schema(title = "题目类型", description = "课文填空或情景式出题")
    private ZcQuestionTypeEnum type;

    @Schema(title = "位置信息")
    private ZcLocation location;

    @Schema(title = "课文列表")
    private List<TextBlankItem> content;

    @Schema(title = "显示配置")
    private Config config;

    @Data
    public static class Config {

        @Schema(title = "标题")
        private String title;

        @Schema(title = "标题大小")
        private Integer titleSize;

        @Schema(title = "信息大小")
        private Integer textInfoSize;

        @Schema(title = "课文名称：四号")
        private Integer textNameSize;

        @Schema(title = "字体大小：四号")
        private Integer textSize;

        @Schema(title = "课文字体，0：宋体，1：楷体")
        private Integer textFont;

        @Schema(title = "课文填空类型，0：隐藏单字、1：隐藏单句")
        private Integer type;

        @Schema(title = "是否显示姓名")
        private Boolean showName;

        @Schema(title = "是否显示学号")
        private Boolean showStudentNo;

        @Schema(title = "是否显示成绩")
        private Boolean showScore;

        @Schema(title = "格子类型 0:方格，1：田字格，2：米字格，3：括号")
        private String gridType;

        @Schema(title = "格子颜色，0：green，1：red，2：black")
        private String gridColor;

        @Schema(title = "文字颜色")
        private String wordColor;

        @Schema(title = "隐藏代替项，0：横线，1：括号，2：田字格，拼音")
        private Integer hideType;

        @Schema(title = "是否显示拼音")
        private Boolean showPy;
    }
}

