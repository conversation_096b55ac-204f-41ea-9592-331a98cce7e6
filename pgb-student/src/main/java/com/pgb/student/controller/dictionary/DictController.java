package com.pgb.student.controller.dictionary;

import cn.hutool.core.util.EnumUtil;
import com.pgb.common.core.global.BaseResult;
import com.pgb.common.core.global.GlobalCode;
import com.pgb.service.enums.IdentityTypeEnum;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * Created by 2025/3/3 14:17
 */
@Tag(name = "学生端/字典管理")
@RequestMapping("/student/dict")
@RestController("studentDictController")
@Transactional(rollbackFor = Exception.class)
@RequiredArgsConstructor
public class DictController {

    public record EnumMapItem(String value, String label, String comment) {
    }

    @Operation(summary = "获取指定枚举字典Key的Map列表")
    @GetMapping("getEnumMapByKey/{key}")
    public BaseResult<List<EnumMapItem>> getEnumMapByKey(@PathVariable String key) {

        List<EnumMapItem> mapList = new ArrayList<>();

        // 学生身份
        if (key.equals(IdentityTypeEnum.class.getSimpleName())) {
            EnumUtil.getNames(IdentityTypeEnum.class).forEach(item -> {
                mapList.add(
                        new EnumMapItem(
                                item,
                                IdentityTypeEnum.valueOf(item).desc,
                                null
                        )
                );
            });
        } else {
            return BaseResult.code(GlobalCode.Item_Null);
        }

        return BaseResult.success(mapList);
    }
}
