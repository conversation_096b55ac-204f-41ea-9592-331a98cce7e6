package com.pgb.service.domain.user;

import java.io.Serializable;

import java.util.Date;

import cn.hutool.core.util.ObjectUtil;
import com.fasterxml.jackson.annotation.JsonGetter;
import com.pgb.service.enums.VipTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.ToString;
import lombok.Builder;

/**
*
* @TableName pg_users
*/
@Schema(description = " VO")
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode
@ToString
@Builder
public class PgUsersVO implements Serializable {

    @Schema(title = "用户", type = "string")
    private Long id;

    @Schema(title = "用户昵称")
    private String nickName;

    @Schema(title = "头像")
    private String avatarImgUrl;

    @Schema(title = "绑定手机号")
    private String phone;

    @Schema(title = "用户注册来源")
    private Integer userFrom;

    @Schema(title = "微信openId")
    private String wxOpenId;

    @Schema(title = "微信unionId")
    private String wxUnionId;

    @Schema(title = "微信服务号openid")
    private String wxMpOpenId;

    @Schema(title = "创建时间")
    private Date createTime;

    @Schema(title = "上次登录时间")
    private Date lastLoginTime;

    @Schema(title = "会员过期时间")
    private Date vipExpireTime;

    @Schema(title = "0：新购，1：续费")
    private Integer buyType;

    @Schema(title = "会员开通时间",description = "若开通若干次，查最新的")
    private Date openTime;

    @Schema(title = "会员类型，0：年卡，1：月卡")
    private VipTypeEnum vipType;

    @Schema(title = "备注信息")
    private String remark;

    @JsonGetter
    public Boolean getIsVip() {
        if (ObjectUtil.isNull(this.vipExpireTime)) {
            return false;
        }

        return this.vipExpireTime.after(new Date());
    }

    @Schema(title = "会员类型")
    @JsonGetter("vipTypeStr")
    public String getVipTypeStr() {
        return ObjectUtil.isNotNull(this.vipType) ? this.vipType.desc : null;
    }

}
