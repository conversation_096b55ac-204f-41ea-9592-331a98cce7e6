package com.pgb.service.db;

import com.baomidou.mybatisplus.extension.service.IService;
import com.pgb.service.domain.tag.PgTag;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【pg_tag】的数据库操作Service
 * @createDate 2024-12-23 16:22:03
 */
public interface PgTagService extends IService<PgTag> {

    /**
     * 根据用户id 获取 标签
     * @param id
     * @return
     */
    List<PgTag> listByUserId(Long id);

    /**
     * 贴标签
     * @param userId
     * @param tagIds
     * @return
     */
    Boolean tagByUserId(Long userId, List<Long> tagIds);
}
