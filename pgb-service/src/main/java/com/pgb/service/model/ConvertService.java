package com.pgb.service.model;

/**
 * <AUTHOR>
 * Created by 2024/8/13 15:49
 */

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.URLUtil;
import jodd.io.IOUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.pdfbox.Loader;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.text.PDFTextStripper;
import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.apache.poi.xwpf.usermodel.XWPFParagraph;
import org.apache.poi.xwpf.usermodel.XWPFRun;
import org.springframework.stereotype.Component;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.URL;
import java.net.URLConnection;

/**
 * 文档识别 服务
 */
@Component
@Slf4j
public class ConvertService {

    /**
     * 提取 PDF文字
     *
     * @param PDFUrl
     * @return
     */
    public String pdf2Text(String PDFUrl) {

        // 构建url
        URL url = URLUtil.url(PDFUrl);
        try {
            URLConnection opened = url.openConnection();

            InputStream inputStream = opened.getInputStream();

            File tempFile = FileUtil.createTempFile();
            tempFile.deleteOnExit();
            FileOutputStream outputStream = new FileOutputStream(tempFile.getPath());
            // 将InputStream写入outputStream
            IOUtil.copy(inputStream, outputStream);

            outputStream.close();
            inputStream.close();

            // 读取 pdf 文档
            PDDocument document = Loader.loadPDF(tempFile);

            // 创建PDFTextStripper对象
            PDFTextStripper pdfStripper = new PDFTextStripper();

            // 读取并提取PDF中的文本内容
            return pdfStripper.getText(document);

        } catch (IOException e) {
            e.printStackTrace();
            return "PDF 识别异常";

        }
    }

    /**
     * 提取word文字
     *
     * @param wordUrl
     * @return
     */
    public String word2Text(String wordUrl) {

        // 构建url
        URL url = URLUtil.url(wordUrl);
        try {
            URLConnection opened = url.openConnection();

            InputStream inputStream = opened.getInputStream();

            // 读取 word 文档
            XWPFDocument document = new XWPFDocument(inputStream);

            StringBuilder textBuilder = new StringBuilder();

            for (XWPFParagraph paragraph : document.getParagraphs()) {
                for (XWPFRun run : paragraph.getRuns()) {
                    String text = run.getText(0);

                    if (ObjectUtil.isNotNull(text)) {
                        textBuilder.append(text);
                    }
                }
            }
            document.close();
            inputStream.close();

            return textBuilder.toString();

        } catch (IOException e) {
            e.printStackTrace();
            return "word 识别异常";

        }
    }
}
