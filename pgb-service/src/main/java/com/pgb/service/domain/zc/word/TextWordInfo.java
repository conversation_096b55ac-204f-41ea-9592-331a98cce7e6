package com.pgb.service.domain.zc.word;

import cn.hutool.core.util.ObjectUtil;
import com.fasterxml.jackson.annotation.JsonGetter;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * Created by 2025/4/23 11:34
 */
@Data
public class TextWordInfo {

    @Schema(title = "第几课")
    private String lesson;

    @Schema(title = "课文名称")
    private String textName;

    @Schema(title = "字词列表")
    private List<WordItem> wordList;


//    @JsonGetter
//    public String getSortStr() {
//
//        return ObjectUtil.isNull(sort) ? "" : "第" + sort + "课";
//    }
}
