package com.pgb.service.domain.homework;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;

import com.pgb.common.mybatis.handler.JsonbTypeHandler;
import com.pgb.service.enums.CorrectStatusEnum;
import lombok.Data;

/**
 *
 * @TableName pg_homework_report
 */
@TableName(value ="pg_homework_report")
@Data
public class PgHomeworkReport {
    /**
     * 班级作业报告
     */
    @TableId
    private Long id;

    /**
     * 作业id
     */
    private Long homeworkId;

    /**
     * 生成用户id
     */
    private Long userId;

    /**
     * 生成状态
     */
    private CorrectStatusEnum status;

    /**
     * 生成的报告内容
     */
    @TableField(typeHandler = JsonbTypeHandler.class)
    private Object report;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 完成时间
     */
    private Date completeTime;

    /**
     * 生成时间，单位：秒
     */
    private Integer duration;

    /**
     * token
     */
    private Integer tokens;
}
