package com.pgb.manage.controller.system;

import cn.dev33.satoken.annotation.SaCheckRole;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.pgb.common.core.global.BaseResult;
import com.pgb.common.core.global.RoleConstants;
import com.pgb.service.db.SystemConfigService;
import com.pgb.service.domain.systemConfig.SystemConfig;
import com.pgb.service.enums.SystemConfigEnum;
import com.pgb.service.domain.systemConfig.SystemXcxConfig;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.util.Date;

/**
 * <AUTHOR>
 * Created by 2024/12/2 14:35
 */
@Tag(name = "管理端/版本管理")
@RestController("ManageSystemController")
@Transactional(rollbackFor = Exception.class)
@RequestMapping("/manage/system")
@RequiredArgsConstructor
@Slf4j
@SaCheckRole(RoleConstants.Manager)
public class SystemController {

    private final SystemConfigService systemConfigService;

    @Operation(summary = "获取小程序版本信息", description = "内部管理系统使用")
    @GetMapping("versionInfo")
    public BaseResult<SystemXcxConfig> getXcxVersionInfo() {

        SystemConfig systemConfig = systemConfigService.getByKeyByCache(SystemConfigEnum.XCX_VERSION);

        if (ObjectUtil.isNull(systemConfig)) {
            return BaseResult.success(new SystemXcxConfig());
        }

        SystemXcxConfig xcxConfig = BeanUtil.toBean(systemConfig.getValue(), SystemXcxConfig.class);

        return BaseResult.success(xcxConfig);
    }

    @Operation(summary = "配置小程序版本信息")
    @PostMapping("setVersionInfo")
    public BaseResult<Boolean> setVersionInfo(@RequestBody SystemXcxConfig config) {

        SystemConfig systemConfig = systemConfigService.getByKeyByCache(SystemConfigEnum.XCX_VERSION);

        if (ObjectUtil.isNull(systemConfig)) {

            systemConfig = new SystemConfig();
            systemConfig.setKey(SystemConfigEnum.XCX_VERSION.name());
            systemConfig.setValue(config);
            systemConfig.setIsValid(true);
            systemConfig.setIsShow(true);
            systemConfig.setRemark("小程序线上版本");
            systemConfig.setCreateTime(new Date());
            systemConfigService.save(systemConfig);
        } else {

            // 清除缓存
            systemConfigService.removeCache(SystemConfigEnum.XCX_VERSION);

            systemConfig.setValue(config);
            systemConfig.setUpdateTime(new Date());
            systemConfigService.updateById(systemConfig);
        }
        return BaseResult.success(true);
    }


}
