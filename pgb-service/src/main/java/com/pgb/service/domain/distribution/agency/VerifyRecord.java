package com.pgb.service.domain.distribution.agency;

import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import com.fasterxml.jackson.annotation.JsonGetter;
import com.pgb.service.enums.ProfitStatusEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * Created by 2025/2/28 12:02
 */
@Data
public class VerifyRecord {

    @Schema(title = "被邀请人昵称")
    private String nickName;

    @Schema(title = "被邀请人头像")
    private String avatarUrl;

    @Schema(title = "下单时间")
    private Date createTime;

    @Schema(title = "用户实际支付金额")
    private Integer payAmount;

    @Schema(title = "分销金额（单位：分）")
    private Integer distributeAmount;

    @Schema(title = "订单id")
    private Long orderId;

    @Schema(title = "转账状态")
    private ProfitStatusEnum status;


    @JsonGetter
    public String getPayStr() {
        return NumberUtil.decimalFormat("0.00", ObjectUtil.isNotNull(this.payAmount) ? this.payAmount / 100.0 : 0);
    }

    @JsonGetter
    public String getDistributionStr() {
        return NumberUtil.decimalFormat("0.00", ObjectUtil.isNotNull(this.distributeAmount) ? this.distributeAmount / 100.0 : 0);
    }
}
