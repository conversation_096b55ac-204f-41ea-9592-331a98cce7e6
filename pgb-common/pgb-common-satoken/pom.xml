<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.pgb</groupId>
        <artifactId>pgb-common</artifactId>
        <version>${revision}</version>
    </parent>

    <artifactId>pgb-common-satoken</artifactId>
    <description>权限校验</description>

    <dependencies>
        <dependency>
            <groupId>com.pgb</groupId>
            <artifactId>pgb-common-core</artifactId>
        </dependency>

        <dependency>
            <groupId>com.pgb</groupId>
            <artifactId>pgb-common-redis</artifactId>
        </dependency>

        <!-- sa-token 权限认证, 在线文档：http://sa-token.dev33.cn/ -->
        <dependency>
            <groupId>cn.dev33</groupId>
            <artifactId>sa-token-spring-boot3-starter</artifactId>
        </dependency>

        <!-- Sa-Token 整合 Redis （使用 jdk 默认序列化方式） -->
        <dependency>
            <groupId>cn.dev33</groupId>
            <artifactId>sa-token-redisson-jackson</artifactId>
        </dependency>


    </dependencies>

</project>
