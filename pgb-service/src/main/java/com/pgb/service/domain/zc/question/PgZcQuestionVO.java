package com.pgb.service.domain.zc.question;

import java.io.Serializable;

import java.util.Date;

import com.fasterxml.jackson.annotation.JsonGetter;
import com.pgb.service.enums.GradeEnum;
import com.pgb.service.enums.SubjectEnum;
import com.pgb.service.enums.ZcQuestionTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.ToString;
import lombok.Builder;

/**
*
* @TableName pg_zc_question
*/
@Schema(description = " VO")
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode
@ToString
@Builder
public class PgZcQuestionVO implements Serializable {

    @Schema(title = "默写题库表", type = "string")
    private Long id;

    @Schema(title = "用户id")
    private Long userId;

    @Schema(title = "科目，注意，不同的科目，默写类型是不同的")
    private SubjectEnum subject;

    @Schema(title = "默写类型，0：通用，1：情景式填空")
    private ZcQuestionTypeEnum type;

    @JsonGetter
    public String getTypeStr() {
        return type.desc;
    }

    @Schema(title = "题目分数")
    private Integer score;

    @Schema(title = "题目名称")
    private String name;

    @Schema(title = "年级，可选")
    private GradeEnum grade;

    @Schema(title = "是否官方题目")
    private Boolean isOfficial;

    @Schema(title = "排序")
    private Integer sort;

    @Schema(title = "创建时间")
    private Date createTime;

    @Schema(title = "修改时间")
    private Date updateTime;

}
