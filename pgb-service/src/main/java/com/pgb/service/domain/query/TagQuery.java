package com.pgb.service.domain.query;

import com.pgb.common.mybatis.global.PageQuery;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * Created by 2024/12/23 18:29
 */
@EqualsAndHashCode(callSuper = true)
@Schema(description = "标签 搜索实体")
@Data
public class TagQuery extends PageQuery {

    @Schema(title = "标签名称")
    private String name;
}
