package com.pgb.service.db.impl;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.pgb.common.core.global.RoleConstants;
import com.pgb.common.satoken.LoginVO;
import com.pgb.service.db.PgUsersService;
import com.pgb.service.domain.user.PgUsers;
import com.pgb.service.domain.user.PgUsersVO;
import com.pgb.service.enums.DeviceTypeEnum;
import com.pgb.service.mapper.PgUsersMapper;
import me.chanjar.weixin.common.bean.WxOAuth2UserInfo;
import org.springframework.stereotype.Service;

import java.util.Date;

/**
* <AUTHOR>
* @description 针对表【pg_users】的数据库操作Service实现
* @createDate 2024-06-19 13:37:53
*/
@Service
public class PgUsersServiceImpl extends ServiceImpl<PgUsersMapper, PgUsers>
    implements PgUsersService {

    @Override
    public PgUsers getOrCreateUserByPhone(String phone) {
        // 先查有没有
        PgUsers pgUsers = getOne(new LambdaQueryWrapper<PgUsers>().eq(PgUsers::getPhone, phone), false);

        if (ObjectUtil.isNull(pgUsers)) {
            return registerUser(phone, null, null, null);
        }

        return pgUsers;
    }

    @Override
    public LoginVO<PgUsersVO> loginSuccess(PgUsers user, DeviceTypeEnum deviceType) {
        // -- 设置上次登录时间
        user.setLastLoginTime(new Date());

        updateById(user);

        // 保存信息，生成token，保存token
        if (!StpUtil.isLogin()) {
            StpUtil.login(user.getId(), ObjectUtil.defaultIfNull(deviceType, DeviceTypeEnum.XCX).name());
        }

        // 保存用户信息
        StpUtil.getSession().set(RoleConstants.User, user);

        // 保存角色
        StpUtil.getSession().set("role", RoleConstants.User);

        // 返回数据
        LoginVO<PgUsersVO> loginVO = new LoginVO<>();
        loginVO.setName(user.getNickName());
        loginVO.setPhone(user.getPhone());
        loginVO.setToken(StpUtil.getTokenValue());
        loginVO.setExpireTime(DateUtil.offsetSecond(new Date(), (int) StpUtil.getTokenTimeout()).getTime());
        loginVO.setAvatarUrl(user.getAvatarImgUrl());
        loginVO.setUserInfo(BeanUtil.copyProperties(user, PgUsersVO.class));

        return loginVO;
    }

    @Override
    public void addVipDay(Long userId, Integer duration) {
        PgUsers user = getById(userId);

        if (ObjectUtil.isNull(user.getVipExpireTime())) {
            user.setVipExpireTime(new Date());
        }

        // 判断是否过期
        if (user.getVipExpireTime().before(new Date())) {
            user.setVipExpireTime(
                    DateUtil.offsetDay(new Date(), duration)
            );
        } else {
            user.setVipExpireTime(
                    DateUtil.offsetDay(user.getVipExpireTime(), duration)
            );
        }

        // 保存
        updateById(user);
    }

    @Override
    // 注册用户
    public PgUsers registerUser(String phone, String openId, String unionId, WxOAuth2UserInfo wxMpUser) {

        // 新用户注册，将生成的新用户信息保存到数据库中
        PgUsers user = new PgUsers();

        // 初始化用户数据
        if (ObjectUtil.isNull(wxMpUser)) {
            // 默认生成用户数数据
            user.setNickName("邦友" + StrUtil.subSufByLength(phone, 4));
            user.setAvatarImgUrl("https://cdn.pigaibang.com/common/xcx-zw/avatar.png");
        } else {
            user.setNickName(ObjectUtil.defaultIfBlank(
                    wxMpUser.getNickname(),
                    StrUtil.format("微信用户 {}", StrUtil.subSufByLength(wxMpUser.getOpenid(), 4))
            ));
            user.setAvatarImgUrl(wxMpUser.getHeadImgUrl());
        }

        user.setCreateTime(new Date());
        user.setLastLoginTime(new Date());

        // 手机
        if (!StrUtil.isBlank(phone)) user.setPhone(phone);

        // openId
        if (!StrUtil.isBlank(openId)) user.setWxOpenId(openId);

        // unionId
        if (!StrUtil.isEmpty(unionId)) user.setWxUnionId(unionId);

        save(user);

        return user;
    }

    @Override
    public PgUsers getByOpenId(String account) {
        return getOne(new LambdaQueryWrapper<PgUsers>()
                .eq(PgUsers::getWxOpenId, account)
                .last("LIMIT 1")
        );
    }

}




