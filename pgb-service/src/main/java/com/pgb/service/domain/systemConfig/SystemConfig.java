package com.pgb.service.domain.systemConfig;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.util.Date;

import com.pgb.common.mybatis.handler.JsonbTypeHandler;
import lombok.Data;

/**
 * 系统配置
 *
 * @TableName system_config
 */
@TableName(value = "system_config", autoResultMap = true)
@Data
public class SystemConfig implements Serializable {
    /**
     * 配置key
     */
    @TableId
    private String key;

    /**
     * 配置内容
     */
    @TableField(typeHandler = JsonbTypeHandler.class)
    private Object value;

    /**
     * 是否启用
     */
    private Boolean isValid;

    /**
     * 管理系统是否可见
     */
    private Boolean isShow;

    /**
     * 备注信息
     */
    private String remark;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;
}
