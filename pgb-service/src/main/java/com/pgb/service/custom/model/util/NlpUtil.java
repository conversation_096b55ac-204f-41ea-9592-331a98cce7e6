package com.pgb.service.custom.model.util;

import cn.hutool.core.util.CharUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
public class NlpUtil {
    /**
     * 寻找字符串子串下标
     * 只认为单词部分是相等的，去掉标点符号
     *
     * @param wordList
     * @param search
     * @return
     */
//    public static int findEnglishSubString(List<String> wordList, String search) {
//
//        // 获取当前段落所有字符
//        List<String> searchList = List.of(search.split(""));
//
//        // 寻找两个数组中相同的部分
//        int arrayIndex = findSubArrayIndex(wordList, searchList);
//
//        return arrayIndex;
//    }

    public static int findEnglishSubString(List<String> wordList, String search) {
        // 记录原列表中有效字符的索引位置
        List<Integer> originalIndexes = new ArrayList<>();
        List<String> filteredWordList = new ArrayList<>();

        for (int i = 0; i < wordList.size(); i++) {
            String s = wordList.get(i);
            if (!s.isBlank() && !CharUtil.isBlankChar(s.charAt(0)) && !CharUtil.isEmoji(s.charAt(0))) {
                filteredWordList.add(s);
                originalIndexes.add(i); // 记录有效字符在原列表中的位置
            }
        }

        // 过滤search中的无效字符
        List<String> filteredSearchList = Arrays.stream(search.split(""))
                .filter(s -> !s.isBlank() && !CharUtil.isBlankChar(s.charAt(0)) && !CharUtil.isEmoji(s.charAt(0)))
                .collect(Collectors.toList());

        // 在过滤后的列表中查找匹配位置
        int filteredIndex = findSubArrayIndex(filteredWordList, filteredSearchList);

        // 将过滤后的索引映射回原列表索引
        return filteredIndex == -1 ? -1 : originalIndexes.get(filteredIndex);
    }


    private static int findSubArrayIndex(List<String> mainArray, List<String> subArray) {

        if (subArray.isEmpty() || mainArray.size() < subArray.size()) {
            return -1;
        }

        for (int i = 0; i <= mainArray.size() - subArray.size(); i++) {
            boolean isMatch = true;
            for (int j = 0; j < subArray.size(); j++) {

                if (!mainArray.get(i + j).equals(subArray.get(j))) {
                    isMatch = false;
                    break;
                }

            }
            if (isMatch) {
                return i;
            }
        }
        return -1;
    }

    public static List<Integer> findChineseSubString(List<String> wordList, String search, int fromIndex) {
        List<String> searchList = List.of(search.split(""));

        return findSubArrayIndex(wordList, searchList, fromIndex);
    }

    private static List<Integer> findSubArrayIndex(List<String> mainArray, List<String> subArray, int fromIndex) {
        List<Integer> result = new ArrayList<>();
        if (subArray.isEmpty() || mainArray.size() < subArray.size()) {
            result.add(-1);
            return result;
        }

        int mainIndex = fromIndex;
        int subIndex = 0;
        List<Integer> tempMatches = new ArrayList<>();  // 临时存储匹配位置

        while (mainIndex < mainArray.size() && subIndex < subArray.size()) {
            // 跳过主数组中的非中文字符
            while (mainIndex < mainArray.size() && !mainArray.get(mainIndex).matches("[\\u4E00-\\u9FA5]")) {
                mainIndex++;
            }

            // 跳过子数组中的非中文字符
            while (subIndex < subArray.size() && !subArray.get(subIndex).matches("[\\u4E00-\\u9FA5]")) {
                subIndex++;
            }

            // 边界检查
            if (mainIndex >= mainArray.size() || subIndex >= subArray.size()) {
                break;
            }

            if (mainArray.get(mainIndex).equals(subArray.get(subIndex))) {
                tempMatches.add(mainIndex);  // 暂存匹配位置
                mainIndex++;
                subIndex++;
            } else {
                // 当前字符不匹配
                if (!tempMatches.isEmpty()) {
                    // 如果之前有部分匹配，需要回退
                    mainIndex = tempMatches.get(0) + 1;  // 从第一个匹配位置的下一个开始
                    subIndex = 0;
                    tempMatches.clear();  // 清空临时匹配
                } else {
                    mainIndex++;  // 没有匹配过，继续前进
                }
            }
        }

        // 检查是否完全匹配
        // 在完全匹配时返回所有匹配位置的索引
        // 修改返回逻辑
        if (subIndex == subArray.size()) {
            if (!tempMatches.isEmpty()) {
                return List.of(tempMatches.get(0), tempMatches.get(tempMatches.size()-1));
            }
        }
        return List.of(-1, -1);
    }

}
