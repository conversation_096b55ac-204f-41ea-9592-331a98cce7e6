
package com.pgb.service.domain.agent.cost;

import java.io.Serializable;

import java.util.Date;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.ToString;
import lombok.Builder;
import org.springframework.validation.annotation.Validated;

/**
* 智能体会话生成记录表
* @TableName pg_agent_cost
*/
@Schema(description = "智能体会话生成记录表 DTO")
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode
@ToString
@Validated
@Builder
public class PgAgentCostDTO implements Serializable {

    @Schema(title = "会话生成记录id")
    private Long id;

    @Schema(title = "创建时间")
    private Date createTime;

    @Schema(title = "用户id")
    private Long userId;

    @Schema(title = "关联会话id")
    private Long chatId;

    @Schema(title = "智能体id")
    private Long agentId;

}
