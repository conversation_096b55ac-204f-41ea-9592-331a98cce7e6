package com.pgb.manage.domain.query;

import com.pgb.common.mybatis.global.PageQuery;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * Created by 2025/2/14 12:04
 */
@EqualsAndHashCode(callSuper = true)
@Schema(description = "智能体 搜索实体")
@Data
public class AgentQuery extends PageQuery {

    @Schema(title = "智能体名称")
    String name;

    @Schema(title = "智能体分类id")
    Long categoryId;
}
