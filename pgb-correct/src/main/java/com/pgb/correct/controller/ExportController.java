package com.pgb.correct.controller;

import com.pgb.common.core.global.BaseResult;
import com.pgb.service.custom.ZcQuesPdfService;
import com.pgb.service.db.PgZcQuestionService;
import com.pgb.service.domain.zc.question.PgZcQuestion;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * Created by 2025/7/7 10:32
 */
@RestController("ExportController")
@RequestMapping("/export")
@RequiredArgsConstructor
@Slf4j
public class ExportController {

    private final PgZcQuestionService pgZcQuestionService;

    private final ZcQuesPdfService zcQuesPdfService;

    @Operation(summary = "字词题目导出pdf")
    @GetMapping("zc/{zcQuesId}")
    public BaseResult<Boolean> zcExport(@PathVariable Long zcQuesId) {

        log.info("【接收到字词题目渲染pdf监听信息】：{}", zcQuesId);

        try {
            PgZcQuestion zcQuestion = pgZcQuestionService.getById(zcQuesId);

            // 执行渲染
            zcQuesPdfService.renderPdf(zcQuestion);

        } catch (Exception e) {
            log.error("【字词题目渲染pdf队列异常】：", e);

            return BaseResult.error("云函数 字词题目渲染pdf队列异常");
        }

        return BaseResult.success(true);

    }

}
