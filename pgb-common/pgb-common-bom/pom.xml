<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.pgb</groupId>
    <artifactId>pgb-common-bom</artifactId>
    <version>${revision}</version>
    <packaging>pom</packaging>

    <description>
        common-bom common依赖项
    </description>

    <properties>
        <revision>1.0.2</revision>
    </properties>

    <dependencyManagement>
        <dependencies>
            <!-- 核心模块 -->
            <dependency>
                <groupId>com.pgb</groupId>
                <artifactId>pgb-common-core</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- 接口模块 -->
            <dependency>
                <groupId>com.pgb</groupId>
                <artifactId>pgb-common-doc</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- 消息队列 -->
            <dependency>
                <groupId>com.pgb</groupId>
                <artifactId>pgb-common-mq</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- 数据库服务 -->
            <dependency>
                <groupId>com.pgb</groupId>
                <artifactId>pgb-common-mybatis</artifactId>
                <version>${revision}</version>
            </dependency>

            <!--OCR 识别-->
            <dependency>
                <groupId>com.pgb</groupId>
                <artifactId>pgb-common-ocr</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- 对象存储 -->
            <dependency>
                <groupId>com.pgb</groupId>
                <artifactId>pgb-common-oss</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- 缓存服务 -->
            <dependency>
                <groupId>com.pgb</groupId>
                <artifactId>pgb-common-redis</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- 权限 -->
            <dependency>
                <groupId>com.pgb</groupId>
                <artifactId>pgb-common-satoken</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- 短信服务 -->
            <dependency>
                <groupId>com.pgb</groupId>
                <artifactId>pgb-common-sms</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- 多租户 -->
            <dependency>
                <groupId>com.pgb</groupId>
                <artifactId>pgb-common-tenant</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- 微信模块 -->
            <dependency>
                <groupId>com.pgb</groupId>
                <artifactId>pgb-common-wx</artifactId>
                <version>${revision}</version>
            </dependency>

            <!--  支付模块  -->
            <dependency>
                <groupId>com.pgb</groupId>
                <artifactId>pgb-common-pay</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- AI 大模型-->
            <dependency>
                <groupId>com.pgb</groupId>
                <artifactId>pgb-common-llm</artifactId>
                <version>${revision}</version>
            </dependency>

        </dependencies>
    </dependencyManagement>

</project>
