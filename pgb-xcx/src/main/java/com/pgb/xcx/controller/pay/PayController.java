package com.pgb.xcx.controller.pay;

import cn.dev33.satoken.annotation.SaCheckLogin;
import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.servlet.JakartaServletUtil;
import cn.hutool.json.JSONUtil;
import com.alipay.api.AlipayApiException;
import com.alipay.api.AlipayClient;
import com.alipay.api.domain.AlipayTradeQueryModel;
import com.alipay.api.request.AlipayTradeQueryRequest;
import com.alipay.api.response.AlipayTradeQueryResponse;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.binarywang.wxpay.bean.notify.SignatureHeader;
import com.github.binarywang.wxpay.bean.notify.WxPayNotifyV3Result;
import com.github.binarywang.wxpay.bean.request.WxPayUnifiedOrderV3Request;
import com.github.binarywang.wxpay.bean.result.WxPayOrderQueryV3Result;
import com.github.binarywang.wxpay.bean.result.WxPayUnifiedOrderV3Result;
import com.github.binarywang.wxpay.bean.result.enums.TradeTypeEnum;
import com.github.binarywang.wxpay.exception.WxPayException;
import com.github.binarywang.wxpay.service.WxPayService;
import com.pgb.common.core.global.BaseResult;
import com.pgb.common.core.global.GlobalCode;
import com.pgb.common.core.global.GlobalConstants;
import com.pgb.common.core.utils.EnvUtils;
import com.pgb.common.mybatis.global.PageQuery;
import com.pgb.common.pay.domain.WxPayConfigType;
import com.pgb.common.pay.enums.BuyTypeEnum;
import com.pgb.common.pay.enums.OrderStatusEnum;
import com.pgb.common.pay.enums.OrderTypeEnum;
import com.pgb.common.pay.enums.PayTypeEnum;
import com.pgb.common.redis.QueueUtils;
import com.pgb.service.db.*;
import com.pgb.service.domain.GlobalXcxConstants;
import com.pgb.service.domain.activity.PgActivity;
import com.pgb.service.domain.activity.PgActivityUser;
import com.pgb.service.domain.order.OrderPayParam;
import com.pgb.service.domain.order.PgOrder;
import com.pgb.service.domain.user.PgUsers;
import com.pgb.service.domain.vip.*;
import com.pgb.service.enums.ChannelTypeEnum;
import com.pgb.service.enums.VipTypeEnum;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.time.Duration;
import java.util.Date;
import java.util.List;

@Tag(name = "用户端/订单/支付")
@RestController("UserOrderController")
@Transactional(rollbackFor = Exception.class)
@RequestMapping("/user/order/pay")
@RequiredArgsConstructor
@Slf4j
public class PayController {

    private final WxPayService wxPayService;

    private final PgOrderService pgOrderService;

    private final PgVipService pgVipService;

    private final PgUsersService pgUsersService;

    private final PgVipCodeService pgVipCodeService;

    private final PgActivityUserService pgActivityUserService;

    private final PgActivityService pgActivityService;

    private final AlipayClient alipayClient;

    @Value("${pay.notifyUrl}")
    private String notifyUrl;

    @Operation(summary = "我的活动兑换码")
    @PostMapping("code/page")
    @SaCheckLogin
    public BaseResult<IPage<PgVipCodeVO>> codePage(@RequestBody PageQuery query) {

        LambdaQueryWrapper<PgVipCode> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(PgVipCode::getUserId, StpUtil.getLoginIdAsLong());

        // 查用户参加的活动
        List<Long> codeIds = pgActivityUserService.list(new LambdaQueryWrapper<PgActivityUser>()
                        .eq(PgActivityUser::getUserId, StpUtil.getLoginIdAsLong()))
                .stream()
                .map(PgActivityUser::getCodeId)
                .toList();

        if (!codeIds.isEmpty()) {
            wrapper.or().in(PgVipCode::getId, codeIds);
        }

        IPage<PgVipCodeVO> page = pgVipCodeService.page(query.toMpPageSortByCreateTime(), wrapper).convert(code -> {

            PgVipCodeVO codeVO = BeanUtil.copyProperties(code, PgVipCodeVO.class);

            // 获取所属活动
            if (ObjectUtil.isNotNull(code.getChannelId())) {
                PgActivity activity = pgActivityService.getById(code.getChannelId());
                if (ObjectUtil.isNotNull(activity)) {
                    codeVO.setActivityName(activity.getName());
                }
            }
            return codeVO;
        });

        return BaseResult.success(page);
    }

    @Operation(summary = "购买记录")
    @PostMapping("page")
    @SaCheckLogin
    public BaseResult<IPage<PgVipVO>> page(@RequestBody PageQuery query) {
        IPage<PgVipVO> page = pgVipService.page(query.toMpPageSortByCreateTime(), new LambdaQueryWrapper<PgVip>()
                .eq(PgVip::getUserId, StpUtil.getLoginIdAsLong())
        ).convert(item -> {
            PgVipVO vipVO = BeanUtil.toBean(item, PgVipVO.class);

            PgOrder order = pgOrderService.getById(vipVO.getOrderId());

            vipVO.setAmount(order.getPayAmount());

            return vipVO;
        });

        return BaseResult.success(page);
    }

    @Operation(summary = "获取支付信息")
    @GetMapping("pay")
    @SaCheckLogin
    public BaseResult<WxPayUnifiedOrderV3Result.JsapiResult> pay(HttpServletRequest request, VipTypeEnum vipType) {
        PgUsers users = pgUsersService.getById(StpUtil.getLoginIdAsLong());

        return payV2(
                request, vipType, users.getWxOpenId()
        );
    }


    @Operation(summary = "获取支付信息")
    @GetMapping("pay/v2")
    @SaCheckLogin
    public BaseResult<WxPayUnifiedOrderV3Result.JsapiResult> payV2(HttpServletRequest request, VipTypeEnum vipType, String openId) {
        return BaseResult.success(
                payV3(request, vipType, openId).getData().getParams()
        );
    }

    @Operation(summary = "【小程序】获取支付信息")
    @GetMapping("pay/v3")
    @SaCheckLogin
    public BaseResult<OrderPayParam<WxPayUnifiedOrderV3Result.JsapiResult>> payV3(HttpServletRequest request, VipTypeEnum vipType, String openId) {

        if (StrUtil.isBlank(openId)) {
            return BaseResult.error("当前openId为空");
        }

        // 生成订单
        PgOrder order = pgOrderService.generateOrder(
                vipType,
                PayTypeEnum.WX_MINI,
                BuyTypeEnum.PAY,
                JakartaServletUtil.getClientIP(request, null),
                StpUtil.getLoginIdAsLong()
        );

        // 向redis 队列中，添加延迟队列
        // 30分钟后过期
        QueueUtils.addDelayedQueueObjectInTransaction(GlobalConstants.ORDER_EXPIRE_QUEUE_KEY + ":xcx", order.getId(), Duration.ofMinutes(60));

        WxPayUnifiedOrderV3Request orderV3Request = new WxPayUnifiedOrderV3Request();
        // 店铺商户号  xxxx_WX_MINI
        orderV3Request.setDescription(order.getDescription());
        orderV3Request.setOutTradeNo(order.getOrderNo().concat("_" + PayTypeEnum.WX_MINI.name()));
        orderV3Request.setTimeExpire(DateUtil.format(DateUtil.offsetMinute(order.getCreateTime(), 30), "yyyy-MM-dd'T'HH:mm:ssXXX"));
        orderV3Request.setAttach(order.getId().toString());
        // 回调地址
        orderV3Request.setNotifyUrl(
                notifyUrl + "/user/order/pay/wxPayNotify"
        );
        // 金额
        orderV3Request.setAmount(
                new WxPayUnifiedOrderV3Request.Amount().setTotal(
                        EnvUtils.isDev() ? 10 : order.getPayAmount()
                )
        );

        // 支付者
        orderV3Request.setPayer(
                new WxPayUnifiedOrderV3Request.Payer().setOpenid(openId)
        );
        orderV3Request.setSceneInfo(
                new WxPayUnifiedOrderV3Request.SceneInfo().setPayerClientIp(order.getClientIp())
        );

        // 设置分账标识
        orderV3Request.setSettleInfo(
                new WxPayUnifiedOrderV3Request.SettleInfo().setProfitSharing(true));

        // 获取支付信息
        try {
            wxPayService.switchover(WxPayConfigType.MA.name());
            WxPayUnifiedOrderV3Result.JsapiResult result = wxPayService.createOrderV3(
                    TradeTypeEnum.JSAPI,
                    orderV3Request
            );

            log.info("【微信小程序支付应答】: {}", result);

            OrderPayParam<WxPayUnifiedOrderV3Result.JsapiResult> payParam = new OrderPayParam<>();
            payParam.setOrderId(order.getId());
            payParam.setParams(result);

            return BaseResult.success(payParam);
        } catch (WxPayException e) {
            log.error("【微信小程序支付】异常：", e);
            return BaseResult.error("支付异常");
        }
    }

    @Operation(summary = "获取支付信息",description = "h5页面使用")
    @GetMapping("pay/h5")
    @SaCheckLogin
    public BaseResult<OrderPayParam<WxPayUnifiedOrderV3Result.JsapiResult>> payV4(HttpServletRequest request, VipTypeEnum vipType, String mpOpenId) {

        if (StrUtil.isBlank(mpOpenId)){
            return BaseResult.error("当前mpOpenId为空");
        }

        // 进行下单
        PgOrder order = new PgOrder();
        // 获取订单号
        order.setOrderNo(IdUtil.getSnowflakeNextIdStr());
        // SKU 信息
        PgVip pgVip = new PgVip();
        pgVip.setDuration(vipType.dayNum);
        pgVip.setVipType(vipType);
        pgVip.setBuyType(BuyTypeEnum.PAY);
        order.setSku(pgVip);
        // 商品标题
        order.setTitle("批改邦VIP" + vipType.desc);
        // 商品描述
        order.setDescription("批改邦VIP" + vipType.desc);
        // 金额
        order.setTotalAmount(vipType.price);
        // 实际支付金额
        order.setPayAmount(vipType.price);
        // 是否已支付
        order.setIsPay(false);
        order.setIsRefund(false);
        // 订单类型
        order.setOrderType(OrderTypeEnum.Pay);
        // 订单状态
        order.setStatus(OrderStatusEnum.Generate);
        // 下单时间
        order.setCreateTime(new Date());
        // 订单过期时间
        order.setExpireTime(DateUtil.offsetMinute(new Date(), 60));
        // 保存用户信息
        order.setUserId(StpUtil.getLoginIdAsLong());
        // 获取ip
        order.setClientIp(JakartaServletUtil.getClientIP(request, null));
        // 支付方式
        order.setPayType(PayTypeEnum.WX_H5);

        // 数据库，保存订单
        pgOrderService.save(order);

        // 向redis 队列中，添加延迟队列
        // 30分钟后过期
        QueueUtils.addDelayedQueueObjectInTransaction(GlobalConstants.ORDER_EXPIRE_QUEUE_KEY + ":xcx", order.getId(), Duration.ofMinutes(60));

        WxPayUnifiedOrderV3Request orderV3Request = new WxPayUnifiedOrderV3Request();
        orderV3Request.setDescription(order.getDescription());
        orderV3Request.setOutTradeNo(order.getOrderNo().concat("_" + PayTypeEnum.WX_H5.name()));
        orderV3Request.setTimeExpire(DateUtil.format(DateUtil.offsetMinute(order.getCreateTime(), 30), "yyyy-MM-dd'T'HH:mm:ssXXX"));
        orderV3Request.setAttach(order.getId().toString());
        // 回调地址
        orderV3Request.setNotifyUrl(
                notifyUrl + "/user/order/pay/wxPayNotify"
        );
        // 金额
        orderV3Request.setAmount(
                new WxPayUnifiedOrderV3Request.Amount().setTotal(
                        EnvUtils.isDev() ? 10 : order.getPayAmount()
                )
        );

        // 支付者
        orderV3Request.setPayer(
                new WxPayUnifiedOrderV3Request.Payer().setOpenid(mpOpenId)
        );
        orderV3Request.setSceneInfo(new WxPayUnifiedOrderV3Request.SceneInfo()
                .setPayerClientIp(order.getClientIp()));

        // 设置分账标识
        orderV3Request.setSettleInfo(
                new WxPayUnifiedOrderV3Request.SettleInfo().setProfitSharing(true)
        );

        // 获取支付信息
        try {
            wxPayService.switchover(WxPayConfigType.MP.name());
            WxPayUnifiedOrderV3Result.JsapiResult result = wxPayService.createOrderV3(
                    TradeTypeEnum.JSAPI,
                    orderV3Request
            );

            log.info("【微信H5支付应答】: {}", result);

            OrderPayParam<WxPayUnifiedOrderV3Result.JsapiResult> payParam = new OrderPayParam<>();
            payParam.setOrderId(order.getId());
            payParam.setParams(result);

            return BaseResult.success(payParam);

        } catch (WxPayException e) {
            log.error("【微信H5支付】异常：", e);
            return BaseResult.error("微信H5支付异常");
        }
    }

    // https://pay.weixin.qq.com/doc/v3/merchant/4013070347
    @Operation(summary = "APP支付")
    @GetMapping("pay/app")
    @SaCheckLogin
    public BaseResult<OrderPayParam<WxPayUnifiedOrderV3Result.AppResult>> payV3(HttpServletRequest request, VipTypeEnum vipType) {
        PgOrder order = new PgOrder();
        // 获取订单号
        order.setOrderNo(IdUtil.getSnowflakeNextIdStr());
        // SKU 信息
        PgVip pgVip = new PgVip();
        pgVip.setDuration(vipType.dayNum);
        pgVip.setVipType(vipType);
        pgVip.setBuyType(BuyTypeEnum.PAY);
        order.setSku(pgVip);
        // 商品标题
        order.setTitle("批改邦VIP" + vipType.desc);
        // 商品描述
        order.setDescription("批改邦VIP" + vipType.desc);
        // 金额
        order.setTotalAmount(vipType.price);
        // 实际支付金额
        order.setPayAmount(vipType.price);
        // 是否已支付
        order.setIsPay(false);
        order.setIsRefund(false);
        // 订单类型
        order.setOrderType(OrderTypeEnum.Pay);
        // 订单状态
        order.setStatus(OrderStatusEnum.Generate);
        // 下单时间
        order.setCreateTime(new Date());
        // 订单过期时间
        order.setExpireTime(DateUtil.offsetMinute(new Date(), 60));
        // 保存用户信息
        order.setUserId(StpUtil.getLoginIdAsLong());
        // 获取ip
        order.setClientIp(JakartaServletUtil.getClientIP(request, null));
        // 支付方式
        order.setPayType(PayTypeEnum.WX_APP);

        // 数据库，保存订单
        pgOrderService.save(order);

        // 向redis 队列中，添加延迟队列
        // 30分钟后过期
        QueueUtils.addDelayedQueueObjectInTransaction(GlobalConstants.ORDER_EXPIRE_QUEUE_KEY + ":xcx", order.getId(), Duration.ofMinutes(60));

        WxPayUnifiedOrderV3Request orderV3Request = new WxPayUnifiedOrderV3Request();
        // 店铺商户号  xxxx_WX_MINI
        orderV3Request.setDescription(order.getDescription());
        orderV3Request.setOutTradeNo(order.getOrderNo().concat("_" + PayTypeEnum.WX_MINI.name()));
        orderV3Request.setTimeExpire(DateUtil.format(DateUtil.offsetMinute(order.getCreateTime(), 30), "yyyy-MM-dd'T'HH:mm:ssXXX"));
        orderV3Request.setAttach(order.getId().toString());
        // 回调地址
        orderV3Request.setNotifyUrl(
                notifyUrl + "/user/order/pay/wxPayNotify"
        );
        // 金额
        orderV3Request.setAmount(
                new WxPayUnifiedOrderV3Request.Amount().setTotal(
                        EnvUtils.isDev() ? 10 : order.getPayAmount()
                )
        );

        orderV3Request.setSceneInfo(
                new WxPayUnifiedOrderV3Request.SceneInfo().setPayerClientIp(order.getClientIp())
        );

        // 设置分账标识
        orderV3Request.setSettleInfo(new WxPayUnifiedOrderV3Request.SettleInfo().setProfitSharing(true));

        // 获取支付信息
        try {
            wxPayService.switchoverTo(WxPayConfigType.APP.name());
            WxPayUnifiedOrderV3Result.AppResult result = wxPayService.createOrderV3(
                    TradeTypeEnum.APP,
                    orderV3Request
            );

            log.info("【微信小程序支付应答-APP】: {}", result);

            OrderPayParam<WxPayUnifiedOrderV3Result.AppResult> payParam = new OrderPayParam<>();
            payParam.setOrderId(order.getId());
            payParam.setParams(result);

            return BaseResult.success(payParam);
        } catch (WxPayException e) {
            log.error("【微信小程序支付-APP】异常：", e);
            return BaseResult.error("支付异常");
        }
    }

    @Operation(summary = "查询支付状态")
    @GetMapping("order/query/{orderId}")
    @SaCheckLogin
    public BaseResult<Boolean> payQuery(@PathVariable Long orderId) {
        PgOrder order = pgOrderService.getById(orderId);

        if (ObjectUtil.isNull(order)) {
            return BaseResult.code(GlobalCode.Item_Null);
        }

        if (order.getPayType().equals(PayTypeEnum.WX_MINI) || order.getPayType().equals(PayTypeEnum.WX_JS) ||  order.getPayType().equals(PayTypeEnum.WX_H5)) {
            log.info("查询微信支付订单状态：{}", orderId);

            // 向微信支付查询订单状态
            try {
                wxPayService.switchover(WxPayConfigType.MA.name());
                WxPayOrderQueryV3Result result;

                if (order.getPayType().equals(PayTypeEnum.WX_H5)){
                    result = wxPayService.queryOrderV3(null, order.getOrderNo().concat("_" + PayTypeEnum.WX_H5.name()));
                } else {
                    result = wxPayService.queryOrderV3(null, order.getOrderNo().concat("_" + PayTypeEnum.WX_MINI.name()));
                }

                if (result.getTradeState().equals("SUCCESS")) {
                    pgOrderService.paySuccess(order.getId(), result.getAmount().getTotal(), result);
                }
                return BaseResult.success(true);
            } catch (WxPayException e) {
                log.error("微信支付查询订单错误", e);
                return BaseResult.success(false);
            }
        }
        // 如果是支付宝支付
        else if (order.getPayType().equals(PayTypeEnum.ALI_APP)){
            try {
                // 构造请求参数以调用接口
                AlipayTradeQueryRequest request = new AlipayTradeQueryRequest();
                AlipayTradeQueryModel model = new AlipayTradeQueryModel();
                model.setOutTradeNo(order.getOrderNo());
                request.setBizModel(model);
                AlipayTradeQueryResponse response = alipayClient.execute(request);
                if ("TRADE_SUCCESS".equals(response.getTradeStatus())) {
                    Integer amount = (int) (NumberUtil.parseDouble(response.getTotalAmount()) * 100);
                    pgOrderService.paySuccess(order.getId(), amount, response);
                }
                return BaseResult.success(true);
            } catch (AlipayApiException e) {
                log.error("支付宝支付查询订单错误", e);
                return BaseResult.success(false);
            }
        }

        return BaseResult.success(false);
    }

    @Operation(summary = "微信支付回调接口", description = "微信支付成功后，微信会调用此接口进行回调")
    @PostMapping("wxPayNotify")
    public void wxPayNotify(HttpServletRequest request, @RequestBody String data) {

        String timestamp = request.getHeader("Wechatpay-Timestamp");
        String nonce = request.getHeader("Wechatpay-Nonce");
        String serialNo = request.getHeader("Wechatpay-Serial");
        String signature = request.getHeader("Wechatpay-Signature");
        log.info("timestamp:{} nonce:{} serialNo:{} signature:{} data:{}", timestamp, nonce, serialNo, signature, data);

        SignatureHeader header = new SignatureHeader(timestamp, nonce, signature, serialNo);

        try {
            // 任意一个就行，只要是同一商户
            wxPayService.switchover(WxPayConfigType.MA.name());
            WxPayNotifyV3Result result = wxPayService.parseOrderNotifyV3Result(data, header);

            Long orderId = Long.valueOf(result.getResult().getAttach());

            // ---- 走支付成功逻辑
            WxPayOrderQueryV3Result result1 = BeanUtil.copyProperties(result.getResult(), WxPayOrderQueryV3Result.class);

            pgOrderService.paySuccess(orderId, result1.getAmount().getTotal(), result1);

            log.info("【微信支付回调】：{}", result);

        } catch (WxPayException e) {
            log.error("微信支付回调错误", e);
        }
    }
}
