package com.pgb.service.domain.zc.question.chinese.pinyinAndWord;

import com.pgb.service.domain.common.image.FilePaperImg;
import com.pgb.service.domain.zc.question.chinese.textBlank.TextBlankItem;
import com.pgb.service.domain.zc.question.chinese.textBlank.TextBlankItem.TextBlankItemMark;
import com.pgb.service.domain.zc.word.WordItem;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * Created by 2025/4/23 18:52
 */
@Data
@Schema(title = "批改结果")
public class PinyinAndWordResult {

    @Schema(title = "用户提交的图片", description = "如果是批改结果，则是渲染的图片")
    private List<FilePaperImg> userImgAnswerList;

    @Schema(title = "用户词语坐标",description = "按每页返回")
    private List<List<WordItem>> userWordList;

}
