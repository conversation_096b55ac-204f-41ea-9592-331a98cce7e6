package com.pgb.student.domain.query;

import com.pgb.common.mybatis.global.PageQuery;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * Created by 2025/3/6 16:25
 */
@EqualsAndHashCode(callSuper = true)
@Schema(description = "题目 搜索实体")
@Data
public class AnswerQuery extends PageQuery {

    @Schema(title = "所属作业名称")
    private String homeworkName;
}

