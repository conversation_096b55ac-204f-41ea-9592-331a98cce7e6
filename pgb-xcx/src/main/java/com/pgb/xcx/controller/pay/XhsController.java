package com.pgb.xcx.controller.pay;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.pgb.common.core.utils.EnvUtils;
import com.pgb.service.db.PgVipChannelService;
import com.pgb.service.db.PgVipCodeService;
import com.pgb.xcx.common.SmsService;
import com.pgb.service.domain.vip.PgVipChannel;
import com.pgb.service.domain.vip.PgVipCode;
import com.pgb.service.enums.ChannelTypeEnum;
import com.pgb.service.enums.VipTypeEnum;
import com.pgb.service.custom.XhsService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.util.Date;

@Tag(name = "用户端/订单/小红书支付")
@RestController("UserOrderXhsController")
@Transactional(rollbackFor = Exception.class)
@RequestMapping("/user/order/xhs")
@RequiredArgsConstructor
@Slf4j
public class XhsController {

    private final XhsService xhsService;

    private final PgVipChannelService pgVipChannelService;

    private final PgVipCodeService pgVipCodeService;
    private final SmsService smsService;

    @Operation(summary = "授权回调")
    @GetMapping("auth")
    public String auth(String code) {
        log.info("授权回调: {}", code);

        xhsService.setAccessToken(code);

        return "授权成功：" + code;
    }

    @Operation(summary = "小红书回调")
    @PostMapping("msg")
    public void msg(@RequestBody String dataStr) {

        // 获取body
        log.info("小红书回调: {}", dataStr);

        // 只处理数组类型
        if (JSONUtil.isTypeJSONArray(dataStr)) {
            JSONArray json = JSONUtil.parseArray(dataStr);

            for (int i = 0; i < json.size(); i++) {
                JSONObject jsonObject = json.get(i, JSONObject.class);

                // 只处理订单类型
                if (!"msg_fulfillment_status_change".equals(jsonObject.getStr("msgTag"))) {
                    continue;
                }

                JSONObject data = jsonObject.getJSONObject("data");

                // 只处理已付费，未发货订单
                if (data.getInt("orderStatus") != 4) {
                    continue;
                }

                // 获取订单号
                String orderId = data.getStr("orderId");

                // 获取订单详情
                JSONObject orderDetails = xhsService.getOrderDetails(orderId);

                // 如果是已发货，跳过
                if (orderDetails.getInt("orderStatus") != 4) {
                    log.info("小红书重复回调，跳过");
                    continue;
                }

                String openAddressId = orderDetails.getStr("openAddressId");

                Integer amount = orderDetails.getInt("merchantActualReceiveAmount");

                // 获取订单发货人
                JSONObject receiver = xhsService.getOrderReceiverInfo(orderId, openAddressId);

                // 遍历skuList
                for (int i1 = 0; i1 < orderDetails.getJSONArray("skuList").size(); i1++) {

                    JSONObject sku = orderDetails.getJSONArray("skuList").getJSONObject(i1);

                    // 获取商品id
                    String skuId = sku.getStr("skuId");

                    // 如果是体验版
                    if ("6698c2fa4ce2400001ee73df".equals(skuId)) {
                        // 直接生成用户信息
                        createVipChannel(receiver, orderId, null, amount);

                        // 发货
                        String deliverInfo = "已为您当前收件人手机号开通权限啦，请直接使用微信，搜索「批改邦」小程序，即可直接体验使用！";
                        xhsService.orderDeliver(orderId, deliverInfo);
                        log.info("小红书已发货：体验版");
                    }
                    // 付费版
                    else {
                        VipTypeEnum type;
                        // 周卡
                        if ("66a77f66f826b80001b1525a".equals(skuId)) {
                            type = VipTypeEnum.WEEK;
                        }
                        // 月卡
                        else if ("66989cdcaa8e9e00012a1c29".equals(skuId)) {
                            type = VipTypeEnum.MONTH;
                        }
                        // 年卡
                        else if ("6698737ba3f69e00010f4cf9".equals(skuId)) {
                            type = VipTypeEnum.YEAR;
                        }
                        // 其他的跳过
                        else {
                            continue;
                        }

                        // 生成用户信息
                        PgVipChannel channel = createVipChannel(receiver, orderId, type, amount);

                        // 生成兑换码
                        PgVipCode vipCode = pgVipCodeService.createVipCode(ChannelTypeEnum.XHS, type, channel.getId(), null);

                        // 发货
                        String deliverInfo = StrUtil.format("您的会员兑换码为：{}，请使用微信搜索【批改邦】小程序（我的-兑换码）页面兑换使用", vipCode.getCode());

                        // 发短信
                        if (EnvUtils.isProd()) {
                            xhsService.orderDeliver(orderId, deliverInfo);
                            smsService.sendSms(smsService.getSmsConfigProperty().getExchangeCode(), channel.getPhone(), vipCode.getCode());
                        }

                        log.info("小红书已发货：{}", type.desc);
                    }
                }
            }
        }

        if (JSONUtil.isTypeJSONObject(dataStr)) {
            JSONObject json = JSONUtil.parseObj(dataStr);
        }
    }

    // 生成用户信息
    private PgVipChannel createVipChannel(JSONObject receiver, String orderId, VipTypeEnum vipType, Integer amount) {
        PgVipChannel channel = new PgVipChannel();
        channel.setPhone(receiver.getStr("receiverPhone"));
        channel.setProvince(receiver.getStr("receiverProvinceName"));
        channel.setCity(receiver.getStr("receiverCityName"));
        channel.setDistrict(receiver.getStr("receiverDistrictName"));
        channel.setTown(receiver.getStr("receiverTownName"));
        channel.setName(receiver.getStr("receiverName"));
        channel.setAddress(receiver.getStr("receiverAddress"));
        channel.setPoi(receiver.getStr("receiverPoi"));
        channel.setChannelType(ChannelTypeEnum.XHS);
        channel.setXhsOrderId(orderId);
        channel.setCreateTime(new Date());
        channel.setVipType(vipType);
        channel.setAmount(amount);

        pgVipChannelService.save(channel);

        return channel;
    }
}
