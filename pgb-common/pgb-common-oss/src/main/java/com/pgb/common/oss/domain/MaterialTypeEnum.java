package com.pgb.common.oss.domain;

import com.baomidou.mybatisplus.annotation.EnumValue;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;

@Schema(description = "素材文件类型")
@AllArgsConstructor
public enum MaterialTypeEnum {
    @Schema(title = "图片", description = "店铺上传的图片")
    IMG(0, "image", "image/jpeg;image/png;"),

    @Schema(title = "音频", description = "店铺上传音频素材")
    AUDIO(1, "audio", "application/octet-stream;"),

    @Schema(title = "视频", description = "店铺上传视频素材")
    VIDEO(2, "video", "image/jpeg;image/png;"),

    @Schema(title = "文档", description = "店铺上传文件")
    FILE(3, "file", "application/msword;application/vnd.openxmlformats-officedocument.wordprocessingml.document;application/pdf;"),

    @Schema(title = "电子书", description = "店铺上传电子书")
    EBOOK(4, "ebook", "application/msword;application/vnd.openxmlformats-officedocument.wordprocessingml.document;application/pdf;"),

    @Schema(title = "用户图片", description = "用户签到等前台业务图")
    USER_IMG(5, "userImg", "image/jpeg;image/png;"),

    @Schema(title = "用户文件", description = "用户提交文档等")
    USER_FILE(6, "userFile", "application/msword;application/vnd.openxmlformats-officedocument.wordprocessingml.document;application/pdf;"),

    @Schema(title = "系统图片", description = "包含文档生成的图片等、后台图片和账号图片")
    SYSTEM_IMG(7, "systemImg", "image/jpeg;image/png;"),

    @Schema(title = "系统音频", description = "包含数字人生成的模拟音频")
    SYSTEM_AUDIO(8, "systemAudio", "image/jpeg;image/png;"),

    @Schema(title = "压缩包")
    USER_ZIP(9, "zip", "application/zip;");

    /**
     * 枚举值对应的整数值
     */
    @EnumValue
    public final int value;

    /**
     * 素材所属 路径前缀
     */
    public final String prefix;

    public final String contentType;


}
