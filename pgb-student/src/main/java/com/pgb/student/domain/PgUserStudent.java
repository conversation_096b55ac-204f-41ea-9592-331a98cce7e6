package com.pgb.student.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.util.Date;

import com.pgb.service.enums.IdentityTypeEnum;
import lombok.Data;

/**
 * 用户学生表（一对多）
 *
 * @TableName pg_user_student
 */
@TableName(value = "pg_user_student")
@Data
public class PgUserStudent implements Serializable {
    /**
     * 用户-学生id
     */
    private Long id;

    /**
     * 关联用户id
     */
    private Long userId;

    /**
     * 学生姓名
     */
    private String name;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 头像
     */
    private String avatar;

    /**
     * 身份标识 0：本人
     */
    private IdentityTypeEnum identity;

}
