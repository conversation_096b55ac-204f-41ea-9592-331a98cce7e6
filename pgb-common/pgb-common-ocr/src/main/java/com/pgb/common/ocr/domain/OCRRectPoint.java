package com.pgb.common.ocr.domain;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(title = "矩形 坐标")
public class OCRRectPoint {

    @Schema(title = "是否正确")
    private Boolean isRight;

    @Schema(title = "x1 坐标")
    private Float x1;

    @Schema(title = "y1 坐标")
    private Float y1;

    @Schema(title = "x2 坐标")
    private Float x2;

    @Schema(title = "y2 坐标")
    private Float y2;
}
