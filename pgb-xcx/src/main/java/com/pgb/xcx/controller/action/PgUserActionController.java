package com.pgb.xcx.controller.action;

import cn.dev33.satoken.annotation.SaCheckLogin;
import cn.dev33.satoken.stp.StpUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.pgb.common.core.global.BaseResult;
import com.pgb.service.db.*;
import com.pgb.service.domain.classes.PgClasses;
import com.pgb.service.domain.homework.PgHomework;
import com.pgb.service.domain.userAction.PgUserAction;
import com.pgb.service.domain.userAction.PgUserActionVO;
import com.pgb.service.enums.UserActionType;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * Created by 2025/1/16 15:12
 */
@Tag(name = "用户端/行为/行为信息")
@RestController("UserActionInfoController")
@Transactional(rollbackFor = Exception.class)
@RequestMapping("/user/action/info")
@RequiredArgsConstructor
@Slf4j
public class PgUserActionController {

    private final PgUserActionService pgUserActionService;

    @Operation(summary = "判断登录用户是否创建过示例作业或班级", description = "当列表为空时使用")
    @GetMapping("isSample")
    @SaCheckLogin
    public BaseResult<Boolean> isSample() {
        return BaseResult.success(
                pgUserActionService.creatExampleClassHomework(StpUtil.getLoginIdAsLong())
        );
    }

}
