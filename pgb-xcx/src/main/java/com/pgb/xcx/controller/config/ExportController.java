package com.pgb.xcx.controller.config;

import cn.dev33.satoken.annotation.SaCheckLogin;
import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.util.ObjectUtil;
import com.pgb.common.core.global.BaseResult;
import com.pgb.service.db.PgUserConfigService;
import com.pgb.service.domain.userConfig.*;
import com.pgb.service.enums.UserConfigEnum;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.util.Date;

/**
 * <AUTHOR>
 * Created by 2024/10/8 17:56
 */
@Tag(name = "用户端/配置/导出配置")
@RestController("UserConfigExportController")
@Transactional(rollbackFor = Exception.class)
@RequestMapping("/user/config/export")
@RequiredArgsConstructor
@Slf4j
public class ExportController {

    private final PgUserConfigService pgUserConfigService;

    @Operation(summary = "获取自定义导出配置")
    @GetMapping
    public BaseResult<ExportConfigDTO> getConfig() {

        return BaseResult.success(
                pgUserConfigService.getExportConfig(StpUtil.getLoginIdAsLong())
        );

    }

    @Operation(summary = "保存自定义导出配置")
    @PostMapping
    public BaseResult<Boolean> save(@RequestBody ExportConfigDTO exportConfig) {

        PgUserConfig config = pgUserConfigService.getByKey(UserConfigEnum.EXPORT, StpUtil.getLoginIdAsLong());

        // 如果没设置过
        if (ObjectUtil.isNull(config)) {
            config = new PgUserConfig();
            config.setUserId(StpUtil.getLoginIdAsLong());
            config.setKey(UserConfigEnum.EXPORT.name());
            config.setValue(exportConfig);
            config.setRemark("自定义导出配置");
            config.setIsValid(true);
            config.setCreateTime(new Date());
            pgUserConfigService.save(config);
        }

        // 如果设置过 则更新
        config.setValue(exportConfig);
        pgUserConfigService.updateById(config);

        // 同步修改批改配置
        //PgUserConfig correctUserConfig = pgUserConfigService.getByKey(UserConfigEnum.CORRECT, StpUtil.getLoginIdAsLong());
        //
        //CorrectConfigDTO correctConfig;
        //
        //if (ObjectUtil.isNull(correctUserConfig)){
        //
        //    correctUserConfig = new PgUserConfig();
        //
        //    correctConfig = new CorrectConfigDTO();
        //    correctUserConfig.setUserId(StpUtil.getLoginIdAsLong());
        //    correctUserConfig.setKey(UserConfigEnum.CORRECT.name());
        //    correctUserConfig.setValue(correctConfig);
        //    correctUserConfig.setRemark("批改配置");
        //    correctUserConfig.setIsValid(true);
        //    correctUserConfig.setCreateTime(new Date());
        //    pgUserConfigService.save(correctUserConfig);
        //} else {
        //    correctConfig = BeanUtil.toBean(config.getValue(), CorrectConfigDTO.class);
        //}
        //
        //BeanUtil.copyProperties(exportConfig, correctConfig);
        //correctUserConfig.setValue(correctConfig);
        //pgUserConfigService.updateById(correctUserConfig);

        return BaseResult.success(true);
    }

    @Operation(summary = "保存自定义导出配置")
    @PostMapping("/v2")
    public BaseResult<Boolean> saveV2(@RequestBody ExportConfigDTO exportConfig) {

        PgUserConfig config = pgUserConfigService.getByKey(UserConfigEnum.EXPORT, StpUtil.getLoginIdAsLong());

        // 如果没设置过
        if (ObjectUtil.isNull(config)) {
            config = new PgUserConfig();
            config.setUserId(StpUtil.getLoginIdAsLong());
            config.setKey(UserConfigEnum.EXPORT.name());
            config.setValue(exportConfig);
            config.setRemark("自定义导出配置");
            config.setIsValid(true);
            config.setCreateTime(new Date());
            pgUserConfigService.save(config);
        }

        // 如果设置过 则更新
        config.setValue(exportConfig);
        pgUserConfigService.updateById(config);

        return BaseResult.success(true);
    }

    @Operation(summary = "V3获取排序的自定义导出配置")
    @GetMapping("/v3")
    @SaCheckLogin
    public BaseResult<ExportConfigVOV3> getConfigV2() {
        ExportConfigVOV3 config = pgUserConfigService.getExportConfigV3(StpUtil.getLoginIdAsLong());
        return BaseResult.success(config);
    }

    @Operation(summary = "V3保存自定义导出配置")
    @PostMapping("/v3")
    @SaCheckLogin
    public BaseResult<Boolean> saveV3(@RequestBody ExportConfigVOV3 config) {
        return BaseResult.success(
                pgUserConfigService.saveExportConfigV3(config, StpUtil.getLoginIdAsLong())
        );
    }
}
