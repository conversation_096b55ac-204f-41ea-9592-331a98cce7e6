package com.pgb.service.domain.zc.question.area;

import com.pgb.service.domain.zc.common.ZcLocation;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * Created by 2025/5/14 17:06
 */
@Data
@Schema(title = "计算区域请求参数")
public class CalcAreaForm {

    @Schema(title = "用户图片")
    private String userImg;

    @Schema(title = "pdf转的图片")
    private String templateImg;

    @Schema(title = "按A4缩放后的坐标")
    private List<ZcLocation> locationList;

}
