package com.pgb.subscribe.subcribe;

import cn.hutool.core.util.NumberUtil;
import com.pgb.common.core.utils.EnvUtils;
import com.pgb.service.custom.ZwCorrectService;
import com.pgb.service.db.PgAnswerService;
import com.pgb.service.domain.GlobQueueConstants;
import com.pgb.service.domain.answer.PgAnswer;
import com.pgb.subscribe.service.CorrectSubscribeService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;

/**
 * 批改 队列监听
 */
@Component("QueueCorrectSubscribe")
@Slf4j
@RequiredArgsConstructor
public class CorrectQueueSubscribe implements CommandLineRunner {

    private final PgAnswerService pgAnswerService;

    private final CorrectSubscribeService correctSubscribeService;

    private final ZwCorrectService zwCorrectService;

    @Override
    public void run(String... args) {

         //if (!EnvUtils.isProd()) {
         //    pgAnswerService.queryToCorrect();
         //    correctSubscribeService.cloudSubscribe(GlobQueueConstants.PGB_XCX_CORRECT_QUEUE_VIP.name());
         //    correctSubscribeService.cloudSubscribe(GlobQueueConstants.PGB_XCX_CORRECT_QUEUE.name());
         //}
        //
        //
        //  PgAnswer answer = pgAnswerService.getById(1912520078344364034L);
        //  //兼容化图片
        //  zwCorrectService.processImg(answer);
        //  zwCorrectService.doCorrect(answer);
        //   pgAnswerService.updateById(answer);

        // pgAnswerService.queryToCorrect();

        // 重批
        // cloudCorrectService.cloudCorrect(1893963601878994946L);

        // pgAnswerService.reCorrectByHomeworkId(1909531148628078593L, false);

        if (EnvUtils.isProd() || EnvUtils.isDev()) {
            // 立即执行一次
            pgAnswerService.queryToCorrect();

            // 20个 体验队列
            correctSubscribeService.subscribe(false, GlobQueueConstants.PGB_XCX_CORRECT_QUEUE.name(), 20);

            // 80个 会员队列
            int processNum = NumberUtil.min(Runtime.getRuntime().availableProcessors() * 10, 80);
            correctSubscribeService.subscribe(false, GlobQueueConstants.PGB_XCX_CORRECT_QUEUE_VIP.name(), processNum);
        }
    }
}
