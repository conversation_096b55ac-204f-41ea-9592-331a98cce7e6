package com.pgb.common.sms;

import com.pgb.common.core.global.GlobalCode;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

@Schema(description = "手机验证码发送结果 DTO")
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode
@ToString
@Builder
public class SmsResDTO {
    @Schema(title = "是否发送成功")
    private Boolean success;

    @Schema(title = "异常状态说明")
    private String msg;

    @Schema(title = "异常状态码")
    private GlobalCode errCode;
}
