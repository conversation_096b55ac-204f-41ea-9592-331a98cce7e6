package com.pgb.service.domain.tag;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.io.Serializable;
import java.util.Date;

/**
*
* @TableName pg_tag
*/
@Schema(description = " VO")
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode
@ToString
@Builder
public class PgTagVO implements Serializable {

    @Schema(title = "用户标签", type = "string")
    private Long id;

    @Schema(title = "标签名称")
    private String name;

    @Schema(title = "创建时间")
    private Date createTime;

    @Schema(title = "使用标签的人数")
    private Long userNum;

}
