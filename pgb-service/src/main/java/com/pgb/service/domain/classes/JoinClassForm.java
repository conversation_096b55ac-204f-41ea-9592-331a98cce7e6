package com.pgb.service.domain.classes;

import com.pgb.service.domain.student.StudentInfoDTO;
import com.pgb.service.enums.IdentityTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * Created by 2024/11/21 11:32
 */
@Schema(title = "学生加入班级 参数")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class JoinClassForm {

    @Schema(title = "班级口令", description = "主动加入班级时使用")
    private String password;


    @Schema(title = "学生信息")
    List<StudentInfoDTO> studentInfo;

//    @Schema(title = "学生姓名")
//    private String name;
//
//    @Schema(title = "身份")
//    private IdentityTypeEnum identityType;

}
