package com.pgb.service.domain.order;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;

import com.pgb.common.mybatis.handler.JsonbTypeHandler;
import com.pgb.common.pay.enums.OrderStatusEnum;
import com.pgb.common.pay.enums.OrderTypeEnum;
import com.pgb.common.pay.enums.PayTypeEnum;
import lombok.Data;

/**
 * 订单系统
 * @TableName pg_order
 */
@TableName(value ="pg_order")
@Data
public class PgOrder implements Serializable {
    /**
     * 订单系统
     */
    @TableId
    private Long id;

    /**
     * 全系统唯一订单号，字符串
     */
    private String orderNo;

    /**
     * sku 信息
     */
    @TableField(typeHandler = JsonbTypeHandler.class)
    private Object sku;

    /**
     * 金额，单位分
     */
    private Integer totalAmount;

    /**
     * 用户实际支付金额
     */
    private Integer payAmount;

    /**
     * 实际支付方式
     */
    private PayTypeEnum payType;

    /**
     * 是否已支付
     */
    private Boolean isPay;

    /**
     * 是否已退款
     */
    private Boolean isRefund;

    /**
     * 是否免费获取
     */
    private Boolean isFree;

    /**
     * 0：系统付费订单，1：系统免费订单，2：兑换订单，3：渠道订单，4：导入订单
     */
    private OrderTypeEnum orderType;

    /**
     * 订单状态，0：订单生成，1：支付中，2：支付成功，3：支付失败，4：已撤销，5：退款中，6：已退款，7：订单关闭
     */
    private OrderStatusEnum status;

    /**
     * 下单时间
     */
    private Date createTime;

    /**
     * 付款时间
     */
    private Date payTime;

    /**
     * 订单过期时间
     */
    private Date expireTime;

    /**
     * 支付订单ip地址
     */
    private String clientIp;

    /**
     * 购买用户id，支持多租户模式
     */
    private Long userId;

    /**
     * 商品标题
     */
    private String title;

    /**
     * 商品描述
     */
    private String description;

    /**
     * 平台支付回调返回数据
     */
    @TableField(typeHandler = JsonbTypeHandler.class)
    private Object platformData;
}
