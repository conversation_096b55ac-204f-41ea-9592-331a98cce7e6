package com.pgb.manage.controller.auth;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.captcha.CaptchaUtil;
import cn.hutool.captcha.ShearCaptcha;
import cn.hutool.captcha.generator.MathGenerator;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.math.Calculator;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.crypto.digest.DigestUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.pgb.common.core.global.BaseResult;
import com.pgb.common.core.global.GlobalCode;
import com.pgb.common.core.global.GlobalConstants;
import com.pgb.common.core.global.RoleConstants;
import com.pgb.common.core.utils.EnvUtils;
import com.pgb.common.redis.RedisUtils;
import com.pgb.common.satoken.LoginVO;
import com.pgb.common.satoken.captcha.domain.CaptchaNumVO;
import com.pgb.manage.service.saas.domain.SystemAdmin;
import com.pgb.manage.service.saas.domain.dto.LoginPasswordFormDTO;
import com.pgb.manage.service.saas.service.SystemAdminService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.awt.*;
import java.time.Duration;
import java.util.Date;

@Tag(name = "管理端/权限/登录")
@RestController("manageAuthLoginController")
@Transactional(rollbackFor = Exception.class)
@RequestMapping("/manage/auth/login")
@RequiredArgsConstructor
@Slf4j
public class LoginController {

    private final SystemAdminService systemAdminService;

    @Operation(summary = "获取图片验证码")
    @GetMapping("captcha")
    public BaseResult<CaptchaNumVO> getCaptcha() {
        // 保存验证码信息
        String uuid = IdUtil.simpleUUID();
        String verifyKey = GlobalConstants.GLOBAL_REDIS_KEY + uuid;
        // 生成验证码
        ShearCaptcha captcha = CaptchaUtil.createShearCaptcha(200, 45, 4, 4);
        // 自定义验证码内容为四则运算方式
        MathGenerator mathGenerator = new MathGenerator(1);
        captcha.setGenerator(mathGenerator);
        captcha.setBackground(new Color(255, 175, 175));
        // 重新生成code
        captcha.createCode();

        // 计算结果
        String calculateResult = String.valueOf((int) Calculator.conversion(captcha.getCode()));
        // 设置有效期，5分钟
        RedisUtils.setCacheObject(verifyKey, calculateResult, Duration.ofMinutes(5));

        return BaseResult.success(
                CaptchaNumVO.builder()
                        .verifyKey(uuid)
                        .img(captcha.getImageBase64())
                        .build()
        );
    }

    @Operation(summary = "账号密码登录")
    @PostMapping("password")
    @DS("manage")
    public BaseResult<LoginVO<SystemAdmin>> password(@Validated @RequestBody LoginPasswordFormDTO formDTO) {

        // 查验证码是否有效
        String verifyKey = GlobalConstants.GLOBAL_REDIS_KEY + formDTO.getVerifyKey();
        String captcha = RedisUtils.getCacheObject(verifyKey);

        // 验证码错误
        if (!formDTO.getCode().equalsIgnoreCase(captcha) && !EnvUtils.isDev()) {
            return BaseResult.error(GlobalCode.Login_Captcha_Error.value, GlobalCode.Login_Captcha_Error.desc);
        }

        // 验证是否有该用户
        SystemAdmin systemAdmin = systemAdminService.getOne(new LambdaQueryWrapper<SystemAdmin>()
                        .eq(SystemAdmin::getPhone, formDTO.getPhone()),
                false
        );

        // 用户不存在
        if (ObjectUtil.isNull(systemAdmin)) {
            return BaseResult.error(GlobalCode.Login_User_Null.value, GlobalCode.Login_User_Null.desc);
        }

        // 判断密码是否正确
        if (!DigestUtil.bcryptCheck(formDTO.getPassword(), systemAdmin.getPassword())) {
            return BaseResult.error(GlobalCode.Login_Password_Error.value, GlobalCode.Login_Password_Error.desc);
        }

        // 登录成功
        StpUtil.login(systemAdmin.getId(), RoleConstants.Manager);
        // 保存用户信息
        StpUtil.getSession().set("role", RoleConstants.Manager);
        StpUtil.getSession().set(RoleConstants.Manager, systemAdmin);

        // 移除验证码
        RedisUtils.deleteObject(verifyKey);

        // 返回数据
        return BaseResult.success(
                LoginVO.<SystemAdmin>builder()
                        .token(StpUtil.getTokenValue())
                        .phone(systemAdmin.getPhone())
                        .expireTime(DateUtil.offsetSecond(new Date(), (int) StpUtil.getTokenTimeout()).getTime())
                        .userInfo(systemAdmin)
                        .build()
        );
    }

}
