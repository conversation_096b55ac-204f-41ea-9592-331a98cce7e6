package com.pgb.service.domain.answer.answerCost;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 作文批改明细表
 * @TableName pg_answer_cost
 */
@TableName(value ="pg_answer_cost")
@Data
public class PgAnswerCost implements Serializable {
    /**
     * 消耗明细 id
     */
    private Long id;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 关联的批改id
     */
    private Long answerId;

    /**
     * 类型，0：作文，1：字词
     */
    private Integer type;

}
