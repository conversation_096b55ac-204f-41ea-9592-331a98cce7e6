package com.pgb.xcx.controller.timer;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.pgb.common.core.global.BaseResult;
import com.pgb.common.redis.QueueUtils;
import com.pgb.service.db.PgExportRecordService;
import com.pgb.service.db.PgHomeworkGridPaperService;
import com.pgb.service.db.PgHomeworkReportService;
import com.pgb.service.db.PgZcQuestionService;
import com.pgb.service.domain.export.PgExportRecord;
import com.pgb.service.domain.homework.PgHomeworkReport;
import com.pgb.service.enums.CorrectStatusEnum;
import com.pgb.service.domain.GlobQueueConstants;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Tag(name = "用户端/定时器/订阅队列")
@RestController("CommonTimerController")
@Transactional(rollbackFor = Exception.class)
@RequestMapping("/user/timer/subscribe")
@RequiredArgsConstructor
@Slf4j
public class CommonSubscribeController {

    private final PgExportRecordService pgExportRecordService;

    private final PgHomeworkReportService pgHomeworkReportService;

    private final PgHomeworkGridPaperService pgHomeworkGridPaperService;

    private final PgZcQuestionService pgZcQuestionService;

    @Operation(summary = "将未导出word加入队列")
    @PostMapping("reExport/{key}")
    public BaseResult<Integer> reExport(@PathVariable String key) {
        if (ObjectUtil.isNull(key)) {
            return BaseResult.error("参数错误");
        }

        if (!"reExport".equals(key)) {
            return BaseResult.error("参数错误");
        }

        // 批改报告
        List<PgExportRecord> list = pgExportRecordService.list(new LambdaQueryWrapper<PgExportRecord>()
                        .and(i -> i.eq(PgExportRecord::getType, 0)
                                .or()
                                .isNull(PgExportRecord::getType))
                        .eq(PgExportRecord::getStatus, CorrectStatusEnum.Uploaded)
                // .lt(PgExportRecord::getCreateTime, DateUtil.offsetMinute(new Date(), -7))
        );

        log.info("【待导出word压缩包扫描】超过7分钟未导出数量:{}个", list.size());

        list.forEach(exportRecord -> {
            // 加入批改队列
            if (!QueueUtils.containsQueueObject(GlobQueueConstants.PGB_XCX_BATCH_EXPORT_WORD_QUEUE.name(), exportRecord.getId())) {
                QueueUtils.addQueueObject(GlobQueueConstants.PGB_XCX_BATCH_EXPORT_WORD_QUEUE.name(), exportRecord.getId());
            } else {
                log.info("当前导出已存在导出队列中：{}，跳过", exportRecord.getId());
            }
        });

        // 作文集
        List<PgExportRecord> studentReports = pgExportRecordService.list(new LambdaQueryWrapper<PgExportRecord>()
                        .eq(PgExportRecord::getType, 2)
                        .eq(PgExportRecord::getStatus, CorrectStatusEnum.Uploaded)
                // .lt(PgExportRecord::getCreateTime, DateUtil.offsetMinute(new Date(), -7))
        );
        log.info("【待导出学生作文集压缩包扫描】超过7分钟未导出数量:{}个", studentReports.size());
        studentReports.forEach(exportRecord -> {
            if (!QueueUtils.containsQueueObject(GlobQueueConstants.PGB_XCX_EXPORT_STUDENT_WORD_QUEUE.name(), exportRecord.getId())) {
                QueueUtils.addQueueObject(GlobQueueConstants.PGB_XCX_EXPORT_STUDENT_WORD_QUEUE.name(), exportRecord.getId());
            } else {
                log.info("当前导出已存在导出队列中：{}，跳过", exportRecord.getId());
            }
        });

        return BaseResult.success(list.size() + studentReports.size());
    }

    @Operation(summary = "将未生成的班级作业报告加入队列")
    @PostMapping("reReport/{key}")
    public BaseResult<Integer> reReport(@PathVariable String key) {
        if (ObjectUtil.isNull(key)) {
            return BaseResult.error("参数错误");
        }

        if (!"reReport".equals(key)) {
            return BaseResult.error("参数错误");
        }

        return BaseResult.success(
                pgHomeworkReportService.queryToExport()
        );
    }

    @Operation(summary = "将未执行标准格子纸加入队列")
    @PostMapping("reGridPaper/{key}")
    public BaseResult<Integer> reGridPaper(@PathVariable String key) {
        if (ObjectUtil.isNull(key)) {
            return BaseResult.error("参数错误");
        }

        if (!"reGridPaper".equals(key)) {
            return BaseResult.error("参数错误");
        }

        return BaseResult.success(
                pgHomeworkGridPaperService.queryToExport()
        );
    }

    @Operation(summary = "将未渲染的字词pdf加入队列")
    @PostMapping("reZcPdf/{key}")
    public BaseResult<Integer> reZcPdf(@PathVariable String key) {
        if (ObjectUtil.isNull(key)) {
            return BaseResult.error("参数错误");
        }

        if (!"reZcPdf".equals(key)) {
            return BaseResult.error("参数错误");
        }

        return BaseResult.success(
                pgZcQuestionService.queryToRender()
        );
    }
}
